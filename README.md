# Système de Contrôle d’Accès RFID/UHF

Ce projet est une solution complète de gestion de contrôle d’accès, combinant une application web (React + Node.js/Express + PostgreSQL) et une application mobile Android (Kotlin, SDK Chainway C72) pour la lecture de badges RFID/UHF.

## Architecture du projet

```
projet-Acces/
│
├── control-acces/      # Application web (frontend + backend + base de données)
│   ├── client/         # Frontend React (Vite, TailwindCSS)
│   ├── server/         # Backend Node.js/Express (API REST, WebSocket)
│   └── database/       # Script SQL PostgreSQL
│
└── UHFControlApp/      # Application mobile Android (Kotlin, Compose, SDK Chainway)
```

---

## 1. Application Web (control-acces)

### A. Frontend (client/)
- **Technos :** React, Vite, TailwindCSS, Axios, React Router, Recharts
- **Installation :**
  ```bash
  cd control-acces/client
  npm install
  npm run dev
  ```
- **Accès :** http://localhost:5173

### B. Backend (server/)
- **Technos :** Node.js, Express, Sequelize, PostgreSQL, WebSocket, JWT
- **Installation :**
  ```bash
  cd control-acces/server
  npm install
  npm start
  ```
- **Accès API :** http://localhost:3011/api
- **WebSocket :** ws://localhost:3012

### C. Base de données (database/)
- **Technos :** PostgreSQL
- **Initialisation :**
  - Créer une base PostgreSQL
  - Exécuter le script `Schema-V1.0.sql` dans `control-acces/database/`

---

## 2. Application Mobile (UHFControlApp)

- **Technos :** Kotlin, Jetpack Compose, OkHttp, Coroutines, SDK Chainway C72
- **Fonction :** Scanner des badges RFID/UHF, envoyer les EPC au backend, recevoir la réponse (autorisé/refusé), bip sonore, interface de test/configuration.
- **Installation :**
  - Ouvrir le dossier `UHFControlApp` dans Android Studio
  - Connecter un appareil Chainway C72 (ou émulateur compatible)
  - Compiler et lancer l’application

---

## 3. Fichiers importants

- `.gitignore` : déjà adapté pour chaque sous-projet
- `README.md` : ce fichier
- `control-acces/database/Schema-V1.0.sql` : structure de la base de données

---

## 4. Sauvegarde sur GitHub

### Initialisation du dépôt (si besoin)
```bash
git init
git add .
git commit -m "Initial commit"
```

### Création du dépôt sur GitHub
1. Aller sur https://github.com/new
2. Créer un nouveau dépôt (nom, description, public/privé)
3. Suivre les instructions pour lier le dépôt distant :
   ```bash
   git remote add origin https://github.com/<ton-utilisateur>/<nom-du-depot>.git
   git branch -M main
   git push -u origin main
   ```

---

## 5. Auteurs
- Projet développé par [Ton Nom ou Équipe]

---

## 6. Licence
- MIT (modifiable selon ton besoin) 

## Fonctionnalité avancée : Écriture RFID à distance

Ce projet permet d’écrire à distance un code EPC sur un badge RFID via l’application mobile Android (C72), pilotée depuis l’interface web.

### Comment ça marche ?

1. **L’utilisateur saisit l’IP du terminal Android (C72) dans le frontend web (une seule fois, mémorisation automatique).**
2. **Depuis la page "Badges", il clique sur "Écrire sur badge" pour un badge donné.**
3. **Le frontend appelle le backend (Node.js) via la route `/api/badges/:id/write` en transmettant l’IP du terminal.**
4. **Le backend récupère l’EPC du badge, puis envoie une requête HTTP POST à `http://<ip_terminal>:8080/write-epc` avec `{ "epc": "..." }`.**
5. **L’application Android écrit l’EPC sur le badge présenté, émet un bip sonore en cas de succès, et retourne le résultat.**
6. **Le message de succès ou d’erreur est affiché directement dans l’interface web.**

### Pré-requis
- Le terminal Android et le backend doivent être sur le même réseau local.
- L’application Android doit être lancée et le serveur HTTP démarré (port 8080).

### Points forts
- Saisie de l’IP du terminal une seule fois (mémorisation dans le navigateur).
- Retour visuel immédiat dans l’interface web (succès/erreur).
- Bip sonore sur le terminal Android à chaque écriture réussie. 