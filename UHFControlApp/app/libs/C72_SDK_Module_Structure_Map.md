# 📚 SDK Chainway C72 – Carte des Modules et Sous-Modules
> Ce document fournit une vue hiérarchique des composants du SDK, classés par domaine fonctionnel.

## 🧭 1. RFID (UHF, NFC, HF)
- `RFIDWithUHFUART` – UHF par UART (standard C72)
- `RFIDWithUHFBLE` – UHF via Bluetooth
- `RFIDWithUHFUSB`, `UHFUrxUart`, `RFIDWithUHFA8`, etc. – variantes par port (USB, RS232)
- `RFIDWithISO14443A` – NFC / MIFARE / CPU cards
- `RFIDWithISO14443B` – cartes ISO14443B
- `RFIDWithISO15693` – cartes longue portée
- `RFIDWithLF` – basse fréquence (125kHz)

## 📷 2. Scanner QR / Code-barres
- `IScanner` – Interface de scan 1D / 2D / QR
- `ScanerLedLight` – LED du moteur de scan
- Constantes : `FUNCTION_1D`, `FUNCTION_2D`, `FUNCTION_2D_H`

## 🎮 3. Triggers et LED
- `Trigger` – Gestion des boutons physiques (gâchette)
- `LedLight` – Contrôle LED système
- `ScanerLedLight` – LED spécifique au moteur de scan

## 🔋 4. Informations Système
- `CWDeviceInfo` – Batterie, Android, SN, modèle
- `DeviceInfo`, `VersionInfo` – Détails matériels

## 🖨️ 5. Impression (si disponible)
- `Printer` – Contrôle d’une imprimante intégrée/externe
- `PrinterStatus`, `PrinterStatusCallBack` – État et callback
- `BarcodeType` – Types de code imprimables

## 🧬 6. Biométrie (optionnel)
- `FingerprintWithZAZ`, `FingerprintWithMorpho` – Modules biométriques
- `FingerprintWithFIPS`, `FingerprintWithTLK1NC` – Sécurité élevée

## 🔌 7. Communication Externe
- `RS232`, `Uart`, `OTG` – Accès aux ports physiques
- `BluetoothReader`, `BleDevice` – Communication BLE
- `Infrared` – Infra-rouge (rare)

## 🧭 8. Navigation / Capteurs
- `BDNavigation`, `BDStatusListener` – GNSS / BeiDou / GPS
- `Compass`, `Sensor` – Orientation, magnétomètre

_Cette structure peut être utilisée pour créer une documentation progressive par domaine._