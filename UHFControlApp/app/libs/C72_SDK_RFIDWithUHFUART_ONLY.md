# 📘 SDK C72 – `RFIDWithUHFUART` (Méthodes essentielles)

## 📦 Classe : `RFIDWithUHFUART`
📄 Fichier : `RFIDWithUHFUART.html`

### 🔹 Méthode : `setOnLowBatteryListener`
```java
public void setOnLowBatteryListener(android.content.Context context,LowBatteryEntityentity,OnLowBatteryListeneronLowBatteryListener)
                             throwsLowBatteryExceptionSpecified by:setOnLowBatteryListenerin interfaceIUHFOfAndroidUartOverrides:setOnLowBatteryListenerin classUhfBaseThrows:LowBatteryExceptiongetInstancepublic staticRFIDWithUHFUARTgetInstance()
                                   throwsConfigurationException
```
📝 获取UHF操作实例Acquire UHF operation Instance

### 🔹 Méthode : `getHardwareVersion`
```java
public java.lang.String getHardwareVersion()Specified by:getHardwareVersionin interfaceIUHFOfAndroidUartsetUartpublic void setUart(java.lang.String uart)
```
📝 设置uhf串口，使用此函数需要谨慎，否则uhf会无法工作。

### 🔹 Méthode : `setPowerOnBySystem`
```java
public void setPowerOnBySystem(android.content.Context context)
```
📝 设置uhf由系统内部上电,不使用DeviceAPI的代码上电!

### 🔹 Méthode : `init`
```java
public boolean init(android.content.Context context)
```
📝 初始化UHF模块Initialize UHF module

### 🔹 Méthode : `getVersion`
```java
public java.lang.String getVersion()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `getPower`
```java
public int getPower()
```
📝 Description copied from interface:ISingleAntenna

### 🔹 Méthode : `setPower`
```java
public boolean setPower(int power)
```
📝 Description copied from interface:ISingleAntenna

### 🔹 Méthode : `getFrequencyMode`
```java
public int getFrequencyMode()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setFrequencyMode`
```java
public boolean setFrequencyMode(int freMode)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `startInventoryTag`
```java
public boolean startInventoryTag()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `startInventoryTag`
```java
public boolean startInventoryTag(InventoryParameterinventoryParameter)readTagFromBuffer@Deprecated
publicUHFTAGInforeadTagFromBuffer()
```
📝 Deprecated.

### 🔹 Méthode : `stopInventory`
```java
public boolean stopInventory()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `inventorySingleTag`
```java
publicUHFTAGInfoinventorySingleTag()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `inventorySingleTag`
```java
publicUHFTAGInfoinventorySingleTag(InventoryParameterinventoryParameter)readDatapublic java.lang.String readData(java.lang.String accessPwd,
                                 int bank,
                                 int ptr,
                                 int cnt)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `readData`
```java
public java.lang.String readData(java.lang.String accessPwd,
                                 int filterBank,
                                 int filterPtr,
                                 int filterCnt,
                                 java.lang.String filterData,
                                 int bank,
                                 int ptr,
                                 int cnt)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `writeData`
```java
public boolean writeData(java.lang.String accessPwd,
                         int bank,
                         int ptr,
                         int cnt,
                         java.lang.String data)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `writeData`
```java
public boolean writeData(java.lang.String accessPwd,
                         int filterBank,
                         int filterPtr,
                         int filterCnt,
                         java.lang.String filterData,
                         int bank,
                         int ptr,
                         int cnt,
                         java.lang.String writeData)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `writeDataToEpc`
```java
public boolean writeDataToEpc(java.lang.String accessPwd,
                              int filterBank,
                              int filterPtr,
                              int filterCnt,
                              java.lang.String filterData,
                              java.lang.String writeData)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `writeDataToEpc`
```java
public boolean writeDataToEpc(java.lang.String accessPwd,
                              java.lang.String writeData)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `blockWriteData`
```java
public boolean blockWriteData(java.lang.String accessPwd,
                              int filterBank,
                              int filterPtr,
                              int filterCnt,
                              java.lang.String filterData,
                              int bank,
                              int ptr,
                              int cnt,
                              java.lang.String writeData)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setFilter`
```java
public boolean setFilter(int bank,
                         int ptr,
                         int cnt,
                         java.lang.String data)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `killTag`
```java
public boolean killTag(java.lang.String killPwd)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `killTag`
```java
public boolean killTag(java.lang.String accessPwd,
                       int filterBank,
                       int filterPtr,
                       int filterCnt,
                       java.lang.String filterData)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setProtocol`
```java
public boolean setProtocol(int protocol)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `getProtocol`
```java
public int getProtocol()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setFreHop`
```java
public boolean setFreHop(float fre)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setRFLink`
```java
public boolean setRFLink(int mode)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `getRFLink`
```java
public int getRFLink()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setFastID`
```java
public boolean setFastID(boolean enalbe)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setTagFocus`
```java
public boolean setTagFocus(boolean enalbe)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setEPCMode`
```java
public boolean setEPCMode()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setEPCAndTIDMode`
```java
public boolean setEPCAndTIDMode()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setEPCAndTIDUserMode`
```java
public boolean setEPCAndTIDUserMode(int user_prt,
                                    int user_len)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setEPCAndTIDUserMode`
```java
public boolean setEPCAndTIDUserMode(InventoryModeEntityentity)
```
📝 设置循环盘点同时读取 EPC、TID、USER 模式Setup auto scan to acquire EPC, TID, User mode

### 🔹 Méthode : `getTemperature`
```java
public int getTemperature()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `uhfBlockPermalock`
```java
public boolean uhfBlockPermalock(java.lang.String accessPwd,
                                 int FilterBank,
                                 int FilterStartaddr,
                                 int FilterLen,
                                 java.lang.String FilterData,
                                 int ReadLock,
                                 int uBank,
                                 int uPtr,
                                 int uRange,
                                 byte[] uMaskbuf)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setGen2`
```java
public boolean setGen2(Gen2Entitygen2Entity)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `getGen2`
```java
publicGen2EntitygetGen2()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `uhfStopUpdate`
```java
public boolean uhfStopUpdate()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `getCW`
```java
public int getCW()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setCW`
```java
public boolean setCW(int flag)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `getEPCAndTIDUserMode`
```java
publicInventoryModeEntitygetEPCAndTIDUserMode()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `setConnectionStatusCallback`
```java
public void setConnectionStatusCallback(ConnectionStatusCallback<java.lang.Object> btStatusCallback)
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `getConnectStatus`
```java
publicConnectionStatusgetConnectStatus()
```
📝 Description copied from interface:IUHF

### 🔹 Méthode : `getErrCode`
```java
public int getErrCode()Specified by:getErrCodein interfaceIUHFOfAndroidUartReturns:返回错误码(return error codes):UhfBase.ErrorCodestartLocationpublic boolean startLocation(android.content.Context context,
                             java.lang.String label,
                             int bank,
                             int ptr,IUHFLocationCallbacklocationCallback)
```
📝 开始定位Start location

### 🔹 Méthode : `stopRadarLocation`
```java
public boolean stopRadarLocation()Specified by:stopRadarLocationin interfaceIHandheldRFIDstartRadarLocationpublic boolean startRadarLocation(android.content.Context context,
                                  java.lang.String tag,
                                  int bank,
                                  int ptr,IUHFRadarLocationCallbacklocationCallback)
```
📝 Description copied from interface:IHandheldRFID

### 🔹 Méthode : `setDynamicDistance`
```java
public boolean setDynamicDistance(int value)
```
📝 Radar positioning dynamically adjusts distance.

### 🔹 Méthode : `getTagLocate`
```java
publicITagLocategetTagLocate(android.content.Context context)Specified by:getTagLocatein interfaceIHandheldRFIDstopLocationpublic boolean stopLocation()
```
📝 停止定位
 Stop location

### 🔹 Méthode : `isPowerOn`
```java
public boolean isPowerOn()
```
📝 判断设备是否上电Judge the device is powered on or not.

### 🔹 Méthode : `isInventorying`
```java
public boolean isInventorying()
```
📝 uhf 是否正在盘点

### 🔹 Méthode : `setInventoryCallback`
```java
public void setInventoryCallback(IUHFInventoryCallbackinventoryCallback)
```
📝 设置盘点回调接口，接收循环盘点到的标签数据Set the inventory callback interface to receive the label data from the cyclic inventory备注：需要在开始循环盘点startInventoryTag()之前调用此方法。Note: This method needs to be called before starting the loop inventorystartInventoryTag().

### 🔹 Méthode : `setFastInventoryMode`
```java
public boolean setFastInventoryMode(boolean enable)
```
📝 setFastInventory

### 🔹 Méthode : `getFastInventoryMode`
```java
public int getFastInventoryMode()
```
📝 getFastInventoryMode

### 🔹 Méthode : `factoryReset`
```java
public boolean factoryReset()
```
📝 恢复出厂设置(Reset uhf parameters)
