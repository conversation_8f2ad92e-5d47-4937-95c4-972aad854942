<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>M-Index</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="M-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-12.html">Prev Letter</a></li>
<li><a href="index-14.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-13.html" target="_top">Frames</a></li>
<li><a href="index-13.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;<a name="I:M">
<!--   -->
</a>
<h2 class="title">M</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#M">M</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#M1_ReadData-int-int-">M1_ReadData(int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></dt>
<dd>
<div class="block">读取指定扇区指定block的数据，适用于S50和S70标签。使用此函数的时候，要确保该扇区密钥验证已通过。<br>
 Read data in specified block of specified sector, used for S50 and S70 tag.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.html#M1_ReadData-int-int-">M1_ReadData(int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A</a></dt>
<dd>
<div class="block">读取指定扇区指定block的数据，适用于S50和S70标签。使用此函数的时候，要确保该扇区密钥验证已通过。<br>
 Read data in specified block of specified sector, used for S50 and S70 tag.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html#M1_ReadData-int-int-">M1_ReadData(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443A_mtk</a></dt>
<dd>
<div class="block">读取指定扇区指定block的数据，适用于S50和S70标签。使用此函数的时候，要确保该扇区密钥验证已通过。<br>
 Read data in specified block of specified sector, used for S50 and S70 tag.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#M1_ReadData-int-int-">M1_ReadData(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443A_qcom</a></dt>
<dd>
<div class="block">读取指定扇区指定block的数据，适用于S50和S70标签。使用此函数的时候，要确保该扇区密钥验证已通过。<br>
 Read data in specified block of specified sector, used for S50 and S70 tag.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#M1_WriteData-int-int-java.lang.String-">M1_WriteData(int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></dt>
<dd>
<div class="block">向指定的扇区的Block 写入数据，适用于S50和S70标签。使用此函数的时候，要确保该扇区密钥验证已通过。<br>
 Write data in specified sector of block, used for S50 and S70 tag.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.html#M1_WriteData-int-int-java.lang.String-">M1_WriteData(int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A</a></dt>
<dd>
<div class="block">向指定的扇区的Block 写入数据，适用于S50和S70标签。使用此函数的时候，要确保该扇区密钥验证已通过。<br>
 Write data in specified sector of block, used for S50 and S70 tag.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html#M1_WriteData-int-int-java.lang.String-">M1_WriteData(int, int, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443A_mtk</a></dt>
<dd>
<div class="block">向指定的扇区的Block 写入数据，适用于S50和S70标签。使用此函数的时候，要确保该扇区密钥验证已通过。<br>
 Write data in specified sector of block, used for S50 and S70 tag.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#M1_WriteData-int-int-java.lang.String-">M1_WriteData(int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443A_qcom</a></dt>
<dd>
<div class="block">向指定的扇区的Block 写入数据，适用于S50和S70标签。使用此函数的时候，要确保该扇区密钥验证已通过。<br>
 Write data in specified sector of block, used for S50 and S70 tag.</div>
</dd>
<dt><a href="../com/rscja/custom/M775Authenticate.html" title="class in com.rscja.custom"><span class="typeNameLink">M775Authenticate</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>
<div class="block">英频杰特殊标签定制<br>
Special label customization<br>

 第一步:通过<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        盘点标签之前先要设置回调函数<a href="../com/rscja/custom/M775Authenticate.html#setInventoryCallback-com.rscja.custom.M775Authenticate.IUHFInventoryCallback-"><code>M775Authenticate.setInventoryCallback(IUHFInventoryCallback)</code></a>,然后调用盘点函数<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#startInventoryTag--"><code>RFIDWithUHFUART.startInventoryTag()</code></a>开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#stopInventory--"><code>RFIDWithUHFUART.stopInventory()</code></a>函数。<br>
 For parameter setting, after the connection is successful, call the corresponding function to set parameters and read/write operations.</div>
</dd>
<dt><a href="../com/rscja/custom/M775Authenticate.AuthenticateInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">M775Authenticate.AuthenticateInfo</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>
<div class="block">AuthenticateInfo对象实体(AuthenticateInfo object entity)</div>
</dd>
<dt><a href="../com/rscja/custom/M775Authenticate.IUHFInventoryCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">M775Authenticate.IUHFInventoryCallback</span></a> - Interface in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>
<div class="block">盘点回调函数（Inventory callback function）</div>
</dd>
<dt><a href="../com/rscja/team/mtk/custom/M775Authenticate_mtk.html" title="class in com.rscja.team.mtk.custom"><span class="typeNameLink">M775Authenticate_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/custom/package-summary.html">com.rscja.team.mtk.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/custom/M775Authenticate_qcom.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">M775Authenticate_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#makeSendData-int-byte:A-">makeSendData(int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_CW">MANUFACTOR_CW</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_HONYWELL">MANUFACTOR_HONYWELL</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_IA">MANUFACTOR_IA</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_IDATA">MANUFACTOR_IDATA</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_MOBYDATA">MANUFACTOR_MOBYDATA</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_NEWLAND">MANUFACTOR_NEWLAND</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_ZEBRA">MANUFACTOR_ZEBRA</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#match--">match()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">对比模板缓冲区1与模板缓冲区2的指纹模板文件<br>
 fingerprint template file of comparison template buffer zone 1 and template buffer zone 2.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#match-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">match(FingerprintWithZAZ.BufferEnum, FingerprintWithZAZ.BufferEnum, int[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>
<div class="block">指定的两个 Ram Buffer 中的 Template 之间进行比对</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#match--">match()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">对比模板缓冲区1与模板缓冲区2的指纹模板文件<br>
 fingerprint template file of comparison template buffer zone 1 and template buffer zone 2.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#match-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">match(FingerprintWithZAZ.BufferEnum, FingerprintWithZAZ.BufferEnum, int[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>
<div class="block">指定的两个 Ram Buffer 中的 Template 之间进行比对</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#match--">match()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">对比模板缓冲区1与模板缓冲区2的指纹模板文件<br>
 fingerprint template file of comparison template buffer zone 1 and template buffer zone 2.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#match-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">match(FingerprintWithZAZ.BufferEnum, FingerprintWithZAZ.BufferEnum, int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>
<div class="block">指定的两个 Ram Buffer 中的 Template 之间进行比对</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/CWDeviceInfo.html#MC50_4350_120">MC50_4350_120</a></span> - Static variable in class com.rscja.<a href="../com/rscja/CWDeviceInfo.html" title="class in com.rscja">CWDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#MC50_4350_120">MC50_4350_120</a></span> - Static variable in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/scanner/led/MC50_4350_12_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led"><span class="typeNameLink">MC50_4350_12_ScanLed_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/scanner/led/package-summary.html">com.rscja.team.qcom.scanner.led</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/CWDeviceInfo.html#MC51_4350_120">MC51_4350_120</a></span> - Static variable in class com.rscja.<a href="../com/rscja/CWDeviceInfo.html" title="class in com.rscja">CWDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#merge-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int-int:A-">merge(FingerprintWithZAZ.BufferEnum, int, int[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>
<div class="block">合并模版</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#merge-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int-int:A-">merge(FingerprintWithZAZ.BufferEnum, int, int[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>
<div class="block">合并模版</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#merge-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int-int:A-">merge(FingerprintWithZAZ.BufferEnum, int, int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>
<div class="block">合并模版</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUpgradeProgress.html#mesage-java.lang.String-">mesage(String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUpgradeProgress.html" title="interface in com.rscja.deviceapi.interfaces">IUpgradeProgress</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.PrinterStatusCallBack.html#message-com.rscja.deviceapi.Printer.PrinterStatus-">message(Printer.PrinterStatus)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.PrinterStatusCallBack.html" title="interface in com.rscja.deviceapi">Printer.PrinterStatusCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.EnrollCallBack.html#messageInfo-java.lang.String-">messageInfo(String)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.EnrollCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.GRABCallBack.html#messageInfo-java.lang.String-">messageInfo(String)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.GRABCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.GRABCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.IdentificationCallBack.html#messageInfo-java.lang.String-">messageInfo(String)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.IdentificationCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.IdentificationCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.PtCaptureCallBack.html#messageInfo-java.lang.String-">messageInfo(String)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.PtCaptureCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.TemplateVerifyCallBack.html#messageInfo-java.lang.String-">messageInfo(String)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.TemplateVerifyCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html#messageInfo-java.lang.String-int-">messageInfo(String, int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.GrabCallBack.html#messageInfo-java.lang.String-int-">messageInfo(String, int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.GrabCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.GrabCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.IdentificationCallBack.html#messageInfo-java.lang.String-int-">messageInfo(String, int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.IdentificationCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.IdentificationCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.PtCaptureCallBack.html#messageInfo-java.lang.String-int-">messageInfo(String, int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.PtCaptureCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.TemplateVerifyCallBack.html#messageInfo-java.lang.String-int-">messageInfo(String, int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.TemplateVerifyCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.IUPImageCallback.html#messageInfo-java.lang.String-">messageInfo(String)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.IUPImageCallback.html" title="interface in com.rscja.deviceapi">FingerprintWithTLK1NC.IUPImageCallback</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/http/NetResult_qcom.html#messages">messages</a></span> - Variable in class com.rscja.team.qcom.http.<a href="../com/rscja/team/qcom/http/NetResult_qcom.html" title="class in com.rscja.team.qcom.http">NetResult_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#mFd">mFd</a></span> - Variable in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#mFd">mFd</a></span> - Variable in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/barcode/symbol/MobyDataBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol"><span class="typeNameLink">MobyDataBarcodeSymbol_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/barcode/symbol/package-summary.html">com.rscja.team.qcom.barcode.symbol</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html#MODE_EPC">MODE_EPC</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html#MODE_EPC_RESERVED">MODE_EPC_RESERVED</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html#MODE_EPC_TID">MODE_EPC_TID</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html#MODE_EPC_TID_USER">MODE_EPC_TID_USER</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html#MODE_LED_TAG">MODE_LED_TAG</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html#MODE_TEMPERATURE_TAG">MODE_TEMPERATURE_TAG</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#MODEL_CW_CW9281">MODEL_CW_CW9281</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_3601">Model_HONYWELL_3601</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_3603">Model_HONYWELL_3603</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_6603">Model_HONYWELL_6603</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_EX30">Model_HONYWELL_EX30</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_N5703">Model_HONYWELL_N5703</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_N6703">Model_HONYWELL_N6703</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_100S">Model_IA_100S</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_101S">Model_IA_101S</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_166S">Model_IA_166S</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_171S">Model_IA_171S</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_181S">Model_IA_181S</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_400S">Model_IA_400S</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_417S">Model_IA_417S</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IDATA_DS7000">Model_IDATA_DS7000</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_MOBYDATA_E3200">Model_MOBYDATA_E3200</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_MOTO_5500">Model_MOTO_5500</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_NEWLAND_CM30">Model_NEWLAND_CM30</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_NEWLAND_CM47">Model_NEWLAND_CM47</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_NEWLAND_CM60">Model_NEWLAND_CM60</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_2100">Model_ZEBRA_2100</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4100">Model_ZEBRA_4100</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4710">Model_ZEBRA_4710</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4720">Model_ZEBRA_4720</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4750">Model_ZEBRA_4750</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4770">Model_ZEBRA_4770</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4850">Model_ZEBRA_4850</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/Module.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Module</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/deviceapi/Module_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Module_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/Module_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Module_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleFree-java.lang.String-">ModuleFree(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#ModuleFree-java.lang.String-">ModuleFree(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleInit-java.lang.String-java.lang.String-int-int-">ModuleInit(String, String, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#ModuleInit-java.lang.String-java.lang.String-int-int-">ModuleInit(String, String, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleInitEX-java.lang.String-java.lang.String-int-int-int-int-int-">ModuleInitEX(String, String, int, int, int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#ModuleInitEX-java.lang.String-java.lang.String-int-int-int-int-int-">ModuleInitEX(String, String, int, int, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModulePowerOff-java.lang.String-int-">ModulePowerOff(String, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#ModulePowerOff-java.lang.String-int-">ModulePowerOff(String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModulePowerOn-java.lang.String-int-">ModulePowerOn(String, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#ModulePowerOn-java.lang.String-int-">ModulePowerOn(String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleReceive--">ModuleReceive()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#ModuleReceive--">ModuleReceive()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleReceiveEx-byte:A-">ModuleReceiveEx(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#ModuleReceiveEx--">ModuleReceiveEx()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleSend-byte:A-int-">ModuleSend(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#ModuleSend-byte:A-int-">ModuleSend(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleSendAndReceive-byte:A-int-byte:A-int-">ModuleSendAndReceive(byte[], int, byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#ModuleSendAndReceive-byte:A-int-byte:A-int-">ModuleSendAndReceive(byte[], int, byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoCancel--">MorphoCancel()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoCancel--">MorphoCancel()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoCapture-char-char-">MorphoCapture(char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoCapture-char-char-">MorphoCapture(char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoCapturePKComp-char-char-">MorphoCapturePKComp(char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoCapturePKComp-char-char-">MorphoCapturePKComp(char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoDescriptor--">MorphoDescriptor()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoDescriptor--">MorphoDescriptor()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoEnroll-char-char:A-char:A-java.lang.String-int-">MorphoEnroll(char, char[], char[], String, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoEnroll-char-char:A-char:A-java.lang.String-int-">MorphoEnroll(char, char[], char[], String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.html#morphoEraseAllBase--">morphoEraseAllBase()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.html" title="class in com.rscja.deviceapi">FingerprintWithMorpho</a></dt>
<dd>
<div class="block">删除所有指纹信息<br>
 delete all fingerprint infor<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#morphoEraseAllBase--">morphoEraseAllBase()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></dt>
<dd>
<div class="block">删除所有指纹信息<br>
 delete all fingerprint infor<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoEraseAllBase--">MorphoEraseAllBase()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#morphoEraseAllBase--">morphoEraseAllBase()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithMorpho_mtk</a></dt>
<dd>
<div class="block">删除所有指纹信息<br>
 delete all fingerprint infor<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoEraseAllBase--">MorphoEraseAllBase()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html#morphoEraseAllBase--">morphoEraseAllBase()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithMorpho_qcom</a></dt>
<dd>
<div class="block">删除所有指纹信息<br>
 delete all fingerprint infor<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoFingerFree-java.lang.String-">MorphoFingerFree(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoFingerFree-java.lang.String-">MorphoFingerFree(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoFingerInit-java.lang.String-java.lang.String-int-">MorphoFingerInit(String, String, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoFingerInit-java.lang.String-java.lang.String-int-">MorphoFingerInit(String, String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoFingerMessage--">MorphoFingerMessage()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoFingerMessage--">MorphoFingerMessage()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoGetSecurityLevel--">MorphoGetSecurityLevel()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoGetSecurityLevel--">MorphoGetSecurityLevel()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoGrab-char-java.lang.String-">MorphoGrab(char, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoGrab-char-java.lang.String-">MorphoGrab(char, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoIdentify-char-">MorphoIdentify(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoIdentify-char-">MorphoIdentify(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoLoadKs-byte:A-">MorphoLoadKs(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoLoadKs-byte:A-">MorphoLoadKs(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoPIDSN--">MorphoPIDSN()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoPIDSN--">MorphoPIDSN()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoSetSecurityLevel-int-">MorphoSetSecurityLevel(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoSetSecurityLevel-int-">MorphoSetSecurityLevel(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoStop--">MorphoStop()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoStop--">MorphoStop()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoVerifyPKComp-char-byte:A-int-">MorphoVerifyPKComp(char, byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#MorphoVerifyPKComp-char-byte:A-int-">MorphoVerifyPKComp(char, byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html#mRFID">mRFID</a></span> - Variable in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html" title="class in com.rscja.team.qcom.deviceapi">CardWithBYL_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.Platform.html#MTK">MTK</a></span> - Static variable in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.Platform.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk.Platform</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#MTK">MTK</a></span> - Static variable in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom.Platform</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html#MultipleTemperatureInfo--">MultipleTemperatureInfo()</a></span> - Constructor for class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/utility/LogUtility_mtk.html#myLogDebug-java.lang.String-java.lang.String-">myLogDebug(String, String)</a></span> - Static method in class com.rscja.team.mtk.utility.<a href="../com/rscja/team/mtk/utility/LogUtility_mtk.html" title="class in com.rscja.team.mtk.utility">LogUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/LogUtility_qcom.html#myLogDebug-java.lang.String-java.lang.String-">myLogDebug(String, String)</a></span> - Static method in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/LogUtility_qcom.html" title="class in com.rscja.team.qcom.utility">LogUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/utility/LogUtility_mtk.html#myLogErr-java.lang.String-java.lang.String-">myLogErr(String, String)</a></span> - Static method in class com.rscja.team.mtk.utility.<a href="../com/rscja/team/mtk/utility/LogUtility_mtk.html" title="class in com.rscja.team.mtk.utility">LogUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/LogUtility_qcom.html#myLogErr-java.lang.String-java.lang.String-">myLogErr(String, String)</a></span> - Static method in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/LogUtility_qcom.html" title="class in com.rscja.team.qcom.utility">LogUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/utility/LogUtility_mtk.html#myLogInfo-java.lang.String-java.lang.String-">myLogInfo(String, String)</a></span> - Static method in class com.rscja.team.mtk.utility.<a href="../com/rscja/team/mtk/utility/LogUtility_mtk.html" title="class in com.rscja.team.mtk.utility">LogUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/LogUtility_qcom.html#myLogInfo-java.lang.String-java.lang.String-">myLogInfo(String, String)</a></span> - Static method in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/LogUtility_qcom.html" title="class in com.rscja.team.qcom.utility">LogUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/utility/LogUtility_mtk.html#myLogV-java.lang.String-java.lang.String-">myLogV(String, String)</a></span> - Static method in class com.rscja.team.mtk.utility.<a href="../com/rscja/team/mtk/utility/LogUtility_mtk.html" title="class in com.rscja.team.mtk.utility">LogUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/LogUtility_qcom.html#myLogV-java.lang.String-java.lang.String-">myLogV(String, String)</a></span> - Static method in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/LogUtility_qcom.html" title="class in com.rscja.team.qcom.utility">LogUtility_qcom</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-12.html">Prev Letter</a></li>
<li><a href="index-14.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-13.html" target="_top">Frames</a></li>
<li><a href="index-13.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
