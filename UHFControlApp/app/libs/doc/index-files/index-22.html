<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>V-Index</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="V-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-21.html">Prev Letter</a></li>
<li><a href="index-23.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-22.html" target="_top">Frames</a></li>
<li><a href="index-22.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;<a name="I:V">
<!--   -->
</a>
<h2 class="title">V</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#V">V</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#validPWD-java.lang.String-">validPWD(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">验证密码<br>
 Verify password<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#validPWD-java.lang.String-">validPWD(String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">验证密码<br>
 Verify password<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#validPWD-java.lang.String-">validPWD(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">验证密码<br>
 Verify password<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#validTempleteNum--">validTempleteNum()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">获取模块中已保存的指纹特征数据个数<br>
 Acquire number of saved fingerprint feature data in module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#validTempleteNum--">validTempleteNum()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">获取模块中已保存的指纹特征数据个数<br>
 Acquire number of saved fingerprint feature data in module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#validTempleteNum--">validTempleteNum()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">获取模块中已保存的指纹特征数据个数<br>
 Acquire number of saved fingerprint feature data in module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFXSAPI.Bank.html#value">value</a></span> - Variable in enum com.rscja.custom.<a href="../com/rscja/custom/UHFXSAPI.Bank.html" title="enum in com.rscja.custom">UHFXSAPI.Bank</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.BufferEnum.html#value">value</a></span> - Variable in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html#value">value</a></span> - Variable in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html#value">value</a></span> - Variable in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.ModuleType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom">UHFTemperatureTagsAPI.TagState</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFXSAPI.Bank.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.custom.<a href="../com/rscja/custom/UHFXSAPI.Bank.html" title="enum in com.rscja.custom">UHFXSAPI.Bank</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.TagType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.TagType.html" title="enum in com.rscja.deviceapi.entity">HF15693RequestEntity.TagType</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/enums/AntennaEnum.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.enums.<a href="../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.BufferEnum.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.DataFormat.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.DataFormat.html" title="enum in com.rscja.deviceapi">FingerprintWithFIPS.DataFormat</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/ConnectionStatus.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/ConnectionStatus.html#valueOf-int-">valueOf(int)</a></span> - Static method in enum com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.BarcodeType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.BarcodeType.html" title="enum in com.rscja.deviceapi">Printer.BarcodeType</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.PrinterStatus.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.PrinterStatus.html" title="enum in com.rscja.deviceapi">Printer.PrinterStatus</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireFileTypekEnum</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.KeyType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.KeyType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.KeyType</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.TagType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.TagType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.TagType</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO15693.TagType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO15693.TagType.html" title="enum in com.rscja.deviceapi">RFIDWithISO15693.TagType</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.TagType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.TagType.html" title="enum in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk.TagType</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.ModuleType.html#values--">values()</a></span> - Static method in enum com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html#values--">values()</a></span> - Static method in enum com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom">UHFTemperatureTagsAPI.TagState</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFXSAPI.Bank.html#values--">values()</a></span> - Static method in enum com.rscja.custom.<a href="../com/rscja/custom/UHFXSAPI.Bank.html" title="enum in com.rscja.custom">UHFXSAPI.Bank</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.TagType.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.TagType.html" title="enum in com.rscja.deviceapi.entity">HF15693RequestEntity.TagType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/enums/AntennaEnum.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.enums.<a href="../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.BufferEnum.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.DataFormat.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.DataFormat.html" title="enum in com.rscja.deviceapi">FingerprintWithFIPS.DataFormat</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/ConnectionStatus.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.BarcodeType.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.BarcodeType.html" title="enum in com.rscja.deviceapi">Printer.BarcodeType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.PrinterStatus.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.PrinterStatus.html" title="enum in com.rscja.deviceapi">Printer.PrinterStatus</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireFileTypekEnum</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.KeyType.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.KeyType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.KeyType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.TagType.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.TagType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.TagType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO15693.TagType.html#values--">values()</a></span> - Static method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO15693.TagType.html" title="enum in com.rscja.deviceapi">RFIDWithISO15693.TagType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.TagType.html#values--">values()</a></span> - Static method in enum com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.TagType.html" title="enum in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk.TagType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html#values--">values()</a></span> - Static method in enum com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.html#Verify-char:A-int-">Verify(char[], int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></dt>
<dd>
<div class="block">模版比对指纹,导入模版数据和当前指纹比对<br>
 Compare template and fingerprint, import template data and current fingerprint comparison.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#verify-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-int:A-int:A-">verify(int, FingerprintWithZAZ.BufferEnum, int[], int[], int[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#Verify-char:A-int-">Verify(char[], int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a></dt>
<dd>
<div class="block">模版比对指纹,导入模版数据和当前指纹比对<br>
 Compare template and fingerprint, import template data and current fingerprint comparison.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#verify-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-int:A-int:A-">verify(int, FingerprintWithZAZ.BufferEnum, int[], int[], int[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#Verify-char:A-int-">Verify(char[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a></dt>
<dd>
<div class="block">模版比对指纹,导入模版数据和当前指纹比对<br>
 Compare template and fingerprint, import template data and current fingerprint comparison.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#Verify-char:A-int-">Verify(char[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a></dt>
<dd>
<div class="block">模版比对指纹,导入模版数据和当前指纹比对<br>
 Compare template and fingerprint, import template data and current fingerprint comparison.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#verify-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-int:A-int:A-">verify(int, FingerprintWithZAZ.BufferEnum, int[], int[], int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.html#verifyALL--">verifyALL()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></dt>
<dd>
<div class="block">验证指纹,验证当前指纹是否在指纹模块库里面存在<br>
 Verify fingerprint, verify current fingerprint exist in fingerprint library or not.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#verifyALL--">verifyALL()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a></dt>
<dd>
<div class="block">验证指纹,验证当前指纹是否在指纹模块库里面存在<br>
 Verify fingerprint, verify current fingerprint exist in fingerprint library or not.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#verifyALL--">verifyALL()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a></dt>
<dd>
<div class="block">验证指纹,验证当前指纹是否在指纹模块库里面存在<br>
 Verify fingerprint, verify current fingerprint exist in fingerprint library or not.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#verifyALL--">verifyALL()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a></dt>
<dd>
<div class="block">验证指纹,验证当前指纹是否在指纹模块库里面存在<br>
 Verify fingerprint, verify current fingerprint exist in fingerprint library or not.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IPrinter.html#verifyFW-int-">verifyFW(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.html#verifyFW-int-">verifyFW(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html#verifyFW-int-">verifyFW(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Printer_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#VerifySector-int-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-">VerifySector(int, String, RFIDWithISO14443A.KeyType)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></dt>
<dd>
<div class="block">验证扇区。S50和S70标签需要使用到此函数。密钥验证通过才可对该扇区进行读写操作。<br>
 verificate sector.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.html#VerifySector-int-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-">VerifySector(int, String, RFIDWithISO14443A.KeyType)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A</a></dt>
<dd>
<div class="block">验证扇区。S50和S70标签需要使用到此函数。密钥验证通过才可对该扇区进行读写操作。<br>
 verificate sector.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html#VerifySector-int-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-">VerifySector(int, String, RFIDWithISO14443A.KeyType)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443A_mtk</a></dt>
<dd>
<div class="block">验证扇区。S50和S70标签需要使用到此函数。密钥验证通过才可对该扇区进行读写操作。<br>
 verificate sector.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#VerifySector-int-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-">VerifySector(int, String, RFIDWithISO14443A.KeyType)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443A_qcom</a></dt>
<dd>
<div class="block">验证扇区。S50和S70标签需要使用到此函数。密钥验证通过才可对该扇区进行读写操作。<br>
 verificate sector.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_FIRMWARE">VERSION_BT_FIRMWARE</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_HARDWARE">VERSION_BT_HARDWARE</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_SOFTWARE">VERSION_BT_SOFTWARE</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#VERSION_FIRMWARE_UUID">VERSION_FIRMWARE_UUID</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#VERSION_HARDWARE_UUID">VERSION_HARDWARE_UUID</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#VERSION_SOFTWARE_UUID">VERSION_SOFTWARE_UUID</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#VERSION_UUID">VERSION_UUID</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/VersionInfo.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">VersionInfo</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">jar包信息类<br>
 jar pack infor type<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/VersionInfo.html#VersionInfo--">VersionInfo()</a></span> - Constructor for class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/VersionInfo.html" title="class in com.rscja.deviceapi">VersionInfo</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-21.html">Prev Letter</a></li>
<li><a href="index-23.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-22.html" target="_top">Frames</a></li>
<li><a href="index-22.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
