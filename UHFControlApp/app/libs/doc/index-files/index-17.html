<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Q-Index</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Q-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-16.html">Prev Letter</a></li>
<li><a href="index-18.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-17.html" target="_top">Frames</a></li>
<li><a href="index-17.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;<a name="I:Q">
<!--   -->
</a>
<h2 class="title">Q</h2>
<dl>
<dt><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax"><span class="typeNameLink">QcomURA4Gpio</span></a> - Class in <a href="../com/rscja/team/qcom/urax/package-summary.html">com.rscja.team.qcom.urax</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html#QcomURA4Gpio--">QcomURA4Gpio()</a></span> - Constructor for class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/urax/QcomURAxDevice.html" title="class in com.rscja.team.qcom.urax"><span class="typeNameLink">QcomURAxDevice</span></a> - Class in <a href="../com/rscja/team/qcom/urax/package-summary.html">com.rscja.team.qcom.urax</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURAxDevice.html#QcomURAxDevice--">QcomURAxDevice()</a></span> - Constructor for class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURAxDevice.html" title="class in com.rscja.team.qcom.urax">QcomURAxDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.Platform.html#QUACOMM">QUACOMM</a></span> - Static variable in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.Platform.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk.Platform</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#QUACOMM">QUACOMM</a></span> - Static variable in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom.Platform</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-16.html">Prev Letter</a></li>
<li><a href="index-18.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-17.html" target="_top">Frames</a></li>
<li><a href="index-17.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
