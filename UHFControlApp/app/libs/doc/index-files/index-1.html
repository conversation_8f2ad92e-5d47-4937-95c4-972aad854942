<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>A-Index</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="A-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev Letter</li>
<li><a href="index-2.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-1.html" target="_top">Frames</a></li>
<li><a href="index-1.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;<a name="I:A">
<!--   -->
</a>
<h2 class="title">A</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#A">A</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#A4_8953_90">A4_8953_90</a></span> - Static variable in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#A4_RK_3568_110">A4_RK_3568_110</a></span> - Static variable in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#A4C8_RK_3568_110">A4C8_RK_3568_110</a></span> - Static variable in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4OptoCoupler3Off-java.lang.String-">A4OptoCoupler3Off(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#A4OptoCoupler3Off-java.lang.String-">A4OptoCoupler3Off(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4OptoCoupler3On-java.lang.String-">A4OptoCoupler3On(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#A4OptoCoupler3On-java.lang.String-">A4OptoCoupler3On(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4OptoCoupler4Off-java.lang.String-">A4OptoCoupler4Off(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#A4OptoCoupler4Off-java.lang.String-">A4OptoCoupler4Off(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4OptoCoupler4On-java.lang.String-">A4OptoCoupler4On(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#A4OptoCoupler4On-java.lang.String-">A4OptoCoupler4On(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4WgData0Off-java.lang.String-">A4WgData0Off(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#A4WgData0Off-java.lang.String-">A4WgData0Off(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4WgData0On-java.lang.String-">A4WgData0On(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#A4WgData0On-java.lang.String-">A4WgData0On(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4WgData1Off-java.lang.String-">A4WgData1Off(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#A4WgData1Off-java.lang.String-">A4WgData1Off(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4WgData1On-java.lang.String-">A4WgData1On(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#A4WgData1On-java.lang.String-">A4WgData1On(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#A8_8909">A8_8909</a></span> - Static variable in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#A8_8953_90">A8_8953_90</a></span> - Static variable in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A8UhfOutput3Off-java.lang.String-">A8UhfOutput3Off(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#A8UhfOutput3Off-java.lang.String-">A8UhfOutput3Off(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A8UhfOutput3On-java.lang.String-">A8UhfOutput3On(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#A8UhfOutput3On-java.lang.String-">A8UhfOutput3On(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A8UhfOutput4Off-java.lang.String-">A8UhfOutput4Off(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#A8UhfOutput4Off-java.lang.String-">A8UhfOutput4Off(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A8UhfOutput4On-java.lang.String-">A8UhfOutput4On(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#A8UhfOutput4On-java.lang.String-">A8UhfOutput4On(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#ACTION_DATA_AVAILABLE">ACTION_DATA_AVAILABLE</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#ACTION_GATT_DISCONNECTED">ACTION_GATT_DISCONNECTED</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#ACTION_GATT_SERVICES_DISCOVERED">ACTION_GATT_SERVICES_DISCOVERED</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html#ACTION_GET_LAST_DEC_IMAGE">ACTION_GET_LAST_DEC_IMAGE</a></span> - Static variable in class com.rscja.team.qcom.barcode.barcode2d.<a href="../com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d">KeyboardEmulator2DDecoder_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.html#ACTION_SCAN_KEYBOARD_HELPER_PARAM_RESPONSE">ACTION_SCAN_KEYBOARD_HELPER_PARAM_RESPONSE</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html#ACTION_SCAN_PARAMETER">ACTION_SCAN_PARAMETER</a></span> - Static variable in class com.rscja.team.qcom.barcode.barcode2d.<a href="../com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d">KeyboardEmulator2DDecoder_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#ACTION_SEARCH_DEVICES">ACTION_SEARCH_DEVICES</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#ACTION_USB_PERMISSION">ACTION_USB_PERMISSION</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#ACTION_USB_PERMISSION">ACTION_USB_PERMISSION</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#addBleDevice-java.lang.String-android.content.Context-">addBleDevice(String, Context)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLEManage.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLEManage</a></dt>
<dd>
<div class="block">增加蓝牙设备</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#addCmd-com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase.CMDInfo-">addCmd(UHFProtocolProtocolParseBase.CMDInfo)</a></span> - Method in class com.rscja.team.qcom.uhfparse.<a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/HFR1UsbDataHandle.html#addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">addCmdList(UHFDataHandleBase.CMDInfo)</a></span> - Method in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/HFR1UsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">HFR1UsbDataHandle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">addCmdList(UHFDataHandleBase.CMDInfo)</a></span> - Method in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFRxBLEDataHandle.html#addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">addCmdList(UHFDataHandleBase.CMDInfo)</a></span> - Method in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFRxBLEDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">UHFRxBLEDataHandle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFRxUsbDataHandle.html#addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">addCmdList(UHFDataHandleBase.CMDInfo)</a></span> - Method in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFRxUsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">UHFRxUsbDataHandle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFUR4DataHandle.html#addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">addCmdList(UHFDataHandleBase.CMDInfo)</a></span> - Method in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFUR4DataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">UHFUR4DataHandle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFUrAxDataHandle.html#addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">addCmdList(UHFDataHandleBase.CMDInfo)</a></span> - Method in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFUrAxDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">UHFUrAxDataHandle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html#addCmdList-com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase.CMDInfo-">addCmdList(UHFProtocolProtocolParseBase.CMDInfo)</a></span> - Method in class com.rscja.team.qcom.uhfparse.<a href="../com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html" title="class in com.rscja.team.qcom.uhfparse">UHFBLEProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#addCmdList-com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase.CMDInfo-">addCmdList(UHFProtocolProtocolParseBase.CMDInfo)</a></span> - Method in class com.rscja.team.qcom.uhfparse.<a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/GyroAngle.html#addData-float-float-float-">addData(float, float, float)</a></span> - Method in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/GyroAngle.html" title="class in com.rscja.team.qcom.utility">GyroAngle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/system/ISystemInterfaces.html#addLiveAPP-java.lang.String:A-">addLiveAPP(String[])</a></span> - Method in interface com.rscja.system.<a href="../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></dt>
<dd>
<div class="block">增加保活APP</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/utility/FileUtils.html#ADDR">ADDR</a></span> - Static variable in class com.rscja.team.mtk.utility.<a href="../com/rscja/team/mtk/utility/FileUtils.html" title="class in com.rscja.team.mtk.utility">FileUtils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/GyroAngle.html#angleX">angleX</a></span> - Variable in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/GyroAngle.html" title="class in com.rscja.team.qcom.utility">GyroAngle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/GyroAngle.html#angleY">angleY</a></span> - Variable in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/GyroAngle.html" title="class in com.rscja.team.qcom.utility">GyroAngle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/GyroAngle.html#angleZ">angleZ</a></span> - Variable in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/GyroAngle.html" title="class in com.rscja.team.qcom.utility">GyroAngle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">AnimalEntity</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>
<div class="block">动物标签数据实体类<br>
 animal tag data entity<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AnimalEntity.html#AnimalEntity-long-long-long-long-long-">AnimalEntity(long, long, long, long, long)</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity">AnimalEntity</a></dt>
<dd>
<div class="block">构造函数<br>
 Constructor<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AnimalEntity.html#AnimalEntity--">AnimalEntity()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity">AnimalEntity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/entity/AntennaConnectState.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">AntennaConnectState</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AntennaConnectState.html#AntennaConnectState-com.rscja.deviceapi.enums.AntennaEnum-boolean-">AntennaConnectState(AntennaEnum, boolean)</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AntennaConnectState.html" title="class in com.rscja.deviceapi.entity">AntennaConnectState</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums"><span class="typeNameLink">AntennaEnum</span></a> - Enum in <a href="../com/rscja/deviceapi/enums/package-summary.html">com.rscja.deviceapi.enums</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/entity/AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">AntennaPowerEntity</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AntennaPowerEntity.html#AntennaPowerEntity--">AntennaPowerEntity()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity">AntennaPowerEntity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">AntennaState</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AntennaState.html#AntennaState-com.rscja.deviceapi.enums.AntennaEnum-boolean-">AntennaState(AntennaEnum, boolean)</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity">AntennaState</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html#AntInfo--">AntInfo()</a></span> - Constructor for class com.rscja.custom.<a href="../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html" title="class in com.rscja.custom">UHFSFForUrxNetwork.AntInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURAxDevice.html#antLedSwitch-int-boolean-">antLedSwitch(int, boolean)</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax">IURAxDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURAxDevice.html#antLedSwitch-int-boolean-">antLedSwitch(int, boolean)</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURAxDevice.html" title="class in com.rscja.team.qcom.urax">QcomURAxDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4C8Device.html#antLedSwitch-int-boolean-">antLedSwitch(int, boolean)</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4C8Device.html" title="class in com.rscja.team.qcom.urax">RKURA4C8Device</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURAxDevice.html#antLedSwitch-int-boolean-">antLedSwitch(int, boolean)</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURAxDevice.html" title="class in com.rscja.team.qcom.urax">RKURAxDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Auth_ActiveFile-byte-byte:A-byte:A-byte:A-">Auth_ActiveFile(byte, byte[], byte[], byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Auth_ActiveFile-byte-byte:A-byte:A-byte:A-">Auth_ActiveFile(byte, byte[], byte[], byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Auth_CertificationChain-byte-byte-int-byte:A-">Auth_CertificationChain(byte, byte, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Auth_CertificationChain-byte-byte-int-byte:A-">Auth_CertificationChain(byte, byte, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Auth_RequestRandom--">Auth_RequestRandom()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Auth_RequestRandom--">Auth_RequestRandom()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Auth_SendData-byte:A-byte:A-byte:A-byte:A-byte:A-int-byte:A-">Auth_SendData(byte[], byte[], byte[], byte[], byte[], int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Auth_SendData-byte:A-byte:A-byte:A-byte:A-byte:A-int-byte:A-">Auth_SendData(byte[], byte[], byte[], byte[], byte[], int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Auth_UserInfo-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-">Auth_UserInfo(byte[], byte[], byte[], byte[], byte[], byte[], byte[], byte[], byte[], byte[], byte[], byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Auth_UserInfo-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-">Auth_UserInfo(byte[], byte[], byte[], byte[], byte[], byte[], byte[], byte[], byte[], byte[], byte[], byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/M775Authenticate.AuthenticateInfo.html#AuthenticateInfo--">AuthenticateInfo()</a></span> - Constructor for class com.rscja.custom.<a href="../com/rscja/custom/M775Authenticate.AuthenticateInfo.html" title="class in com.rscja.custom">M775Authenticate.AuthenticateInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IHF14443A.html#authentication-byte-byte-byte:A-">authentication(byte, byte, byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IHF14443A.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443A</a></dt>
<dd>
<div class="block">验证14443A卡(Authentication 14443A card)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/r1/hf/HF14443A.html#authentication-byte-byte-byte:A-">authentication(byte, byte, byte[])</a></span> - Method in class com.rscja.team.qcom.r1.hf.<a href="../com/rscja/team/qcom/r1/hf/HF14443A.html" title="class in com.rscja.team.qcom.r1.hf">HF14443A</a></dt>
<dd>
<div class="block">验证14443A卡(Authentication 14443A card)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#autoEnroll-int-int-">autoEnroll(int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">刷指定次数指纹，模块自动完成注册功能<br>
 Scan fingerprint in specified number, module complete registered function automatically<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#autoEnroll-int-int-">autoEnroll(int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">刷指定次数指纹，模块自动完成注册功能<br>
 Scan fingerprint in specified number, module complete registered function automatically<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#autoEnroll-int-int-">autoEnroll(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">刷指定次数指纹，模块自动完成注册功能<br>
 Scan fingerprint in specified number, module complete registered function automatically<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFUrxAutoInventoryTagFactory_qcom.html#autoInventoryTag-java.lang.String-int-">autoInventoryTag(String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFUrxAutoInventoryTagFactory_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFUrxAutoInventoryTagFactory_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#autoMatch-int-int-int-">autoMatch(int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">刷指定次数指纹，自动完成比对功能<br>
 Scan fingerprint in specified number, complete comparison function automatically<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#autoMatch-int-int-int-">autoMatch(int, int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">刷指定次数指纹，自动完成比对功能<br>
 Scan fingerprint in specified number, complete comparison function automatically<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#autoMatch-int-int-int-">autoMatch(int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">刷指定次数指纹，自动完成比对功能<br>
 Scan fingerprint in specified number, complete comparison function automatically<br></div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev Letter</li>
<li><a href="index-2.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-1.html" target="_top">Frames</a></li>
<li><a href="index-1.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
