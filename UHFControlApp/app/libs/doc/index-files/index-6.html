<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>F-Index</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="F-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-5.html">Prev Letter</a></li>
<li><a href="index-7.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-6.html" target="_top">Frames</a></li>
<li><a href="index-6.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;<a name="I:F">
<!--   -->
</a>
<h2 class="title">F</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#F">F</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#factoryReset--">factoryReset()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">恢复出厂设置(Reset uhf parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURx.html#factoryReset--">factoryReset()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></dt>
<dd>
<div class="block">还原出厂设置(Restore factory settings)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">恢复出厂设置(Reset uhf parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">恢复出厂设置(Reset uhf parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">恢复出厂设置(Reset uhf parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">恢复出厂设置(Reset uhf parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>
<div class="block">恢复出厂设置(Reset uhf parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>
<div class="block">恢复出厂设置(Reset uhf parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>
<div class="block">还原出厂设置(Restore factory settings)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">还原出厂设置(Restore factory settings)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>
<div class="block">还原出厂设置(Restore factory settings)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></dt>
<dd>
<div class="block">恢复出厂设置(Reset uhf parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#FactoryReset--">FactoryReset()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>
<div class="block">恢复出厂设置(Reset uhf parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#FactoryReset--">FactoryReset()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>
<div class="block">恢复出厂设置(Reset uhf parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>
<div class="block">恢复出厂设置(Reset uhf parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>
<div class="block">还原出厂设置(Restore factory settings)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>
<div class="block">还原出厂设置(Restore factory settings)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#factoryReset--">factoryReset()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>
<div class="block">还原出厂设置(Restore factory settings)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/utility/FileUtils.html#filePath">filePath</a></span> - Static variable in class com.rscja.team.mtk.utility.<a href="../com/rscja/team/mtk/utility/FileUtils.html" title="class in com.rscja.team.mtk.utility">FileUtils</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/utility/FileUtility.html" title="class in com.rscja.utility"><span class="typeNameLink">FileUtility</span></a> - Class in <a href="../com/rscja/utility/package-summary.html">com.rscja.utility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/FileUtility.html#FileUtility--">FileUtility()</a></span> - Constructor for class com.rscja.utility.<a href="../com/rscja/utility/FileUtility.html" title="class in com.rscja.utility">FileUtility</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/utility/FileUtils.html" title="class in com.rscja.team.mtk.utility"><span class="typeNameLink">FileUtils</span></a> - Class in <a href="../com/rscja/team/mtk/utility/package-summary.html">com.rscja.team.mtk.utility</a></dt>
<dd>
<div class="block">Created by Administrator on 2017-5-22.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/utility/FileUtils.html#FileUtils--">FileUtils()</a></span> - Constructor for class com.rscja.team.mtk.utility.<a href="../com/rscja/team/mtk/utility/FileUtils.html" title="class in com.rscja.team.mtk.utility">FileUtils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.html#filterCharacter-android.content.Context-java.lang.String-">filterCharacter(Context, String)</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a></dt>
<dd>
<div class="block">过滤字符串</br>
 Filter charactor string</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#filterCharacter-android.content.Context-java.lang.String-">filterCharacter(Context, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></dt>
<dd>
<div class="block">过滤字符串</br>
 Filter charactor string</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#filterCharacter-android.content.Context-java.lang.String-">filterCharacter(Context, String)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">过滤字符串<br>
 Filter string</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#filterCharacter-android.content.Context-java.lang.String-">filterCharacter(Context, String)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html#filterCharacter-android.content.Context-java.lang.String-">filterCharacter(Context, String)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeUtility_mtk</a></dt>
<dd>
<div class="block">过滤字符串</br>
 Filter charactor string</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#filterCharacter-android.content.Context-java.lang.String-">filterCharacter(Context, String)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html#filterCharacter-android.content.Context-java.lang.String-">filterCharacter(Context, String)</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeUtility_qcom</a></dt>
<dd>
<div class="block">过滤字符串</br>
 Filter charactor string</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#filterCharacter-android.content.Context-java.lang.String-">filterCharacter(Context, String)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/GyroAngle.html#finalZ">finalZ</a></span> - Variable in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/GyroAngle.html" title="class in com.rscja.team.qcom.utility">GyroAngle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerCLEARTemplate--">fingerCLEARTemplate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">清空FLASH中指纹模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerCLEARTemplate--">fingerCLEARTemplate()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">清空FLASH中指纹模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerCLEARTemplate--">fingerCLEARTemplate()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">清空FLASH中指纹模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerCLEARTemplate--">fingerCLEARTemplate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">清空FLASH中指纹模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerCLEARTemplateBuffer--">fingerCLEARTemplateBuffer()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">清空指纹特征缓冲区</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerCLEARTemplateBuffer--">fingerCLEARTemplateBuffer()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">清空指纹特征缓冲区</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerCLEARTemplateBuffer--">fingerCLEARTemplateBuffer()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">清空指纹特征缓冲区</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerCLEARTemplateBuffer--">fingerCLEARTemplateBuffer()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">清空指纹特征缓冲区</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerDELTemplateBufferID-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerDELTemplateBufferID(FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">删除内存中指定的模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerDELTemplateBufferID-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerDELTemplateBufferID(FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">删除内存中指定的模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerDELTemplateBufferID-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerDELTemplateBufferID(FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">删除内存中指定的模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerDELTemplateBufferID-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerDELTemplateBufferID(FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">删除内存中指定的模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerDELTemplatePageID-int-">fingerDELTemplatePageID(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">删除FLASH中指定的模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerDELTemplatePageID-int-">fingerDELTemplatePageID(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">删除FLASH中指定的模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerDELTemplatePageID-int-">fingerDELTemplatePageID(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">删除FLASH中指定的模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerDELTemplatePageID-int-">fingerDELTemplatePageID(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">删除FLASH中指定的模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerDOWNTemplateToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-byte:A-">fingerDOWNTemplateToBuffer(FingerprintWithTLK1NC.BufferEnum, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">下载指纹特征到指定的特征缓冲区中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerDOWNTemplateToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-byte:A-">fingerDOWNTemplateToBuffer(FingerprintWithTLK1NC.BufferEnum, byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">下载指纹特征到指定的特征缓冲区中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerDOWNTemplateToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-byte:A-">fingerDOWNTemplateToBuffer(FingerprintWithTLK1NC.BufferEnum, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">下载指纹特征到指定的特征缓冲区中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerDOWNTemplateToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-byte:A-">fingerDOWNTemplateToBuffer(FingerprintWithTLK1NC.BufferEnum, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">下载指纹特征到指定的特征缓冲区中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerDOWNTemplateToFlashPage-int-byte:A-">fingerDOWNTemplateToFlashPage(int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">下载指纹特征到指定的Flash Page中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerDOWNTemplateToFlashPage-int-byte:A-">fingerDOWNTemplateToFlashPage(int, byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">下载指纹特征到指定的Flash Page中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerDOWNTemplateToFlashPage-int-byte:A-">fingerDOWNTemplateToFlashPage(int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">下载指纹特征到指定的Flash Page中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerDOWNTemplateToFlashPage-int-byte:A-">fingerDOWNTemplateToFlashPage(int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">下载指纹特征到指定的Flash Page中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerGETImage--">fingerGETImage()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">从采集器采集指纹图像并保存于 ImageBuffer 中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerGETImage--">fingerGETImage()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">从采集器采集指纹图像并保存于 ImageBuffer 中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerGETImage--">fingerGETImage()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">从采集器采集指纹图像并保存于 ImageBuffer 中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerGETImage--">fingerGETImage()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">从采集器采集指纹图像并保存于 ImageBuffer 中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerGETTemplateCount--">fingerGETTemplateCount()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">获取可存储的指纹模板数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerGETTemplateCount--">fingerGETTemplateCount()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">获取可存储的指纹模板数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerGETTemplateCount--">fingerGETTemplateCount()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">获取可存储的指纹模板数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerGETTemplateCount--">fingerGETTemplateCount()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">获取可存储的指纹模板数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerGRABHalfImage--">fingerGRABHalfImage()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerGRABHalfImage--">fingerGRABHalfImage()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerGRABHalfImageProgress--">fingerGRABHalfImageProgress()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerGRABHalfImageProgress--">fingerGRABHalfImageProgress()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerGRABImage--">fingerGRABImage()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerGRABImage--">fingerGRABImage()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerPKTemplate-int-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerPKTemplate(int, FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">精确比对指定特征缓冲区和指定的Flash Page中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerPKTemplate-int-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerPKTemplate(int, FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">精确比对指定特征缓冲区和指定的Flash Page中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerPKTemplate-int-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerPKTemplate(int, FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">精确比对指定特征缓冲区和指定的Flash Page中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerPKTemplate-int-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerPKTemplate(int, FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">精确比对指定特征缓冲区和指定的Flash Page中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerPKTemplate2-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-">fingerPKTemplate2(FingerprintWithTLK1NC.BufferEnum, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">精确比对指定特征缓冲区和指定的Flash Page中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerPKTemplate2-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-">fingerPKTemplate2(FingerprintWithTLK1NC.BufferEnum, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">精确比对指定特征缓冲区和指定的Flash Page中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerPKTemplate2-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-">fingerPKTemplate2(FingerprintWithTLK1NC.BufferEnum, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">精确比对指定特征缓冲区和指定的Flash Page中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerPKTemplate2-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-">fingerPKTemplate2(FingerprintWithTLK1NC.BufferEnum, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">精确比对指定特征缓冲区和指定的Flash Page中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerPKTemplateBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerPKTemplateBuffer(FingerprintWithTLK1NC.BufferEnum, FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">精确比对指定特征缓冲区中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerPKTemplateBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerPKTemplateBuffer(FingerprintWithTLK1NC.BufferEnum, FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">精确比对指定特征缓冲区中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerPKTemplateBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerPKTemplateBuffer(FingerprintWithTLK1NC.BufferEnum, FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">精确比对指定特征缓冲区中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerPKTemplateBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerPKTemplateBuffer(FingerprintWithTLK1NC.BufferEnum, FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">精确比对指定特征缓冲区中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerPKTemplatePage-int-int-">fingerPKTemplatePage(int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">精确比对指定Flash Page中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerPKTemplatePage-int-int-">fingerPKTemplatePage(int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">精确比对指定Flash Page中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerPKTemplatePage-int-int-">fingerPKTemplatePage(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">精确比对指定Flash Page中特征</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerPKTemplatePage-int-int-">fingerPKTemplatePage(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">精确比对指定Flash Page中特征</div>
</dd>
<dt><a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Fingerprint</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">指纹识别模块操作类,<br>
 Fingerprint identify module operation type<br></div>
</dd>
<dt><a href="../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Fingerprint.BufferEnum</span></a> - Enum in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Fingerprint_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>
<div class="block">指纹识别模块操作类,<br>
 Fingerprint identify module operation type<br></div>
</dd>
<dt><a href="../com/rscja/deviceapi/exception/FingerprintAlreadyEnrolledException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">FingerprintAlreadyEnrolledException</span></a> - Exception in <a href="../com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a></dt>
<dd>
<div class="block">指纹已经存在异常类<br>
 fingerprint has exception type<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/exception/FingerprintAlreadyEnrolledException.html#FingerprintAlreadyEnrolledException--">FingerprintAlreadyEnrolledException()</a></span> - Constructor for exception com.rscja.deviceapi.exception.<a href="../com/rscja/deviceapi/exception/FingerprintAlreadyEnrolledException.html" title="class in com.rscja.deviceapi.exception">FingerprintAlreadyEnrolledException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.FingerprintInfo.html#FingerprintInfo--">FingerprintInfo()</a></span> - Constructor for class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.FingerprintInfo.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS.FingerprintInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/exception/FingerprintInvalidIDException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">FingerprintInvalidIDException</span></a> - Exception in <a href="../com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a></dt>
<dd>
<div class="block">ID已被占用异常类<br>
 ID has been occupied exception type<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/exception/FingerprintInvalidIDException.html#FingerprintInvalidIDException--">FingerprintInvalidIDException()</a></span> - Constructor for exception com.rscja.deviceapi.exception.<a href="../com/rscja/deviceapi/exception/FingerprintInvalidIDException.html" title="class in com.rscja.deviceapi.exception">FingerprintInvalidIDException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/utility/FingerprintPictureUtility.html" title="class in com.rscja.utility"><span class="typeNameLink">FingerprintPictureUtility</span></a> - Class in <a href="../com/rscja/utility/package-summary.html">com.rscja.utility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/FingerprintPictureUtility.html#FingerprintPictureUtility--">FingerprintPictureUtility()</a></span> - Constructor for class com.rscja.utility.<a href="../com/rscja/utility/FingerprintPictureUtility.html" title="class in com.rscja.utility">FingerprintPictureUtility</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintSM206B.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">FingerprintSM206B</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">SM206B 指纹模块操作接口<br/>

  第一步:调用init(Context context) 函数初始化指纹模块<br/>
  第二步:调用指纹相关接口,比如：search()，getDeviceVersion()...<br/>
  第三步:调用free()释放指纹模块相关资源<br/>
  示例代码:<br/>
 public void Test() {<br/>
 <br>&emsp;     Context context;
 <br>&emsp;FingerprintSM206B fingerprintSM206B= FingerprintSM206B.getInstance();
 <br>&emsp;boolean result= fingerprintSM206B.init(context);
 <br>&emsp;if(!result){
 <br>&emsp;&emsp;//init fail
 <br>&emsp;&emsp;return;
 <br>&emsp;}
 <br>&emsp; fingerprintSM206B.getImage();
 <br>&emsp; fingerprintSM206B.getFingerTemplate();
 <br>&emsp; fingerprintSM206B.getDeviceVersion();
 <br>&emsp; //...........</div>
</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">FingerprintSM206B_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUsbFingerprint.html#FingerprintSwitchUart--">FingerprintSwitchUart()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUsbFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IUsbFingerprint</a></dt>
<dd>
<div class="block">指纹切换到串口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UsbFingerprint.html#FingerprintSwitchUart--">FingerprintSwitchUart()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UsbFingerprint.html" title="class in com.rscja.deviceapi">UsbFingerprint</a></dt>
<dd>
<div class="block">指纹切换到串口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#FingerprintSwitchUart-java.lang.String-">FingerprintSwitchUart(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html#FingerprintSwitchUart--">FingerprintSwitchUart()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html" title="class in com.rscja.team.mtk.deviceapi">UsbFingerprint_mtk</a></dt>
<dd>
<div class="block">指纹切换到串口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#FingerprintSwitchUart-java.lang.String-">FingerprintSwitchUart(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UsbFingerprint_qcom.html#FingerprintSwitchUart--">FingerprintSwitchUart()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UsbFingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UsbFingerprint_qcom</a></dt>
<dd>
<div class="block">指纹切换到串口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUsbFingerprint.html#FingerprintSwitchUsb--">FingerprintSwitchUsb()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUsbFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IUsbFingerprint</a></dt>
<dd>
<div class="block">指纹切换到USB</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UsbFingerprint.html#FingerprintSwitchUsb--">FingerprintSwitchUsb()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UsbFingerprint.html" title="class in com.rscja.deviceapi">UsbFingerprint</a></dt>
<dd>
<div class="block">指纹切换到USB</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#FingerprintSwitchUsb-java.lang.String-">FingerprintSwitchUsb(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html#FingerprintSwitchUsb--">FingerprintSwitchUsb()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html" title="class in com.rscja.team.mtk.deviceapi">UsbFingerprint_mtk</a></dt>
<dd>
<div class="block">指纹切换到USB</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#FingerprintSwitchUsb-java.lang.String-">FingerprintSwitchUsb(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UsbFingerprint_qcom.html#FingerprintSwitchUsb--">FingerprintSwitchUsb()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UsbFingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UsbFingerprint_qcom</a></dt>
<dd>
<div class="block">指纹切换到USB</div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">FIPS指纹识别模块操作类,<br>
 FIPS fingerprint indentify module operation type,<br></div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithFIPS.DataFormat.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.DataFormat</span></a> - Enum in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">指纹数据格式<br>
 Fingerprint data format<br></div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithFIPS.EnrollCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.EnrollCallBack</span></a> - Interface in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">采集指纹回调接口<br>
 call-back contact for acquiring fingerprint<br></div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithFIPS.FingerprintInfo.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.FingerprintInfo</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithFIPS.GRABCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.GRABCallBack</span></a> - Interface in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">指纹图像回调接口<br>
 fingerprint image call-back contact<br></div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithFIPS.IdentificationCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.IdentificationCallBack</span></a> - Interface in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">验证指纹的回调接口<br>
 call-back contact of verify fingerprint<br></div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithFIPS.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.PtCaptureCallBack</span></a> - Interface in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">获取指纹模版数据回调接口<br>
 Acquire call-back contact fingerprint template data<br></div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithFIPS.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.TemplateVerifyCallBack</span></a> - Interface in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">指纹模版比对<br>
 fingerprint template comparison<br></div>
</dd>
<dt><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">FingerprintWithFIPS_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></dt>
<dd>
<div class="block">FIPS指纹识别模块操作类,<br>
 FIPS fingerprint indentify module operation type,<br></div>
</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">FingerprintWithFIPS_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>
<div class="block">FIPS指纹识别模块操作类,<br>
 FIPS fingerprint indentify module operation type,<br></div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithMorpho.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithMorpho</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithMorpho.EnrollCallBack</span></a> - Interface in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">采集指纹回调接口<br>
 acquire fingerprint call-back contact<br></div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithMorpho.GrabCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithMorpho.GrabCallBack</span></a> - Interface in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithMorpho.IdentificationCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithMorpho.IdentificationCallBack</span></a> - Interface in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">验证指纹的回调接口<br>
 call-back contact for fingerprint verification<br></div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithMorpho.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithMorpho.PtCaptureCallBack</span></a> - Interface in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">设置获取指纹模版回调接口<br>
 setup fingerprint template acquire call-back contact<br></div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithMorpho.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithMorpho.TemplateVerifyCallBack</span></a> - Interface in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">FingerprintWithMorpho_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">FingerprintWithMorpho_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithTLK1NC</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">迪安杰</div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithTLK1NC.BufferEnum</span></a> - Enum in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.IUPImageCallback.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithTLK1NC.IUPImageCallback</span></a> - Interface in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">指纹图像回调接口<br>
 fingerprint image call-back contact<br></div>
</dd>
<dt><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">FingerprintWithTLK1NC_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></dt>
<dd>
<div class="block">迪安杰</div>
</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">FingerprintWithTLK1NC_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>
<div class="block">迪安杰</div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithZAZ</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">指昂</div>
</dd>
<dt><a href="../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithZAZ.BufferEnum</span></a> - Enum in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">FingerprintWithZAZ_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>
<div class="block">指昂</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerSearchTemplate-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-int-">fingerSearchTemplate(FingerprintWithTLK1NC.BufferEnum, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">下载一个特征到指定特征缓冲区，然后用此特征搜索指纹库中的模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerSearchTemplate-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-int-">fingerSearchTemplate(FingerprintWithTLK1NC.BufferEnum, int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">下载一个特征到指定特征缓冲区，然后用此特征搜索指纹库中的模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerSearchTemplate-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-int-">fingerSearchTemplate(FingerprintWithTLK1NC.BufferEnum, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">下载一个特征到指定特征缓冲区，然后用此特征搜索指纹库中的模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerSearchTemplate-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-int-">fingerSearchTemplate(FingerprintWithTLK1NC.BufferEnum, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">下载一个特征到指定特征缓冲区，然后用此特征搜索指纹库中的模板</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerStopGRABImage--">fingerStopGRABImage()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerStopGRABImage--">fingerStopGRABImage()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerStoreCharToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerStoreCharToBuffer(FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">生成指纹特征，存储在指定特征缓冲区</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerStoreCharToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerStoreCharToBuffer(FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">生成指纹特征，存储在指定特征缓冲区</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerStoreCharToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerStoreCharToBuffer(FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">生成指纹特征，存储在指定特征缓冲区</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerStoreCharToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerStoreCharToBuffer(FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">生成指纹特征，存储在指定特征缓冲区</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerStoreCharToFlashPage-int-">fingerStoreCharToFlashPage(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">生成指纹特征，存储在Flash Page</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerStoreCharToFlashPage-int-">fingerStoreCharToFlashPage(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">生成指纹特征，存储在Flash Page</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerStoreCharToFlashPage-int-">fingerStoreCharToFlashPage(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">生成指纹特征，存储在Flash Page</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerStoreCharToFlashPage-int-">fingerStoreCharToFlashPage(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">生成指纹特征，存储在Flash Page</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerUPTemplateFromBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerUPTemplateFromBuffer(FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">上传指纹特征值，将指定缓冲区中的特征值上传到上位机</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerUPTemplateFromBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerUPTemplateFromBuffer(FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">上传指纹特征值，将指定缓冲区中的特征值上传到上位机</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerUPTemplateFromBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerUPTemplateFromBuffer(FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">上传指纹特征值，将指定缓冲区中的特征值上传到上位机</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerUPTemplateFromBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerUPTemplateFromBuffer(FingerprintWithTLK1NC.BufferEnum)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">上传指纹特征值，将指定缓冲区中的特征值上传到上位机</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerUPTemplateFromFlashPage-int-">fingerUPTemplateFromFlashPage(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">上传指纹特征值，将指定Flash Page中的特征值上传到上位机</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerUPTemplateFromFlashPage-int-">fingerUPTemplateFromFlashPage(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">上传指纹特征值，将指定Flash Page中的特征值上传到上位机</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#fingerUPTemplateFromFlashPage-int-">fingerUPTemplateFromFlashPage(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>
<div class="block">上传指纹特征值，将指定Flash Page中的特征值上传到上位机</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#fingerUPTemplateFromFlashPage-int-">fingerUPTemplateFromFlashPage(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>
<div class="block">上传指纹特征值，将指定Flash Page中的特征值上传到上位机</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#fips_encryption_decryption-byte:A-int-byte:A-int-byte-">fips_encryption_decryption(byte[], int, byte[], int, byte)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#fips_encryption_decryption-byte:A-int-byte:A-int-byte-">fips_encryption_decryption(byte[], int, byte[], int, byte)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#fips_encryption_decryption_EX-byte:A-int-byte:A-int-byte-java.lang.Object-int-">fips_encryption_decryption_EX(byte[], int, byte[], int, byte, Object, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#fips_encryption_decryption_EX-byte:A-int-byte:A-int-byte-java.lang.Object-int-">fips_encryption_decryption_EX(byte[], int, byte[], int, byte, Object, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_NONE">FLOWCONTROL_NONE</a></span> - Static variable in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">No flow control.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_RTSCTS_IN">FLOWCONTROL_RTSCTS_IN</a></span> - Static variable in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">RTS/CTS input flow control.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_RTSCTS_OUT">FLOWCONTROL_RTSCTS_OUT</a></span> - Static variable in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">RTS/CTS output flow control.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_XONXOFF_IN">FLOWCONTROL_XONXOFF_IN</a></span> - Static variable in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">XON/XOFF input flow control.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_XONXOFF_OUT">FLOWCONTROL_XONXOFF_OUT</a></span> - Static variable in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">XON/XOFF output flow control.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FORMAT_ASCII">FORMAT_ASCII</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">编码格式：ASCII<br>
 decoding format: ASCII</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FORMAT_DECIMAL">FORMAT_DECIMAL</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">编码格式：十进制<br>
 decoding format: decimalism</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FORMAT_DEFAULT">FORMAT_DEFAULT</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">编码格式：默认格式<br>
 decoding format: default format</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FORMAT_GB18030">FORMAT_GB18030</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">编码格式：GB18030<br>
 decoding format: GB18030</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FORMAT_GB2312">FORMAT_GB2312</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">编码格式：GB2312<br>
 decoding format: GB2312</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FORMAT_GBK">FORMAT_GBK</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">编码格式：GBK<br>
 decoding format: GBK</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FORMAT_HEX">FORMAT_HEX</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">编码格式：十六进制<br>
 decoding format: hexvalue</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FORMAT_UNICODE">FORMAT_UNICODE</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">编码格式：UNICODE<br>
 decoding format: UNICODE</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FORMAT_UTF8">FORMAT_UTF8</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">编码格式：UTF8<br>
 decoding format: UTF8</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/interfaces/IRFIDWithUHFUARTUAE.html#free--">free()</a></span> - Method in interface com.rscja.custom.interfaces.<a href="../com/rscja/custom/interfaces/IRFIDWithUHFUARTUAE.html" title="interface in com.rscja.custom.interfaces">IRFIDWithUHFUARTUAE</a></dt>
<dd>
<div class="block">关闭UHF模块<br>
 Switch off UHF module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFCSYXForURx.html#free--">free()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFCSYXForURx.html" title="class in com.rscja.custom">UHFCSYXForURx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BluetoothReader.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/CardWithBYL.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/CardWithBYL.html" title="class in com.rscja.deviceapi">CardWithBYL</a></dt>
<dd>
<div class="block">释放模块</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 Release fingerprint module.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintSM206B.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintSM206B.html" title="class in com.rscja.deviceapi">FingerprintSM206B</a></dt>
<dd>
<div class="block">释放指纹模块</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 Release fingerprint module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.html" title="class in com.rscja.deviceapi">FingerprintWithMorpho</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 free fingerprint module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 free fingerprint module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></dt>
<dd>
<div class="block">释放蓝牙相关的资源(free Bluetooth resources)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/ICardWithBYL.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/ICardWithBYL.html" title="interface in com.rscja.deviceapi.interfaces">ICardWithBYL</a></dt>
<dd>
<div class="block">释放模块</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 Release fingerprint module.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 Release fingerprint module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 free fingerprint module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 free fingerprint module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IModule.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></dt>
<dd>
<div class="block">释放模块,模块断电同时关闭串口<br>
 Release module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IPrinter.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></dt>
<dd>
<div class="block">关闭打印机模块<br>
 Switch off printer module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IPSAM.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a></dt>
<dd>
<div class="block">释放PSAM<br>
 Release PSAM<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDBase.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDBase</a></dt>
<dd>
<div class="block">释放rfid模块<br>
 Release RFID module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithLF</a></dt>
<dd>
<div class="block">释放RFID低频模块<br>
 Release RFID LF module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#free--">free()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">关闭UHF模块<br>
 Switch OFF UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Module.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Module.html" title="class in com.rscja.deviceapi">Module</a></dt>
<dd>
<div class="block">释放模块,模块断电同时关闭串口<br>
 Release module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></dt>
<dd>
<div class="block">关闭打印机模块<br>
 Switch off printer module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/PSAM.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/PSAM.html" title="class in com.rscja.deviceapi">PSAM</a></dt>
<dd>
<div class="block">释放PSAM<br>
 Release PSAM<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDBase.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDBase.html" title="class in com.rscja.deviceapi">RFIDBase</a></dt>
<dd>
<div class="block">释放rfid模块<br>
 Release RFID module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithLF.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi">RFIDWithLF</a></dt>
<dd>
<div class="block">释放RFID低频模块<br>
 Release RFID LF module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">断开uhf连接<br>
 Switch OFF UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">断开uhf连接<br>
 Switch OFF UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">断开uhf连接<br>
 Switch OFF UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">断开uhf连接<br>
 Switch OFF UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>
<div class="block">关闭UHF模块<br>
 Switch off UHF module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>
<div class="block">断开uhf连接<br>
 Switch OFF UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">关闭UHF模块<br>
 Switch OFF UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>
<div class="block">断开usb连接，释放usb资源<br>
 Disconnect USB connection, release USB resources</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.html#free--">free()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/led/ScanLed.html#free--">free()</a></span> - Method in class com.rscja.scanner.led.<a href="../com/rscja/scanner/led/ScanLed.html" title="class in com.rscja.scanner.led">ScanLed</a></dt>
<dd>
<div class="block">释放设备资源<br>
 release device source<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#free--">free()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 Release fingerprint module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#free--">free()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithMorpho_mtk</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 free fingerprint module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#free--">free()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Module_mtk.html#free--">free()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Module_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Module_mtk</a></dt>
<dd>
<div class="block">释放模块,模块断电同时关闭串口<br>
 Release module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html#free--">free()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Printer_mtk</a></dt>
<dd>
<div class="block">关闭打印机模块<br>
 Switch off printer module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/PSAM_mtk.html#free--">free()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/PSAM_mtk.html" title="class in com.rscja.team.mtk.deviceapi">PSAM_mtk</a></dt>
<dd>
<div class="block">释放PSAM<br>
 Release PSAM<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html#free--">free()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDBase_mtk</a></dt>
<dd>
<div class="block">释放rfid模块<br>
 Release RFID module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#free--">free()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>
<div class="block">关闭UHF模块<br>
 Switch off UHF module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#free--">free()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi">ScanerLedLight_mtk</a></dt>
<dd>
<div class="block">释放设备资源<br>
 release device source<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/ScanLed_mtk.html#free--">free()</a></span> - Method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">ScanLed_mtk</a></dt>
<dd>
<div class="block">释放设备资源<br>
 release device source<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/M775Authenticate_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/M775Authenticate_qcom.html" title="class in com.rscja.team.qcom.custom">M775Authenticate_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/RFIDWithUHFUARTUAE_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/RFIDWithUHFUARTUAE_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFUARTUAE_qcom</a></dt>
<dd>
<div class="block">关闭UHF模块<br>
 Switch off UHF module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/UHFCSYX_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/UHFCSYX_qcom.html" title="class in com.rscja.team.qcom.custom">UHFCSYX_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html" title="class in com.rscja.team.qcom.deviceapi">CardWithBYL_qcom</a></dt>
<dd>
<div class="block">释放模块</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 Release fingerprint module.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintSM206B_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 Release fingerprint module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithMorpho_qcom</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 free fingerprint module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>
<div class="block">释放指纹模块<br>
 free fingerprint module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html#free-com.rscja.team.qcom.deviceapi.HardwareInterface_qcom.FunctionEnum-">free(HardwareInterface_qcom.FunctionEnum)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html" title="class in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom</a></dt>
<dd>
<div class="block">释放模块,模块断电同时关闭串口<br>
 Release module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Module_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Module_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Module_qcom</a></dt>
<dd>
<div class="block">释放模块,模块断电同时关闭串口<br>
 Release module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/PSAM_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/PSAM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">PSAM_qcom</a></dt>
<dd>
<div class="block">释放PSAM<br>
 Release PSAM<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDBase_qcom</a></dt>
<dd>
<div class="block">释放rfid模块<br>
 Release RFID module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithLF_qcom</a></dt>
<dd>
<div class="block">释放RFID低频模块<br>
 Release RFID LF module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>
<div class="block">断开uhf连接<br>
 Switch OFF UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>
<div class="block">断开uhf连接<br>
 Switch OFF UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8NetWork_qcom</a></dt>
<dd>
<div class="block">断开uhf连接<br>
 Switch OFF UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a></dt>
<dd>
<div class="block">断开uhf连接<br>
 Switch OFF UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>
<div class="block">关闭UHF模块<br>
 Switch off UHF module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>
<div class="block">断开uhf连接<br>
 Switch OFF UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">关闭UHF模块<br>
 Switch OFF UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>
<div class="block">断开usb连接，释放usb资源<br>
 Disconnect USB connection, release USB resources</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi">ScanerLedLight_qcom</a></dt>
<dd>
<div class="block">释放设备资源<br>
 release device source<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/r1/IPSAM.html#free-byte-">free(byte)</a></span> - Method in interface com.rscja.team.qcom.r1.<a href="../com/rscja/team/qcom/r1/IPSAM.html" title="interface in com.rscja.team.qcom.r1">IPSAM</a></dt>
<dd>
<div class="block">释放PSAM(Release PSAM)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/r1/psam/PSAM.html#free-byte-">free(byte)</a></span> - Method in class com.rscja.team.qcom.r1.psam.<a href="../com/rscja/team/qcom/r1/psam/PSAM.html" title="class in com.rscja.team.qcom.r1.psam">PSAM</a></dt>
<dd>
<div class="block">释放PSAM(Release PSAM)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#free--">free()</a></span> - Method in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.CMDInfo.html#fullData">fullData</a></span> - Variable in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.CMDInfo.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUtils_qcom.CMDInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html#fullData">fullData</a></span> - Variable in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.CMDInfo.html#fullData">fullData</a></span> - Variable in class com.rscja.team.qcom.uhfparse.<a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase.CMDInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FUNCTION_14443A">FUNCTION_14443A</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">14443A</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FUNCTION_15693">FUNCTION_15693</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">15693</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FUNCTION_1D">FUNCTION_1D</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">1D</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FUNCTION_2D">FUNCTION_2D</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">2D软解码<br>
 2D soft_decoding</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FUNCTION_2D_H">FUNCTION_2D_H</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">2D硬解码<br>
 2D Hard_decoding</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FUNCTION_LF_ANIMAL">FUNCTION_LF_ANIMAL</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">LF-动物标签<br>
 LF_Animal tag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FUNCTION_LF_EM4450">FUNCTION_LF_EM4450</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">LF-EM4450</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FUNCTION_LF_HDX">FUNCTION_LF_HDX</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">LF-hdx</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FUNCTION_LF_HID">FUNCTION_LF_HID</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">LF-HID</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FUNCTION_LF_HITAG">FUNCTION_LF_HITAG</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">LF-hiTag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FUNCTION_LF_ID">FUNCTION_LF_ID</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">LF-id卡<br>
 LF_ID card</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FUNCTION_LF_NEEDLE">FUNCTION_LF_NEEDLE</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">LF二合一模块<br>
 LF dual_protocol module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#FUNCTION_UHF">FUNCTION_UHF</a></span> - Static variable in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">UHF</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-5.html">Prev Letter</a></li>
<li><a href="index-7.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-6.html" target="_top">Frames</a></li>
<li><a href="index-6.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
