<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>L-Index</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="L-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-11.html">Prev Letter</a></li>
<li><a href="index-13.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-12.html" target="_top">Frames</a></li>
<li><a href="index-12.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;<a name="I:L">
<!--   -->
</a>
<h2 class="title">L</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#L">L</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/GyroAngle.html#last_sx">last_sx</a></span> - Variable in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/GyroAngle.html" title="class in com.rscja.team.qcom.utility">GyroAngle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/GyroAngle.html#last_sy">last_sy</a></span> - Variable in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/GyroAngle.html" title="class in com.rscja.team.qcom.utility">GyroAngle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/GyroAngle.html#last_sz">last_sz</a></span> - Variable in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/GyroAngle.html" title="class in com.rscja.team.qcom.utility">GyroAngle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/GyroAngle.html#last_time">last_time</a></span> - Variable in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/GyroAngle.html" title="class in com.rscja.team.qcom.utility">GyroAngle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html#led--">led()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a></dt>
<dd>
<div class="block">成功的通知提示(指示灯闪烁)<br>
 Success Notification Sound (flashing light)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#led--">led()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">成功的通知提示(指示灯闪烁)<br>
 Success Notification Sound (flashing light)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8.html#led--">led()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a></dt>
<dd>
<div class="block">成功的通知提示(指示灯闪烁)<br>
 Success Notification Sound (flashing light)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFAxBase.html#led--">led()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFAxBase.html" title="class in com.rscja.deviceapi">RFIDWithUHFAxBase</a></dt>
<dd>
<div class="block">成功的通知提示(指示灯闪烁)<br>
 Success Notification Sound (flashing light)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#led--">led()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">成功的通知提示(指示灯闪烁)<br>
 Success Notification Sound (flashing light)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html#led--">led()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8_qcom</a></dt>
<dd>
<div class="block">成功的通知提示(指示灯闪烁)<br>
 Success Notification Sound (flashing light)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#led--">led()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFAxBase_qcom</a></dt>
<dd>
<div class="block">成功的通知提示(指示灯闪烁)<br>
 Success Notification Sound (flashing light)</div>
</dd>
<dt><a href="../com/rscja/deviceapi/LedLight.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">LedLight</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">手柄LED灯控制类<br>
 Handdeld LED control type<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/LedLight.html#LedLight--">LedLight()</a></span> - Constructor for class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/LedLight.html" title="class in com.rscja.deviceapi">LedLight</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/deviceapi/LedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">LedLight_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></dt>
<dd>
<div class="block">手柄LED灯控制类<br>
 Handdeld LED control type<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/LedLight_mtk.html#LedLight_mtk--">LedLight_mtk()</a></span> - Constructor for class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/LedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi">LedLight_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/LedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">LedLight_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>
<div class="block">手柄LED灯控制类<br>
 Handdeld LED control type<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/LedLight_qcom.html#LedLight_qcom--">LedLight_qcom()</a></span> - Constructor for class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/LedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi">LedLight_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#LedOff-java.lang.String-int-">LedOff(String, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#LedOff-java.lang.String-int-">LedOff(String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#LedOn-java.lang.String-int-">LedOn(String, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#LedOn-java.lang.String-int-">LedOn(String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#loadChar-com.rscja.deviceapi.Fingerprint.BufferEnum-int-">loadChar(Fingerprint.BufferEnum, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">加载指定ID页到特征值缓存区<br>
 Load specified ID page to feature value buffer zone<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#loadChar-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">loadChar(int, FingerprintWithZAZ.BufferEnum, int[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>
<div class="block">读取模块中的指纹并暂存在RamBuffer中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#loadChar-com.rscja.deviceapi.Fingerprint.BufferEnum-int-">loadChar(Fingerprint.BufferEnum, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">加载指定ID页到特征值缓存区<br>
 Load specified ID page to feature value buffer zone<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#loadChar-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">loadChar(int, FingerprintWithZAZ.BufferEnum, int[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>
<div class="block">读取模块中的指纹并暂存在RamBuffer中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#loadChar-com.rscja.deviceapi.Fingerprint.BufferEnum-int-">loadChar(Fingerprint.BufferEnum, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">加载指定ID页到特征值缓存区<br>
 Load specified ID page to feature value buffer zone<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#loadChar-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">loadChar(int, FingerprintWithZAZ.BufferEnum, int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>
<div class="block">读取模块中的指纹并暂存在RamBuffer中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IHF15693.html#lockAFI--">lockAFI()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces">IHF15693</a></dt>
<dd>
<div class="block">锁定AFI(lock AFI)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#lockAFI--">lockAFI()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></dt>
<dd>
<div class="block">锁定AFI<br>
 lock AFI<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO15693.html#lockAFI--">lockAFI()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi">RFIDWithISO15693</a></dt>
<dd>
<div class="block">锁定AFI<br>
 lock AFI<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html#lockAFI--">lockAFI()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk</a></dt>
<dd>
<div class="block">锁定AFI<br>
 lock AFI<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#lockAFI--">lockAFI()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a></dt>
<dd>
<div class="block">锁定AFI<br>
 lock AFI<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/r1/hf/HF15693.html#lockAFI--">lockAFI()</a></span> - Method in class com.rscja.team.qcom.r1.hf.<a href="../com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf">HF15693</a></dt>
<dd>
<div class="block">锁定AFI(lock AFI)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_ACCESS">LockBank_ACCESS</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">锁定的区域ACCESS (ACCESS LOCK area)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_EPC">LockBank_EPC</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">锁定的区域EPC (EPC LOCK area)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_KILL">LockBank_KILL</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">锁定的区域KILL (KILL LOCK area)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_TID">LockBank_TID</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">锁定的区域TID (TID LOCK area)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_USER">LockBank_USER</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">锁定的区域USER (USER LOCK area)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IHF15693.html#lockDsfid--">lockDsfid()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces">IHF15693</a></dt>
<dd>
<div class="block">锁定Dsfid(lock Dsfid)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#lockDSFID--">lockDSFID()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></dt>
<dd>
<div class="block">锁定DSFID<br>
 lock DSFID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO15693.html#lockDSFID--">lockDSFID()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi">RFIDWithISO15693</a></dt>
<dd>
<div class="block">锁定DSFID<br>
 lock DSFID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html#lockDSFID--">lockDSFID()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk</a></dt>
<dd>
<div class="block">锁定DSFID<br>
 lock DSFID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#lockDSFID--">lockDSFID()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a></dt>
<dd>
<div class="block">锁定DSFID<br>
 lock DSFID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/r1/hf/HF15693.html#lockDsfid--">lockDsfid()</a></span> - Method in class com.rscja.team.qcom.r1.hf.<a href="../com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf">HF15693</a></dt>
<dd>
<div class="block">锁定Dsfid(lock Dsfid)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">锁定指定标签<br>
 Lock specific tag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">锁定标签<br>
 Lock tag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#lockMem-java.lang.String-java.lang.String-java.lang.String-">lockMem(String, String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">锁定指定标签<br>
 Lock specific tag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">锁定标签<br>
 Lock tag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">锁指定标签(Lock specified label)
 示例代码:比如锁定EPC区和USER区  (Sample code: such as locking EPC area and USER area)
                List<Integer> lockBank=new ArrayList<>();
                lockBank.add(IUHF.LockBank_EPC);
                lockBank.add(IUHF.LockBank_USER);
                int lockMode =IUHF.LockMode_LOCK;
                String hexLockCode=generateLockCode(lockBank,lockMode);
            lockMem(accessPwd,bank,ptr,cnt,filterData,hexLockCode);</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">锁标签(lock label)

 示例代码:比如锁定EPC区和USER区  (Sample code: such as locking EPC area and USER area)
                List<Integer> lockBank=new ArrayList<>();
                lockBank.add(IUHF.LockBank_EPC);
                lockBank.add(IUHF.LockBank_USER);
                int lockMode =IUHF.LockMode_LOCK;
                String hexLockCode=generateLockCode(lockBank,lockMode);
            lockMem(accessPwd,hexLockCode);</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">锁指定标签(Lock specified label)
 示例代码:比如锁定EPC区和USER区  (Sample code: such as locking EPC area and USER area)
                List<Integer> lockBank=new ArrayList<>();
                lockBank.add(IUHF.LockBank_EPC);
                lockBank.add(IUHF.LockBank_USER);
                int lockMode =IUHF.LockMode_LOCK;
                String hexLockCode=generateLockCode(lockBank,lockMode);
            lockMem(accessPwd,bank,ptr,cnt,filterData,hexLockCode);</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">锁标签(lock label)

 示例代码:比如锁定EPC区和USER区  (Sample code: such as locking EPC area and USER area)
                List<Integer> lockBank=new ArrayList<>();
                lockBank.add(IUHF.LockBank_EPC);
                lockBank.add(IUHF.LockBank_USER);
                int lockMode =IUHF.LockMode_LOCK;
                String hexLockCode=generateLockCode(lockBank,lockMode);
            lockMem(accessPwd,hexLockCode);</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">锁指定标签(Lock specified label)
 示例代码:比如锁定EPC区和USER区  (Sample code: such as locking EPC area and USER area)
                List<Integer> lockBank=new ArrayList<>();
                lockBank.add(IUHF.LockBank_EPC);
                lockBank.add(IUHF.LockBank_USER);
                int lockMode =IUHF.LockMode_LOCK;
                String hexLockCode=generateLockCode(lockBank,lockMode);
            lockMem(accessPwd,bank,ptr,cnt,filterData,hexLockCode);</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">锁标签(lock label)

 示例代码:比如锁定EPC区和USER区  (Sample code: such as locking EPC area and USER area)
                List<Integer> lockBank=new ArrayList<>();
                lockBank.add(IUHF.LockBank_EPC);
                lockBank.add(IUHF.LockBank_USER);
                int lockMode =IUHF.LockMode_LOCK;
                String hexLockCode=generateLockCode(lockBank,lockMode);
            lockMem(accessPwd,hexLockCode);</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">锁指定标签(Lock specified label)
 示例代码:比如锁定EPC区和USER区  (Sample code: such as locking EPC area and USER area)
                List<Integer> lockBank=new ArrayList<>();
                lockBank.add(IUHF.LockBank_EPC);
                lockBank.add(IUHF.LockBank_USER);
                int lockMode =IUHF.LockMode_LOCK;
                String hexLockCode=generateLockCode(lockBank,lockMode);
            lockMem(accessPwd,bank,ptr,cnt,filterData,hexLockCode);</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">锁标签(lock label)

 示例代码:比如锁定EPC区和USER区  (Sample code: such as locking EPC area and USER area)
                List<Integer> lockBank=new ArrayList<>();
                lockBank.add(IUHF.LockBank_EPC);
                lockBank.add(IUHF.LockBank_USER);
                int lockMode =IUHF.LockMode_LOCK;
                String hexLockCode=generateLockCode(lockBank,lockMode);
            lockMem(accessPwd,hexLockCode);</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi">RFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html#lockMem-java.lang.String-java.lang.String-java.lang.String-">lockMem(String, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi">RFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">锁定指定标签<br>
 Lock specific tag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">锁定标签<br>
 Lock tag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#lockMem-java.lang.String-java.lang.String-java.lang.String-">lockMem(String, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">锁定指定标签<br>
 Lock specific tag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">锁定标签<br>
 Lock tag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_LOCK">LockMode_LOCK</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">锁定模式:锁定(LOCK mode: lock)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_OPEN">LockMode_OPEN</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">锁定模式:开放(LOCK mode: Release)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_PLOCK">LockMode_PLOCK</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">锁定模式:永久锁定(LOCK mode: Permanent LOCK)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_POPEN">LockMode_POPEN</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">锁定模式:永久开放(LOCK mode: Permanent release)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html#logD-java.lang.String-">logD(String)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/utility/LogUtility_mtk.html" title="class in com.rscja.team.mtk.utility"><span class="typeNameLink">LogUtility_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/utility/package-summary.html">com.rscja.team.mtk.utility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/utility/LogUtility_mtk.html#LogUtility_mtk--">LogUtility_mtk()</a></span> - Constructor for class com.rscja.team.mtk.utility.<a href="../com/rscja/team/mtk/utility/LogUtility_mtk.html" title="class in com.rscja.team.mtk.utility">LogUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/utility/LogUtility_qcom.html" title="class in com.rscja.team.qcom.utility"><span class="typeNameLink">LogUtility_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/utility/package-summary.html">com.rscja.team.qcom.utility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/LogUtility_qcom.html#LogUtility_qcom--">LogUtility_qcom()</a></span> - Constructor for class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/LogUtility_qcom.html" title="class in com.rscja.team.qcom.utility">LogUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#long2Bytes-long-">long2Bytes(long)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>
<div class="block">long转byte数组</div>
</dd>
<dt><a href="../com/rscja/deviceapi/entity/LowBatteryEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">LowBatteryEntity</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/LowBatteryEntity.html#LowBatteryEntity--">LowBatteryEntity()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/LowBatteryEntity.html" title="class in com.rscja.deviceapi.entity">LowBatteryEntity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/exception/LowBatteryException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">LowBatteryException</span></a> - Exception in <a href="../com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/exception/LowBatteryException.html#LowBatteryException-java.lang.String-">LowBatteryException(String)</a></span> - Constructor for exception com.rscja.deviceapi.exception.<a href="../com/rscja/deviceapi/exception/LowBatteryException.html" title="class in com.rscja.deviceapi.exception">LowBatteryException</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-11.html">Prev Letter</a></li>
<li><a href="index-13.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-12.html" target="_top">Frames</a></li>
<li><a href="index-12.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
