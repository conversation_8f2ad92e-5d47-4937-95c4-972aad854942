<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>B-Index</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="B-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-1.html">Prev Letter</a></li>
<li><a href="index-3.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-2.html" target="_top">Frames</a></li>
<li><a href="index-2.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;<a name="I:B">
<!--   -->
</a>
<h2 class="title">B</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#B">B</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC">Bank_EPC</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">EPC存储区(EPC area)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED">Bank_RESERVED</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">RESERVED存储区（RESERVED area）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID">Bank_TID</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">TID存储区(TID area)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER">Bank_USER</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">USER存储区(USER area)</div>
</dd>
<dt><a href="../com/rscja/deviceapi/Barcode1D.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Barcode1D</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">一维条码操作类<br>1D barcode operation class<br></div>
</dd>
<dt><a href="../com/rscja/team/mtk/deviceapi/Barcode1D_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Barcode1D_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></dt>
<dd>
<div class="block">一维条码操作类<br>1D barcode operation class<br></div>
</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Barcode1D_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>
<div class="block">一维条码操作类<br>1D barcode operation class<br></div>
</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.UHFProtocolParseBase.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Barcode1D_qcom.UHFProtocolParseBase</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/Barcode2D.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Barcode2D</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">二维条码操作类 <br>
 2D barcode operation class.</div>
</dd>
<dt><a href="../com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Barcode2D_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></dt>
<dd>
<div class="block">二维条码操作类 <br>
 2D barcode operation class.</div>
</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/Barcode2D_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Barcode2D_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>
<div class="block">二维条码操作类 <br>
 2D barcode operation class.</div>
</dd>
<dt><a href="../com/rscja/team/mtk/barcode/barcode2d/Barcode2DFactory_mtk.html" title="class in com.rscja.team.mtk.barcode.barcode2d"><span class="typeNameLink">Barcode2DFactory_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/barcode/barcode2d/package-summary.html">com.rscja.team.mtk.barcode.barcode2d</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/barcode/barcode2d/Barcode2DFactory_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d"><span class="typeNameLink">Barcode2DFactory_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/barcode/barcode2d/package-summary.html">com.rscja.team.qcom.barcode.barcode2d</a></dt>
<dd>
<div class="block">2D 条码工厂类</div>
</dd>
<dt><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode"><span class="typeNameLink">Barcode2DSHardwareInfo</span></a> - Class in <a href="../com/rscja/barcode/package-summary.html">com.rscja.barcode</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#Barcode2DSHardwareInfo--">Barcode2DSHardwareInfo()</a></span> - Constructor for class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Barcode2DSHardwareInfo_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/barcode/package-summary.html">com.rscja.team.mtk.barcode</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html#Barcode2DSHardwareInfo_mtk--">Barcode2DSHardwareInfo_mtk()</a></span> - Constructor for class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html" title="class in com.rscja.team.mtk.barcode">Barcode2DSHardwareInfo_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/barcode/Barcode2DSHardwareInfo_qcom.html" title="class in com.rscja.team.qcom.barcode"><span class="typeNameLink">Barcode2DSHardwareInfo_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/barcode/package-summary.html">com.rscja.team.qcom.barcode</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/Barcode2DSHardwareInfo_qcom.html#Barcode2DSHardwareInfo_qcom--">Barcode2DSHardwareInfo_qcom()</a></span> - Constructor for class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/Barcode2DSHardwareInfo_qcom.html" title="class in com.rscja.team.qcom.barcode">Barcode2DSHardwareInfo_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#Barcode2DSoftCommon-android.content.Context-">Barcode2DSoftCommon(Context)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html" title="class in com.rscja.team.mtk.barcode">Barcode2DSoftCommon_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Barcode2DSoftCommon_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/barcode/package-summary.html">com.rscja.team.mtk.barcode</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#Barcode2DSoftCommon_mtk--">Barcode2DSoftCommon_mtk()</a></span> - Constructor for class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html" title="class in com.rscja.team.mtk.barcode">Barcode2DSoftCommon_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_1D_Close-java.lang.String-">Barcode_1D_Close(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Barcode_1D_Close-java.lang.String-">Barcode_1D_Close(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_1D_Open-java.lang.String-java.lang.String-int-">Barcode_1D_Open(String, String, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Barcode_1D_Open-java.lang.String-java.lang.String-int-">Barcode_1D_Open(String, String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_1D_Scan-java.lang.String-">Barcode_1D_Scan(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Barcode_1D_Scan-java.lang.String-">Barcode_1D_Scan(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_1D_SetTimeOut-int-">Barcode_1D_SetTimeOut(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Barcode_1D_SetTimeOut-int-">Barcode_1D_SetTimeOut(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_1D_StopScan-java.lang.String-">Barcode_1D_StopScan(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Barcode_1D_StopScan-java.lang.String-">Barcode_1D_StopScan(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_2D_Close-java.lang.String-">Barcode_2D_Close(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Barcode_2D_Close-java.lang.String-">Barcode_2D_Close(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_2D_Open-java.lang.String-java.lang.String-int-">Barcode_2D_Open(String, String, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Barcode_2D_Open-java.lang.String-java.lang.String-int-">Barcode_2D_Open(String, String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_2D_Scan-java.lang.String-">Barcode_2D_Scan(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Barcode_2D_Scan-java.lang.String-">Barcode_2D_Scan(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_2D_SetTimeOut-int-">Barcode_2D_SetTimeOut(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Barcode_2D_SetTimeOut-int-">Barcode_2D_SetTimeOut(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_2D_StopScan-java.lang.String-">Barcode_2D_StopScan(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Barcode_2D_StopScan-java.lang.String-">Barcode_2D_StopScan(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/ConstantUtil.html#BARCODE_BYTE_DATA">BARCODE_BYTE_DATA</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/ConstantUtil.html" title="class in com.rscja.barcode">ConstantUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/ConstantUtil.html#BARCODE_CODE">BARCODE_CODE</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/ConstantUtil.html" title="class in com.rscja.barcode">ConstantUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/ConstantUtil.html#BARCODE_DECODE_TIME">BARCODE_DECODE_TIME</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/ConstantUtil.html" title="class in com.rscja.barcode">ConstantUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/ConstantUtil.html#BARCODE_HEX_DATA">BARCODE_HEX_DATA</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/ConstantUtil.html" title="class in com.rscja.barcode">ConstantUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/ConstantUtil.html#BARCODE_NAME">BARCODE_NAME</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/ConstantUtil.html" title="class in com.rscja.barcode">ConstantUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/ConstantUtil.html#BARCODE_RESULT_CODE">BARCODE_RESULT_CODE</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/ConstantUtil.html" title="class in com.rscja.barcode">ConstantUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/ConstantUtil.html#BARCODE_STRING_DATA">BARCODE_STRING_DATA</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/ConstantUtil.html" title="class in com.rscja.barcode">ConstantUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode"><span class="typeNameLink">BarcodeDecoder</span></a> - Class in <a href="../com/rscja/barcode/package-summary.html">com.rscja.barcode</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeDecoder.html#BarcodeDecoder--">BarcodeDecoder()</a></span> - Constructor for class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/barcode/BarcodeDecoder.DecodeCallback.html" title="interface in com.rscja.barcode"><span class="typeNameLink">BarcodeDecoder.DecodeCallback</span></a> - Interface in <a href="../com/rscja/barcode/package-summary.html">com.rscja.barcode</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/barcode/BarcodeDecoder.IBarcodeImageCallback.html" title="interface in com.rscja.barcode"><span class="typeNameLink">BarcodeDecoder.IBarcodeImageCallback</span></a> - Interface in <a href="../com/rscja/barcode/package-summary.html">com.rscja.barcode</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">BarcodeDecoder_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/barcode/package-summary.html">com.rscja.team.mtk.barcode</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#BarcodeDecoder_mtk--">BarcodeDecoder_mtk()</a></span> - Constructor for class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeDecoder_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.DecodeCallback.html" title="interface in com.rscja.team.mtk.barcode"><span class="typeNameLink">BarcodeDecoder_mtk.DecodeCallback</span></a> - Interface in <a href="../com/rscja/team/mtk/barcode/package-summary.html">com.rscja.team.mtk.barcode</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">BarcodeEntity</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeEntity.html#BarcodeEntity--">BarcodeEntity()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity">BarcodeEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeEntity.html#BarcodeEntity-int-int-">BarcodeEntity(int, int)</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity">BarcodeEntity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/barcode/BarcodeFactory.html" title="class in com.rscja.barcode"><span class="typeNameLink">BarcodeFactory</span></a> - Class in <a href="../com/rscja/barcode/package-summary.html">com.rscja.barcode</a></dt>
<dd>
<div class="block">条码工厂类</div>
</dd>
<dt><a href="../com/rscja/team/mtk/barcode/BarcodeFactory_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">BarcodeFactory_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/barcode/package-summary.html">com.rscja.team.mtk.barcode</a></dt>
<dd>
<div class="block">条码工厂类</div>
</dd>
<dt><a href="../com/rscja/team/qcom/barcode/BarcodeFactory_qcom.html" title="class in com.rscja.team.qcom.barcode"><span class="typeNameLink">BarcodeFactory_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/barcode/package-summary.html">com.rscja.team.qcom.barcode</a></dt>
<dd>
<div class="block">条码工厂类</div>
</dd>
<dt><a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">BarcodeResult</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#barcodeScanTimeOutSend--">barcodeScanTimeOutSend()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/barcode/BarcodeSymbolUtility.html" title="class in com.rscja.barcode"><span class="typeNameLink">BarcodeSymbolUtility</span></a> - Class in <a href="../com/rscja/barcode/package-summary.html">com.rscja.barcode</a></dt>
<dd>
<div class="block">条码符号工具类</div>
</dd>
<dt><a href="../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">BarcodeSymbolUtility_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/barcode/package-summary.html">com.rscja.team.mtk.barcode</a></dt>
<dd>
<div class="block">条码符号工具类</div>
</dd>
<dt><a href="../com/rscja/team/qcom/barcode/BarcodeSymbolUtility_qcom.html" title="class in com.rscja.team.qcom.barcode"><span class="typeNameLink">BarcodeSymbolUtility_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/barcode/package-summary.html">com.rscja.team.qcom.barcode</a></dt>
<dd>
<div class="block">条码符号工具类</div>
</dd>
<dt><a href="../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode"><span class="typeNameLink">BarcodeUtility</span></a> - Class in <a href="../com/rscja/barcode/package-summary.html">com.rscja.barcode</a></dt>
<dd>
<div class="block">条码操作类
 barcode operate class



 注意：
 Attention:
 1、使用前请确认您的机器已安装键盘助手v1.9.0 之后的版本。
 1.</div>
</dd>
<dt><a href="../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode"><span class="typeNameLink">BarcodeUtility.ModuleType</span></a> - Enum in <a href="../com/rscja/barcode/package-summary.html">com.rscja.barcode</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">BarcodeUtility_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/barcode/package-summary.html">com.rscja.team.mtk.barcode</a></dt>
<dd>
<div class="block">条码操作类
 barcode operate class



 注意：
 Attention:
 1、使用前请确认您的机器已安装键盘助手v1.9.0 之后的版本。
 1.</div>
</dd>
<dt><a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode"><span class="typeNameLink">BarcodeUtility_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/barcode/package-summary.html">com.rscja.team.qcom.barcode</a></dt>
<dd>
<div class="block">条码操作类
 barcode operate class



 注意：
 Attention:
 1、使用前请确认您的机器已安装键盘助手v1.9.0 之后的版本。
 1.</div>
</dd>
<dt><a href="../com/rscja/deviceapi/entity/BatteryEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">BatteryEntity</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BatteryEntity.html#BatteryEntity-int-">BatteryEntity(int)</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BatteryEntity.html" title="class in com.rscja.deviceapi.entity">BatteryEntity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/utility/BatteryManage.html" title="class in com.rscja.team.mtk.utility"><span class="typeNameLink">BatteryManage</span></a> - Class in <a href="../com/rscja/team/mtk/utility/package-summary.html">com.rscja.team.mtk.utility</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility"><span class="typeNameLink">BatteryUtils</span></a> - Class in <a href="../com/rscja/utility/package-summary.html">com.rscja.utility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/BatteryUtils.html#BatteryUtils--">BatteryUtils()</a></span> - Constructor for class com.rscja.utility.<a href="../com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility">BatteryUtils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#bdOff-java.lang.String-">bdOff(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#bdOff-java.lang.String-">bdOff(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#bdOn-java.lang.String-">bdOn(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#bdOn-java.lang.String-">bdOn(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#BitToByte-java.lang.String-">BitToByte(String)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>
<div class="block">Bit转Byte</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#BLE_UUID_GAP">BLE_UUID_GAP</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#BLE_UUID_GAP_CHARACTERISTIC_DEVICE_NAME">BLE_UUID_GAP_CHARACTERISTIC_DEVICE_NAME</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">BleDevice</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">BLE蓝牙的操作对象<br>

 1.通过<code>#connect(ConnectionStatusCallback<Object> bleStatusCallback)</code> 来连接蓝牙，bleStatusCallback是蓝牙状态的连接回调<br>
 2.通过<a href="../com/rscja/deviceapi/BleDevice.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>BleDevice.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a> 标签数据回调接口。<br>
 3.通过<a href="../com/rscja/deviceapi/BleDevice.html#startInventoryTag--"><code>BleDevice.startInventoryTag()</code></a> 开始盘点，在盘点过程中，ble设备只会响应<a href="../com/rscja/deviceapi/BleDevice.html#stopInventory--"><code>BleDevice.stopInventory()</code></a> ()}函数，不能设置和获取ble设备的相关其他参数。<br>
 4.通过<a href="../com/rscja/deviceapi/BleDevice.html#stopInventory--"><code>BleDevice.stopInventory()</code></a> ()} 停止盘点<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#BleDevice-java.lang.String-android.content.Context-">BleDevice(String, Context)</a></span> - Constructor for class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/BleDevice.BleDeviceInfo.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">BleDevice.BleDeviceInfo</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">BleDevice_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#BleDevice_qcom-java.lang.String-android.content.Context-">BleDevice_qcom(String, Context)</a></span> - Constructor for class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service"><span class="typeNameLink">BLEService_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/service/package-summary.html">com.rscja.team.qcom.service</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#BLEService_qcom--">BLEService_qcom()</a></span> - Constructor for class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/service/BLEService_qcom.BluetoothStateReceiver.html" title="class in com.rscja.team.qcom.service"><span class="typeNameLink">BLEService_qcom.BluetoothStateReceiver</span></a> - Class in <a href="../com/rscja/team/qcom/service/package-summary.html">com.rscja.team.qcom.service</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/service/BLEService_qcom.IDataCallBack.html" title="interface in com.rscja.team.qcom.service"><span class="typeNameLink">BLEService_qcom.IDataCallBack</span></a> - Interface in <a href="../com/rscja/team/qcom/service/package-summary.html">com.rscja.team.qcom.service</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/led/ScanLed.html#blink--">blink()</a></span> - Method in class com.rscja.scanner.led.<a href="../com/rscja/scanner/led/ScanLed.html" title="class in com.rscja.scanner.led">ScanLed</a></dt>
<dd>
<div class="block">亮0.5S后熄灭<br>
 light up for 0.5s then OFF<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/led/ScanLed.html#blink-int-int-">blink(int, int)</a></span> - Method in class com.rscja.scanner.led.<a href="../com/rscja/scanner/led/ScanLed.html" title="class in com.rscja.scanner.led">ScanLed</a></dt>
<dd>
<div class="block">控制亮灯后自动熄灭</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#blink-android.content.Context-">blink(Context)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi">ScanerLedLight_mtk</a></dt>
<dd>
<div class="block">亮0.5S后熄灭<br>
 light up for 0.5s then OFF<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#blink-android.content.Context-int-int-">blink(Context, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi">ScanerLedLight_mtk</a></dt>
<dd>
<div class="block">控制亮灯后自动熄灭</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/ScanLed_mtk.html#blink--">blink()</a></span> - Method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">ScanLed_mtk</a></dt>
<dd>
<div class="block">亮0.5S后熄灭<br>
 light up for 0.5s then OFF<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/ScanLed_mtk.html#blink-int-int-">blink(int, int)</a></span> - Method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">ScanLed_mtk</a></dt>
<dd>
<div class="block">控制亮灯后自动熄灭</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html#blink-android.content.Context-">blink(Context)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi">ScanerLedLight_qcom</a></dt>
<dd>
<div class="block">亮0.5S后熄灭<br>
 light up for 0.5s then OFF<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html#blink-android.content.Context-int-int-">blink(Context, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi">ScanerLedLight_qcom</a></dt>
<dd>
<div class="block">控制亮灯后自动熄灭</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BluetoothReader.html#blinkOfLed-int-int-int-">blinkOfLed(int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></dt>
<dd>
<div class="block">led 闪烁</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IReader.html#blinkOfLed-int-int-int-">blinkOfLed(int, int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></dt>
<dd>
<div class="block">led 闪烁</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#blinkOfLed-int-int-int-">blinkOfLed(int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>
<div class="block">led 闪烁</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#blinkOfLed-int-int-int-">blinkOfLed(int, int, int)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#blinkOfLed-int-int-int-">blinkOfLed(int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>
<div class="block">led 闪烁</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#blinkOfLed-int-int-int-">blinkOfLed(int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>
<div class="block">led 闪烁</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#blinkOfLedSendData-int-int-int-">blinkOfLedSendData(int, int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#blinkOfLedSendData-int-int-int-">blinkOfLedSendData(int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#blockEraseDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-">blockEraseDataSendData(String, char, int, int, String, char, int, char)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#blockEraseDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-">blockEraseDataSendData(String, char, int, int, String, char, int, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">向标签写入数据(支持大数据标签写入)、当需要写入的标签数据比较大可以使用此函数写入<br>
 Write data into tag(support big-sized data writing), user could use this function when big-sized data need to be written.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">向标签写入数据(支持大数据标签写入)、当需要写入的标签数据比较大可以使用此函数写入<br>
 Write data into tag(support big-sized data writing), user could use this function when big-sized data need to be written.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">将数据写入指定标签(支持大数据标签写入)、当需要写入的标签数据比较大可以使用此函数写入(Write data into tag(support big-sized data writing), user could use this function when big-sized data need to be written.)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">将数据写入指定标签(支持大数据标签写入)、当需要写入的标签数据比较大可以使用此函数写入(Write data into tag(support big-sized data writing), user could use this function when big-sized data need to be written.)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">将数据写入指定标签(支持大数据标签写入)、当需要写入的标签数据比较大可以使用此函数写入(Write data into tag(support big-sized data writing), user could use this function when big-sized data need to be written.)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">将数据写入指定标签(支持大数据标签写入)、当需要写入的标签数据比较大可以使用此函数写入(Write data into tag(support big-sized data writing), user could use this function when big-sized data need to be written.)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">向标签写入数据(支持大数据标签写入)、当需要写入的标签数据比较大可以使用此函数写入<br>
 Write data into tag(support big-sized data writing), user could use this function when big-sized data need to be written.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">向标签写入数据(支持大数据标签写入)、当需要写入的标签数据比较大可以使用此函数写入<br>
 Write data into tag(support big-sized data writing), user could use this function when big-sized data need to be written.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#blockWriteDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-java.lang.String-">blockWriteDataSendData(String, char, int, int, String, char, int, char, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#blockWriteDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-java.lang.String-">blockWriteDataSendData(String, char, int, int, String, char, int, char, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">BluetoothReader</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">蓝牙读写器操作类：<br>
 Bluetoot reader operation
 第一步：调用<a href="../com/rscja/deviceapi/BluetoothReader.html#init-android.content.Context-"><code>BluetoothReader.init(Context context)</code></a> 函数初始化蓝牙相关服务，退出应用程序需要调用<a href="../com/rscja/deviceapi/BluetoothReader.html#free--"><code>BluetoothReader.free()</code></a>是否蓝牙相关资源<br>
 First step: Call <a href="../com/rscja/deviceapi/BluetoothReader.html#init-android.content.Context-"><code>BluetoothReader.init(Context context)</code></a>to initiate BT service, call <a href="../com/rscja/deviceapi/BluetoothReader.html#free--"><code>BluetoothReader.free()</code></a> to exit application.</div>
</dd>
<dt><a href="../com/rscja/deviceapi/BluetoothReader.DecodeCallback.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">BluetoothReader.DecodeCallback</span></a> - Interface in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/BluetoothReader.OnDataChangeListener.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">BluetoothReader.OnDataChangeListener</span></a> - Interface in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>
<div class="block">接收蓝牙原始数据的接口 <br>
 Interface of receiving initial data.</div>
</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">BluetoothReader_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>
<div class="block">蓝牙读写器操作类：<br>
 Bluetoot reader operation
 第一步：调用<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#init-android.content.Context-"><code>BluetoothReader_qcom.init(Context context)</code></a> 函数初始化蓝牙相关服务，退出应用程序需要调用<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#free--"><code>BluetoothReader_qcom.free()</code></a>是否蓝牙相关资源<br>
 First step: Call <a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#init-android.content.Context-"><code>BluetoothReader_qcom.init(Context context)</code></a>to initiate BT service, call <a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#free--"><code>BluetoothReader_qcom.free()</code></a> to exit application.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.BluetoothStateReceiver.html#BluetoothStateReceiver--">BluetoothStateReceiver()</a></span> - Constructor for class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.BluetoothStateReceiver.html" title="class in com.rscja.team.qcom.service">BLEService_qcom.BluetoothStateReceiver</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#BT_DEVICE">BT_DEVICE</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#BT_RECORD">BT_RECORD</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#BT_RSSI">BT_RSSI</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btDeleteAllTagToFlashSendData--">btDeleteAllTagToFlashSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#btDeleteAllTagToFlashSendData--">btDeleteAllTagToFlashSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btGetAllTagNumFromFlashSendData--">btGetAllTagNumFromFlashSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#btGetAllTagNumFromFlashSendData--">btGetAllTagNumFromFlashSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btGetNewTagNumFromFlashSendData--">btGetNewTagNumFromFlashSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#btGetNewTagNumFromFlashSendData--">btGetNewTagNumFromFlashSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btGetTagDataFromFlashSendData--">btGetTagDataFromFlashSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#btGetTagDataFromFlashSendData--">btGetTagDataFromFlashSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#build-byte:A-boolean-boolean-">build(byte[], boolean, boolean)</a></span> - Static method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryModeEntity.Builder.html#build--">build()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryModeEntity.Builder.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/RadarLocationEntity.Builder.html#build--">build()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/RadarLocationEntity.Builder.html" title="class in com.rscja.deviceapi.entity">RadarLocationEntity.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.Builder.html#build--">build()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.Builder.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.Builder.html#build--">build()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.Builder.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo.ChipInfo.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/HF14443RequestEntity.html#builder-byte:A-byte:A-">builder(byte[], byte[])</a></span> - Static method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/HF14443RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF14443RequestEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.Builder.html#Builder--">Builder()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.Builder.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryModeEntity.Builder.html#Builder--">Builder()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryModeEntity.Builder.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/RadarLocationEntity.Builder.html#Builder--">Builder()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/RadarLocationEntity.Builder.html" title="class in com.rscja.deviceapi.entity">RadarLocationEntity.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.Builder.html#Builder--">Builder()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.Builder.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.Builder.html#Builder--">Builder()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.Builder.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo.ChipInfo.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builder1DConfiguration--">builder1DConfiguration()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">创建一维条码配置信息<br>
 Create 1D barcode config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builder1DConfiguration--">builder1DConfiguration()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">创建一维条码配置信息<br>
 Create 1D barcode config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builder2DConfiguration--">builder2DConfiguration()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">创建二维条码配置信息<br>
 Create 2D barcode config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builder2DConfiguration--">builder2DConfiguration()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">创建二维条码配置信息<br>
 Create 2D barcode config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderA8ExtendedUartConfiguration--">builderA8ExtendedUartConfiguration()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">创建A8扩展串口配置信息<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderA8ExtendedUartConfiguration--">builderA8ExtendedUartConfiguration()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">创建A8扩展串口配置信息<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderBDConfiguration--">builderBDConfiguration()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">创建北斗配置信息<br>
 Create Beidou config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderBDConfiguration--">builderBDConfiguration()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">创建北斗配置信息<br>
 Create Beidou config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderDefaultConfiguration--">builderDefaultConfiguration()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderDefaultConfiguration--">builderDefaultConfiguration()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderFingerprintConfiguration--">builderFingerprintConfiguration()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">创建Fingerprint配置信息<br>
 Create Fingerprint config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderFingerprintConfiguration--">builderFingerprintConfiguration()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">创建Fingerprint配置信息<br>
 Create Fingerprint config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderInfraredConfiguration--">builderInfraredConfiguration()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">创建红外配置信息<br>
 Create infrared config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderInfraredConfiguration--">builderInfraredConfiguration()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">创建红外配置信息<br>
 Create infrared config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderLFConfiguration--">builderLFConfiguration()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">创建低频配置信息<br>
 Create LF config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderLFConfiguration--">builderLFConfiguration()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">创建低频配置信息<br>
 Create LF config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderPrinterConfiguration--">builderPrinterConfiguration()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">创建打印机配置信息<br>
 Create printer config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderPrinterConfiguration--">builderPrinterConfiguration()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">创建打印机配置信息<br>
 Create printer config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderRFIDConfiguration--">builderRFIDConfiguration()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">创建RFID配置信息<br>
 Create RFID config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderRFIDConfiguration--">builderRFIDConfiguration()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">创建RFID配置信息<br>
 Create RFID config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderUHFConfiguration--">builderUHFConfiguration()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">创建UHF配置信息<br>
 Create UHF config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderUHFConfiguration--">builderUHFConfiguration()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">创建UHF配置信息<br>
 Create UHF config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderUrx--">builderUrx()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">创建ur 4 配置信息<br>
 Create 1D barcode config infor.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html#buzzer--">buzzer()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a></dt>
<dd>
<div class="block">成功的通知提示(发出提示音)<br>
 Success Notification Sound (Play Sound)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#buzzer--">buzzer()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">成功的通知提示(发出提示音)<br>
 Success Notification Sound (Play Sound)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8.html#buzzer--">buzzer()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a></dt>
<dd>
<div class="block">成功的通知提示(发出提示音)<br>
 Success Notification Sound (Play Sound)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFAxBase.html#buzzer--">buzzer()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFAxBase.html" title="class in com.rscja.deviceapi">RFIDWithUHFAxBase</a></dt>
<dd>
<div class="block">成功的通知提示(发出提示音)<br>
 Success Notification Sound (Play Sound)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#buzzer--">buzzer()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">成功的通知提示(发出提示音)<br>
 Success Notification Sound (Play Sound)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html#buzzer--">buzzer()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8_qcom</a></dt>
<dd>
<div class="block">成功的通知提示(发出提示音)<br>
 Success Notification Sound (Play Sound)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#buzzer--">buzzer()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFAxBase_qcom</a></dt>
<dd>
<div class="block">成功的通知提示(发出提示音)<br>
 Success Notification Sound (Play Sound)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#buzzerOn-int-int-int-">buzzerOn(int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#byte2Bit-byte-">byte2Bit(byte)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>
<div class="block">Byte转Bit</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html#byte2HexString-byte-">byte2HexString(byte)</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html" title="class in com.rscja.team.qcom.deviceapi">CardWithBYL_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#byte2HexString-byte-">byte2HexString(byte)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>
<div class="block">byte转十六进制字符</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#byteArrayTolong-byte:A-">byteArrayTolong(byte[])</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>
<div class="block">将8字节的byte数组转成一个long值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#bytes2HexString-byte:A-int-">bytes2HexString(byte[], int)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>
<div class="block">byte类型数组转十六进制字符串</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#bytes2HexString-byte:A-int-int-">bytes2HexString(byte[], int, int)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#bytes2HexString-byte:A-">bytes2HexString(byte[])</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#bytes2HexString2-byte:A-int-">bytes2HexString2(byte[], int)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#bytes2HexString2-java.util.ArrayList-">bytes2HexString2(ArrayList&lt;Byte&gt;)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#bytesConvertHexString-byte:A-int-int-">bytesConvertHexString(byte[], int, int)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#bytesHexString-java.util.ArrayList-">bytesHexString(ArrayList&lt;Byte&gt;)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#bytesHexString-byte:A-int-int-boolean-">bytesHexString(byte[], int, int, boolean)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#bytesHexString-byte:A-">bytesHexString(byte[])</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#bytesHexString-byte:A-int-">bytesHexString(byte[], int)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>
<div class="block">byte类型数组转十六进制字符串</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#bytesTochars-byte:A-int-">bytesTochars(byte[], int)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html#bytesToHexString-byte:A-int-">bytesToHexString(byte[], int)</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html" title="class in com.rscja.team.qcom.deviceapi">CardWithBYL_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#bytesToHexString-byte:A-int-">bytesToHexString(byte[], int)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#bytesToInt-byte:A-">bytesToInt(byte[])</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>
<div class="block">byte数组转成int值</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-1.html">Prev Letter</a></li>
<li><a href="index-3.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-2.html" target="_top">Frames</a></li>
<li><a href="index-2.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
