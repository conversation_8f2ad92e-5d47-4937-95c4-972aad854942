<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>G-Index</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="G-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">Prev Letter</a></li>
<li><a href="index-8.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">Frames</a></li>
<li><a href="index-7.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;<a name="I:G">
<!--   -->
</a>
<h2 class="title">G</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#G">G</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#GBTagLockSendData-java.lang.String-char-int-int-java.lang.String-char-char-char-">GBTagLockSendData(String, char, int, int, String, char, char, char)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#GBTagLockSendData-java.lang.String-char-int-int-java.lang.String-char-char-char-">GBTagLockSendData(String, char, int, int, String, char, char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Gen2Entity</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#Gen2Entity--">Gen2Entity()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#genChar-com.rscja.deviceapi.Fingerprint.BufferEnum-">genChar(Fingerprint.BufferEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">生成特征值（存于指定缓存区）<br>
 Generate feature value( save in specified buffer zone)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#genChar-com.rscja.deviceapi.Fingerprint.BufferEnum-">genChar(Fingerprint.BufferEnum)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">生成特征值（存于指定缓存区）<br>
 Generate feature value( save in specified buffer zone)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#genChar-com.rscja.deviceapi.Fingerprint.BufferEnum-">genChar(Fingerprint.BufferEnum)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">生成特征值（存于指定缓存区）<br>
 Generate feature value( save in specified buffer zone)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#generate-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">generate(FingerprintWithZAZ.BufferEnum, int[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>
<div class="block">生成模版</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#generate-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">generate(FingerprintWithZAZ.BufferEnum, int[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>
<div class="block">生成模版</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#generate-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">generate(FingerprintWithZAZ.BufferEnum, int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>
<div class="block">生成模版</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.html#generateImg-byte:A-java.lang.String-">generateImg(byte[], String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></dt>
<dd>
<div class="block">生成bmp图片<br>
 Generate bmp<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#generateImg-byte:A-java.lang.String-">generateImg(byte[], String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#generateImg-byte:A-java.lang.String-">generateImg(byte[], String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a></dt>
<dd>
<div class="block">生成bmp图片<br>
 Generate bmp<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#generateImg-byte:A-java.lang.String-">generateImg(byte[], String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#generateImg-byte:A-java.lang.String-">generateImg(byte[], String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a></dt>
<dd>
<div class="block">生成bmp图片<br>
 Generate bmp<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#generateImg-byte:A-java.lang.String-">generateImg(byte[], String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#generateImg-byte:A-java.lang.String-">generateImg(byte[], String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a></dt>
<dd>
<div class="block">生成bmp图片<br>
 Generate bmp<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#generateImg-byte:A-java.lang.String-">generateImg(byte[], String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">获取锁定码<br>
 Get lock code</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">获取锁定码<br>
 Get lock code</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取锁标签的锁定码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取锁定码(Get lock code)<br>
 示例代码:比如锁定EPC区和USER区  (Sample code: such as locking EPC area and USER area)
                List<Integer> lockBank=new ArrayList<>();
                lockBank.add(IUHF.LockBank_EPC);
                lockBank.add(IUHF.LockBank_USER);
                int lockMode =IUHF.LockMode_LOCK;
                String hexLockCode=generateLockCode(lockBank,lockMode);</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取锁定码(Get lock code)<br>
 示例代码:比如锁定EPC区和USER区  (Sample code: such as locking EPC area and USER area)
                List<Integer> lockBank=new ArrayList<>();
                lockBank.add(IUHF.LockBank_EPC);
                lockBank.add(IUHF.LockBank_USER);
                int lockMode =IUHF.LockMode_LOCK;
                String hexLockCode=generateLockCode(lockBank,lockMode);</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取锁定码(Get lock code)<br>
 示例代码:比如锁定EPC区和USER区  (Sample code: such as locking EPC area and USER area)
                List<Integer> lockBank=new ArrayList<>();
                lockBank.add(IUHF.LockBank_EPC);
                lockBank.add(IUHF.LockBank_USER);
                int lockMode =IUHF.LockMode_LOCK;
                String hexLockCode=generateLockCode(lockBank,lockMode);</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取锁定码(Get lock code)<br>
 示例代码:比如锁定EPC区和USER区  (Sample code: such as locking EPC area and USER area)
                List<Integer> lockBank=new ArrayList<>();
                lockBank.add(IUHF.LockBank_EPC);
                lockBank.add(IUHF.LockBank_USER);
                int lockMode =IUHF.LockMode_LOCK;
                String hexLockCode=generateLockCode(lockBank,lockMode);</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取锁定码<br>
 Get lock code</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UHFProtocolParseBase.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UHFProtocolParseBase.html" title="class in com.rscja.deviceapi">UHFProtocolParseBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.UHFProtocolParseBase.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.UHFProtocolParseBase.html" title="class in com.rscja.team.qcom.deviceapi">Barcode1D_qcom.UHFProtocolParseBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取锁定码<br>
 Get lock code</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#generateLockCode-java.util.ArrayList-int-">generateLockCode(ArrayList&lt;Integer&gt;, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#genericFunction-char-char:A-char-">genericFunction(char, char[], char)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO15693.html#genericFunction-char-char:A-char-">genericFunction(char, char[], char)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi">RFIDWithISO15693</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html#genericFunction-char-char:A-char-">genericFunction(char, char[], char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#genericFunction-char-char:A-char-">genericFunction(char, char[], char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#genericFunction_ex-char-char-char:A-int-">genericFunction_ex(char, char, char[], int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO15693.html#genericFunction_ex-char-char-char:A-int-">genericFunction_ex(char, char, char[], int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi">RFIDWithISO15693</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html#genericFunction_ex-char-char-char:A-int-">genericFunction_ex(char, char, char[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#genericFunction_ex-char-char-char:A-int-">genericFunction_ex(char, char, char[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#GetAccState--">GetAccState()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#GetAccState--">GetAccState()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.BleDeviceInfo.html#getAddress--">getAddress()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.BleDeviceInfo.html" title="class in com.rscja.deviceapi">BleDevice.BleDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.html#getAfi--">getAfi()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ISO15693Entity.html#getAFI--">getAFI()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity">ISO15693Entity</a></dt>
<dd>
<div class="block">获取AFI值<br>
 acquire AFI value<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeEntity.html#getAimId--">getAimId()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity">BarcodeEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#getAllBleDevice--">getAllBleDevice()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLEManage.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLEManage</a></dt>
<dd>
<div class="block">获取所有的ble设备</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getAllTagTotalFromFlash--">getAllTagTotalFromFlash()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">获取R2、R6 缓存的标签数量<br>
 Acquire tag amounts in buffer of R2 and R6.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getAllTagTotalFromFlash--">getAllTagTotalFromFlash()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUhfReader.html#getAllTagTotalFromFlash--">getAllTagTotalFromFlash()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a></dt>
<dd>
<div class="block">获取R2、R6 缓存的标签数量<br>
 Acquire tag amounts in buffer of R2 and R6.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getAllTagTotalFromFlash--">getAllTagTotalFromFlash()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getAllTagTotalFromFlash--">getAllTagTotalFromFlash()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getAllTagTotalFromFlash--">getAllTagTotalFromFlash()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getAllTagTotalFromFlash--">getAllTagTotalFromFlash()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getAllTagTotalFromFlash--">getAllTagTotalFromFlash()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getAllTagTotalFromFlash--">getAllTagTotalFromFlash()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html#getAmbientTemperature--">getAmbientTemperature()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html" title="class in com.rscja.custom">UHFTemperatureSensors.TemperatureTag</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getAndroidDeviceHardwareVersion--">getAndroidDeviceHardwareVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getAndroidDeviceHardwareVersion--">getAndroidDeviceHardwareVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取android设备的硬件版本</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getAndroidDeviceHardwareVersion--">getAndroidDeviceHardwareVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取android设备的硬件版本</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getAndroidDeviceHardwareVersion--">getAndroidDeviceHardwareVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取android设备的硬件版本</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getAndroidDeviceHardwareVersion--">getAndroidDeviceHardwareVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取android设备的硬件版本</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getAndroidDeviceHardwareVersionSendData--">getAndroidDeviceHardwareVersionSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getAndroidDeviceRebootSendData--">getAndroidDeviceRebootSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/RadarLocationEntity.html#getAngle--">getAngle()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/RadarLocationEntity.html" title="class in com.rscja.deviceapi.entity">RadarLocationEntity</a></dt>
<dd>
<div class="block">获取标签的方位角 (Get the azimuth of the label)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html#getAngleValue-int-">getAngleValue(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFRadarLocationCallback</a></dt>
<dd>
<div class="block">获取设备方位角 (Obtain the device azimuth)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AnimalEntity.html#getAnimalFlag--">getAnimalFlag()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity">AnimalEntity</a></dt>
<dd>
<div class="block">获取动物标签的AnimalFlag<br>
 acquire AnimalFlag of animal tag<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getAnt--">getAnt()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html" title="class in com.rscja.custom">RFIDWithUHFJieCe.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html#getAnt--">getAnt()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html" title="class in com.rscja.custom">UHFSFForUrxNetwork.AntInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html#getAnt--">getAnt()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html#getAnt--">getAnt()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFUartTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AntennaPowerEntity.html#getAnt--">getAnt()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity">AntennaPowerEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getAnt--">getAnt()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getANT--">getANT()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a></dt>
<dd>
<div class="block">获取当前设置的天线</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURx.html#getANT--">getANT()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></dt>
<dd>
<div class="block">获取天线</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#getANT--">getANT()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">获取当前设置的天线<br>
 Acquire current antenna</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getANT--">getANT()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取天线启用状态(Get the antenna enable status)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getANT--">getANT()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取天线启用状态(Get the antenna enable status)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8.html#getANT--">getANT()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a></dt>
<dd>
<div class="block">获取当前设置的天线<br>
 Acquire current antenna</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getANT--">getANT()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取天线启用状态(Get the antenna enable status)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getANT--">getANT()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取天线启用状态(Get the antenna enable status)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFAxBase.html#getANT--">getANT()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFAxBase.html" title="class in com.rscja.deviceapi">RFIDWithUHFAxBase</a></dt>
<dd>
<div class="block">获取当前设置的天线</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getANT--">getANT()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getANT--">getANT()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取天线</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getANT--">getANT()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#getANT--">getANT()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">获取当前设置的天线<br>
 Acquire current antenna</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html#getANT--">getANT()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8_qcom</a></dt>
<dd>
<div class="block">获取当前设置的天线<br>
 Acquire current antenna</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#getANT--">getANT()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFAxBase_qcom</a></dt>
<dd>
<div class="block">获取当前设置的天线</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getANT--">getANT()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getANT--">getANT()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getANT--">getANT()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取天线</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getANT--">getANT()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURx.html#getAntennaConnectState--">getAntennaConnectState()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFAxBase.html#getAntennaConnectState--">getAntennaConnectState()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFAxBase.html" title="class in com.rscja.deviceapi">RFIDWithUHFAxBase</a></dt>
<dd>
<div class="block">获取天线连接状态</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getAntennaConnectState--">getAntennaConnectState()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getAntennaConnectState--">getAntennaConnectState()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getAntennaConnectState--">getAntennaConnectState()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#getAntennaConnectState--">getAntennaConnectState()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getAntennaConnectState--">getAntennaConnectState()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getAntennaConnectState--">getAntennaConnectState()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getAntennaConnectState--">getAntennaConnectState()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getAntennaConnectState--">getAntennaConnectState()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getAntennaConnectStateSendData--">getAntennaConnectStateSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html#getAntennaConnectStateSendData--">getAntennaConnectStateSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AntennaConnectState.html#getAntennaName--">getAntennaName()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AntennaConnectState.html" title="class in com.rscja.deviceapi.entity">AntennaConnectState</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AntennaState.html#getAntennaName--">getAntennaName()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity">AntennaState</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a></dt>
<dd>
<div class="block">获取单个天线的功率</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getAntennaPower--">getAntennaPower()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getAntennaPower--">getAntennaPower()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取天线功率</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getAntennaPower--">getAntennaPower()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取天线功率</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getAntennaPower--">getAntennaPower()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取天线功率</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getAntennaPower--">getAntennaPower()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取天线功率</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFAxBase.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFAxBase.html" title="class in com.rscja.deviceapi">RFIDWithUHFAxBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFAxBase.html#getAntennaPower--">getAntennaPower()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFAxBase.html" title="class in com.rscja.deviceapi">RFIDWithUHFAxBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getAntennaPower--">getAntennaPower()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getAntennaPower--">getAntennaPower()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getAntennaPower--">getAntennaPower()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#getAntennaPower--">getAntennaPower()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getAntennaPower--">getAntennaPower()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getAntennaPower--">getAntennaPower()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getAntennaPower--">getAntennaPower()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower(AntennaEnum)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getAntennaPower--">getAntennaPower()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getAntSendData--">getAntSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html#getAntSendData--">getAntSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getAntSendData--">getAntSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getAntWorkTimeSendData-byte-">getAntWorkTimeSendData(byte)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html#getAntWorkTimeSendData-byte-">getAntWorkTimeSendData(byte)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getAntWorkTimeSendData-byte-">getAntWorkTimeSendData(byte)</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/system/ISystemInterfaces.html#getAppWhiteList--">getAppWhiteList()</a></span> - Method in interface com.rscja.system.<a href="../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></dt>
<dd>
<div class="block">获取App白名单列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getAxGPIOInputStatusSendData--">getAxGPIOInputStatusSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/CardWithBYL.html#getBalance--">getBalance()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/CardWithBYL.html" title="class in com.rscja.deviceapi">CardWithBYL</a></dt>
<dd>
<div class="block">获取余额和有效期</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/ICardWithBYL.html#getBalance--">getBalance()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/ICardWithBYL.html" title="interface in com.rscja.deviceapi.interfaces">ICardWithBYL</a></dt>
<dd>
<div class="block">获取余额和有效期</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html#getBalance--">getBalance()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html" title="class in com.rscja.team.qcom.deviceapi">CardWithBYL_qcom</a></dt>
<dd>
<div class="block">获取余额和有效期</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeEntity.html#getBarcodeBytesData--">getBarcodeBytesData()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity">BarcodeEntity</a></dt>
<dd>
<div class="block">条码原始数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#getBarcodeBytesData--">getBarcodeBytesData()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#getBarcodeCodeID--">getBarcodeCodeID()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getBarcodeContinuousScanIntervalTime--">getBarcodeContinuousScanIntervalTime()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity</a></dt>
<dd>
<div class="block">连续扫描间隔时间<br>
continuous scanning intervals</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getBarcodeContinuousScanTimeOut--">getBarcodeContinuousScanTimeOut()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity</a></dt>
<dd>
<div class="block">连续扫描超时时间<br>
 continuous scanning time-out</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeEntity.html#getBarcodeData--">getBarcodeData()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity">BarcodeEntity</a></dt>
<dd>
<div class="block">条码数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBarcodeDataCodeIdContainType--">getBarcodeDataCodeIdContainType()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeFactory.html#getBarcodeDecoder--">getBarcodeDecoder()</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeFactory.html" title="class in com.rscja.barcode">BarcodeFactory</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/barcode2d/Barcode2DFactory_mtk.html#getBarcodeDecoder--">getBarcodeDecoder()</a></span> - Method in class com.rscja.team.mtk.barcode.barcode2d.<a href="../com/rscja/team/mtk/barcode/barcode2d/Barcode2DFactory_mtk.html" title="class in com.rscja.team.mtk.barcode.barcode2d">Barcode2DFactory_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeFactory_mtk.html#getBarcodeDecoder--">getBarcodeDecoder()</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeFactory_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeFactory_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/barcode2d/Barcode2DFactory_qcom.html#getBarcodeDecoder--">getBarcodeDecoder()</a></span> - Method in class com.rscja.team.qcom.barcode.barcode2d.<a href="../com/rscja/team/qcom/barcode/barcode2d/Barcode2DFactory_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d">Barcode2DFactory_qcom</a></dt>
<dd>
<div class="block">获取条码解码器</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeFactory_qcom.html#getBarcodeDecoder--">getBarcodeDecoder()</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeFactory_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeFactory_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getBarcodeFormat--">getBarcodeFormat()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity</a></dt>
<dd>
<div class="block">条码编码格式<br>
 barcode decoding format</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeSymbolUtility.html#getBarcodeName-int-">getBarcodeName(int)</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeSymbolUtility.html" title="class in com.rscja.barcode">BarcodeSymbolUtility</a></dt>
<dd>
<div class="block">获取条码名称(get barcode name)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeEntity.html#getBarcodeName--">getBarcodeName()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity">BarcodeEntity</a></dt>
<dd>
<div class="block">条码类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html#getBarcodeName-int-">getBarcodeName(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeSymbolUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#getBarcodeName-int-">getBarcodeName(int)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeSymbolUtility_mtk</a></dt>
<dd>
<div class="block">获取条码名称(get barcode name)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeSymbolUtility_qcom.html#getBarcodeName-int-">getBarcodeName(int)</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeSymbolUtility_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeSymbolUtility_qcom</a></dt>
<dd>
<div class="block">获取条码名称(get barcode name)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#getBarcodeSSIID--">getBarcodeSSIID()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeEntity.html#getBarcodeSymbology--">getBarcodeSymbology()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity">BarcodeEntity</a></dt>
<dd>
<div class="block">symbologyCode 条码类型code</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#getBarcodeTypeByCodeId-java.lang.String-">getBarcodeTypeByCodeId(String)</a></span> - Static method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#getBarcodeTypeBySSIID-int-">getBarcodeTypeBySSIID(int)</a></span> - Static method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BluetoothReader.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IReader.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getBattery--">getBattery()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">获取电池电量<br>
 Acquire battery capacity</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BluetoothReader.html#getBattery--">getBattery()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getBattery--">getBattery()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IReader.html#getBattery--">getBattery()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></dt>
<dd>
<div class="block">获取电池电量<br>
 Acquire battery capacity</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getBattery--">getBattery()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getBattery--">getBattery()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getBattery--">getBattery()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBattery--">getBattery()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getBattery--">getBattery()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/BatteryUtils.html#getBatteryHealth--">getBatteryHealth()</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility">BatteryUtils</a></dt>
<dd>
<div class="block">电池健康状况，单位 %.<br/>
 Battery health status, unit %.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getBatterySendData--">getBatterySendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取电量的发送数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getBatterySendData--">getBatterySendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/BatteryUtils.html#getBatteryUniqueId--">getBatteryUniqueId()</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility">BatteryUtils</a></dt>
<dd>
<div class="block">电池ID.<br/>
 Battery ID.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#getBaudrate--">getBaudrate()</a></span> - Method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">获取波特率<br>
 Acquire baud rate<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#getBaudrate--">getBaudrate()</a></span> - Method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">获取波特率<br>
 Acquire baud rate<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BluetoothReader.html#getBeep--">getBeep()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBeep--">getBeep()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getBeep--">getBeep()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBeep--">getBeep()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getBeepSendData-boolean-">getBeepSendData(boolean)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取设置蜂鸣器的发送数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getBeepSendData-boolean-">getBeepSendData(boolean)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a></dt>
<dd>
<div class="block">获取设置蜂鸣器的发送数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getBeepSendData-boolean-">getBeepSendData(boolean)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html#getBeepSendData-boolean-">getBeepSendData(boolean)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getBeepSendData-boolean-">getBeepSendData(boolean)</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/system/SystemPropValues_mtk.html#getBlackWhiteListState--">getBlackWhiteListState()</a></span> - Method in class com.rscja.team.mtk.system.<a href="../com/rscja/team/mtk/system/SystemPropValues_mtk.html" title="class in com.rscja.team.mtk.system">SystemPropValues_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/system/SystemPropValues_qcom.html#getBlackWhiteListState--">getBlackWhiteListState()</a></span> - Method in class com.rscja.team.qcom.system.<a href="../com/rscja/team/qcom/system/SystemPropValues_qcom.html" title="class in com.rscja.team.qcom.system">SystemPropValues_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#getBleDeviceByMac-java.lang.String-">getBleDeviceByMac(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLEManage.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLEManage</a></dt>
<dd>
<div class="block">通过蓝牙地址获取蓝牙设备</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getBleDeviceInfo--">getBleDeviceInfo()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getBleDeviceInfo--">getBleDeviceInfo()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getBleDeviceInfo--">getBleDeviceInfo()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getBleHardwareVersion--">getBleHardwareVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">获取蓝牙硬件版本</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BluetoothReader.html#getBleHardwareVersion--">getBleHardwareVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getBleHardwareVersion--">getBleHardwareVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBleHardwareVersion--">getBleHardwareVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getBleHardwareVersion--">getBleHardwareVersion()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getBleHardwareVersion--">getBleHardwareVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBleHardwareVersion--">getBleHardwareVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.BleDeviceInfo.html#getBluetoothDevice--">getBluetoothDevice()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.BleDeviceInfo.html" title="class in com.rscja.deviceapi">BleDevice.BleDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBluetoothData.html#getBluetoothDeviceAddress--">getBluetoothDeviceAddress()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBluetoothData.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothData</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBluetoothDeviceAddress--">getBluetoothDeviceAddress()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#getBluetoothDeviceAddress--">getBluetoothDeviceAddress()</a></span> - Method in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getBluetoothVersion--">getBluetoothVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">获取蓝牙版本号(acquire Bluetooth version)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BluetoothReader.html#getBluetoothVersion--">getBluetoothVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getBluetoothVersion--">getBluetoothVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBluetoothVersion--">getBluetoothVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></dt>
<dd>
<div class="block">获取蓝牙版本号(acquire Bluetooth version)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getBluetoothVersion--">getBluetoothVersion()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getBluetoothVersion--">getBluetoothVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBluetoothVersion--">getBluetoothVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#getBrokenId-int-int-int:A-">getBrokenId(int, int, int[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#getBrokenId-int-int-int:A-">getBrokenId(int, int, int[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#getBrokenId-int-int-int:A-">getBrokenId(int, int, int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#getBTConnectStatus--">getBTConnectStatus()</a></span> - Method in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#getBytes-char:A-">getBytes(char[])</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>
<div class="block">char类型数组转byte类型数组</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getCalibrationData--">getCalibrationData()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html" title="class in com.rscja.custom">RFIDWithUHFJieCe.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html#getCalibrationData--">getCalibrationData()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html#getCalibrationData--">getCalibrationData()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFUartTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getCD--">getCD()</a></span> - Method in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">Gets the CD (Carrier Detect) bit from the underlying UART.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html#getCD--">getCD()</a></span> - Method in class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbSerialPortImpl_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/StringUtility.html#getChars-byte:A-">getChars(byte[])</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/StringUtility.html" title="class in com.rscja.utility">StringUtility</a></dt>
<dd>
<div class="block">byte类型数组转char类型数组</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getChipInfo--">getChipInfo()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html#getChipType--">getChipType()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo.ChipInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html#getCmd-int-int-">getCmd(int, int)</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUtils_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html#getCmdList--">getCmdList()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUtils_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html#getCmdList-int-">getCmdList(int)</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUtils_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html#getCmdList-int-int-">getCmdList(int, int)</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUtils_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getCmdList-int-">getCmdList(int)</a></span> - Method in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getCmdList-int-int-">getCmdList(int, int)</a></span> - Method in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#getCmdList--">getCmdList()</a></span> - Method in class com.rscja.team.qcom.uhfparse.<a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#getCmdList-int-">getCmdList(int)</a></span> - Method in class com.rscja.team.qcom.uhfparse.<a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#getCmdList-int-int-">getCmdList(int, int)</a></span> - Method in class com.rscja.team.qcom.uhfparse.<a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/CoAsiaBarcodeSymbol_qcom.html#getCodeId-java.lang.String-">getCodeId(String)</a></span> - Method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/CoAsiaBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">CoAsiaBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/R1HFAndPsamManage.html#getConnectionStatus--">getConnectionStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi">R1HFAndPsamManage</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html#getConnectionStatus--">getConnectionStatus()</a></span> - Method in class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbPL2302</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/UsbBase_qcom.html#getConnectionStatus--">getConnectionStatus()</a></span> - Method in class com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">获取UHF连接状态<br>
 Acquire UHF connection status</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BluetoothReader.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getConnectStatus--">getConnectStatus()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getConnectStatus--">getConnectStatus()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></dt>
<dd>
<div class="block">获取蓝牙连接状态(Acquire Bluetooth connection status)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#getConnectStatus--">getConnectStatus()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">获取UHF连接状态<br>
 Acquire UHF connection status</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取连接状态(Acquire UHF connection status)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取连接状态(Acquire UHF connection status)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取连接状态(Acquire UHF connection status)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取连接状态(Acquire UHF connection status)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取UHF连接状态<br>
 Acquire UHF connection status</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取UHF连接状态<br>
 Acquire UHF connection status</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/socket/SocketManageA4.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.team.qcom.socket.<a href="../com/rscja/team/qcom/socket/SocketManageA4.html" title="class in com.rscja.team.qcom.socket">SocketManageA4</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/socket/SocketManageUR4.html#getConnectStatus--">getConnectStatus()</a></span> - Method in class com.rscja.team.qcom.socket.<a href="../com/rscja/team/qcom/socket/SocketManageUR4.html" title="class in com.rscja.team.qcom.socket">SocketManageUR4</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPIOInfo.html#getControlWork--">getControlWork()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPIOInfo.html" title="class in com.rscja.deviceapi.entity">GPIOInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html#getCount--">getCount()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html" title="class in com.rscja.custom">UHFSFForUrxNetwork.AntInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getCount--">getCount()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AnimalEntity.html#getCountryID--">getCountryID()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity">AnimalEntity</a></dt>
<dd>
<div class="block">获取动物标签的国家代码<br>
 acquire national code of animal tag<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/CWDeviceInfo.html#getCpuType--">getCpuType()</a></span> - Method in class com.rscja.<a href="../com/rscja/CWDeviceInfo.html" title="class in com.rscja">CWDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getCTS--">getCTS()</a></span> - Method in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">Gets the CTS (Clear To Send) bit from the underlying UART.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html#getCTS--">getCTS()</a></span> - Method in class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbSerialPortImpl_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/BatteryUtils.html#getCurrentCapacity--">getCurrentCapacity()</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility">BatteryUtils</a></dt>
<dd>
<div class="block">实际容量，单位mAh.<br/>
 Battery Actual capacity, unit mAh.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/BatteryUtils.html#getCurrentCharge--">getCurrentCharge()</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility">BatteryUtils</a></dt>
<dd>
<div class="block">可用容量，单位mAh.<br/>
 Battery Available capacity, unit mAh.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html#getCurrentNumber--">getCurrentNumber()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getCW--">getCW()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">获取连续波设置<br>
 Acquire CW setup</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getCW--">getCW()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#getCW--">getCW()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">获取连续波设置<br>
 Acquire CW setup</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getCW--">getCW()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取连续波状态(Get CW status)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getCW--">getCW()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取连续波状态(Get CW status)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getCW--">getCW()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取连续波状态(Get CW status)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getCW--">getCW()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取连续波状态(Get CW status)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getCW--">getCW()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getCW--">getCW()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getCW--">getCW()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getCW--">getCW()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取连续波设置<br>
 Acquire CW setup</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getCW--">getCW()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getCW--">getCW()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getCW--">getCW()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getCW--">getCW()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getCW--">getCW()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getCW--">getCW()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getCW--">getCW()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getCW--">getCW()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getCW--">getCW()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getCW--">getCW()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取连续波设置<br>
 Acquire CW setup</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getCW--">getCW()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getCW--">getCW()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getCWSendData--">getCWSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getCWSendData--">getCWSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/BatteryUtils.html#getCycleCount--">getCycleCount()</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility">BatteryUtils</a></dt>
<dd>
<div class="block">循环次数.<br/>
 Cycle count.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/DESFireFile.html#getData--">getData()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity">DESFireFile</a></dt>
<dd>
<div class="block">获取数据，仅对数据文件类型有效<br>
 acquire data, it is only valid for data file type only<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPIOInfo.html#getData--">getData()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPIOInfo.html" title="class in com.rscja.deviceapi.entity">GPIOInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/SimpleRFIDEntity.html#getData--">getData()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a></dt>
<dd>
<div class="block">获取标签数据<br>
 acquire tag data<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/http/NetResult_qcom.html#getData--">getData()</a></span> - Method in class com.rscja.team.qcom.http.<a href="../com/rscja/team/qcom/http/NetResult_qcom.html" title="class in com.rscja.team.qcom.http">NetResult_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/R1HFUSB.html#getData-byte:A-">getData(byte[])</a></span> - Method in class com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/R1HFUSB.html" title="class in com.rscja.team.qcom.usb">R1HFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/UsbBase_qcom.DataCallback.html#getData-byte:A-">getData(byte[])</a></span> - Method in interface com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/UsbBase_qcom.DataCallback.html" title="interface in com.rscja.team.qcom.usb">UsbBase_qcom.DataCallback</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AnimalEntity.html#getDataBlock--">getDataBlock()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity">AnimalEntity</a></dt>
<dd>
<div class="block">获取动物标签的DataBlock<br>
 acquire DataBlock of animal tag<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeDecoder.html#getDecoderSVersionInfo--">getDecoderSVersionInfo()</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></dt>
<dd>
<div class="block">返回扫描头和解码库信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#getDecoderSVersionInfo--">getDecoderSVersionInfo()</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeDecoder_mtk</a></dt>
<dd>
<div class="block">返回扫描头和解码库信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeEntity.html#getDecodeTime--">getDecodeTime()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity">BarcodeEntity</a></dt>
<dd>
<div class="block">解码时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.html#getDesfid--">getDesfid()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ISO15693Entity.html#getDESFID--">getDESFID()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity">ISO15693Entity</a></dt>
<dd>
<div class="block">获取DESFID值<br>
 acquire DESFID value<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURx.html#getDestIP--">getDestIP()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></dt>
<dd>
<div class="block">获取目标IP</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getDestIP--">getDestIP()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getDestIP--">getDestIP()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取目标IP</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getDestIP--">getDestIP()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getDestIP--">getDestIP()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getDestIP--">getDestIP()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getDestIP--">getDestIP()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取目标IP</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getDestIP--">getDestIP()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/CWDeviceInfo.html#getDeviceInfo--">getDeviceInfo()</a></span> - Static method in class com.rscja.<a href="../com/rscja/CWDeviceInfo.html" title="class in com.rscja">CWDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#getDeviceInfo--">getDeviceInfo()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>
<div class="block">获取模块的设备信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#getDeviceInfo--">getDeviceInfo()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>
<div class="block">获取模块的设备信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#getDeviceInfo--">getDeviceInfo()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>
<div class="block">获取模块的设备信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#getDeviceInfoFromFile--">getDeviceInfoFromFile()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#getDeviceInfoFromFile--">getDeviceInfoFromFile()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/UsbBase_qcom.html#getDeviceList-android.content.Context-">getDeviceList(Context)</a></span> - Method in class com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></dt>
<dd>
<div class="block">获取 USB 设备列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#getDeviceName--">getDeviceName()</a></span> - Method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">获取设备名<br>
 Acquire device name<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#getDeviceName--">getDeviceName()</a></span> - Method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">获取设备名<br>
 Acquire device name<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/ScanBTCallback.html#getDevices-android.bluetooth.BluetoothDevice-int-byte:A-">getDevices(BluetoothDevice, int, byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces">ScanBTCallback</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintSM206B.html#getDeviceVersion--">getDeviceVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintSM206B.html" title="class in com.rscja.deviceapi">FingerprintSM206B</a></dt>
<dd>
<div class="block">获取指纹模块固件版本</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html#getDeviceVersion--">getDeviceVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html#getDeviceVersion--">getDeviceVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintSM206B_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html#getDns1--">getDns1()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html#getDns2--">getDns2()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getDSR--">getDSR()</a></span> - Method in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">Gets the DSR (Data Set Ready) bit from the underlying UART.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html#getDSR--">getDSR()</a></span> - Method in class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbSerialPortImpl_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getDTR--">getDTR()</a></span> - Method in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">Gets the DTR (Data Terminal Ready) bit from the underlying UART.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html#getDTR--">getDTR()</a></span> - Method in class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbSerialPortImpl_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#getEmptyId-int-int-">getEmptyId(int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>
<div class="block">获取指定编号范围内可注册的首个编号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#getEmptyId-int-int-">getEmptyId(int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>
<div class="block">获取指定编号范围内可注册的首个编号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#getEmptyId-int-int-">getEmptyId(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>
<div class="block">获取指定编号范围内可注册的首个编号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/DESFireFile.html#getEncryptionType--">getEncryptionType()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity">DESFireFile</a></dt>
<dd>
<div class="block">获取加密方式<br>
 acquire encryption method<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#getEngineExtrasInfo--">getEngineExtrasInfo()</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>
<div class="block">扫描头额外的扩展信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/IBarcode2DSHardwareInfo.html#getEngineExtrasInfo--">getEngineExtrasInfo()</a></span> - Method in interface com.rscja.barcode.<a href="../com/rscja/barcode/IBarcode2DSHardwareInfo.html" title="interface in com.rscja.barcode">IBarcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html#getEngineExtrasInfo--">getEngineExtrasInfo()</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html" title="class in com.rscja.team.mtk.barcode">Barcode2DSHardwareInfo_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/Barcode2DSHardwareInfo_qcom.html#getEngineExtrasInfo--">getEngineExtrasInfo()</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/Barcode2DSHardwareInfo_qcom.html" title="class in com.rscja.team.qcom.barcode">Barcode2DSHardwareInfo_qcom</a></dt>
<dd>
<div class="block">扫描头的额外信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#getEngineName--">getEngineName()</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>
<div class="block">扫描头型号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/IBarcode2DSHardwareInfo.html#getEngineName--">getEngineName()</a></span> - Method in interface com.rscja.barcode.<a href="../com/rscja/barcode/IBarcode2DSHardwareInfo.html" title="interface in com.rscja.barcode">IBarcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html#getEngineName--">getEngineName()</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html" title="class in com.rscja.team.mtk.barcode">Barcode2DSHardwareInfo_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/Barcode2DSHardwareInfo_qcom.html#getEngineName--">getEngineName()</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/Barcode2DSHardwareInfo_qcom.html" title="class in com.rscja.team.qcom.barcode">Barcode2DSHardwareInfo_qcom</a></dt>
<dd>
<div class="block">扫描头型号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#getEnrollCount-int-int-int:A-">getEnrollCount(int, int, int[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#getEnrollCount-int-int-int:A-">getEnrollCount(int, int, int[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#getEnrollCount-int-int-int:A-">getEnrollCount(int, int, int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getEpc--">getEpc()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html" title="class in com.rscja.custom">RFIDWithUHFJieCe.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html#getEpc--">getEpc()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html#getEpc--">getEpc()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFUartTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getEPC--">getEPC()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFXSAPI.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFXSAPI.html" title="class in com.rscja.custom">UHFXSAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">获取当前循环盘点的模式(EPC或者EPC+TID或者EPC+TID+USER)<br>
 Acquire current scan mode(EPC or EPC+TID or EPC+TID+USER)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">获取当前设置的盘点模式 (Get the currently set inventory mode)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取当前设置的盘点模式 (Get the currently set inventory mode)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取当前设置的盘点模式 (Get the currently set inventory mode)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取当前设置的盘点模式 (Get the currently set inventory mode)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取当前设置的盘点模式 (Get the currently set inventory mode)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>
<div class="block">设置R6工作模式<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取当前循环盘点的模式(EPC或者EPC+TID或者EPC+TID+USER)<br>
 Acquire current scan mode(EPC or EPC+TID or EPC+TID+USER)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>
<div class="block">设置R6工作模式<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取当前循环盘点的模式(EPC或者EPC+TID或者EPC+TID+USER)<br>
 Acquire current scan mode(EPC or EPC+TID or EPC+TID+USER)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getEPCAndTIDUserModeEx-int:A-int:A-int:A-int:A-">getEPCAndTIDUserModeEx(int[], int[], int[], int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getEpcBytes--">getEpcBytes()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getEPCTIDModeSendData-char-char-">getEPCTIDModeSendData(char, char)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getEPCTIDModeSendData-char-char-">getEPCTIDModeSendData(char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeEntity.html#getErrCode--">getErrCode()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity">BarcodeEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html#getErrCode--">getErrCode()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#getErrCode--">getErrCode()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURx.html#getErrCode--">getErrCode()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getErrCode--">getErrCode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getErrCode--">getErrCode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getErrCode--">getErrCode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getErrCode--">getErrCode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>
<div class="block">获取<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#init-android.hardware.usb.UsbDevice-android.content.Context-"><code>RFIDWithUHFUrxUsbToUart.init(android.hardware.usb.UsbDevice, android.content.Context)</code></a>失败的错误码<br>
 Acquire <a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#init-android.hardware.usb.UsbDevice-android.content.Context-"><code>RFIDWithUHFUrxUsbToUart.init(android.hardware.usb.UsbDevice, android.content.Context)</code></a> error codes of failure.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getErrCode--">getErrCode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>
<div class="block">获取<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#init-android.hardware.usb.UsbDevice-android.content.Context-"><code>RFIDWithUHFUSB.init(android.hardware.usb.UsbDevice, android.content.Context)</code></a>失败的错误码<br>
 Acquire <a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#init-android.hardware.usb.UsbDevice-android.content.Context-"><code>RFIDWithUHFUSB.init(android.hardware.usb.UsbDevice, android.content.Context)</code></a> error codes of failure.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getErrCode--">getErrCode()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getErrCode--">getErrCode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getErrCode--">getErrCode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getErrCode--">getErrCode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getErrCode--">getErrCode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getErrCode--">getErrCode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>
<div class="block">获取<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#init-android.hardware.usb.UsbDevice-android.content.Context-"><code>RFIDWithUHFUrxUsbToUart_qcom.init(android.hardware.usb.UsbDevice, android.content.Context)</code></a>失败的错误码<br>
 Acquire <a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#init-android.hardware.usb.UsbDevice-android.content.Context-"><code>RFIDWithUHFUrxUsbToUart_qcom.init(android.hardware.usb.UsbDevice, android.content.Context)</code></a> error codes of failure.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getErrCode--">getErrCode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>
<div class="block">获取<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#init-android.hardware.usb.UsbDevice-android.content.Context-"><code>RFIDWithUHFUSB_qcom.init(android.hardware.usb.UsbDevice, android.content.Context)</code></a>失败的错误码<br>
 Acquire <a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#init-android.hardware.usb.UsbDevice-android.content.Context-"><code>RFIDWithUHFUSB_qcom.init(android.hardware.usb.UsbDevice, android.content.Context)</code></a> error codes of failure.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#getErrorMsg-int-">getErrorMsg(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getEthernetIpAssignModeSendData--">getEthernetIpAssignModeSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getEthernetIpConfig--">getEthernetIpConfig()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getEthernetIpConfig--">getEthernetIpConfig()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取以太网配置信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getEthernetIpConfig--">getEthernetIpConfig()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取以太网配置信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getEthernetIpConfig--">getEthernetIpConfig()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取以太网配置信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getEthernetIpConfig--">getEthernetIpConfig()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取以太网配置信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getEthernetIpConfigSendData--">getEthernetIpConfigSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getEthernetIpv6ConfigSendData--">getEthernetIpv6ConfigSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html#getEthernetMac--">getEthernetMac()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html#getEthernetMac--">getEthernetMac()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html#getEthernetMac--">getEthernetMac()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html#getEthernetMac--">getEthernetMac()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getEthernetMac--">getEthernetMac()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getEthernetMac--">getEthernetMac()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getEthernetMac--">getEthernetMac()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getEthernetMac--">getEthernetMac()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUhfBle.html#getEx10SDKFirmware--">getEx10SDKFirmware()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getEx10SDKFirmware--">getEx10SDKFirmware()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getEx10SDKFirmware--">getEx10SDKFirmware()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getEx10SDKFirmware--">getEx10SDKFirmware()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getExtraData-java.lang.String-">getExtraData(String)</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html#getFactory--">getFactory()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo.ChipInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/UhfUtils.html#getFactoryAndChip-java.lang.String-">getFactoryAndChip(String)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/UhfUtils.html" title="class in com.rscja.utility">UhfUtils</a></dt>
<dd>
<div class="block">根据tid匹配厂家名称和芯片型号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getFastID--">getFastID()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getFastID--">getFastID()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUhfReader.html#getFastID--">getFastID()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getFastID--">getFastID()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getFastID--">getFastID()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getFastID--">getFastID()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getFastID--">getFastID()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getFastID--">getFastID()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getFastID--">getFastID()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getFastIDSendData--">getFastIDSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getFastIDSendData--">getFastIDSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURx.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></dt>
<dd>
<div class="block">getFastInventoryMode</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>
<div class="block">getFastInventoryMode</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>
<div class="block">getFastInventoryMode</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">getFastInventoryMode</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>
<div class="block">getFastInventoryMode</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>
<div class="block">getFastInventoryMode</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getFastInventoryMode--">getFastInventoryMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>
<div class="block">getFastInventoryMode</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/DESFireFile.html#getFileNo--">getFileNo()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity">DESFireFile</a></dt>
<dd>
<div class="block">获取文件号<br>
 acquire file number<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/DESFireFile.html#getFileSize--">getFileSize()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity">DESFireFile</a></dt>
<dd>
<div class="block">获取文件大小，仅对数据文件类型有效<br>
 acquire file size, it is valid for data file type only<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/DESFireFile.html#getFileType--">getFileType()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity">DESFireFile</a></dt>
<dd>
<div class="block">获取文件类型<br>
 acquire file type<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getFilterChars--">getFilterChars()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity</a></dt>
<dd>
<div class="block">过滤字符串<br>
 Filter string</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.html#getFingersCount--">getFingersCount()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></dt>
<dd>
<div class="block">获取模块中采集的指纹数量<br>
 Acquire collected fingerprint amounts in module.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#getFingersCount--">getFingersCount()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a></dt>
<dd>
<div class="block">获取模块中采集的指纹数量<br>
 Acquire collected fingerprint amounts in module.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#getFingersCount--">getFingersCount()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a></dt>
<dd>
<div class="block">获取模块中采集的指纹数量<br>
 Acquire collected fingerprint amounts in module.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#getFingersCount--">getFingersCount()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a></dt>
<dd>
<div class="block">获取模块中采集的指纹数量<br>
 Acquire collected fingerprint amounts in module.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintSM206B.html#getFingerTemplate--">getFingerTemplate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintSM206B.html" title="class in com.rscja.deviceapi">FingerprintSM206B</a></dt>
<dd>
<div class="block">获取指纹模板数据，用户录入 1 次指纹，生成 256 字节指纹特征值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html#getFingerTemplate--">getFingerTemplate()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html#getFingerTemplate--">getFingerTemplate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintSM206B_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">读取模块的工作模式<br>
 Read work mode of module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">读取模块的工作模式<br>
 Read work mode of module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取模块的工作频率(Get the working frequency of the module)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取模块的工作频率(Get the working frequency of the module)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取模块的工作频率(Get the working frequency of the module)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取模块的工作频率(Get the working frequency of the module)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi">RFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">读取模块的工作模式<br>
 Read work mode of module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">读取模块的工作模式<br>
 Read work mode of module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getFrequencyMode--">getFrequencyMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getFrequencyModeSendData--">getFrequencyModeSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getFrequencyModeSendData--">getFrequencyModeSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getFrequencyPoint--">getFrequencyPoint()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPIOInfo.html#getFullData--">getFullData()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPIOInfo.html" title="class in com.rscja.deviceapi.entity">GPIOInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html#getGateway--">getGateway()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getGen2--">getGen2()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#getGen2--">getGen2()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">获取GEN2参数值(Get GEN2 parameter value)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取GEN2参数值(Get GEN2 parameter value)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取GEN2参数值(Get GEN2 parameter value)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取GEN2参数值(Get GEN2 parameter value)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取GEN2参数值(Get GEN2 parameter value)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取Gen2参数<br>
 Acquire Gen2 parameter<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#GetGen2--">GetGen2()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#GetGen2--">GetGen2()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getGen2--">getGen2()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getGen2SendData--">getGen2SendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getGen2SendData--">getGen2SendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPIStateEntity.html#getGpiName--">getGpiName()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity">GPIStateEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFUrAxDataHandle.html#getGPIOCmd--">getGPIOCmd()</a></span> - Method in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFUrAxDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">UHFUrAxDataHandle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPIStateEntity.html#getGpiState--">getGpiState()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity">GPIStateEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPOEntity.html#getGpoName--">getGpoName()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPOEntity.html#getGpoState--">getGpoState()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#getHardwareVersion--">getHardwareVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithLF</a></dt>
<dd>
<div class="block">读取硬件版本<br>
 read hardware version<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#getHardwareVersion--">getHardwareVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithLF.html#getHardwareVersion--">getHardwareVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi">RFIDWithLF</a></dt>
<dd>
<div class="block">读取硬件版本<br>
 read hardware version<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getHardwareVersion--">getHardwareVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getHardwareVersion--">getHardwareVersion()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html#getHardwareVersion--">getHardwareVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithLF_qcom</a></dt>
<dd>
<div class="block">读取硬件版本<br>
 read hardware version<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getHardwareVersion--">getHardwareVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#getHardwareVersion--">getHardwareVersion()</a></span> - Method in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/M775Authenticate.AuthenticateInfo.html#getHexChallenge--">getHexChallenge()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/M775Authenticate.AuthenticateInfo.html" title="class in com.rscja.custom">M775Authenticate.AuthenticateInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/M775Authenticate.AuthenticateInfo.html#getHexTagResponse--">getHexTagResponse()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/M775Authenticate.AuthenticateInfo.html" title="class in com.rscja.custom">M775Authenticate.AuthenticateInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/M775Authenticate.AuthenticateInfo.html#getHexTagsShortenedTID--">getHexTagsShortenedTID()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/M775Authenticate.AuthenticateInfo.html" title="class in com.rscja.custom">M775Authenticate.AuthenticateInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443A--">getHF14443A()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi">R1HFAndPsamManage</a></dt>
<dd>
<div class="block">获取14443A操作对象 (Get the 14443A operation object)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443B--">getHF14443B()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi">R1HFAndPsamManage</a></dt>
<dd>
<div class="block">获取14443B操作对象 (Get the 14443B operation object)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF15693--">getHF15693()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi">R1HFAndPsamManage</a></dt>
<dd>
<div class="block">获取15693操作对象 (Get the 15693 operation object)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/R1HFAndPsamManage.html#getHFVersion--">getHFVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi">R1HFAndPsamManage</a></dt>
<dd>
<div class="block">获取高频版本号(Get the HF version)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/HF14443RequestEntity.html#getId--">getId()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/HF14443RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF14443RequestEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/SimpleRFIDEntity.html#getId--">getId()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a></dt>
<dd>
<div class="block">获取标签ID<br>
 acquire tag ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.FingerprintInfo.html#getId--">getId()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.FingerprintInfo.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS.FingerprintInfo</a></dt>
<dd>
<div class="block">获取指纹模块ID(十六进制)<br>
 Acquire fingerprint module ID (hex)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.html#getID--">getID()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></dt>
<dd>
<div class="block">获取指纹ID<br>
 Acquire fingerprint ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#getID--">getID()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a></dt>
<dd>
<div class="block">获取指纹ID<br>
 Acquire fingerprint ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#getID--">getID()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a></dt>
<dd>
<div class="block">获取指纹ID<br>
 Acquire fingerprint ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#getID--">getID()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a></dt>
<dd>
<div class="block">获取指纹ID<br>
 Acquire fingerprint ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Infrared.html#getIDAndPowerWithWattmeter--">getIDAndPowerWithWattmeter()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Infrared.html" title="class in com.rscja.deviceapi">Infrared</a></dt>
<dd>
<div class="block">获取电表ID和电能值,97标准<br>
 Acquire powermeter ID and energy value, 97 standard<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Infrared.html#getIDAndPowerWithWattmeter-int-">getIDAndPowerWithWattmeter(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Infrared.html" title="class in com.rscja.deviceapi">Infrared</a></dt>
<dd>
<div class="block">获取电表ID和电能值<br>
 Acquire powermeter ID and energy value<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IInfrared.html#getIDAndPowerWithWattmeter--">getIDAndPowerWithWattmeter()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces">IInfrared</a></dt>
<dd>
<div class="block">获取电表ID和电能值,97标准<br>
 Acquire powermeter ID and energy value, 97 standard<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IInfrared.html#getIDAndPowerWithWattmeter-int-">getIDAndPowerWithWattmeter(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces">IInfrared</a></dt>
<dd>
<div class="block">获取电表ID和电能值<br>
 Acquire powermeter ID and energy value<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html#getIDAndPowerWithWattmeter--">getIDAndPowerWithWattmeter()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Infrared_mtk</a></dt>
<dd>
<div class="block">获取电表ID和电能值,97标准<br>
 Acquire powermeter ID and energy value, 97 standard<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html#getIDAndPowerWithWattmeter-int-">getIDAndPowerWithWattmeter(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Infrared_mtk</a></dt>
<dd>
<div class="block">获取电表ID和电能值<br>
 Acquire powermeter ID and energy value<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/SimpleRFIDEntity.html#getIdBytes--">getIdBytes()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a></dt>
<dd>
<div class="block">获取标签ID<br>
 acquire tag ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AnimalEntity.html#getIdData--">getIdData()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity">AnimalEntity</a></dt>
<dd>
<div class="block">获取64bit的识别号数据，十六进制表示<br>
 acquire 64bit identification number, hexdecimal<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#getImage--">getImage()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">获取指纹图像（存于模块图像缓存区）<br>
 Acquire fingerprint image( save in buffer zone of module image)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintSM206B.html#getImage--">getImage()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintSM206B.html" title="class in com.rscja.deviceapi">FingerprintSM206B</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#getImage-int:A-">getImage(int[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>
<div class="block">从采集器采集指纹图像并保存于 ImageBuffer 中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#getImage--">getImage()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">获取指纹图像（存于模块图像缓存区）<br>
 Acquire fingerprint image( save in buffer zone of module image)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html#getImage--">getImage()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#getImage-int:A-">getImage(int[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>
<div class="block">从采集器采集指纹图像并保存于 ImageBuffer 中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#getImage--">getImage()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">获取指纹图像（存于模块图像缓存区）<br>
 Acquire fingerprint image( save in buffer zone of module image)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html#getImage--">getImage()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintSM206B_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#getImage-int:A-">getImage(int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>
<div class="block">从采集器采集指纹图像并保存于 ImageBuffer 中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getIndex--">getIndex()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURx.html#getInputStatus--">getInputStatus()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></dt>
<dd>
<div class="block">获取输入口状态</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getInputStatus--">getInputStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getInputStatus--">getInputStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取输入口状态</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getInputStatus--">getInputStatus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getInputStatus--">getInputStatus()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>
<div class="block">功能：获取继电器和 IO 控制输出设置状态
     返回值：null -- 执行失败
     data：2字节
     第一个字节：output1: 0:低电平   1：高电平
     第二个字节:  output2: 0:低电平   1：高电平</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getInputStatus--">getInputStatus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getInputStatus--">getInputStatus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getInputStatus--">getInputStatus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取输入口状态</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getInputStatus--">getInputStatus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/CheckUtils.html#getInsertIndex-java.util.List-com.rscja.deviceapi.entity.UHFTAGInfo-boolean:A-int-">getInsertIndex(List&lt;UHFTAGInfo&gt;, UHFTAGInfo, boolean[], int)</a></span> - Static method in class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/CheckUtils.html" title="class in com.rscja.team.qcom.utility">CheckUtils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/UhfUtils.html#getInsertIndex-java.util.List-com.rscja.deviceapi.entity.UHFTAGInfo-boolean:A-">getInsertIndex(List&lt;UHFTAGInfo&gt;, UHFTAGInfo, boolean[])</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/UhfUtils.html" title="class in com.rscja.utility">UhfUtils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeFactory.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeFactory.html" title="class in com.rscja.barcode">BarcodeFactory</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeSymbolUtility.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeSymbolUtility.html" title="class in com.rscja.barcode">BarcodeSymbolUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a></dt>
<dd>
<div class="block">获取条码操作实例</br>
 Acquire barcode operation Instance.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/M775Authenticate.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.custom.<a href="../com/rscja/custom/M775Authenticate.html" title="class in com.rscja.custom">M775Authenticate</a></dt>
<dd>
<div class="block">实例化M775Authenticate对象(Instantiating M775Authenticate Object)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/RFIDWithUHFJieCe.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.custom.<a href="../com/rscja/custom/RFIDWithUHFJieCe.html" title="class in com.rscja.custom">RFIDWithUHFJieCe</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFCSYX.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.custom.<a href="../com/rscja/custom/UHFCSYX.html" title="class in com.rscja.custom">UHFCSYX</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFCSYXForURx.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.custom.<a href="../com/rscja/custom/UHFCSYXForURx.html" title="class in com.rscja.custom">UHFCSYXForURx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTamperAPI.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTamperAPI.html" title="class in com.rscja.custom">UHFTamperAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureSensors.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureSensors.html" title="class in com.rscja.custom">UHFTemperatureSensors</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTag.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTag.html" title="class in com.rscja.custom">UHFTemperatureTag</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTagsAPI.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTagsAPI.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFUartFoxconn.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.custom.<a href="../com/rscja/custom/UHFUartFoxconn.html" title="class in com.rscja.custom">UHFUartFoxconn</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFUartTemperatureTag.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.custom.<a href="../com/rscja/custom/UHFUartTemperatureTag.html" title="class in com.rscja.custom">UHFUartTemperatureTag</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFXSAPI.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.custom.<a href="../com/rscja/custom/UHFXSAPI.html" title="class in com.rscja.custom">UHFXSAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Barcode1D.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Barcode1D.html" title="class in com.rscja.deviceapi">Barcode1D</a></dt>
<dd>
<div class="block">获取一维条码操作实例<br>
 Acquire 1D barcode operation Instance.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Barcode2D.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Barcode2D.html" title="class in com.rscja.deviceapi">Barcode2D</a></dt>
<dd>
<div class="block">获取二维条码操作实例<br>
 * Acquire 2D barcode operation Instance.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BluetoothReader.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></dt>
<dd>
<div class="block">Get BluetoothReader</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/CardWithBYL.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/CardWithBYL.html" title="class in com.rscja.deviceapi">CardWithBYL</a></dt>
<dd>
<div class="block">获取RFID低频操作实例</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">获取指纹模块操作实例<br>
 Acquire fingerprint module operation Instance.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintSM206B.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintSM206B.html" title="class in com.rscja.deviceapi">FingerprintSM206B</a></dt>
<dd>
<div class="block">获取指纹模块操作实例<br>
 Acquire fingerprint module operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></dt>
<dd>
<div class="block">获取指纹模块操作实例<br>
 Acquire fingerprint module operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.html" title="class in com.rscja.deviceapi">FingerprintWithMorpho</a></dt>
<dd>
<div class="block">获取指纹模块操作实例<br>
 acquire fingerprint module operation eample<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>
<div class="block">获取指纹模块操作实例<br>
 acquire fingerprint module operation eample<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Infrared.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Infrared.html" title="class in com.rscja.deviceapi">Infrared</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/LedLight.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/LedLight.html" title="class in com.rscja.deviceapi">LedLight</a></dt>
<dd>
<div class="block">获取LED灯操作实例<br>
 Acquire LED operation example<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Module.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Module.html" title="class in com.rscja.deviceapi">Module</a></dt>
<dd>
<div class="block">获取操作实例<br>
 Acquire operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></dt>
<dd>
<div class="block">获取打印机模块操作实例<br>
 Acquire printer module operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/PSAM.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/PSAM.html" title="class in com.rscja.deviceapi">PSAM</a></dt>
<dd>
<div class="block">获取PSAM操作实例<br>
 Acquire PSAM operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/R1HFAndPsamManage.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi">R1HFAndPsamManage</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A</a></dt>
<dd>
<div class="block">获取ISO14443A协议操作实例<br>
 Acquire ISO14443A protocol operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A4CPU.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A4CPU.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A4CPU</a></dt>
<dd>
<div class="block">获取ISO14443A CPU卡协议操作实例<br>
 Acquire ISO14443A CPU card protocol operation example<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443B.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443B.html" title="class in com.rscja.deviceapi">RFIDWithISO14443B</a></dt>
<dd>
<div class="block">获取ISO14443B协议操作实例<br>
 Acquire ISO14443B protocol operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO15693.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi">RFIDWithISO15693</a></dt>
<dd>
<div class="block">获取ISO15693协议操作实例<br>
 Acquire ISO15693 protocol operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithLF.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi">RFIDWithLF</a></dt>
<dd>
<div class="block">获取RFID低频操作实例<br>
 Acquire RFID LF operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLEManage.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLEManage</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi">RFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>
<div class="block">获取UHF操作实例<br>
 Acquire UHF operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取UHF操作实例<br>
 Acquire UHF operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/ScanerLedLight.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/ScanerLedLight.html" title="class in com.rscja.deviceapi">ScanerLedLight</a></dt>
<dd>
<div class="block">获取LED灯操作实例<br>
 Acquire LED light operation example<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UsbFingerprint.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UsbFingerprint.html" title="class in com.rscja.deviceapi">UsbFingerprint</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/led/ScanLedManage.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.scanner.led.<a href="../com/rscja/scanner/led/ScanLedManage.html" title="class in com.rscja.scanner.led">ScanLedManage</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/system/SystemInterfacesFactory.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.system.<a href="../com/rscja/system/SystemInterfacesFactory.html" title="class in com.rscja.system">SystemInterfacesFactory</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/barcode2d/Barcode2DFactory_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.barcode.barcode2d.<a href="../com/rscja/team/mtk/barcode/barcode2d/Barcode2DFactory_mtk.html" title="class in com.rscja.team.mtk.barcode.barcode2d">Barcode2DFactory_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeFactory_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeFactory_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeFactory_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeSymbolUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeUtility_mtk</a></dt>
<dd>
<div class="block">获取条码操作实例</br>
 Acquire barcode operation Instance.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/symbol/DlBarcodeSymbol_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.barcode.symbol.<a href="../com/rscja/team/mtk/barcode/symbol/DlBarcodeSymbol_mtk.html" title="class in com.rscja.team.mtk.barcode.symbol">DlBarcodeSymbol_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/custom/M775Authenticate_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.custom.<a href="../com/rscja/team/mtk/custom/M775Authenticate_mtk.html" title="class in com.rscja.team.mtk.custom">M775Authenticate_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.custom.<a href="../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html" title="class in com.rscja.team.mtk.custom">UHFTemperatureTagsAPI_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.custom.<a href="../com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html" title="class in com.rscja.team.mtk.custom">UHFUartFoxconn_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Barcode1D_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Barcode1D_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Barcode1D_mtk</a></dt>
<dd>
<div class="block">获取一维条码操作实例<br>
 Acquire 1D barcode operation Instance.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Barcode2D_mtk</a></dt>
<dd>
<div class="block">获取二维条码操作实例<br>
 * Acquire 2D barcode operation Instance.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a></dt>
<dd>
<div class="block">获取指纹模块操作实例<br>
 Acquire fingerprint module operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithMorpho_mtk</a></dt>
<dd>
<div class="block">获取指纹模块操作实例<br>
 acquire fingerprint module operation eample<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Infrared_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/LedLight_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/LedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi">LedLight_mtk</a></dt>
<dd>
<div class="block">获取LED灯操作实例<br>
 Acquire LED operation example<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Module_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Module_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Module_mtk</a></dt>
<dd>
<div class="block">获取操作实例<br>
 Acquire operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Printer_mtk</a></dt>
<dd>
<div class="block">获取打印机模块操作实例<br>
 Acquire printer module operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/PSAM_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/PSAM_mtk.html" title="class in com.rscja.team.mtk.deviceapi">PSAM_mtk</a></dt>
<dd>
<div class="block">获取PSAM操作实例<br>
 Acquire PSAM operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A4CPU_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A4CPU_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443A4CPU_mtk</a></dt>
<dd>
<div class="block">获取ISO14443A CPU卡协议操作实例<br>
 Acquire ISO14443A CPU card protocol operation example<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443A_mtk</a></dt>
<dd>
<div class="block">获取ISO14443A协议操作实例<br>
 Acquire ISO14443A protocol operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443B_mtk</a></dt>
<dd>
<div class="block">获取ISO14443B协议操作实例<br>
 Acquire ISO14443B protocol operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk</a></dt>
<dd>
<div class="block">获取ISO15693协议操作实例<br>
 Acquire ISO15693 protocol operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>
<div class="block">获取UHF操作实例<br>
 Acquire UHF operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi">ScanerLedLight_mtk</a></dt>
<dd>
<div class="block">获取LED灯操作实例<br>
 Acquire LED light operation example<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html" title="class in com.rscja.team.mtk.deviceapi">UsbFingerprint_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/ScanLedManage_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/ScanLedManage_mtk.html" title="class in com.rscja.team.mtk.scanner.led">ScanLedManage_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/system/SystemInterfacesFactory_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.system.<a href="../com/rscja/team/mtk/system/SystemInterfacesFactory_mtk.html" title="class in com.rscja.team.mtk.system">SystemInterfacesFactory_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/system/SystemPropValues_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.system.<a href="../com/rscja/team/mtk/system/SystemPropValues_mtk.html" title="class in com.rscja.team.mtk.system">SystemPropValues_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.system.<a href="../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html" title="class in com.rscja.team.mtk.system">SystemSettingsDBData_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/utility/BatteryManage.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.mtk.utility.<a href="../com/rscja/team/mtk/utility/BatteryManage.html" title="class in com.rscja.team.mtk.utility">BatteryManage</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/barcode2d/Barcode2DFactory_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.barcode.barcode2d.<a href="../com/rscja/team/qcom/barcode/barcode2d/Barcode2DFactory_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d">Barcode2DFactory_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeFactory_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeFactory_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeFactory_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeSymbolUtility_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeSymbolUtility_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeSymbolUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeUtility_qcom</a></dt>
<dd>
<div class="block">获取条码操作实例</br>
 Acquire barcode operation Instance.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/CoAsiaBarcodeSymbol_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/CoAsiaBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">CoAsiaBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/DlBarcodeSymbol_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/DlBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">DlBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/HoneywellBarcodeSymbol_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/HoneywellBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">HoneywellBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/IdataBarcodeSymbol_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/IdataBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">IdataBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/MobyDataBarcodeSymbol_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/MobyDataBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">MobyDataBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/ZebraBarcodeSymbol_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/ZebraBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">ZebraBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/M775Authenticate_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/M775Authenticate_qcom.html" title="class in com.rscja.team.qcom.custom">M775Authenticate_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/RFIDWithUHFJieCe_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/RFIDWithUHFJieCe_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFJieCe_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/RFIDWithUHFShuangYingDianZi_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/RFIDWithUHFShuangYingDianZi_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFShuangYingDianZi_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/RFIDWithUHFUARTUAE_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/RFIDWithUHFUARTUAE_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFUARTUAE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/UHFCSYX_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/UHFCSYX_qcom.html" title="class in com.rscja.team.qcom.custom">UHFCSYX_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/UHFTamperAPI_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/UHFTamperAPI_qcom.html" title="class in com.rscja.team.qcom.custom">UHFTamperAPI_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/UHFTemperatureTagsAPI_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/UHFTemperatureTagsAPI_qcom.html" title="class in com.rscja.team.qcom.custom">UHFTemperatureTagsAPI_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/UHFTemperatureTagsBLEAPI.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/UHFTemperatureTagsBLEAPI.html" title="class in com.rscja.team.qcom.custom">UHFTemperatureTagsBLEAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/UHFUartFoxconn_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/UHFUartFoxconn_qcom.html" title="class in com.rscja.team.qcom.custom">UHFUartFoxconn_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Barcode1D_qcom</a></dt>
<dd>
<div class="block">获取一维条码操作实例<br>
 Acquire 1D barcode operation Instance.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Barcode2D_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Barcode2D_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Barcode2D_qcom</a></dt>
<dd>
<div class="block">获取二维条码操作实例<br>
 * Acquire 2D barcode operation Instance.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>
<div class="block">Get BluetoothReader</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html" title="class in com.rscja.team.qcom.deviceapi">CardWithBYL_qcom</a></dt>
<dd>
<div class="block">获取RFID低频操作实例</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">获取指纹模块操作实例<br>
 Acquire fingerprint module operation Instance.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintSM206B_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a></dt>
<dd>
<div class="block">获取指纹模块操作实例<br>
 Acquire fingerprint module operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithMorpho_qcom</a></dt>
<dd>
<div class="block">获取指纹模块操作实例<br>
 acquire fingerprint module operation eample<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>
<div class="block">获取指纹模块操作实例<br>
 acquire fingerprint module operation eample<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html" title="class in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom</a></dt>
<dd>
<div class="block">获取操作实例<br>
 Acquire operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/LedLight_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/LedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi">LedLight_qcom</a></dt>
<dd>
<div class="block">获取LED灯操作实例<br>
 Acquire LED operation example<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Module_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Module_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Module_qcom</a></dt>
<dd>
<div class="block">获取操作实例<br>
 Acquire operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/PSAM_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/PSAM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">PSAM_qcom</a></dt>
<dd>
<div class="block">获取PSAM操作实例<br>
 Acquire PSAM operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A4CPU_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A4CPU_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443A4CPU_qcom</a></dt>
<dd>
<div class="block">获取ISO14443A CPU卡协议操作实例<br>
 Acquire ISO14443A CPU card protocol operation example<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443A_qcom</a></dt>
<dd>
<div class="block">获取ISO14443A协议操作实例<br>
 Acquire ISO14443A protocol operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443B_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443B_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443B_qcom</a></dt>
<dd>
<div class="block">获取ISO14443B协议操作实例<br>
 Acquire ISO14443B protocol operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a></dt>
<dd>
<div class="block">获取ISO15693协议操作实例<br>
 Acquire ISO15693 protocol operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithLF_qcom</a></dt>
<dd>
<div class="block">获取RFID低频操作实例<br>
 Acquire RFID LF operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>
<div class="block">获取UHF操作实例<br>
 Acquire UHF operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取UHF操作实例<br>
 Acquire UHF operation Instance<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi">ScanerLedLight_qcom</a></dt>
<dd>
<div class="block">获取LED灯操作实例<br>
 Acquire LED light operation example<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseBLE.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseBLE.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseBleByJava_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseBleByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseBleByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFUrxAutoInventoryTagFactory_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFUrxAutoInventoryTagFactory_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFUrxAutoInventoryTagFactory_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UsbFingerprint_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UsbFingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UsbFingerprint_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/ScanLedManage_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/ScanLedManage_qcom.html" title="class in com.rscja.team.qcom.scanner.led">ScanLedManage_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/serialportapi/SerialportAPI.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.serialportapi.<a href="../com/rscja/team/qcom/serialportapi/SerialportAPI.html" title="class in com.rscja.team.qcom.serialportapi">SerialportAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/system/SystemInterfacesFactory_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.system.<a href="../com/rscja/team/qcom/system/SystemInterfacesFactory_qcom.html" title="class in com.rscja.team.qcom.system">SystemInterfacesFactory_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/system/SystemPropValues_qcom.html#getInstance--">getInstance()</a></span> - Static method in class com.rscja.team.qcom.system.<a href="../com/rscja/team/qcom/system/SystemPropValues_qcom.html" title="class in com.rscja.team.qcom.system">SystemPropValues_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#getIntegerSomeBit-int-int-">getIntegerSomeBit(int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></dt>
<dd>
<div class="block">取整数的某一位<br>
 get one place of integer<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.html#getIntegerSomeBit-int-int-">getIntegerSomeBit(int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A</a></dt>
<dd>
<div class="block">取整数的某一位<br>
 get one place of integer<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html#getIntegerSomeBit-int-int-">getIntegerSomeBit(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443A_mtk</a></dt>
<dd>
<div class="block">取整数的某一位<br>
 get one place of integer<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#getIntegerSomeBit-int-int-">getIntegerSomeBit(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443A_qcom</a></dt>
<dd>
<div class="block">取整数的某一位<br>
 get one place of integer<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getInterceptTrimLeft--">getInterceptTrimLeft()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity</a></dt>
<dd>
<div class="block">截取左边字符串<br>
 Capture string on left</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getInterceptTrimRight--">getInterceptTrimRight()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity</a></dt>
<dd>
<div class="block">截取右边字符串<br>
 capture string on right</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/TagInfoRule.html#getInventoryModeEntity--">getInventoryModeEntity()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a></dt>
<dd>
<div class="block">获取盘点模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/TagInfoRule.html#getInventoryParameter--">getInventoryParameter()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a></dt>
<dd>
<div class="block">获取盘点参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getInventorySingleTagSendData--">getInventorySingleTagSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取开启单次盘点标签的发送数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getInventorySingleTagSendData-byte:A-">getInventorySingleTagSendData(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getInventorySingleTagSendData--">getInventorySingleTagSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getIOControlSendData--">getIOControlSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html#getIOControlSendData--">getIOControlSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html#getIp--">getIp()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#getIP--">getIP()</a></span> - Method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">获取端口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html#getIpv6Dns1--">getIpv6Dns1()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html#getIpv6Dns2--">getIpv6Dns2()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html#getIpv6Ip1--">getIpv6Ip1()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html#getIpv6Ip2--">getIpv6Ip2()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html#getIpv6Ip3--">getIpv6Ip3()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html#getKeyboardHelperParam-android.content.Context-">getKeyboardHelperParam(Context)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getKeyMode--">getKeyMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getKillSendData-java.lang.String-int-int-int-java.lang.String-">getKillSendData(String, int, int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取销毁标签的发送数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getKillSendData-java.lang.String-int-int-int-java.lang.String-">getKillSendData(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getLastBarcodeCmd-int-int-int-">getLastBarcodeCmd(int, int, int)</a></span> - Method in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getLastCmd-int-int-int-">getLastCmd(int, int, int)</a></span> - Method in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getLastCmd-int-int-">getLastCmd(int, int)</a></span> - Method in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeDecoder.html#getLastDecImage-com.rscja.barcode.BarcodeDecoder.IBarcodeImageCallback-">getLastDecImage(BarcodeDecoder.IBarcodeImageCallback)</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#getLastDecImage-android.content.Context-">getLastDecImage(Context)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#getLastDecImage-android.content.Context-">getLastDecImage(Context)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#getLastDecImage-android.content.Context-">getLastDecImage(Context)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html#getLastDecImage-com.rscja.barcode.BarcodeDecoder.IBarcodeImageCallback-">getLastDecImage(BarcodeDecoder.IBarcodeImageCallback)</a></span> - Method in class com.rscja.team.qcom.barcode.barcode2d.<a href="../com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d">KeyboardEmulator2DDecoder_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#getLastDecImage-android.content.Context-">getLastDecImage(Context)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#GetLastError--">GetLastError()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#GetLastError--">GetLastError()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getLBTMode--">getLBTMode()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getLBTMode--">getLBTMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getLinkFrequency--">getLinkFrequency()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>
<div class="block">Link Frequency 设置(Link Frequency setting)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFLocationCallback.html#getLocationValue-int-boolean-">getLocationValue(int, boolean)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFLocationCallback</a></dt>
<dd>
<div class="block">获取定位数据 (Obtain location data)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html#getLocationValue-java.util.List-">getLocationValue(List&lt;RadarLocationEntity&gt;)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFRadarLocationCallback</a></dt>
<dd>
<div class="block">获取定位数据 (Obtain location data)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getLockSendData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">getLockSendData(String, int, int, int, String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取锁标签需要发送的数据 <br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getLockSendData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">getLockSendData(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getMainboardVersion--">getMainboardVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">获取主板吧版本</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getMainboardVersion--">getMainboardVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getMainboardVersion--">getMainboardVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#getManufactor--">getManufactor()</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>
<div class="block">扫描头厂家</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/IBarcode2DSHardwareInfo.html#getManufactor--">getManufactor()</a></span> - Method in interface com.rscja.barcode.<a href="../com/rscja/barcode/IBarcode2DSHardwareInfo.html" title="interface in com.rscja.barcode">IBarcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html#getManufactor--">getManufactor()</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html" title="class in com.rscja.team.mtk.barcode">Barcode2DSHardwareInfo_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/Barcode2DSHardwareInfo_qcom.html#getManufactor--">getManufactor()</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/Barcode2DSHardwareInfo_qcom.html" title="class in com.rscja.team.qcom.barcode">Barcode2DSHardwareInfo_qcom</a></dt>
<dd>
<div class="block">扫描头厂家名称</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/BatteryUtils.html#getManufactureDate--">getManufactureDate()</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility">BatteryUtils</a></dt>
<dd>
<div class="block">生产日期.<br/>
 Battery production date.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getMaxQ--">getMaxQ()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/DESFireFile.html#getMaxValue--">getMaxValue()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity">DESFireFile</a></dt>
<dd>
<div class="block">获取最大值，仅对值文件类型有效<br>
 acquire maximum value, it is valid for value file type only<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/CardWithBYL.html#getMessage-int-">getMessage(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/CardWithBYL.html" title="class in com.rscja.deviceapi">CardWithBYL</a></dt>
<dd>
<div class="block">将错误代码转换为语义消息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/ICardWithBYL.html#getMessage-int-">getMessage(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/ICardWithBYL.html" title="interface in com.rscja.deviceapi.interfaces">ICardWithBYL</a></dt>
<dd>
<div class="block">将错误代码转换为语义消息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html#getMessage-int-">getMessage(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html" title="class in com.rscja.team.qcom.deviceapi">CardWithBYL_qcom</a></dt>
<dd>
<div class="block">将错误代码转换为语义消息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFCSYX.TagAuthenticationResponseInfo.html#getMessageHex--">getMessageHex()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFCSYX.TagAuthenticationResponseInfo.html" title="class in com.rscja.custom">UHFCSYX.TagAuthenticationResponseInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/http/NetResult_qcom.html#getMessages--">getMessages()</a></span> - Method in class com.rscja.team.qcom.http.<a href="../com/rscja/team/qcom/http/NetResult_qcom.html" title="class in com.rscja.team.qcom.http">NetResult_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getMinQ--">getMinQ()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/DESFireFile.html#getMinValue--">getMinValue()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity">DESFireFile</a></dt>
<dd>
<div class="block">获取最小值，仅对值文件类型有效<br>
 acquire minimum value, it is valid for value file type only<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html#getMode--">getMode()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></dt>
<dd>
<div class="block">获取盘点模式(Get inventory mode)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/CWDeviceInfo.html#getModel--">getModel()</a></span> - Method in class com.rscja.<a href="../com/rscja/CWDeviceInfo.html" title="class in com.rscja">CWDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#getModel--">getModel()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">获取设备型号<br>
 Acquire device model NO.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#getModel--">getModel()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">获取设备型号<br>
 Acquire device model NO.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/CWDeviceInfo.html#getModelAndCpu--">getModelAndCpu()</a></span> - Method in class com.rscja.<a href="../com/rscja/CWDeviceInfo.html" title="class in com.rscja">CWDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.html#getMorphoDescriptor--">getMorphoDescriptor()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.html" title="class in com.rscja.deviceapi">FingerprintWithMorpho</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#getMorphoDescriptor--">getMorphoDescriptor()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#getMorphoDescriptor--">getMorphoDescriptor()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithMorpho_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html#getMorphoDescriptor--">getMorphoDescriptor()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithMorpho_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.html#getMorphoPIDSN--">getMorphoPIDSN()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.html" title="class in com.rscja.deviceapi">FingerprintWithMorpho</a></dt>
<dd>
<div class="block">获取指纹版本<br>
 acquire fingerprint version<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#getMorphoPIDSN--">getMorphoPIDSN()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></dt>
<dd>
<div class="block">获取指纹版本<br>
 acquire fingerprint version<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#getMorphoPIDSN--">getMorphoPIDSN()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithMorpho_mtk</a></dt>
<dd>
<div class="block">获取指纹版本<br>
 acquire fingerprint version<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html#getMorphoPIDSN--">getMorphoPIDSN()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithMorpho_qcom</a></dt>
<dd>
<div class="block">获取指纹版本<br>
 acquire fingerprint version<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.html#getMorphoSecurityLevel--">getMorphoSecurityLevel()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.html" title="class in com.rscja.deviceapi">FingerprintWithMorpho</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#getMorphoSecurityLevel--">getMorphoSecurityLevel()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#getMorphoSecurityLevel--">getMorphoSecurityLevel()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithMorpho_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html#getMorphoSecurityLevel--">getMorphoSecurityLevel()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithMorpho_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.BleDeviceInfo.html#getName--">getName()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.BleDeviceInfo.html" title="class in com.rscja.deviceapi">BleDevice.BleDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AnimalEntity.html#getNationalID--">getNationalID()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity">AnimalEntity</a></dt>
<dd>
<div class="block">获取动物标签的NationalID信息<br>
 acquire national infor of animal tag<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/NetUtils.html#getNetworkClass-android.app.Activity-">getNetworkClass(Activity)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/NetUtils.html" title="class in com.rscja.utility">NetUtils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html#getNetworkType--">getNetworkType()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/NetUtils.html#getNetworkType-android.app.Activity-">getNetworkType(Activity)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/NetUtils.html" title="class in com.rscja.utility">NetUtils</a></dt>
<dd>
<div class="block">获取当前网络类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/symbol/DlBarcodeSymbol_mtk.html#getNewSymbolId-int-">getNewSymbolId(int)</a></span> - Method in class com.rscja.team.mtk.barcode.symbol.<a href="../com/rscja/team/mtk/barcode/symbol/DlBarcodeSymbol_mtk.html" title="class in com.rscja.team.mtk.barcode.symbol">DlBarcodeSymbol_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/symbol/IBarcodeSymbol_mtk.html#getNewSymbolId-int-">getNewSymbolId(int)</a></span> - Method in interface com.rscja.team.mtk.barcode.symbol.<a href="../com/rscja/team/mtk/barcode/symbol/IBarcodeSymbol_mtk.html" title="interface in com.rscja.team.mtk.barcode.symbol">IBarcodeSymbol_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/CoAsiaBarcodeSymbol_qcom.html#getNewSymbolId-int-">getNewSymbolId(int)</a></span> - Method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/CoAsiaBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">CoAsiaBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/DlBarcodeSymbol_qcom.html#getNewSymbolId-int-">getNewSymbolId(int)</a></span> - Method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/DlBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">DlBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/HoneywellBarcodeSymbol_qcom.html#getNewSymbolId-int-">getNewSymbolId(int)</a></span> - Method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/HoneywellBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">HoneywellBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/IBarcodeSymbol_qcom.html#getNewSymbolId-int-">getNewSymbolId(int)</a></span> - Method in interface com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/IBarcodeSymbol_qcom.html" title="interface in com.rscja.team.qcom.barcode.symbol">IBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/IdataBarcodeSymbol_qcom.html#getNewSymbolId-int-">getNewSymbolId(int)</a></span> - Method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/IdataBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">IdataBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/MobyDataBarcodeSymbol_qcom.html#getNewSymbolId-int-">getNewSymbolId(int)</a></span> - Method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/MobyDataBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">MobyDataBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/ZebraBarcodeSymbol_qcom.html#getNewSymbolId-int-">getNewSymbolId(int)</a></span> - Method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/ZebraBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">ZebraBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getNewTagTotalFromFlash--">getNewTagTotalFromFlash()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getNewTagTotalFromFlash--">getNewTagTotalFromFlash()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUhfReader.html#getNewTagTotalFromFlash--">getNewTagTotalFromFlash()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getNewTagTotalFromFlash--">getNewTagTotalFromFlash()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getNewTagTotalFromFlash--">getNewTagTotalFromFlash()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getNewTagTotalFromFlash--">getNewTagTotalFromFlash()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getNewTagTotalFromFlash--">getNewTagTotalFromFlash()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getNewTagTotalFromFlash--">getNewTagTotalFromFlash()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getNewTagTotalFromFlash--">getNewTagTotalFromFlash()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ISO15693Entity.html#getOriginalUID--">getOriginalUID()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity">ISO15693Entity</a></dt>
<dd>
<div class="block">获取标签原始的UID<br>
 acquire original UID of tag<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getOutputMode--">getOutputMode()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity</a></dt>
<dd>
<div class="block">输出模式<br>
 Output mode</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#getParam-int-">getParam(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>
<div class="block">根据指定的参数类型，读取设备参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#getParam-int-">getParam(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>
<div class="block">根据指定的参数类型，读取设备参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#getParam-int-">getParam(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>
<div class="block">根据指定的参数类型，读取设备参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#getParam_zebra-android.content.Context-int-">getParam_zebra(Context, int)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#getParam_zebra-android.content.Context-int-">getParam_zebra(Context, int)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#getParam_zebra-android.content.Context-int-">getParam_zebra(Context, int)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#getParam_zebra-android.content.Context-int-">getParam_zebra(Context, int)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeDecoder.html#getParameter-int-">getParameter(int)</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BluetoothReader.html#getParameter-byte:A-">getParameter(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IReader.html#getParameter-byte:A-">getParameter(byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getParameter-byte:A-">getParameter(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html#getParameter-int-">getParameter(int)</a></span> - Method in class com.rscja.team.qcom.barcode.barcode2d.<a href="../com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d">KeyboardEmulator2DDecoder_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getParameter-byte:A-">getParameter(byte[])</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getParameter-byte:A-">getParameter(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getParameter-byte:A-">getParameter(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getParameterSendData-byte:A-">getParameterSendData(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/BatteryUtils.html#getPartNumber--">getPartNumber()</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility">BatteryUtils</a></dt>
<dd>
<div class="block">部件号.<br/>
 Part number.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/WifiConfig.html#getPassword--">getPassword()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getPc--">getPc()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getPhase--">getPhase()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/CWDeviceInfo.html#getPlatform--">getPlatform()</a></span> - Method in class com.rscja.<a href="../com/rscja/CWDeviceInfo.html" title="class in com.rscja">CWDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html#getPlatform--">getPlatform()</a></span> - Static method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html" title="class in com.rscja.team.mtk.barcode">Barcode2DSHardwareInfo_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#getPlatform--">getPlatform()</a></span> - Static method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#getPlatform--">getPlatform()</a></span> - Static method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/NumberTool.html#getPointDouble-int-double-">getPointDouble(int, double)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/NumberTool.html" title="class in com.rscja.utility">NumberTool</a></dt>
<dd>
<div class="block">保留指定位数小数的double值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/NumberTool.html#getPointDouble-int-int-">getPointDouble(int, int)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/NumberTool.html" title="class in com.rscja.utility">NumberTool</a></dt>
<dd>
<div class="block">保留指定位数小数的double值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/NumberTool.html#getPointDouble-int-long-">getPointDouble(int, long)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/NumberTool.html" title="class in com.rscja.utility">NumberTool</a></dt>
<dd>
<div class="block">保留指定位数小数的double值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/NumberTool.html#getPointDouble-int-java.lang.String-">getPointDouble(int, String)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/NumberTool.html" title="class in com.rscja.utility">NumberTool</a></dt>
<dd>
<div class="block">保留指定位数小数的double值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/NumberTool.html#getPointFloat-int-float-">getPointFloat(int, float)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/NumberTool.html" title="class in com.rscja.utility">NumberTool</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html#getPort--">getPort()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#getPort--">getPort()</a></span> - Method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">获取端口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getPortNumber--">getPortNumber()</a></span> - Method in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">Port number within driver.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html#getPortNumber--">getPortNumber()</a></span> - Method in class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbSerialPortImpl_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getPower--">getPower()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">读取模块的功率<br>
 Read module output power</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AntennaPowerEntity.html#getPower--">getPower()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity">AntennaPowerEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getPower--">getPower()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/ISingleAntenna.html#getPower--">getPower()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a></dt>
<dd>
<div class="block">读取模块的功率<br>
 Read module output power</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getPower--">getPower()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getPower--">getPower()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getPower--">getPower()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getPower--">getPower()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getPower--">getPower()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getPower--">getPower()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getPower--">getPower()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getPower--">getPower()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getPower--">getPower()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getPower--">getPower()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getPowerSendData--">getPowerSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取功率的发送数据<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getPowerSendData--">getPowerSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeEntity.html#getPrefix--">getPrefix()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity">BarcodeEntity</a></dt>
<dd>
<div class="block">前缀</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getPrefix--">getPrefix()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity</a></dt>
<dd>
<div class="block">Prefix</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html#getPressure--">getPressure()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html" title="class in com.rscja.custom">UHFTemperatureSensors.TemperatureTag</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IPrinter.html#getPrintCodePage--">getPrintCodePage()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.html#getPrintCodePage--">getPrintCodePage()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html#getPrintCodePage--">getPrintCodePage()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Printer_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IPrinter.html#getPrinterType--">getPrinterType()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.html#getPrinterType--">getPrinterType()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html#getPrinterType--">getPrinterType()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Printer_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/http/IDownLoadProgress_qcom.html#getProgress-int-int-int-">getProgress(int, int, int)</a></span> - Method in interface com.rscja.team.qcom.http.<a href="../com/rscja/team/qcom/http/IDownLoadProgress_qcom.html" title="interface in com.rscja.team.qcom.http">IDownLoadProgress_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/http/IUploadProgress_qcom.html#getProgress-int-int-int-">getProgress(int, int, int)</a></span> - Method in interface com.rscja.team.qcom.http.<a href="../com/rscja/team/qcom/http/IUploadProgress_qcom.html" title="interface in com.rscja.team.qcom.http">IUploadProgress_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">获取协议<br>
 Acquire protocol</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getProtocol--">getProtocol()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#getProtocol--">getProtocol()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">获取协议<br>
 Acquire protocol</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取协议(Get the protocol)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取协议(Get the protocol)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取协议(Get the protocol)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取协议(Get the protocol)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取协议<br>
 Acquire protocol</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取协议<br>
 Acquire protocol</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getProtocol--">getProtocol()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getProtocolSendData--">getProtocolSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取协议需要发送数据<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getProtocolSendData--">getProtocolSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/WifiConfig.html#getProxy--">getProxy()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/R1HFAndPsamManage.html#getPSAM--">getPSAM()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi">R1HFAndPsamManage</a></dt>
<dd>
<div class="block">获取PSAM操作对象 (Get the PSAM operation object)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/CardWithBYL.html#getPsamCardID--">getPsamCardID()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/CardWithBYL.html" title="class in com.rscja.deviceapi">CardWithBYL</a></dt>
<dd>
<div class="block">获取psam卡id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/ICardWithBYL.html#getPsamCardID--">getPsamCardID()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/ICardWithBYL.html" title="interface in com.rscja.deviceapi.interfaces">ICardWithBYL</a></dt>
<dd>
<div class="block">获取psam卡id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html#getPsamCardID--">getPsamCardID()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html" title="class in com.rscja.team.qcom.deviceapi">CardWithBYL_qcom</a></dt>
<dd>
<div class="block">获取psam卡id</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#getPsamId--">getPsamId()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#getPsamId--">getPsamId()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.html#getPTInfo--">getPTInfo()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></dt>
<dd>
<div class="block">获取模块信息<br>
 Acquire module information<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#getPTInfo--">getPTInfo()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a></dt>
<dd>
<div class="block">获取模块信息<br>
 Acquire module information<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#getPTInfo--">getPTInfo()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a></dt>
<dd>
<div class="block">获取模块信息<br>
 Acquire module information<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#getPTInfo--">getPTInfo()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a></dt>
<dd>
<div class="block">获取模块信息<br>
 Acquire module information<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getQ--">getQ()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>
<div class="block">获取Q值(Q value)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#GetQTPara--">GetQTPara()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#GetQTPara--">GetQTPara()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getQueryDR--">getQueryDR()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>
<div class="block">获取query命令的DR参数(DR parameter of the query command)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getQueryM--">getQueryM()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>
<div class="block">获取query命令的M参数(M parameter of the query command )</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getQuerySel--">getQuerySel()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>
<div class="block">获取query命令的sel参数(sel parameter of the query command)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getQuerySession--">getQuerySession()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>
<div class="block">获取query命令的session参数(session parameter of the query command)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getQueryTarget--">getQueryTarget()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>
<div class="block">获取query命令的Target参数(Target parameter of the query command)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getQueryTRext--">getQueryTRext()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>
<div class="block">获取query命令的TRext参数(TRext parameter of the query command )</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#getRandomData--">getRandomData()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">获取随机数，检测模块是否正常<br>
 Acquire random number, detect module is normal or not.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#getRandomData--">getRandomData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">获取随机数，检测模块是否正常<br>
 Acquire random number, detect module is normal or not.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#getRandomData--">getRandomData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">获取随机数，检测模块是否正常<br>
 Acquire random number, detect module is normal or not.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/BatteryUtils.html#getRatedCapacity--">getRatedCapacity()</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility">BatteryUtils</a></dt>
<dd>
<div class="block">额定容量，单位mAh.<br/>
 Battery Rated capacity, unit mAh.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AnimalEntity.html#getRawData--">getRawData()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity">AnimalEntity</a></dt>
<dd>
<div class="block">获取原始数据，十六进制表示<br>
 acquire original data, hexdecimal<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUhfReader.html#getReaderAwaitSleepTime--">getReaderAwaitSleepTime()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a></dt>
<dd>
<div class="block">获取读写器等待休眠的时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getReaderAwaitSleepTime--">getReaderAwaitSleepTime()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getReaderAwaitSleepTime--">getReaderAwaitSleepTime()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getReaderAwaitSleepTime--">getReaderAwaitSleepTime()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getReaderAwaitSleepTime--">getReaderAwaitSleepTime()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getReaderAwaitSleepTime--">getReaderAwaitSleepTime()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReaderAwaitSleepTimeSendData--">getReaderAwaitSleepTimeSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getReaderAwaitSleepTimeSendData--">getReaderAwaitSleepTimeSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getReaderBeepStatusSendData--">getReaderBeepStatusSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html#getReaderBeepStatusSendData--">getReaderBeepStatusSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getReaderBeepStatusSendData--">getReaderBeepStatusSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getReaderCurrentIp--">getReaderCurrentIp()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getReaderCurrentIp--">getReaderCurrentIp()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取设备当前的IP地址</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getReaderCurrentIp--">getReaderCurrentIp()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取设备当前的IP地址</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getReaderCurrentIp--">getReaderCurrentIp()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取设备当前的IP地址</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getReaderCurrentIp--">getReaderCurrentIp()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取设备当前的IP地址</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getReaderDestIpSendData--">getReaderDestIpSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html#getReaderDestIpSendData--">getReaderDestIpSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getReaderIpSendData--">getReaderIpSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html#getReaderIpSendData--">getReaderIpSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/DESFireFile.html#getReadPermissions--">getReadPermissions()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity">DESFireFile</a></dt>
<dd>
<div class="block">获取读取权限<br>
 acquire read authorization<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReadSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-">getReadSendData(String, int, int, int, String, int, int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取读标签的发送数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getReadSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-">getReadSendData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReadTagSendData--">getReadTagSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取在循环盘点标签的模式中,读取缓存标签的发送数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseBleByJava_qcom.html#getReadTagSendData-int-">getReadTagSendData(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseBleByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseBleByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getReadTagSendData--">getReadTagSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUSBByJava_qcom.html#getReadTagSendData--">getReadTagSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUSBByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUSBByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/DESFireFile.html#getReadWritePermissions--">getReadWritePermissions()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity">DESFireFile</a></dt>
<dd>
<div class="block">获取读写权限<br>
 acquire read-write authorization<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getRemain--">getRemain()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/AnimalEntity.html#getReserved--">getReserved()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity">AnimalEntity</a></dt>
<dd>
<div class="block">获取动物标签的Reserved信息<br>
 acquire Reserved infor of animal tag<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getReserved--">getReserved()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html#getReservedLength--">getReservedLength()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html#getReservedOffset--">getReservedOffset()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFCSYX.TagAuthenticationResponseInfo.html#getResponseHex--">getResponseHex()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFCSYX.TagAuthenticationResponseInfo.html" title="class in com.rscja.custom">UHFCSYX.TagAuthenticationResponseInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeEntity.html#getResultCode--">getResultCode()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity">BarcodeEntity</a></dt>
<dd>
<div class="block">结果码
   <code>BarcodeDecoder.DECODE_SUCCESS</code><br>
   <code>BarcodeDecoder.DECODE_FAILURE</code><br>
   <code>BarcodeDecoder.DECODE_TIMEOUT</code><br>
   <code>BarcodeDecoder.DECODE_CANCEL</code><br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryParameter.html#getResultData--">getResultData()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryParameter.html" title="class in com.rscja.deviceapi.entity">InventoryParameter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTagsAPI.html#getRFIDWithUHFA4--">getRFIDWithUHFA4()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTagsAPI.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">获取链路参数<br>
 acquire link parameter</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getRFLink--">getRFLink()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#getRFLink--">getRFLink()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">获取链路参数<br>
 acquire link parameter</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取链路参数(Get link parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取链路参数(Get link parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取链路参数(Get link parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取链路参数(Get link parameters)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取链路参数<br>
 acquire link parameter</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取链路参数<br>
 acquire link parameter</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getRFLink--">getRFLink()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getRFLinkSendData--">getRFLinkSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getRFLinkSendData--">getRFLinkSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getRI--">getRI()</a></span> - Method in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">Gets the RI (Ring Indicator) bit from the underlying UART.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html#getRI--">getRI()</a></span> - Method in class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbSerialPortImpl_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getRssi--">getRssi()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html" title="class in com.rscja.custom">RFIDWithUHFJieCe.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html#getRssi--">getRssi()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html#getRssi--">getRssi()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFUartTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getRssi--">getRssi()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getRssiCode--">getRssiCode()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html" title="class in com.rscja.custom">RFIDWithUHFJieCe.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html#getRssiCode--">getRssiCode()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html#getRssiCode--">getRssiCode()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFUartTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getRTS--">getRTS()</a></span> - Method in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">Gets the RTS (Request To Send) bit from the underlying UART.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html#getRTS--">getRTS()</a></span> - Method in class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbSerialPortImpl_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getScanBarcodeInBlinkModeSendData--">getScanBarcodeInBlinkModeSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getScanBarcodeInTriggleModeSendData--">getScanBarcodeInTriggleModeSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getScanBarcodeSendData--">getScanBarcodeSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取扫描条码的发送数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getScanBarcodeSendData--">getScanBarcodeSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/led/ScanLedManage.html#getScanLed--">getScanLed()</a></span> - Method in class com.rscja.scanner.led.<a href="../com/rscja/scanner/led/ScanLedManage.html" title="class in com.rscja.scanner.led">ScanLedManage</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/C6000_6762_ScanLed_mtk.html#getScanLed--">getScanLed()</a></span> - Static method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/C6000_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C6000_6762_ScanLed_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/C7X_6765_ScanLed_mtk.html#getScanLed--">getScanLed()</a></span> - Static method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/C7X_6765_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C7X_6765_ScanLed_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html#getScanLed--">getScanLed()</a></span> - Static method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C90_6762_ScanLed_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/ScanLedManage_mtk.html#getScanLed--">getScanLed()</a></span> - Method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/ScanLedManage_mtk.html" title="class in com.rscja.team.mtk.scanner.led">ScanLedManage_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C60_6765_11_ScanLed_qcom.html#getScanLed--">getScanLed()</a></span> - Static method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C60_6765_11_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C60_6765_11_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C60_qcm2150_10_ScanLed_qcom.html#getScanLed--">getScanLed()</a></span> - Static method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C60_qcm2150_10_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C60_qcm2150_10_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C61_smd450_90_ScanLed_qcom.html#getScanLed--">getScanLed()</a></span> - Static method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C61_smd450_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C61_smd450_90_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C66_smd450_90_ScanLed_qcom.html#getScanLed--">getScanLed()</a></span> - Static method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C66_smd450_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C66_smd450_90_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C66m_sm6115_10_ScanLed_qcom.html#getScanLed--">getScanLed()</a></span> - Static method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C66m_sm6115_10_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C66m_sm6115_10_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/MC50_4350_12_ScanLed_qcom.html#getScanLed--">getScanLed()</a></span> - Static method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/MC50_4350_12_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">MC50_4350_12_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/P80_8786_130_ScanLed_qcom.html#getScanLed--">getScanLed()</a></span> - Static method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/P80_8786_130_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">P80_8786_130_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/P80_8953_90_ScanLed_qcom.html#getScanLed--">getScanLed()</a></span> - Static method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/P80_8953_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">P80_8953_90_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/ScanLedManage_qcom.html#getScanLed--">getScanLed()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/ScanLedManage_qcom.html" title="class in com.rscja.team.qcom.scanner.led">ScanLedManage_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#getScannerInerface--">getScannerInerface()</a></span> - Static method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#getScannerInerface--">getScannerInerface()</a></span> - Static method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#getScannerInerface--">getScannerInerface()</a></span> - Static method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#getScannerParameter-android.content.Context-">getScannerParameter(Context)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#getScannerParameter-android.content.Context-">getScannerParameter(Context)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#getScannerParameter-android.content.Context-">getScannerParameter(Context)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#getScannerParameter-android.content.Context-">getScannerParameter(Context)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#getScannerParameter-android.content.Context-">getScannerParameter(Context)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getScanResultBroadcastAction--">getScanResultBroadcastAction()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity</a></dt>
<dd>
<div class="block">Broadcast Action</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getScanResultBroadcastKey--">getScanResultBroadcastKey()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity</a></dt>
<dd>
<div class="block">Broadcast Key</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/WifiConfig.html#getSecurityType--">getSecurityType()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getSelectAction--">getSelectAction()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>
<div class="block">获取select 命令的Action参数 (Action parameter of the select command)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getSelectTarget--">getSelectTarget()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>
<div class="block">获取select命令的Target参数 (Target parameter of the select command)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getSelectTruncate--">getSelectTruncate()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>
<div class="block">获取select命令的Truncate参数(Truncate parameter of the select command)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getSensorCode--">getSensorCode()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html" title="class in com.rscja.custom">RFIDWithUHFJieCe.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html#getSensorCode--">getSensorCode()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html#getSensorCode--">getSensorCode()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFUartTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getSerial--">getSerial()</a></span> - Method in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">The serial number of the underlying UsbDeviceConnection, or <code>null</code>.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html#getSerial--">getSerial()</a></span> - Method in class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbSerialPortImpl_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/BatteryUtils.html#getSerialNumber--">getSerialNumber()</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility">BatteryUtils</a></dt>
<dd>
<div class="block">序列号.<br/>
 Serial number.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getSetSoftResetSendData--">getSetSoftResetSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getSetSoftResetSendData--">getSetSoftResetSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html#getSetSoftResetSendData--">getSetSoftResetSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFCSYX.TagAuthenticationResponseInfo.html#getShortenedTidHex--">getShortenedTidHex()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFCSYX.TagAuthenticationResponseInfo.html" title="class in com.rscja.custom">UHFCSYX.TagAuthenticationResponseInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/TagLocationInfo.html#getSignalValue--">getSignalValue()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/TagLocationInfo.html" title="class in com.rscja.deviceapi.entity">TagLocationInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/BatteryUtils.html#getSmartBattery--">getSmartBattery()</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility">BatteryUtils</a></dt>
<dd>
<div class="block">智能电池.<br/>
 Smart battery.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/system/ISystemInterfaces.html#getSN--">getSN()</a></span> - Method in interface com.rscja.system.<a href="../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></dt>
<dd>
<div class="block">获取SN号</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/system/SysInterfacesOfC7XA11_mtk.html#getSN--">getSN()</a></span> - Method in class com.rscja.team.mtk.system.<a href="../com/rscja/team/mtk/system/SysInterfacesOfC7XA11_mtk.html" title="class in com.rscja.team.mtk.system">SysInterfacesOfC7XA11_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/system/SystemPropValues_mtk.html#getSN--">getSN()</a></span> - Method in class com.rscja.team.mtk.system.<a href="../com/rscja/team/mtk/system/SystemPropValues_mtk.html" title="class in com.rscja.team.mtk.system">SystemPropValues_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#getSN-byte:A-">getSN(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/system/SystemPropValues_qcom.html#getSN--">getSN()</a></span> - Method in class com.rscja.team.qcom.system.<a href="../com/rscja/team/qcom/system/SystemPropValues_qcom.html" title="class in com.rscja.team.qcom.system">SystemPropValues_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html#getSsid--">getSsid()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/WifiConfig.html#getSsid--">getSsid()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getStartInventoryTagSendData--">getStartInventoryTagSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取循环盘点标签的发送数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStartInventoryTagSendData-byte:A-">getStartInventoryTagSendData(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStartInventoryTagSendData--">getStartInventoryTagSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/Gen2Entity.html#getStartQ--">getStartQ()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/WifiConfig.html#getStaticIpConfig--">getStaticIpConfig()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html#getStatus-com.rscja.deviceapi.interfaces.ConnectionStatus-T-">getStatus(ConnectionStatus, T)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#getStatus-com.rscja.deviceapi.interfaces.ConnectionStatus-java.lang.Object-">getStatus(ConnectionStatus, Object)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html" title="class in com.rscja.team.qcom.ble">RFIDWithUHFBLEN51_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html#getStatus-com.rscja.deviceapi.interfaces.ConnectionStatus-java.lang.Object-">getStatus(ConnectionStatus, Object)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html" title="class in com.rscja.team.qcom.ble">RFIDWithUHFBLEN52_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.html#getStatusMsg-int-">getStatusMsg(int)</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.html#getStatusMsg-int-">getStatusMsg(int)</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.html" title="class in com.rscja.deviceapi">FingerprintWithMorpho</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#getStatusMsg-int-">getStatusMsg(int)</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BluetoothReader.html#getSTM32Version--">getSTM32Version()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IReader.html#getSTM32Version--">getSTM32Version()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></dt>
<dd>
<div class="block">获取stm32版本号(Acquire stm32 version )</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURx.html#getSTM32Version--">getSTM32Version()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getSTM32Version--">getSTM32Version()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getSTM32Version--">getSTM32Version()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getSTM32Version--">getSTM32Version()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getSTM32Version--">getSTM32Version()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getSTM32Version--">getSTM32Version()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getSTM32Version--">getSTM32Version()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getSTM32Version--">getSTM32Version()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getSTM32Version--">getSTM32Version()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getSTM32Version--">getSTM32Version()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getSTM32Version--">getSTM32Version()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getSTM32Version--">getSTM32Version()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getSTM32VersionSendData--">getSTM32VersionSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getSTM32VersionSendData--">getSTM32VersionSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getStopInventorySendData--">getStopInventorySendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取停止循环盘点标签的发送数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStopInventorySendData--">getStopInventorySendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStopScanBarcodeInBlinkModeSendData--">getStopScanBarcodeInBlinkModeSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStopScanBarcodeInTriggleModeSendData--">getStopScanBarcodeInTriggleModeSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html#getSubnetMask--">getSubnetMask()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getSuffix--">getSuffix()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity</a></dt>
<dd>
<div class="block">suffix</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/symbol/DlBarcodeSymbol_mtk.html#getSymbolIdByAIM-java.lang.String-">getSymbolIdByAIM(String)</a></span> - Method in class com.rscja.team.mtk.barcode.symbol.<a href="../com/rscja/team/mtk/barcode/symbol/DlBarcodeSymbol_mtk.html" title="class in com.rscja.team.mtk.barcode.symbol">DlBarcodeSymbol_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/symbol/DlBarcodeSymbol_qcom.html#getSymbolIdByAIM-java.lang.String-">getSymbolIdByAIM(String)</a></span> - Method in class com.rscja.team.qcom.barcode.symbol.<a href="../com/rscja/team/qcom/barcode/symbol/DlBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol">DlBarcodeSymbol_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/r1/hf/HF15693.html#GetSystemInfo-int-byte:A-">GetSystemInfo(int, byte[])</a></span> - Method in class com.rscja.team.qcom.r1.hf.<a href="../com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf">HF15693</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/system/SystemInterfacesFactory.html#getSystemInterfaces-android.content.Context-">getSystemInterfaces(Context)</a></span> - Method in class com.rscja.system.<a href="../com/rscja/system/SystemInterfacesFactory.html" title="class in com.rscja.system">SystemInterfacesFactory</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/system/SystemInterfacesFactory_mtk.html#getSystemInterfaces-android.content.Context-">getSystemInterfaces(Context)</a></span> - Method in class com.rscja.team.mtk.system.<a href="../com/rscja/team/mtk/system/SystemInterfacesFactory_mtk.html" title="class in com.rscja.team.mtk.system">SystemInterfacesFactory_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/system/SystemInterfacesFactory_qcom.html#getSystemInterfaces-android.content.Context-">getSystemInterfaces(Context)</a></span> - Method in class com.rscja.team.qcom.system.<a href="../com/rscja/team/qcom/system/SystemInterfacesFactory_qcom.html" title="class in com.rscja.team.qcom.system">SystemInterfacesFactory_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/RadarLocationEntity.html#getTag--">getTag()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/RadarLocationEntity.html" title="class in com.rscja.deviceapi.entity">RadarLocationEntity</a></dt>
<dd>
<div class="block">获取标签值 (Get the value of the label)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBluetoothData.html#getTagCmd--">getTagCmd()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBluetoothData.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothData</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getTagCmd--">getTagCmd()</a></span> - Method in class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#getTagCmd--">getTagCmd()</a></span> - Method in class com.rscja.team.qcom.uhfparse.<a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getTagDataFromFlash--">getTagDataFromFlash()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">获取R2、R6缓存的标签信息<br>
 Acquire tag information in buffer of R2 and R6</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getTagDataFromFlash--">getTagDataFromFlash()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUhfReader.html#getTagDataFromFlash--">getTagDataFromFlash()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a></dt>
<dd>
<div class="block">获取R2、R6缓存的标签信息<br>
 Acquire tag information in buffer of R2 and R6</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getTagDataFromFlash--">getTagDataFromFlash()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getTagDataFromFlash--">getTagDataFromFlash()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getTagDataFromFlash--">getTagDataFromFlash()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getTagDataFromFlash--">getTagDataFromFlash()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getTagDataFromFlash--">getTagDataFromFlash()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getTagDataFromFlash--">getTagDataFromFlash()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getTagfocus--">getTagfocus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getTagfocus--">getTagfocus()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUhfReader.html#getTagfocus--">getTagfocus()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getTagfocus--">getTagfocus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getTagfocus--">getTagfocus()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getTagfocus--">getTagfocus()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getTagfocus--">getTagfocus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getTagfocus--">getTagfocus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getTagfocus--">getTagfocus()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getTagfocusSendData--">getTagfocusSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getTagfocusSendData--">getTagfocusSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/socket/SocketManageUR4.html#getTagInfo--">getTagInfo()</a></span> - Method in class com.rscja.team.qcom.socket.<a href="../com/rscja/team/qcom/socket/SocketManageUR4.html" title="class in com.rscja.team.qcom.socket">SocketManageUR4</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IHandheldRFID.html#getTagLocate-android.content.Context-">getTagLocate(Context)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getTagLocate-android.content.Context-">getTagLocate(Context)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getTagLocate-android.content.Context-">getTagLocate(Context)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getTagLocate-android.content.Context-">getTagLocate(Context)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getTagLocate-android.content.Context-">getTagLocate(Context)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getTagLocate-android.content.Context-">getTagLocate(Context)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getTagLocate-android.content.Context-">getTagLocate(Context)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getTagLocate-android.content.Context-">getTagLocate(Context)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getTagLocate-android.content.Context-">getTagLocate(Context)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/TagLocationEntity.html#getTagLocationInfo--">getTagLocationInfo()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/TagLocationEntity.html" title="class in com.rscja.deviceapi.entity">TagLocationEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTamperAPI.TamperInfo.html#getTamper--">getTamper()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTamperAPI.TamperInfo.html" title="class in com.rscja.custom">UHFTamperAPI.TamperInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getTcpServiceVersion--">getTcpServiceVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getTcpServiceVersion--">getTcpServiceVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取UHF TCP服务版本</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getTcpServiceVersion--">getTcpServiceVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取UHF TCP服务版本</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getTcpServiceVersion--">getTcpServiceVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取UHF TCP服务版本</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getTcpServiceVersion--">getTcpServiceVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取UHF TCP服务版本</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/CWDeviceInfo.html#getTeam--">getTeam()</a></span> - Method in class com.rscja.<a href="../com/rscja/CWDeviceInfo.html" title="class in com.rscja">CWDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTagsAPI.TempertureInfo.html#getTempCount--">getTempCount()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTagsAPI.TempertureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.TempertureInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getTempeCode--">getTempeCode()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html" title="class in com.rscja.custom">RFIDWithUHFJieCe.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html#getTempeCode--">getTempeCode()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html#getTempeCode--">getTempeCode()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFUartTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTagsAPI.TempertureInfo.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTagsAPI.TempertureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.TempertureInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFUartTemperatureTag.TemperatureTagInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">获取模块温度<br>
 Acquire module Temperature</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getTemperature--">getTemperature()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#getTemperature--">getTemperature()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">获取模块温度<br>
 Acquire module Temperature</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取UHF模块温度 (Get UHF module temperature)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取UHF模块温度 (Get UHF module temperature)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取UHF模块温度 (Get UHF module temperature)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取UHF模块温度 (Get UHF module temperature)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取模块温度<br>
 Acquire module Temperature</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#GetTemperature--">GetTemperature()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#GetTemperature--">GetTemperature()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取模块温度<br>
 Acquire module Temperature</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getTemperature--">getTemperature()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html#getTemperatureByContactResistance--">getTemperatureByContactResistance()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html" title="class in com.rscja.custom">UHFTemperatureSensors.TemperatureTag</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getTemperatureSendData--">getTemperatureSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取模块温度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getTemperatureSendData--">getTemperatureSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html#getTemperture--">getTemperture()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getTid--">getTid()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getTidBytes--">getTidBytes()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getTimestamp--">getTimestamp()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html#getTotal--">getTotal()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURx.html#getTriggerWorkModePara--">getTriggerWorkModePara()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></dt>
<dd>
<div class="block">获取触发工作模式参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getTriggerWorkModePara--">getTriggerWorkModePara()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getTriggerWorkModePara--">getTriggerWorkModePara()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取触发工作模式参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getTriggerWorkModePara--">getTriggerWorkModePara()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getTriggerWorkModePara--">getTriggerWorkModePara()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getTriggerWorkModePara--">getTriggerWorkModePara()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getTriggerWorkModePara--">getTriggerWorkModePara()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取触发工作模式参数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getTriggerWorkModePara--">getTriggerWorkModePara()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getTriggerWorkModeParaSendData--">getTriggerWorkModeParaSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html#getTriggerWorkModeParaSendData--">getTriggerWorkModeParaSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getTurnkey--">getTurnkey()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/HF14443RequestEntity.html#getType--">getType()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/HF14443RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF14443RequestEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.html#getType--">getType()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/SimpleRFIDEntity.html#getType--">getType()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a></dt>
<dd>
<div class="block">获取标签类型<br>
 acquire tag type<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html#getUart--">getUart()</a></span> - Method in class com.rscja.team.mtk.<a href="../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></dt>
<dd>
<div class="block">获取串口路径<br>
 Acquire serial port directory<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html#getUart--">getUart()</a></span> - Method in class com.rscja.team.qcom.<a href="../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></dt>
<dd>
<div class="block">获取串口路径<br>
 Acquire serial port directory<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/RadarLocationEntity.html#getUhfBank--">getUhfBank()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/RadarLocationEntity.html" title="class in com.rscja.deviceapi.entity">RadarLocationEntity</a></dt>
<dd>
<div class="block">获取标签的类型 (Get the type of label)
 <code>#IUHF.Bank_RESERVED</code>、<code>#IUHF.Bank_EPC</code>、<code>#IUHF.Bank_TID</code>、<code>#IUHF.Bank_USER</code></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getUHFCurrentIpConfigSendData--">getUHFCurrentIpConfigSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURx.html#getUhfReaderIP--">getUhfReaderIP()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></dt>
<dd>
<div class="block">获取UHF读写器IP</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getUhfReaderIP--">getUhfReaderIP()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getUhfReaderIP--">getUhfReaderIP()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取UHF读写器IP</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getUhfReaderIP--">getUhfReaderIP()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getUhfReaderIP--">getUhfReaderIP()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getUhfReaderIP--">getUhfReaderIP()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getUhfReaderIP--">getUhfReaderIP()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取UHF读写器IP</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getUhfReaderIP--">getUhfReaderIP()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#getUhfSoftwareVersion--">getUhfSoftwareVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">读取UHF模块版本号<br>
 Read UHF module version</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#getUhfSoftwareVersion--">getUhfSoftwareVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getUhfSoftwareVersion--">getUhfSoftwareVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/M775Authenticate.AuthenticateInfo.html#getUhftagInfo--">getUhftagInfo()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/M775Authenticate.AuthenticateInfo.html" title="class in com.rscja.custom">M775Authenticate.AuthenticateInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFCSYX.TagAuthenticationResponseInfo.html#getUhftagInfo--">getUhftagInfo()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFCSYX.TagAuthenticationResponseInfo.html" title="class in com.rscja.custom">UHFCSYX.TagAuthenticationResponseInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html#getUhftagInfo--">getUhftagInfo()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html" title="class in com.rscja.custom">UHFSFForUrxNetwork.AntInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTamperAPI.TamperInfo.html#getUhftagInfo--">getUhftagInfo()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTamperAPI.TamperInfo.html" title="class in com.rscja.custom">UHFTamperAPI.TamperInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html#getUhftagInfo--">getUhftagInfo()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html" title="class in com.rscja.custom">UHFTemperatureSensors.TemperatureTag</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFTemperatureTagsAPI.TempertureInfo.html#getUhftagInfo--">getUhftagInfo()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFTemperatureTagsAPI.TempertureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.TempertureInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/TagLocationEntity.html#getUhftagInfo--">getUhftagInfo()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/TagLocationEntity.html" title="class in com.rscja.deviceapi.entity">TagLocationEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/UrxUsb_qcom.html#getUHFTAGInfo--">getUHFTAGInfo()</a></span> - Method in class com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/UrxUsb_qcom.html" title="class in com.rscja.team.qcom.usb">UrxUsb_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UhfUartManage_qcom.html#getUHFUrAxDataHandle--">getUHFUrAxDataHandle()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UhfUartManage_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UhfUartManage_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UhfUartUR4Manage_qcom.html#getUHFUrAxDataHandle--">getUHFUrAxDataHandle()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UhfUartUR4Manage_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UhfUartUR4Manage_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/socket/SocketManageA4.html#getUHFUrAxDataHandle--">getUHFUrAxDataHandle()</a></span> - Method in class com.rscja.team.qcom.socket.<a href="../com/rscja/team/qcom/socket/SocketManageA4.html" title="class in com.rscja.team.qcom.socket">SocketManageA4</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.UHFProtocolParseBase.html#getUhfvalidData-byte:A-">getUhfvalidData(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.UHFProtocolParseBase.html" title="class in com.rscja.team.qcom.deviceapi">Barcode1D_qcom.UHFProtocolParseBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.UHFProtocolParseBase.html#getUhfvalidData-java.util.AbstractQueue-int-boolean-">getUhfvalidData(AbstractQueue&lt;Byte&gt;, int, boolean)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.UHFProtocolParseBase.html" title="class in com.rscja.team.qcom.deviceapi">Barcode1D_qcom.UHFProtocolParseBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.html#getUid--">getUid()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html#getUID--">getUID()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443B</a></dt>
<dd>
<div class="block">获取卡片ID<br>
 Acquire card ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443B.html#getUID--">getUID()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443B.html" title="class in com.rscja.deviceapi">RFIDWithISO14443B</a></dt>
<dd>
<div class="block">获取卡片ID<br>
 Acquire card ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html#getUID--">getUID()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443B_mtk</a></dt>
<dd>
<div class="block">获取卡片ID<br>
 Acquire card ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443B_qcom.html#getUID--">getUID()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443B_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443B_qcom</a></dt>
<dd>
<div class="block">获取卡片ID<br>
 Acquire card ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html#getUIDBytes--">getUIDBytes()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443B</a></dt>
<dd>
<div class="block">获取卡片ID<br>
 Acquire card ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443B.html#getUIDBytes--">getUIDBytes()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443B.html" title="class in com.rscja.deviceapi">RFIDWithISO14443B</a></dt>
<dd>
<div class="block">获取卡片ID<br>
 Acquire card ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html#getUIDBytes--">getUIDBytes()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443B_mtk</a></dt>
<dd>
<div class="block">获取卡片ID<br>
 Acquire card ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443B_qcom.html#getUIDBytes--">getUIDBytes()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443B_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443B_qcom</a></dt>
<dd>
<div class="block">获取卡片ID<br>
 Acquire card ID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IHF14443B.html#getUidTypeB--">getUidTypeB()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443B</a></dt>
<dd>
<div class="block">获取卡片ID (Acquire card ID)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/r1/hf/HF14443B.html#getUidTypeB--">getUidTypeB()</a></span> - Method in class com.rscja.team.qcom.r1.hf.<a href="../com/rscja/team/qcom/r1/hf/HF14443B.html" title="class in com.rscja.team.qcom.r1.hf">HF14443B</a></dt>
<dd>
<div class="block">获取卡片ID (Acquire card ID)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#getUIDWith4450Card--">getUIDWith4450Card()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithLF</a></dt>
<dd>
<div class="block">获取UID,用于4450Card<br>
 Acquire UID, used for 4450 card<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithLF.html#getUIDWith4450Card--">getUIDWith4450Card()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi">RFIDWithLF</a></dt>
<dd>
<div class="block">获取UID,用于4450Card<br>
 Acquire UID, used for 4450 card<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html#getUIDWith4450Card--">getUIDWith4450Card()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithLF_qcom</a></dt>
<dd>
<div class="block">获取UID,用于4450Card<br>
 Acquire UID, used for 4450 card<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#getUIDWithHID--">getUIDWithHID()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithLF</a></dt>
<dd>
<div class="block">读取HID卡<br>
 Read HID card<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithLF.html#getUIDWithHID--">getUIDWithHID()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi">RFIDWithLF</a></dt>
<dd>
<div class="block">读取HID卡<br>
 Read HID card<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html#getUIDWithHID--">getUIDWithHID()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithLF_qcom</a></dt>
<dd>
<div class="block">读取HID卡<br>
 Read HID card<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#getUIDWithHitagS--">getUIDWithHitagS()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithLF</a></dt>
<dd>
<div class="block">获取UID,用于hitag S<br>
 Acquire UID, used for hitag S<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithLF.html#getUIDWithHitagS--">getUIDWithHitagS()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi">RFIDWithLF</a></dt>
<dd>
<div class="block">获取UID,用于hitag S<br>
 Acquire UID, used for hitag S<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html#getUIDWithHitagS--">getUIDWithHitagS()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithLF_qcom</a></dt>
<dd>
<div class="block">获取UID,用于hitag S<br>
 Acquire UID, used for hitag S<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/DESFireFile.html#getUpdatePermissions--">getUpdatePermissions()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity">DESFireFile</a></dt>
<dd>
<div class="block">获取修改权限<br>
 acquire modify authorization<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getUpgradeTcpServiceVersionSendData--">getUpgradeTcpServiceVersionSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/UsbBase_qcom.html#getUsbDevice-android.content.Context-int-int-">getUsbDevice(Context, int, int)</a></span> - Method in class com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html#getUsbDeviceConnection--">getUsbDeviceConnection()</a></span> - Method in class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbPL2302</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/RxUsb_qcom.html#getUsbDeviceConnection--">getUsbDeviceConnection()</a></span> - Method in class com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/RxUsb_qcom.html" title="class in com.rscja.team.qcom.usb">RxUsb_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/UrxUsb_qcom.html#getUsbDeviceConnection--">getUsbDeviceConnection()</a></span> - Method in class com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/UrxUsb_qcom.html" title="class in com.rscja.team.qcom.usb">UrxUsb_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/UsbBase_qcom.html#getUsbDeviceConnection--">getUsbDeviceConnection()</a></span> - Method in class com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html#getUsbDeviceList-android.content.Context-">getUsbDeviceList(Context)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getUsbDeviceList-android.content.Context-">getUsbDeviceList(Context)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>
<div class="block">获取usb列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getUsbDeviceList-android.content.Context-">getUsbDeviceList(Context)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>
<div class="block">获取usb列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getUser--">getUser()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#getUserBytes--">getUserBytes()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html#getUserLength--">getUserLength()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></dt>
<dd>
<div class="block">获取要盘点的User区长度(User area data length)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html#getUserOffset--">getUserOffset()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></dt>
<dd>
<div class="block">获取要盘点的User区起始地址(User area start address)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getUserSettingSendData-byte-">getUserSettingSendData(byte)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.ModuleType.html#getValue--">getValue()</a></span> - Method in enum com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFXSAPI.Bank.html#getValue--">getValue()</a></span> - Method in enum com.rscja.custom.<a href="../com/rscja/custom/UHFXSAPI.Bank.html" title="enum in com.rscja.custom">UHFXSAPI.Bank</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.TagType.html#getValue--">getValue()</a></span> - Method in enum com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/HF15693RequestEntity.TagType.html" title="enum in com.rscja.deviceapi.entity">HF15693RequestEntity.TagType</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/RadarLocationEntity.html#getValue--">getValue()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/RadarLocationEntity.html" title="class in com.rscja.deviceapi.entity">RadarLocationEntity</a></dt>
<dd>
<div class="block">获取标签的信号值 (Get the signal value of the tag)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/enums/AntennaEnum.html#getValue--">getValue()</a></span> - Method in enum com.rscja.deviceapi.enums.<a href="../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/enums/AntennaEnum.html#getValue-int-">getValue(int)</a></span> - Static method in enum com.rscja.deviceapi.enums.<a href="../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.BufferEnum.html#getValue--">getValue()</a></span> - Method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a></dt>
<dd>
<div class="block">获取枚举的值<br>
 Acquire example value<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html#getValue--">getValue()</a></span> - Method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a></dt>
<dd>
<div class="block">获取枚举的值<br>
 Acquire example value<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html#getValue--">getValue()</a></span> - Method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a></dt>
<dd>
<div class="block">获取枚举的值<br>
 Acquire example value<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/ConnectionStatus.html#getValue--">getValue()</a></span> - Method in enum com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html#getValue--">getValue()</a></span> - Method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html#getValue--">getValue()</a></span> - Method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireFileTypekEnum</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO15693.TagType.html#getValue--">getValue()</a></span> - Method in enum com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO15693.TagType.html" title="enum in com.rscja.deviceapi">RFIDWithISO15693.TagType</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.TagType.html#getValue--">getValue()</a></span> - Method in enum com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.TagType.html" title="enum in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk.TagType</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html#getValue--">getValue()</a></span> - Method in enum com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">获取模块版本<br>
 Acquire module version<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></dt>
<dd>
<div class="block">获取模块的设备信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#getVersion--">getVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">获取模块版本<br>
 Acquire module version<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#getVersion--">getVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IPrinter.html#getVersion--">getVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDBase.html#getVersion--">getVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDBase</a></dt>
<dd>
<div class="block">获取rfid 版本信息<br>
 Acquire RFID version infor<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#getVersion--">getVersion()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">读取UHF模块版本号<br>
 Read UHF module version</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDBase.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDBase.html" title="class in com.rscja.deviceapi">RFIDBase</a></dt>
<dd>
<div class="block">获取rfid 版本信息<br>
 Acquire RFID version infor<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">读取UHF模块版本号(Read UHF module version)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">读取UHF模块版本号(Read UHF module version)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">读取UHF模块版本号(Read UHF module version)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">读取UHF模块版本号(Read UHF module version)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">读取UHF模块版本号<br>
 Read UHF module version</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/VersionInfo.html#getVersion--">getVersion()</a></span> - Static method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/VersionInfo.html" title="class in com.rscja.deviceapi">VersionInfo</a></dt>
<dd>
<div class="block">获取jar包版本信息<br>
 acquire jar pack version infor<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Printer_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDBase_mtk</a></dt>
<dd>
<div class="block">获取rfid 版本信息<br>
 Acquire RFID version infor<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">获取模块版本<br>
 Acquire module version<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDBase_qcom</a></dt>
<dd>
<div class="block">获取rfid 版本信息<br>
 Acquire RFID version infor<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">读取UHF模块版本号<br>
 Read UHF module version</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#getVersion--">getVersion()</a></span> - Method in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.FingerprintInfo.html#getVersions--">getVersions()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.FingerprintInfo.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS.FingerprintInfo</a></dt>
<dd>
<div class="block">获取指纹模块版本号<br>
 Acquire fingerprint module version<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getVersionSendData--">getVersionSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getVersionSendData--">getVersionSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BatteryEntity.html#getVoltage--">getVoltage()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BatteryEntity.html" title="class in com.rscja.deviceapi.entity">BatteryEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getWifiInfo--">getWifiInfo()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getWifiInfo--">getWifiInfo()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取wifi热点信息,json格式数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getWifiInfo--">getWifiInfo()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取wifi热点信息,json格式数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getWifiInfo--">getWifiInfo()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取wifi热点信息,json格式数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getWifiInfo--">getWifiInfo()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取wifi热点信息,json格式数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getWifiInfoSendData--">getWifiInfoSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getWifiIpConfig--">getWifiIpConfig()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getWifiIpConfig--">getWifiIpConfig()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">获取wifi配置信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getWifiIpConfig--">getWifiIpConfig()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">获取wifi配置信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getWifiIpConfig--">getWifiIpConfig()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">获取wifi配置信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getWifiIpConfig--">getWifiIpConfig()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">获取wifi配置信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getWifiIpConfigSendData--">getWifiIpConfigSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getWifiIpv6ConfigSendData--">getWifiIpv6ConfigSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURx.html#getWorkMode--">getWorkMode()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></dt>
<dd>
<div class="block">获取工作模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getWorkMode--">getWorkMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getWorkMode--">getWorkMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">获取工作模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getWorkMode--">getWorkMode()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getWorkMode--">getWorkMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getWorkMode--">getWorkMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getWorkMode--">getWorkMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">获取工作模式</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getWorkMode--">getWorkMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getWorkModeSendData--">getWorkModeSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html#getWorkModeSendData--">getWorkModeSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/DESFireFile.html#getWritePermissions--">getWritePermissions()</a></span> - Method in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity">DESFireFile</a></dt>
<dd>
<div class="block">获取写权限<br>
 acquire write authorization<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getWriteSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">getWriteSendData(String, int, int, int, String, int, int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>
<div class="block">获取写标签的发送数据<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getWriteSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">getWriteSendData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPIStateEntity.html#GPI1">GPI1</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity">GPIStateEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPIStateEntity.html#GPI2">GPI2</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity">GPIStateEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPIStateEntity.html#GPI3">GPI3</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity">GPIStateEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPIStateEntity.html#GPI4">GPI4</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity">GPIStateEntity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/entity/GPIOInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">GPIOInfo</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPIOInfo.html#GPIOInfo--">GPIOInfo()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPIOInfo.html" title="class in com.rscja.deviceapi.entity">GPIOInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">GPIStateEntity</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPIStateEntity.html#GPIStateEntity--">GPIStateEntity()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity">GPIStateEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPIStateEntity.html#GPIStateEntity-java.lang.String-int-">GPIStateEntity(String, int)</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity">GPIStateEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPOEntity.html#GPO1">GPO1</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPOEntity.html#GPO2">GPO2</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPOEntity.html#GPO3">GPO3</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPOEntity.html#GPO4">GPO4</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">GPOEntity</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPOEntity.html#GPOEntity-java.lang.String-int-">GPOEntity(String, int)</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.html#grab--">grab()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></dt>
<dd>
<div class="block">采集图像<br>
 Acquire image<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#grab--">grab()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a></dt>
<dd>
<div class="block">采集图像<br>
 Acquire image<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#grab--">grab()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a></dt>
<dd>
<div class="block">采集图像<br>
 Acquire image<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#grab--">grab()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a></dt>
<dd>
<div class="block">采集图像<br>
 Acquire image<br></div>
</dd>
<dt><a href="../com/rscja/team/qcom/utility/GyroAngle.html" title="class in com.rscja.team.qcom.utility"><span class="typeNameLink">GyroAngle</span></a> - Class in <a href="../com/rscja/team/qcom/utility/package-summary.html">com.rscja.team.qcom.utility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/utility/GyroAngle.html#GyroAngle--">GyroAngle()</a></span> - Constructor for class com.rscja.team.qcom.utility.<a href="../com/rscja/team/qcom/utility/GyroAngle.html" title="class in com.rscja.team.qcom.utility">GyroAngle</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">Prev Letter</a></li>
<li><a href="index-8.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">Frames</a></li>
<li><a href="index-7.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
