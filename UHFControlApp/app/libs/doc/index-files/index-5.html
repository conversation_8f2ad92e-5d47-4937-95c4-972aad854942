<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>E-Index</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="E-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-4.html">Prev Letter</a></li>
<li><a href="index-6.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-5.html" target="_top">Frames</a></li>
<li><a href="index-5.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;<a name="I:E">
<!--   -->
</a>
<h2 class="title">E</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#E">E</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_free-java.lang.String-">EM125k_free(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM125k_free-java.lang.String-">EM125k_free(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125K_GetEm4450UID--">EM125K_GetEm4450UID()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM125K_GetEm4450UID--">EM125K_GetEm4450UID()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_init-java.lang.String-java.lang.String-int-">EM125k_init(String, String, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM125k_init-java.lang.String-java.lang.String-int-">EM125k_init(String, String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_init_Ex-java.lang.String-java.lang.String-int-">EM125k_init_Ex(String, String, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM125k_init_Ex-java.lang.String-java.lang.String-int-">EM125k_init_Ex(String, String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_read-int-">EM125k_read(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM125k_read-int-">EM125k_read(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_Read4305-int-">EM125k_Read4305(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">读EM4305卡 ******************************** 功能描述：读4305卡的某一页（0~31）数据
 输入参数：nPage -- 页地址(0~31) 第0、3、5~13页可读可写，可供用户存储数据使用 第1页为UID存储区，只读 第2页为密码区
 第4页为参数配置区域 第14、15页为保护区 输出参数：pszData-- 4字节数据（每页可存放4字节）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM125k_Read4305-int-">EM125k_Read4305(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">读EM4305卡 ******************************** 功能描述：读4305卡的某一页（0~31）数据
 输入参数：nPage -- 页地址(0~31) 第0、3、5~13页可读可写，可供用户存储数据使用 第1页为UID存储区，只读 第2页为密码区
 第4页为参数配置区域 第14、15页为保护区 输出参数：pszData-- 4字节数据（每页可存放4字节）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_read_Ex--">EM125k_read_Ex()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM125k_read_Ex--">EM125k_read_Ex()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_ReadHitag-int-">EM125k_ReadHitag(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM125k_ReadHitag-int-">EM125k_ReadHitag(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#EM125k_ReadHitag1--">EM125k_ReadHitag1()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithLF</a></dt>
<dd>
<div class="block">获取所有页的数据<br>
 Acquire data of all pages<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithLF.html#EM125k_ReadHitag1--">EM125k_ReadHitag1()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi">RFIDWithLF</a></dt>
<dd>
<div class="block">获取所有页的数据<br>
 Acquire data of all pages<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_ReadHitag1--">EM125k_ReadHitag1()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM125k_ReadHitag1--">EM125k_ReadHitag1()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html#EM125k_ReadHitag1--">EM125k_ReadHitag1()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithLF_qcom</a></dt>
<dd>
<div class="block">获取所有页的数据<br>
 Acquire data of all pages<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_UID_REQ--">EM125k_UID_REQ()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM125k_UID_REQ--">EM125k_UID_REQ()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_Write4305-int-char:A-">EM125k_Write4305(int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">写EM4305卡 ******************************** 功能描述：写4305卡的某一页（0~31）数据
 输入参数：nPage -- 页地址(0~31) 第0、3、5~13页可读可写，可供用户存储数据使用 第1页为UID存储区，只读 第2页为密码区
 第4页为参数配置区域 第14、15页为保护区 pszData-- 4字节数据（每页可存放4字节）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM125k_Write4305-int-char:A-">EM125k_Write4305(int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">写EM4305卡 ******************************** 功能描述：写4305卡的某一页（0~31）数据
 输入参数：nPage -- 页地址(0~31) 第0、3、5~13页可读可写，可供用户存储数据使用 第1页为UID存储区，只读 第2页为密码区
 第4页为参数配置区域 第14、15页为保护区 pszData-- 4字节数据（每页可存放4字节）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_WriteHitagPage-int-char:A-">EM125k_WriteHitagPage(int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM125k_WriteHitagPage-int-char:A-">EM125k_WriteHitagPage(int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM4325SensorData-char-int-int-char:A-">EM4325SensorData(char, int, int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM4325SensorData-char-int-int-char:A-">EM4325SensorData(char, int, int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM_25kread--">EM_25kread()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">读半双工的动物标签 ************************ 输出参数：pszData-- 16字节动物标签数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EM_25kread--">EM_25kread()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">读半双工的动物标签 ************************ 输出参数：pszData-- 16字节动物标签数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMAutoEnroll-int-int-">EMAutoEnroll(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMAutoEnroll-int-int-">EMAutoEnroll(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMAutoMatch-int-int-int-">EMAutoMatch(int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMAutoMatch-int-int-int-">EMAutoMatch(int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMDeletChar-int-int-">EMDeletChar(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMDeletChar-int-int-">EMDeletChar(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMDownChar-int-char:A-">EMDownChar(int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMDownChar-int-char:A-">EMDownChar(int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMEmpty--">EMEmpty()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMEmpty--">EMEmpty()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMFingerFree-java.lang.String-">EMFingerFree(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMFingerFree-java.lang.String-">EMFingerFree(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMFingerInit-java.lang.String-java.lang.String-int-">EMFingerInit(String, String, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMFingerInit-java.lang.String-java.lang.String-int-">EMFingerInit(String, String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMFingerMoudleSet-int-">EMFingerMoudleSet(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMFingerMoudleSet-int-">EMFingerMoudleSet(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMGenChar-int-">EMGenChar(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMGenChar-int-">EMGenChar(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMGetImage--">EMGetImage()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMGetImage--">EMGetImage()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMGetRandomData--">EMGetRandomData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMGetRandomData--">EMGetRandomData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMLoadChar-int-int-">EMLoadChar(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMLoadChar-int-int-">EMLoadChar(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMMatch--">EMMatch()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMMatch--">EMMatch()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#empty--">empty()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">清空模块中保存的指纹数据<br>
 empty out saved fingerprint data in module.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#empty--">empty()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">清空模块中保存的指纹数据<br>
 empty out saved fingerprint data in module.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#empty--">empty()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">清空模块中保存的指纹数据<br>
 empty out saved fingerprint data in module.<br></div>
</dd>
<dt><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble"><span class="typeNameLink">EmptyUhfBle</span></a> - Class in <a href="../com/rscja/team/qcom/ble/package-summary.html">com.rscja.team.qcom.ble</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#EmptyUhfBle--">EmptyUhfBle()</a></span> - Constructor for class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMReadChipSN--">EMReadChipSN()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMReadChipSN--">EMReadChipSN()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMReadSysPara--">EMReadSysPara()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMReadSysPara--">EMReadSysPara()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMReadSysParaMore--">EMReadSysParaMore()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMReadSysParaMore--">EMReadSysParaMore()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMRegModel--">EMRegModel()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMRegModel--">EMRegModel()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMSearch-int-int-int-">EMSearch(int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMSearch-int-int-int-">EMSearch(int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMSetDeviceName-char:A-">EMSetDeviceName(char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMSetDeviceName-char:A-">EMSetDeviceName(char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMSetManuFacture-char:A-">EMSetManuFacture(char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMSetManuFacture-char:A-">EMSetManuFacture(char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMSetPSW-char:A-">EMSetPSW(char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMSetPSW-char:A-">EMSetPSW(char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMSetReg-int-int-">EMSetReg(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMSetReg-int-int-">EMSetReg(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMStorChar-int-int-">EMStorChar(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMStorChar-int-int-">EMStorChar(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMUpChar-int-">EMUpChar(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMUpChar-int-">EMUpChar(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMUpImage-int-java.lang.String-">EMUpImage(int, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMUpImage-int-java.lang.String-">EMUpImage(int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMUpImageISO-int-java.lang.String-">EMUpImageISO(int, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMUpImageISO-int-java.lang.String-">EMUpImageISO(int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMValidTempleteNum--">EMValidTempleteNum()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMValidTempleteNum--">EMValidTempleteNum()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMVfyPSW-char:A-">EMVfyPSW(char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EMVfyPSW-char:A-">EMVfyPSW(char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#enableAuxiliaryLight-android.content.Context-boolean-">enableAuxiliaryLight(Context, boolean)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">开启扫描辅助灯(C7x才有此功能)<br>
 switch on scanning aux.light for C7x series</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#enableAuxiliaryLight-android.content.Context-boolean-">enableAuxiliaryLight(Context, boolean)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#enableAuxiliaryLight-android.content.Context-boolean-">enableAuxiliaryLight(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#enableAuxiliaryLight-android.content.Context-boolean-">enableAuxiliaryLight(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#enableBarcodeACK-boolean-">enableBarcodeACK(boolean)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#enableBarcodeNotRepeat-android.content.Context-boolean-">enableBarcodeNotRepeat(Context, boolean)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">不输出重复标签(前后两次标签不重复)<br>
 donot outout repeated tags</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#enableBarcodeNotRepeat-android.content.Context-boolean-">enableBarcodeNotRepeat(Context, boolean)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#enableBarcodeNotRepeat-android.content.Context-boolean-">enableBarcodeNotRepeat(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#enableBarcodeNotRepeat-android.content.Context-boolean-">enableBarcodeNotRepeat(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#enableBeep--">enableBeep()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURx.html#enableBeep--">enableBeep()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></dt>
<dd>
<div class="block">启用蜂鸣器</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#enableBeep--">enableBeep()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">启用蜂鸣器，打开蜂鸣器后盘点到标签会响应蜂鸣器。(Turn the buzzer on, and the label will respond to the buzzer after turning on the buzzer.)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#enableBeep--">enableBeep()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">启用蜂鸣器，打开蜂鸣器后盘点到标签会响应蜂鸣器。(Turn the buzzer on, and the label will respond to the buzzer after turning on the buzzer.)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#enableBeep--">enableBeep()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">启用蜂鸣器，打开蜂鸣器后盘点到标签会响应蜂鸣器。(Turn the buzzer on, and the label will respond to the buzzer after turning on the buzzer.)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#enableBeep--">enableBeep()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">启用蜂鸣器，打开蜂鸣器后盘点到标签会响应蜂鸣器。(Turn the buzzer on, and the label will respond to the buzzer after turning on the buzzer.)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#enableBeep--">enableBeep()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#enableBeep--">enableBeep()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">启用蜂鸣器</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#enableBeep--">enableBeep()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#enableBeep--">enableBeep()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#enableBeep--">enableBeep()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#enableBeep--">enableBeep()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">启用蜂鸣器</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#enableBeep--">enableBeep()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#enableBlockScankey-android.content.Context-boolean-">enableBlockScankey(Context, boolean)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">拦截扫描按键 (备注：键盘助手v2.3.5 之后的版本才支持)<br>
 Block scan button (Comment: Supports after keyboardemualator v2.3.5 has been released)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#enableBlockScankey-android.content.Context-boolean-">enableBlockScankey(Context, boolean)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#enableBlockScankey-android.content.Context-boolean-">enableBlockScankey(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#enableBlockScankey-android.content.Context-boolean-">enableBlockScankey(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/BatteryUtils.html#enableCharge--">enableCharge()</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility">BatteryUtils</a></dt>
<dd>
<div class="block">开启充电.<br/>
 Enable charging.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.html#enableContinuousScan-android.content.Context-boolean-">enableContinuousScan(Context, boolean)</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a></dt>
<dd>
<div class="block">启用连续扫描</br>
 Enable continuous scan</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enableContinuousScan-android.content.Context-boolean-">enableContinuousScan(Context, boolean)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></dt>
<dd>
<div class="block">启用连续扫描</br>
 Enable continuous scan</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html#enableContinuousScan-android.content.Context-boolean-">enableContinuousScan(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeUtility_mtk</a></dt>
<dd>
<div class="block">启用连续扫描</br>
 Enable continuous scan</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html#enableContinuousScan-android.content.Context-boolean-">enableContinuousScan(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeUtility_qcom</a></dt>
<dd>
<div class="block">启用连续扫描</br>
 Enable continuous scan</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.html#enableEnter-android.content.Context-boolean-">enableEnter(Context, boolean)</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a></dt>
<dd>
<div class="block">是否启用回车</br>
 Whether enable Enter function</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enableEnter-android.content.Context-boolean-">enableEnter(Context, boolean)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></dt>
<dd>
<div class="block">是否启用回车</br>
 Whether enable Enter function</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#enableEnter-android.content.Context-boolean-">enableEnter(Context, boolean)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">是否启用回车<br>
 Enter ON/OFF</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#enableEnter-android.content.Context-boolean-">enableEnter(Context, boolean)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html#enableEnter-android.content.Context-boolean-">enableEnter(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeUtility_mtk</a></dt>
<dd>
<div class="block">是否启用回车</br>
 Whether enable Enter function</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#enableEnter-android.content.Context-boolean-">enableEnter(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html#enableEnter-android.content.Context-boolean-">enableEnter(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeUtility_qcom</a></dt>
<dd>
<div class="block">是否启用回车</br>
 Whether enable Enter function</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#enableEnter-android.content.Context-boolean-">enableEnter(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#enableFunction-android.content.Context-int-">enableFunction(Context, int)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">启用指定功能模块<br>
 Enable specific function module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#enableFunction-android.content.Context-int-">enableFunction(Context, int)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#enableFunction-android.content.Context-int-">enableFunction(Context, int)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#enableFunction-android.content.Context-int-">enableFunction(Context, int)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.html#enablePlayFailureSound-android.content.Context-boolean-">enablePlayFailureSound(Context, boolean)</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a></dt>
<dd>
<div class="block">扫描失败是否播放提示音</br>
 whether playing notification when scanning failure.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enablePlayFailureSound-android.content.Context-boolean-">enablePlayFailureSound(Context, boolean)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></dt>
<dd>
<div class="block">扫描失败是否播放提示音</br>
 whether playing notification when scanning failure.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#enablePlayFailureSound-android.content.Context-boolean-">enablePlayFailureSound(Context, boolean)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">扫描失败是否播放提示音<br>
 scan failure sound ON/OFF</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#enablePlayFailureSound-android.content.Context-boolean-">enablePlayFailureSound(Context, boolean)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html#enablePlayFailureSound-android.content.Context-boolean-">enablePlayFailureSound(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeUtility_mtk</a></dt>
<dd>
<div class="block">扫描失败是否播放提示音</br>
 whether playing notification when scanning failure.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#enablePlayFailureSound-android.content.Context-boolean-">enablePlayFailureSound(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html#enablePlayFailureSound-android.content.Context-boolean-">enablePlayFailureSound(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeUtility_qcom</a></dt>
<dd>
<div class="block">扫描失败是否播放提示音</br>
 whether playing notification when scanning failure.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#enablePlayFailureSound-android.content.Context-boolean-">enablePlayFailureSound(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.html#enablePlaySuccessSound-android.content.Context-boolean-">enablePlaySuccessSound(Context, boolean)</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a></dt>
<dd>
<div class="block">是否播放成功的提示音</br>
 whether playing notification when scanning success.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enablePlaySuccessSound-android.content.Context-boolean-">enablePlaySuccessSound(Context, boolean)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></dt>
<dd>
<div class="block">是否播放成功的提示音</br>
 whether playing notification when scanning success.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#enablePlaySuccessSound-android.content.Context-boolean-">enablePlaySuccessSound(Context, boolean)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">扫描成功是否播放提示音<br>
 Scan success sound ON/OFF</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#enablePlaySuccessSound-android.content.Context-boolean-">enablePlaySuccessSound(Context, boolean)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html#enablePlaySuccessSound-android.content.Context-boolean-">enablePlaySuccessSound(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeUtility_mtk</a></dt>
<dd>
<div class="block">是否播放成功的提示音</br>
 whether playing notification when scanning success.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#enablePlaySuccessSound-android.content.Context-boolean-">enablePlaySuccessSound(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html#enablePlaySuccessSound-android.content.Context-boolean-">enablePlaySuccessSound(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeUtility_qcom</a></dt>
<dd>
<div class="block">是否播放成功的提示音</br>
 whether playing notification when scanning success.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#enablePlaySuccessSound-android.content.Context-boolean-">enablePlaySuccessSound(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#enableScanOnRelease-android.content.Context-boolean-">enableScanOnRelease(Context, boolean)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">释放扫描按键开始扫描<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#enableScanOnRelease-android.content.Context-boolean-">enableScanOnRelease(Context, boolean)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>
<div class="block">释放扫描按键开始扫描<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#enableScanOnRelease-android.content.Context-boolean-">enableScanOnRelease(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>
<div class="block">释放扫描按键开始扫描<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#enableScanOnRelease-android.content.Context-boolean-">enableScanOnRelease(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>
<div class="block">释放扫描按键开始扫描<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.html#enableTAB-android.content.Context-boolean-">enableTAB(Context, boolean)</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a></dt>
<dd>
<div class="block">是否启用TAB</br>
 Whether enable TAB function</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enableTAB-android.content.Context-boolean-">enableTAB(Context, boolean)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></dt>
<dd>
<div class="block">是否启用TAB</br>
 Whether enable TAB function</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#enableTAB-android.content.Context-boolean-">enableTAB(Context, boolean)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">是否启用TAB<br>
 TAB ON/OFF</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#enableTAB-android.content.Context-boolean-">enableTAB(Context, boolean)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html#enableTAB-android.content.Context-boolean-">enableTAB(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeUtility_mtk</a></dt>
<dd>
<div class="block">是否启用TAB</br>
 Whether enable TAB function</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#enableTAB-android.content.Context-boolean-">enableTAB(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html#enableTAB-android.content.Context-boolean-">enableTAB(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeUtility_qcom</a></dt>
<dd>
<div class="block">是否启用TAB</br>
 Whether enable TAB function</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#enableTAB-android.content.Context-boolean-">enableTAB(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#enableTXNotification--">enableTXNotification()</a></span> - Method in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.html#enableVibrate-android.content.Context-boolean-">enableVibrate(Context, boolean)</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a></dt>
<dd>
<div class="block">扫描成功是否震动提示</br>
 whether vibrating when scanning success</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enableVibrate-android.content.Context-boolean-">enableVibrate(Context, boolean)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></dt>
<dd>
<div class="block">扫描成功是否震动提示</br>
 whether vibrating when scanning success</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#enableVibrate-android.content.Context-boolean-">enableVibrate(Context, boolean)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">扫描成功是否震动提示<br>
 scan success vibrate ON/OFF</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#enableVibrate-android.content.Context-boolean-">enableVibrate(Context, boolean)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html#enableVibrate-android.content.Context-boolean-">enableVibrate(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeUtility_mtk</a></dt>
<dd>
<div class="block">扫描成功是否震动提示</br>
 whether vibrating when scanning success</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#enableVibrate-android.content.Context-boolean-">enableVibrate(Context, boolean)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html#enableVibrate-android.content.Context-boolean-">enableVibrate(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeUtility_qcom</a></dt>
<dd>
<div class="block">扫描成功是否震动提示</br>
 whether vibrating when scanning success</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#enableVibrate-android.content.Context-boolean-">enableVibrate(Context, boolean)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#Encryption-byte:A-int-">Encryption(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#Encryption-byte:A-int-java.lang.String-">Encryption(byte[], int, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#Encryption-byte:A-int-">Encryption(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#Encryption-byte:A-int-java.lang.String-">Encryption(byte[], int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintSM206B.html#enroll--">enroll()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintSM206B.html" title="class in com.rscja.deviceapi">FingerprintSM206B</a></dt>
<dd>
<div class="block">录入指纹，用户录入 3~5 次指纹，生成 256 字节指纹特征值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.html#enroll--">enroll()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></dt>
<dd>
<div class="block">采集指纹<br>
 Acquire fingerprint<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html#enroll--">enroll()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#enroll--">enroll()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a></dt>
<dd>
<div class="block">采集指纹<br>
 Acquire fingerprint<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#enroll--">enroll()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a></dt>
<dd>
<div class="block">采集指纹<br>
 Acquire fingerprint<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html#enroll--">enroll()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintSM206B_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#enroll--">enroll()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a></dt>
<dd>
<div class="block">采集指纹<br>
 Acquire fingerprint<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">擦除指定标签的数据，被擦除的数据块其内容将被设置成0.<br>
 Clean specific tag data, data block after cleaned will be set to 0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">擦除数据，被擦除的数据块其内容将被设置成0.<br>
 Data block that has been cleaned will be setup to 0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">擦除指定标签的数据，被擦除的数据块其内容将被设置成0.<br>
 Clean specific tag data, data block after cleaned will be set to 0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">擦除数据，被擦除的数据块其内容将被设置成0.<br>
 Data block that has been cleaned will be setup to 0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">擦除指定标签的数据，被擦除的数据块其内容将被设置成0.(Clean specific tag data, data block after cleaned will be set to 0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">擦除数据，被擦除的数据块其内容将被设置成0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">擦除指定标签的数据，被擦除的数据块其内容将被设置成0.(Clean specific tag data, data block after cleaned will be set to 0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">擦除数据，被擦除的数据块其内容将被设置成0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">擦除指定标签的数据，被擦除的数据块其内容将被设置成0.(Clean specific tag data, data block after cleaned will be set to 0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">擦除数据，被擦除的数据块其内容将被设置成0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">擦除指定标签的数据，被擦除的数据块其内容将被设置成0.(Clean specific tag data, data block after cleaned will be set to 0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">擦除数据，被擦除的数据块其内容将被设置成0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">擦除指定标签的数据，被擦除的数据块其内容将被设置成0.<br>
 Clean specific tag data, data block after cleaned will be set to 0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">擦除数据，被擦除的数据块其内容将被设置成0.<br>
 Data block that has been cleaned will be setup to 0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">擦除指定标签的数据，被擦除的数据块其内容将被设置成0.<br>
 Clean specific tag data, data block after cleaned will be set to 0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">擦除数据，被擦除的数据块其内容将被设置成0.<br>
 Data block that has been cleaned will be setup to 0.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData(String, int, int, int, String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#eraseData-java.lang.String-int-int-int-">eraseData(String, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IPrinter.html#eraseFlash--">eraseFlash()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.html#eraseFlash--">eraseFlash()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html#eraseFlash--">eraseFlash()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Printer_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IPrinter.html#eraseFW-long-">eraseFW(long)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.html#eraseFW-long-">eraseFW(long)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html#eraseFW-long-">eraseFW(long)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Printer_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERRCODE_FAILURE">ERRCODE_FAILURE</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>
<div class="block">失败(Failure)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERRCODE_SUCCESS">ERRCODE_SUCCESS</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>
<div class="block">成功(Success)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_INSUFFICIENT_PRIVILEGES">ERROR_INSUFFICIENT_PRIVILEGES</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>
<div class="block">没有权限访问(The Interrogator did not authenticate itself with sufficient privileges for the Tag to perform the operation)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_MEMORY_LOCK">ERROR_MEMORY_LOCK</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>
<div class="block">数据区被锁定(Memory lock)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_MEMORY_OVERRUN">ERROR_MEMORY_OVERRUN</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>
<div class="block">数据区超限(Memory Overflow)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_NO_ENOUGH_POWER_ON_TAG">ERROR_NO_ENOUGH_POWER_ON_TAG</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>
<div class="block">标签能量不足(no enough power on tag)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_NO_TAG">ERROR_NO_TAG</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>
<div class="block">找不到标签(No tags found.)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_OPERATION_FAILED">ERROR_OPERATION_FAILED</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>
<div class="block">操作失败(0peration failure)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_PASSWORD_IS_INCORRECT">ERROR_PASSWORD_IS_INCORRECT</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>
<div class="block">密码不正确(Incorrect Password)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_RECV_FAIL">ERROR_RECV_FAIL</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>
<div class="block">接收失败(Receive failure)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_RESPONSE_BUFFER_OVERFLOW">ERROR_RESPONSE_BUFFER_OVERFLOW</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>
<div class="block">缓冲区溢出(The operation failed because the ResponseBuffer overflowed)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_SEND_FAIL">ERROR_SEND_FAIL</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>
<div class="block">发送失败(Send failure)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_TAG_NO_REPLY">ERROR_TAG_NO_REPLY</a></span> - Static variable in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>
<div class="block">标签没有应答(Tag not responding)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html#ErrorCode--">ErrorCode()</a></span> - Constructor for class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EventReport--">EventReport()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#EventReport--">EventReport()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IPSAM.html#executeCmd-java.lang.String-java.lang.String-">executeCmd(String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a></dt>
<dd>
<div class="block">执行PSAM命令<br>
 Execute PSAM command<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/PSAM.html#executeCmd-java.lang.String-java.lang.String-">executeCmd(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/PSAM.html" title="class in com.rscja.deviceapi">PSAM</a></dt>
<dd>
<div class="block">执行PSAM命令<br>
 Execute PSAM command<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/PSAM_mtk.html#executeCmd-java.lang.String-java.lang.String-">executeCmd(String, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/PSAM_mtk.html" title="class in com.rscja.team.mtk.deviceapi">PSAM_mtk</a></dt>
<dd>
<div class="block">执行PSAM命令<br>
 Execute PSAM command<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/PSAM_qcom.html#executeCmd-java.lang.String-java.lang.String-">executeCmd(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/PSAM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">PSAM_qcom</a></dt>
<dd>
<div class="block">执行PSAM命令<br>
 Execute PSAM command<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#exists-int-">exists(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>
<div class="block">检查指定的编号是否已被注册</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#exists-int-">exists(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>
<div class="block">检查指定的编号是否已被注册</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#exists-int-">exists(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>
<div class="block">检查指定的编号是否已被注册</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#EXTRA_DATA">EXTRA_DATA</a></span> - Static variable in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#EXTRADATA_EPCAREA">EXTRADATA_EPCAREA</a></span> - Variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-4.html">Prev Letter</a></li>
<li><a href="index-6.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-5.html" target="_top">Frames</a></li>
<li><a href="index-5.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
