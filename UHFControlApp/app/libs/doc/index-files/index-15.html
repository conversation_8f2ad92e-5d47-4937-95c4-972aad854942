<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>O-Index</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="O-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-14.html">Prev Letter</a></li>
<li><a href="index-16.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-15.html" target="_top">Frames</a></li>
<li><a href="index-15.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;<a name="I:O">
<!--   -->
</a>
<h2 class="title">O</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/led/ScanLed.html#off--">off()</a></span> - Method in class com.rscja.scanner.led.<a href="../com/rscja/scanner/led/ScanLed.html" title="class in com.rscja.scanner.led">ScanLed</a></dt>
<dd>
<div class="block">关闭LED灯<br>
 Switch off LED light<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#off-android.content.Context-">off(Context)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi">ScanerLedLight_mtk</a></dt>
<dd>
<div class="block">关闭LED灯<br>
 Switch off LED light<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/C6000_6762_ScanLed_mtk.html#off--">off()</a></span> - Method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/C6000_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C6000_6762_ScanLed_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/C7X_6765_ScanLed_mtk.html#off--">off()</a></span> - Method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/C7X_6765_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C7X_6765_ScanLed_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html#off--">off()</a></span> - Method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C90_6762_ScanLed_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/ScanLed_mtk.html#off--">off()</a></span> - Method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">ScanLed_mtk</a></dt>
<dd>
<div class="block">关闭LED灯<br>
 Switch off LED light<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html#off-android.content.Context-">off(Context)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi">ScanerLedLight_qcom</a></dt>
<dd>
<div class="block">关闭LED灯<br>
 Switch off LED light<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C60_6765_11_ScanLed_qcom.html#off--">off()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C60_6765_11_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C60_6765_11_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C60_qcm2150_10_ScanLed_qcom.html#off--">off()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C60_qcm2150_10_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C60_qcm2150_10_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C61_smd450_90_ScanLed_qcom.html#off--">off()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C61_smd450_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C61_smd450_90_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C66_smd450_90_ScanLed_qcom.html#off--">off()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C66_smd450_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C66_smd450_90_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C66m_sm6115_10_ScanLed_qcom.html#off--">off()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C66m_sm6115_10_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C66m_sm6115_10_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/MC50_4350_12_ScanLed_qcom.html#off--">off()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/MC50_4350_12_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">MC50_4350_12_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/P80_8786_130_ScanLed_qcom.html#off--">off()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/P80_8786_130_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">P80_8786_130_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/P80_8953_90_ScanLed_qcom.html#off--">off()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/P80_8953_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">P80_8953_90_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/led/ScanLed.html#on--">on()</a></span> - Method in class com.rscja.scanner.led.<a href="../com/rscja/scanner/led/ScanLed.html" title="class in com.rscja.scanner.led">ScanLed</a></dt>
<dd>
<div class="block">打开LED灯<br>
 Switch on LED light<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#On-android.content.Context-">On(Context)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi">ScanerLedLight_mtk</a></dt>
<dd>
<div class="block">打开LED灯<br>
 Switch on LED light<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/C6000_6762_ScanLed_mtk.html#on--">on()</a></span> - Method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/C6000_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C6000_6762_ScanLed_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/C7X_6765_ScanLed_mtk.html#on--">on()</a></span> - Method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/C7X_6765_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C7X_6765_ScanLed_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html#on--">on()</a></span> - Method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C90_6762_ScanLed_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/led/ScanLed_mtk.html#on--">on()</a></span> - Method in class com.rscja.team.mtk.scanner.led.<a href="../com/rscja/team/mtk/scanner/led/ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">ScanLed_mtk</a></dt>
<dd>
<div class="block">打开LED灯<br>
 Switch on LED light<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html#On-android.content.Context-">On(Context)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi">ScanerLedLight_qcom</a></dt>
<dd>
<div class="block">打开LED灯<br>
 Switch on LED light<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C60_6765_11_ScanLed_qcom.html#on--">on()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C60_6765_11_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C60_6765_11_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C60_qcm2150_10_ScanLed_qcom.html#on--">on()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C60_qcm2150_10_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C60_qcm2150_10_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C61_smd450_90_ScanLed_qcom.html#on--">on()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C61_smd450_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C61_smd450_90_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C66_smd450_90_ScanLed_qcom.html#on--">on()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C66_smd450_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C66_smd450_90_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/C66m_sm6115_10_ScanLed_qcom.html#on--">on()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/C66m_sm6115_10_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C66m_sm6115_10_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/MC50_4350_12_ScanLed_qcom.html#on--">on()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/MC50_4350_12_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">MC50_4350_12_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/P80_8786_130_ScanLed_qcom.html#on--">on()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/P80_8786_130_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">P80_8786_130_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/led/P80_8953_90_ScanLed_qcom.html#on--">on()</a></span> - Method in class com.rscja.team.qcom.scanner.led.<a href="../com/rscja/team/qcom/scanner/led/P80_8953_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">P80_8953_90_ScanLed_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UhfRadarLocation_qcom.html#onAccuracyChanged-android.hardware.Sensor-int-">onAccuracyChanged(Sensor, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UhfRadarLocation_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UhfRadarLocation_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeDecoder.IBarcodeImageCallback.html#onBarcodeImage-byte:A-">onBarcodeImage(byte[])</a></span> - Method in interface com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeDecoder.IBarcodeImageCallback.html" title="interface in com.rscja.barcode">BarcodeDecoder.IBarcodeImageCallback</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.EnrollCallBack.html#onComplete-boolean-byte:A-int-int-">onComplete(boolean, byte[], int, int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.EnrollCallBack</a></dt>
<dd>
<div class="block">获取完指纹之后被回调<br>
 call-back after fingerprint acquired<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.GRABCallBack.html#onComplete-boolean-byte:A-int-">onComplete(boolean, byte[], int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.GRABCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.GRABCallBack</a></dt>
<dd>
<div class="block">获取完图像之后被回调<br>
 call-back after acquiring image<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.IdentificationCallBack.html#onComplete-boolean-int-int-">onComplete(boolean, int, int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.IdentificationCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.IdentificationCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.PtCaptureCallBack.html#onComplete-boolean-byte:A-int-">onComplete(boolean, byte[], int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.PtCaptureCallBack</a></dt>
<dd>
<div class="block">获取完指纹模版数据之后被回调<br>
 call-back after fingerprint template data acquired<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithFIPS.TemplateVerifyCallBack.html#onComplete-boolean-int-">onComplete(boolean, int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithFIPS.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.TemplateVerifyCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html#onComplete-boolean-int-">onComplete(boolean, int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a></dt>
<dd>
<div class="block">获取完指纹之后被回调<br>
 call-back after fingerprint acquired<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.GrabCallBack.html#onComplete-boolean-int-">onComplete(boolean, int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.GrabCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.GrabCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.IdentificationCallBack.html#onComplete-boolean-int-java.lang.String-int-">onComplete(boolean, int, String, int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.IdentificationCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.IdentificationCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.PtCaptureCallBack.html#onComplete-boolean-byte:A-int-">onComplete(boolean, byte[], int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.PtCaptureCallBack</a></dt>
<dd>
<div class="block">获取完指纹模版数据之后被回调<br>
 call-back after fingerprint <br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithMorpho.TemplateVerifyCallBack.html#onComplete-boolean-int-">onComplete(boolean, int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithMorpho.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.TemplateVerifyCallBack</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.IUPImageCallback.html#onComplete-boolean-byte:A-int-">onComplete(boolean, byte[], int)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithTLK1NC.IUPImageCallback.html" title="interface in com.rscja.deviceapi">FingerprintWithTLK1NC.IUPImageCallback</a></dt>
<dd>
<div class="block">获取完图像之后被回调<br>
 call-back after acquiring image<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeDecoder.DecodeCallback.html#onDecodeComplete-com.rscja.deviceapi.entity.BarcodeEntity-">onDecodeComplete(BarcodeEntity)</a></span> - Method in interface com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeDecoder.DecodeCallback.html" title="interface in com.rscja.barcode">BarcodeDecoder.DecodeCallback</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BluetoothReader.DecodeCallback.html#onDecodeComplete-com.rscja.deviceapi.entity.BarcodeResult-">onDecodeComplete(BarcodeResult)</a></span> - Method in interface com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BluetoothReader.DecodeCallback.html" title="interface in com.rscja.deviceapi">BluetoothReader.DecodeCallback</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.DecodeCallback.html#onDecodeComplete-com.rscja.deviceapi.entity.BarcodeEntity-">onDecodeComplete(BarcodeEntity)</a></span> - Method in interface com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.DecodeCallback.html" title="interface in com.rscja.team.mtk.barcode">BarcodeDecoder_mtk.DecodeCallback</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/KeyEventCallback.html#onKeyDown-int-">onKeyDown(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/KeyEventCallback.html" title="interface in com.rscja.deviceapi.interfaces">KeyEventCallback</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/KeyEventCallback.html#onKeyUp-int-">onKeyUp(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/KeyEventCallback.html" title="interface in com.rscja.deviceapi.interfaces">KeyEventCallback</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/OnLowBatteryListener.html#OnLowBattery-com.rscja.deviceapi.entity.BatteryEntity-">OnLowBattery(BatteryEntity)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/OnLowBatteryListener.html" title="interface in com.rscja.deviceapi.interfaces">OnLowBatteryListener</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/interfaces/OnLowBatteryListener.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">OnLowBatteryListener</span></a> - Interface in <a href="../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcodePictureCallback.html#onPictureTaken-int-int-int-byte:A-">onPictureTaken(int, int, int, byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcodePictureCallback.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodePictureCallback</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.BluetoothStateReceiver.html#onReceive-android.content.Context-android.content.Intent-">onReceive(Context, Intent)</a></span> - Method in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.BluetoothStateReceiver.html" title="class in com.rscja.team.qcom.service">BLEService_qcom.BluetoothStateReceiver</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UhfRadarLocation_qcom.html#onSensorChanged-android.hardware.SensorEvent-">onSensorChanged(SensorEvent)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UhfRadarLocation_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UhfRadarLocation_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IConnectionStatusChangedListener.html#onStatusChanged-com.rscja.deviceapi.interfaces.ConnectionStatus-">onStatusChanged(ConnectionStatus)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IConnectionStatusChangedListener.html" title="interface in com.rscja.deviceapi.interfaces">IConnectionStatusChangedListener</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/scanner/OnUhfWorkStateListener.html" title="interface in com.rscja.scanner"><span class="typeNameLink">OnUhfWorkStateListener</span></a> - Interface in <a href="../com/rscja/scanner/package-summary.html">com.rscja.scanner</a></dt>
<dd>
<div class="block">uhf 工作状态发生变化时回调的接口定义.</br>
 Interface definition for a callback to be invoked when UHF working state changes.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcodeVideoCallback.html#onVideoFrame-int-int-int-byte:A-">onVideoFrame(int, int, int, byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcodeVideoCallback.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeVideoCallback</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeDecoder.html#open-android.content.Context-">open(Context)</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></dt>
<dd>
<div class="block">打开二维扫描设备<br>
 Switch on 2D scanning device<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.html#open-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">open(Context, BarcodeUtility.ModuleType)</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a></dt>
<dd>
<div class="block">打开指定功能</br>
 Switch on designated function</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Barcode1D.html#open-android.content.Context-">open(Context)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Barcode1D.html" title="class in com.rscja.deviceapi">Barcode1D</a></dt>
<dd>
<div class="block">打开一维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Barcode1D.html#open--">open()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Barcode1D.html" title="class in com.rscja.deviceapi">Barcode1D</a></dt>
<dd>
<div class="block">打开一维扫描设备<br>
 Switch on 1D scanning device.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Barcode2D.html#open--">open()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Barcode2D.html" title="class in com.rscja.deviceapi">Barcode2D</a></dt>
<dd>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Barcode2D.html#open-android.content.Context-">open(Context)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Barcode2D.html" title="class in com.rscja.deviceapi">Barcode2D</a></dt>
<dd>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Infrared.html#open--">open()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Infrared.html" title="class in com.rscja.deviceapi">Infrared</a></dt>
<dd>
<div class="block">打开红外模块,默认1200波特率<br>
 Switch on infared module, default 1200 baud rate<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Infrared.html#open-int-">open(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Infrared.html" title="class in com.rscja.deviceapi">Infrared</a></dt>
<dd>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Infrared.html#open-int-int-">open(int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Infrared.html" title="class in com.rscja.deviceapi">Infrared</a></dt>
<dd>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Infrared.html#open-int-int-int-int-">open(int, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Infrared.html" title="class in com.rscja.deviceapi">Infrared</a></dt>
<dd>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcode1D.html#open-android.content.Context-">open(Context)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcode1D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode1D</a></dt>
<dd>
<div class="block">打开一维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcode1D.html#open--">open()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcode1D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode1D</a></dt>
<dd>
<div class="block">打开一维扫描设备<br>
 Switch on 1D scanning device.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcode2D.html#open--">open()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a></dt>
<dd>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcode2D.html#open-android.content.Context-">open(Context)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a></dt>
<dd>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#open-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">open(Context, BarcodeUtility.ModuleType)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></dt>
<dd>
<div class="block">打开指定功能</br>
 Switch on designated function</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IInfrared.html#open--">open()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces">IInfrared</a></dt>
<dd>
<div class="block">打开红外模块,默认1200波特率<br>
 Switch on infared module, default 1200 baud rate<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IInfrared.html#open-int-">open(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces">IInfrared</a></dt>
<dd>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IInfrared.html#open-int-int-">open(int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces">IInfrared</a></dt>
<dd>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IInfrared.html#open-int-int-int-int-">open(int, int, int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces">IInfrared</a></dt>
<dd>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/ILedLight.html#open--">open()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/ILedLight.html" title="interface in com.rscja.deviceapi.interfaces">ILedLight</a></dt>
<dd>
<div class="block">打开LED灯<br>
 Switch on LED<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/LedLight.html#open--">open()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/LedLight.html" title="class in com.rscja.deviceapi">LedLight</a></dt>
<dd>
<div class="block">打开LED灯<br>
 Switch on LED<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/IScanner.html#open-android.content.Context-">open(Context)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dt>
<dd>
<div class="block">打开键盘助手总开关<br>
 Switch on keyboardemulator</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/utility/ScannerUtility.html#open-android.content.Context-">open(Context)</a></span> - Method in class com.rscja.scanner.utility.<a href="../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html#open-android.content.Context-">open(Context)</a></span> - Method in class com.rscja.team.mtk.barcode.barcode2d.<a href="../com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html" title="class in com.rscja.team.mtk.barcode.barcode2d">KeyboardEmulator2DDecoder_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#open-android.content.Context-">open(Context)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeDecoder_mtk</a></dt>
<dd>
<div class="block">打开二维扫描设备<br>
 Switch on 2D scanning device<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html#open-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">open(Context, BarcodeUtility.ModuleType)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeUtility_mtk</a></dt>
<dd>
<div class="block">打开指定功能</br>
 Switch on designated function</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Barcode1D_mtk.html#open-android.content.Context-">open(Context)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Barcode1D_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Barcode1D_mtk</a></dt>
<dd>
<div class="block">打开一维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Barcode1D_mtk.html#open--">open()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Barcode1D_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Barcode1D_mtk</a></dt>
<dd>
<div class="block">打开一维扫描设备<br>
 Switch on 1D scanning device.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html#open--">open()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Barcode2D_mtk</a></dt>
<dd>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html#open-android.content.Context-">open(Context)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Barcode2D_mtk</a></dt>
<dd>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html#open--">open()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Infrared_mtk</a></dt>
<dd>
<div class="block">打开红外模块,默认1200波特率<br>
 Switch on infared module, default 1200 baud rate<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html#open-int-">open(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Infrared_mtk</a></dt>
<dd>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html#open-int-int-">open(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Infrared_mtk</a></dt>
<dd>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html#open-int-int-int-int-">open(int, int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Infrared_mtk</a></dt>
<dd>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/LedLight_mtk.html#open--">open()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/LedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi">LedLight_mtk</a></dt>
<dd>
<div class="block">打开LED灯<br>
 Switch on LED<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html#open-android.content.Context-">open(Context)</a></span> - Method in class com.rscja.team.mtk.scanner.utility.<a href="../com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility">ScannerUtility_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html#open-android.content.Context-">open(Context)</a></span> - Method in class com.rscja.team.qcom.barcode.barcode2d.<a href="../com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d">KeyboardEmulator2DDecoder_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html#open-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">open(Context, BarcodeUtility.ModuleType)</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeUtility_qcom</a></dt>
<dd>
<div class="block">打开指定功能</br>
 Switch on designated function</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.html#open-android.content.Context-">open(Context)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Barcode1D_qcom</a></dt>
<dd>
<div class="block">打开一维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.html#open--">open()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Barcode1D_qcom</a></dt>
<dd>
<div class="block">打开一维扫描设备<br>
 Switch on 1D scanning device.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Barcode2D_qcom.html#open--">open()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Barcode2D_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Barcode2D_qcom</a></dt>
<dd>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Barcode2D_qcom.html#open-android.content.Context-">open(Context)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Barcode2D_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Barcode2D_qcom</a></dt>
<dd>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/LedLight_qcom.html#open--">open()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/LedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi">LedLight_qcom</a></dt>
<dd>
<div class="block">打开LED灯<br>
 Switch on LED<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UhfUartManage_qcom.html#open--">open()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UhfUartManage_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UhfUartManage_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UhfUartUR4Manage_qcom.html#open--">open()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UhfUartUR4Manage_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UhfUartUR4Manage_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html#open-android.content.Context-">open(Context)</a></span> - Method in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#open-android.hardware.usb.UsbDeviceConnection-">open(UsbDeviceConnection)</a></span> - Method in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">Opens and initializes the port.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html#open-android.hardware.usb.UsbDeviceConnection-">open(UsbDeviceConnection)</a></span> - Method in class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbSerialPortImpl_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IScanerLedLight.html#openAuxiliaryLight-android.content.Context-">openAuxiliaryLight(Context)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces">IScanerLedLight</a></dt>
<dd>
<div class="block">打开扫描辅助灯，目前只支持C70系列<br>
 Switch on scanning light, support C70 series only<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/ScanerLedLight.html#openAuxiliaryLight-android.content.Context-">openAuxiliaryLight(Context)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/ScanerLedLight.html" title="class in com.rscja.deviceapi">ScanerLedLight</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#openAuxiliaryLight-android.content.Context-">openAuxiliaryLight(Context)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi">ScanerLedLight_mtk</a></dt>
<dd>
<div class="block">打开扫描辅助灯，目前只支持C70系列<br>
 Switch on scanning light, support C70 series only<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html#openAuxiliaryLight-android.content.Context-">openAuxiliaryLight(Context)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi">ScanerLedLight_qcom</a></dt>
<dd>
<div class="block">打开扫描辅助灯，目前只支持C70系列<br>
 Switch on scanning light, support C70 series only<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURAxDevice.html#openBuzzer--">openBuzzer()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax">IURAxDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURAxDevice.html#openBuzzer--">openBuzzer()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURAxDevice.html" title="class in com.rscja.team.qcom.urax">QcomURAxDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4C8Device.html#openBuzzer--">openBuzzer()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4C8Device.html" title="class in com.rscja.team.qcom.urax">RKURA4C8Device</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURAxDevice.html#openBuzzer--">openBuzzer()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURAxDevice.html" title="class in com.rscja.team.qcom.urax">RKURAxDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/BarcodeUtility.html#openKeyboardHelper-android.content.Context-">openKeyboardHelper(Context)</a></span> - Method in class com.rscja.barcode.<a href="../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a></dt>
<dd>
<div class="block">打开键盘助手功能总开关</br>
 Switch on main function switch of keyboardemulator</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#openKeyboardHelper-android.content.Context-">openKeyboardHelper(Context)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></dt>
<dd>
<div class="block">打开键盘助手功能总开关</br>
 Switch on main function switch of keyboardemulator</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html#openKeyboardHelper-android.content.Context-">openKeyboardHelper(Context)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeUtility_mtk</a></dt>
<dd>
<div class="block">打开键盘助手功能总开关</br>
 Switch on main function switch of keyboardemulator</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html#openKeyboardHelper-android.content.Context-">openKeyboardHelper(Context)</a></span> - Method in class com.rscja.team.qcom.barcode.<a href="../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeUtility_qcom</a></dt>
<dd>
<div class="block">打开键盘助手功能总开关</br>
 Switch on main function switch of keyboardemulator</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BluetoothReader.html#openLed--">openLed()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></dt>
<dd>
<div class="block">打开led</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Infrared.html#openLED--">openLED()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Infrared.html" title="class in com.rscja.deviceapi">Infrared</a></dt>
<dd>
<div class="block">打开辅助灯<br>
 Open LED</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IInfrared.html#openLED--">openLED()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces">IInfrared</a></dt>
<dd>
<div class="block">打开辅助灯<br>
 Open LED</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IReader.html#openLed--">openLed()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></dt>
<dd>
<div class="block">打开led</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#openLed--">openLed()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>
<div class="block">打开led</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html#openLED--">openLED()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Infrared_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Infrared_mtk</a></dt>
<dd>
<div class="block">打开辅助灯<br>
 Open LED</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#openLed--">openLed()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#openLed--">openLed()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></dt>
<dd>
<div class="block">打开led</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#openLed--">openLed()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>
<div class="block">打开led</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#openLedSendData--">openLedSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#openLedSendData--">openLedSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html#openPort-android.hardware.usb.UsbDevice-">openPort(UsbDevice)</a></span> - Method in class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbPL2302</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/RxUsb_qcom.html#openPort-android.hardware.usb.UsbDevice-">openPort(UsbDevice)</a></span> - Method in class com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/RxUsb_qcom.html" title="class in com.rscja.team.qcom.usb">RxUsb_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/UrxUsb_qcom.html#openPort-android.hardware.usb.UsbDevice-">openPort(UsbDevice)</a></span> - Method in class com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/UrxUsb_qcom.html" title="class in com.rscja.team.qcom.usb">UrxUsb_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/UsbBase_qcom.html#openPort-android.hardware.usb.UsbDevice-">openPort(UsbDevice)</a></span> - Method in class com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IPrinter.html#openPrinterSerialPort-boolean-">openPrinterSerialPort(boolean)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.html#openPrinterSerialPort-boolean-">openPrinterSerialPort(boolean)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html#openPrinterSerialPort-boolean-">openPrinterSerialPort(boolean)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Printer_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURAxDevice.html#openScanLed--">openScanLed()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax">IURAxDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURAxDevice.html#openScanLed--">openScanLed()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURAxDevice.html" title="class in com.rscja.team.qcom.urax">QcomURAxDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4C8Device.html#openScanLed--">openScanLed()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4C8Device.html" title="class in com.rscja.team.qcom.urax">RKURA4C8Device</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURAxDevice.html#openScanLed--">openScanLed()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURAxDevice.html" title="class in com.rscja.team.qcom.urax">RKURAxDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IModule.html#openSerail-java.lang.String-int-int-int-int-">openSerail(String, int, int, int, int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></dt>
<dd>
<div class="block">打开串口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Module.html#openSerail-java.lang.String-int-int-int-int-">openSerail(String, int, int, int, int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Module.html" title="class in com.rscja.deviceapi">Module</a></dt>
<dd>
<div class="block">打开串口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#OpenSerail-java.lang.String-int-int-int-int-">OpenSerail(String, int, int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Module_mtk.html#openSerail-java.lang.String-int-int-int-int-">openSerail(String, int, int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Module_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Module_mtk</a></dt>
<dd>
<div class="block">打开串口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#OpenSerail-java.lang.String-int-int-int-int-">OpenSerail(String, int, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html#openSerail-java.lang.String-int-int-int-int-">openSerail(String, int, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html" title="class in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom</a></dt>
<dd>
<div class="block">打开串口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html#openSerail-java.lang.String-int-">openSerail(String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html" title="class in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Module_qcom.html#openSerail-java.lang.String-int-int-int-int-">openSerail(String, int, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Module_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Module_qcom</a></dt>
<dd>
<div class="block">打开串口</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/serialportapi/SerialportAPI.html#openSerialPort-java.lang.String-int-">openSerialPort(String, int)</a></span> - Method in class com.rscja.team.qcom.serialportapi.<a href="../com/rscja/team/qcom/serialportapi/SerialportAPI.html" title="class in com.rscja.team.qcom.serialportapi">SerialportAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#openUhfSendData--">openUhfSendData()</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#openWifi--">openWifi()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#openWifi--">openWifi()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">打开wifi</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#openWifi--">openWifi()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">打开wifi</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#openWifi--">openWifi()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">打开wifi</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#openWifi--">openWifi()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">打开wifi</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#openWifiSendData-boolean-">openWifiSendData(boolean)</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURAxDevice.html#openWorkLed--">openWorkLed()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax">IURAxDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURAxDevice.html#openWorkLed--">openWorkLed()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURAxDevice.html" title="class in com.rscja.team.qcom.urax">QcomURAxDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4C8Device.html#openWorkLed--">openWorkLed()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4C8Device.html" title="class in com.rscja.team.qcom.urax">RKURA4C8Device</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURAxDevice.html#openWorkLed--">openWorkLed()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURAxDevice.html" title="class in com.rscja.team.qcom.urax">RKURAxDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#OTG_GPIO_OFF-java.lang.String-">OTG_GPIO_OFF(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#OTG_GPIO_OFF-java.lang.String-">OTG_GPIO_OFF(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#OTG_GPIO_ON-java.lang.String-">OTG_GPIO_ON(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#OTG_GPIO_ON-java.lang.String-">OTG_GPIO_ON(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html#output1Off--">output1Off()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio 3 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#output1Off--">output1Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio 3 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#output1Off--">output1Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#output1Off--">output1Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">gpio 3 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#output1Off--">output1Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">gpio 3 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#output1Off--">output1Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#output1Off--">output1Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>
<div class="block">gpio 3 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURA4Gpio.html#output1Off--">output1Off()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html#output1Off--">output1Off()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html#output1Off--">output1Off()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax">RKURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html#output1On--">output1On()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio 3 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#output1On--">output1On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio 3 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#output1On--">output1On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#output1On--">output1On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">gpio 3 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#output1On--">output1On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">gpio 3 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#output1On--">output1On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#output1On--">output1On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>
<div class="block">gpio 3 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURA4Gpio.html#output1On--">output1On()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html#output1On--">output1On()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html#output1On--">output1On()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax">RKURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html#output2Off--">output2Off()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio 4 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#output2Off--">output2Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio 4 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#output2Off--">output2Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#output2Off--">output2Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">gpio 4 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#output2Off--">output2Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">gpio 4 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#output2Off--">output2Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#output2Off--">output2Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>
<div class="block">gpio 4 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURA4Gpio.html#output2Off--">output2Off()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html#output2Off--">output2Off()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html#output2Off--">output2Off()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax">RKURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html#output2On--">output2On()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio 4 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#output2On--">output2On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio 4 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#output2On--">output2On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#output2On--">output2On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">gpio 4 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#output2On--">output2On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">gpio 4 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#output2On--">output2On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#output2On--">output2On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>
<div class="block">gpio 4 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURA4Gpio.html#output2On--">output2On()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html#output2On--">output2On()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html#output2On--">output2On()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax">RKURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html#output3Off--">output3Off()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio OptoCoupler3 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#output3Off--">output3Off()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio OptoCoupler3 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">gpio OptoCoupler3 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>
<div class="block">gpio OptoCoupler3 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a></dt>
<dd>
<div class="block">gpio OptoCoupler3 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURA4Gpio.html#output3Off--">output3Off()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html#output3Off--">output3Off()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax">RKURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html#output3On--">output3On()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio OptoCoupler3 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#output3On--">output3On()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#output3On--">output3On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio OptoCoupler3 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#output3On--">output3On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#output3On--">output3On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8.html#output3On--">output3On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#output3On--">output3On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#output3On--">output3On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#output3On--">output3On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">gpio OptoCoupler3 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#output3On--">output3On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#output3On--">output3On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>
<div class="block">gpio OptoCoupler3 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html#output3On--">output3On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html#output3On--">output3On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#output3On--">output3On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a></dt>
<dd>
<div class="block">gpio OptoCoupler3 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURA4Gpio.html#output3On--">output3On()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html#output3On--">output3On()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html#output3On--">output3On()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax">RKURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html#output4Off--">output4Off()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio OptoCoupler4 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#output4Off--">output4Off()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio OptoCoupler4 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">gpio OptoCoupler4 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>
<div class="block">gpio OptoCoupler4 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a></dt>
<dd>
<div class="block">gpio OptoCoupler4 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURA4Gpio.html#output4Off--">output4Off()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html#output4Off--">output4Off()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax">RKURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html#output4On--">output4On()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio OptoCoupler4 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#output4On--">output4On()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#output4On--">output4On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio OptoCoupler4 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#output4On--">output4On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#output4On--">output4On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8.html#output4On--">output4On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#output4On--">output4On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#output4On--">output4On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#output4On--">output4On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">gpio OptoCoupler4 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#output4On--">output4On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#output4On--">output4On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>
<div class="block">gpio OptoCoupler4 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html#output4On--">output4On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html#output4On--">output4On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#output4On--">output4On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a></dt>
<dd>
<div class="block">gpio OptoCoupler4 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURA4Gpio.html#output4On--">output4On()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html#output4On--">output4On()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html#output4On--">output4On()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax">RKURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFA4.html#outputOnAndOff-java.util.List-">outputOnAndOff(List&lt;GPOEntity&gt;)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IUHFA4</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#outputOnAndOff-java.util.List-">outputOnAndOff(List&lt;GPOEntity&gt;)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#outputOnAndOff-java.util.List-">outputOnAndOff(List&lt;GPOEntity&gt;)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">控制全部GPO输出</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#outputOnAndOff-java.util.List-">outputOnAndOff(List&lt;GPOEntity&gt;)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">控制全部GPO输出</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#outputOnAndOff-java.util.List-">outputOnAndOff(List&lt;GPOEntity&gt;)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">控制全部GPO输出</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#outputOnAndOff-java.util.List-">outputOnAndOff(List&lt;GPOEntity&gt;)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">控制全部GPO输出</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#outputOnAndOff-java.util.List-">outputOnAndOff(List&lt;GPOEntity&gt;)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#outputOnAndOff-java.util.List-">outputOnAndOff(List&lt;GPOEntity&gt;)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html#outputOnAndOff-java.util.List-">outputOnAndOff(List&lt;GPOEntity&gt;)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#outputOnAndOff-java.util.List-">outputOnAndOff(List&lt;GPOEntity&gt;)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#outputOnAndOffSendData-byte:A-">outputOnAndOffSendData(byte[])</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html#outputWgData0Off--">outputWgData0Off()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio WgData0 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#outputWgData0Off--">outputWgData0Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio WgData0 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#outputWgData0Off--">outputWgData0Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#outputWgData0Off--">outputWgData0Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#outputWgData0Off--">outputWgData0Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">gpio WgData0 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#outputWgData0Off--">outputWgData0Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#outputWgData0Off--">outputWgData0Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>
<div class="block">gpio WgData0 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURA4Gpio.html#outputWgData0Off--">outputWgData0Off()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html#outputWgData0Off--">outputWgData0Off()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html#outputWgData0Off--">outputWgData0Off()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax">RKURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html#outputWgData0On--">outputWgData0On()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio WgData0 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#outputWgData0On--">outputWgData0On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio WgData0 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#outputWgData0On--">outputWgData0On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#outputWgData0On--">outputWgData0On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#outputWgData0On--">outputWgData0On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">gpio WgData0 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#outputWgData0On--">outputWgData0On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#outputWgData0On--">outputWgData0On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>
<div class="block">gpio WgData0 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURA4Gpio.html#outputWgData0On--">outputWgData0On()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html#outputWgData0On--">outputWgData0On()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html#outputWgData0On--">outputWgData0On()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax">RKURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html#outputWgData1Off--">outputWgData1Off()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio WgData1 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#outputWgData1Off--">outputWgData1Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio WgData1 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#outputWgData1Off--">outputWgData1Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#outputWgData1Off--">outputWgData1Off()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#outputWgData1Off--">outputWgData1Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">gpio WgData1 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#outputWgData1Off--">outputWgData1Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#outputWgData1Off--">outputWgData1Off()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>
<div class="block">gpio WgData1 off</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURA4Gpio.html#outputWgData1Off--">outputWgData1Off()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html#outputWgData1Off--">outputWgData1Off()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html#outputWgData1Off--">outputWgData1Off()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax">RKURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html#outputWgData1On--">outputWgData1On()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio WgData1 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4.html#outputWgData1On--">outputWgData1On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></dt>
<dd>
<div class="block">gpio WgData1 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#outputWgData1On--">outputWgData1On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#outputWgData1On--">outputWgData1On()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#outputWgData1On--">outputWgData1On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></dt>
<dd>
<div class="block">gpio WgData1 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#outputWgData1On--">outputWgData1On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html#outputWgData1On--">outputWgData1On()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></dt>
<dd>
<div class="block">gpio WgData1 on</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/IURA4Gpio.html#outputWgData1On--">outputWgData1On()</a></span> - Method in interface com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html#outputWgData1On--">outputWgData1On()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html#outputWgData1On--">outputWgData1On()</a></span> - Method in class com.rscja.team.qcom.urax.<a href="../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax">RKURA4Gpio</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-14.html">Prev Letter</a></li>
<li><a href="index-16.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-15.html" target="_top">Frames</a></li>
<li><a href="index-15.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
