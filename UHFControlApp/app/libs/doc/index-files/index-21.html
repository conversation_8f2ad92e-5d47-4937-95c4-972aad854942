<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>U-Index</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="U-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-20.html">Prev Letter</a></li>
<li><a href="index-22.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-21.html" target="_top">Frames</a></li>
<li><a href="index-21.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;<a name="I:U">
<!--   -->
</a>
<h2 class="title">U</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#U">U</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UartSwitch-java.lang.String-int-">UartSwitch(String, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UartSwitch-java.lang.String-int-">UartSwitch(String, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHF706_CloseAndDisconnect--">UHF706_CloseAndDisconnect()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHF706_CloseAndDisconnect--">UHF706_CloseAndDisconnect()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHF706_OpenAndConnect-java.lang.String-">UHF706_OpenAndConnect(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHF706_OpenAndConnect-java.lang.String-">UHF706_OpenAndConnect(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/interfaces/IM775Authenticate.html#UHFAuthenticate-int-int-int-java.lang.String-">UHFAuthenticate(int, int, int, String)</a></span> - Method in interface com.rscja.custom.interfaces.<a href="../com/rscja/custom/interfaces/IM775Authenticate.html" title="interface in com.rscja.custom.interfaces">IM775Authenticate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/interfaces/IM775Authenticate.html#UHFAuthenticate--">UHFAuthenticate()</a></span> - Method in interface com.rscja.custom.interfaces.<a href="../com/rscja/custom/interfaces/IM775Authenticate.html" title="interface in com.rscja.custom.interfaces">IM775Authenticate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/M775Authenticate.html#UHFAuthenticate-int-int-int-java.lang.String-">UHFAuthenticate(int, int, int, String)</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/M775Authenticate.html" title="class in com.rscja.custom">M775Authenticate</a></dt>
<dd>
<div class="block">认证函数(Authentication function)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/M775Authenticate.html#UHFAuthenticate--">UHFAuthenticate()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/M775Authenticate.html" title="class in com.rscja.custom">M775Authenticate</a></dt>
<dd>
<div class="block">认证函数(Authentication function)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/custom/M775Authenticate_mtk.html#UHFAuthenticate-int-int-int-java.lang.String-">UHFAuthenticate(int, int, int, String)</a></span> - Method in class com.rscja.team.mtk.custom.<a href="../com/rscja/team/mtk/custom/M775Authenticate_mtk.html" title="class in com.rscja.team.mtk.custom">M775Authenticate_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/custom/M775Authenticate_mtk.html#UHFAuthenticate--">UHFAuthenticate()</a></span> - Method in class com.rscja.team.mtk.custom.<a href="../com/rscja/team/mtk/custom/M775Authenticate_mtk.html" title="class in com.rscja.team.mtk.custom">M775Authenticate_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/M775Authenticate_qcom.html#UHFAuthenticate-int-int-int-java.lang.String-">UHFAuthenticate(int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/M775Authenticate_qcom.html" title="class in com.rscja.team.qcom.custom">M775Authenticate_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/M775Authenticate_qcom.html#UHFAuthenticate--">UHFAuthenticate()</a></span> - Method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/M775Authenticate_qcom.html" title="class in com.rscja.team.qcom.custom">M775Authenticate_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFAuthenticateCommon-int-int-int-byte:A-int-int-byte:A-">UHFAuthenticateCommon(int, int, int, byte[], int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFAuthenticateCommon-int-int-int-byte:A-int-int-byte:A-">UHFAuthenticateCommon(int, int, int, byte[], int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">UhfBase</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UhfBase.html#UhfBase--">UhfBase()</a></span> - Constructor for class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">UhfBase.ErrorCode</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html" title="class in com.rscja.team.qcom.uhfparse"><span class="typeNameLink">UHFBLEProtocolParse</span></a> - Class in <a href="../com/rscja/team/qcom/uhfparse/package-summary.html">com.rscja.team.qcom.uhfparse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html#UHFBLEProtocolParse--">UHFBLEProtocolParse()</a></span> - Constructor for class com.rscja.team.qcom.uhfparse.<a href="../com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html" title="class in com.rscja.team.qcom.uhfparse">UHFBLEProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockEraseDataRecvData-byte:A-int-">UHFBlockEraseDataRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBlockEraseDataRecvData-byte:A-int-">UHFBlockEraseDataRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockEraseDataSendData-byte:A-char-int-int-byte:A-char-int-char-">UHFBlockEraseDataSendData(byte[], char, int, int, byte[], char, int, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBlockEraseDataSendData-byte:A-char-int-int-byte:A-char-int-char-">UHFBlockEraseDataSendData(byte[], char, int, int, byte[], char, int, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">数据块操作</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">数据块操作</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">数据块操作(Data block operation)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">数据块操作(Data block operation)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">数据块操作(Data block operation)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">数据块操作(Data block operation)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">数据块操作</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockPermalock-char:A-char-char-char-char:A-char-char-char-char-char:A-">UHFBlockPermalock(char[], char, char, char, char[], char, char, char, char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBlockPermalock-char:A-char-char-char-char:A-char-char-char-char-char:A-">UHFBlockPermalock(char[], char, char, char, char[], char, char, char, char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">数据块操作</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock(String, int, int, int, String, int, int, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockPermalock_Ex-char:A-char-int-int-char:A-char-char-int-char-byte:A-">UHFBlockPermalock_Ex(char[], char, int, int, char[], char, char, int, char, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBlockPermalock_Ex-char:A-char-int-int-char:A-char-char-int-char-byte:A-">UHFBlockPermalock_Ex(char[], char, int, int, char[], char, char, int, char, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockWriteData-char:A-char-int-int-char:A-char-int-int-char:A-">UHFBlockWriteData(char[], char, int, int, char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBlockWriteData-char:A-char-int-int-char:A-char-int-int-char:A-">UHFBlockWriteData(char[], char, int, int, char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockWriteDataRecvData-byte:A-int-">UHFBlockWriteDataRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBlockWriteDataRecvData-byte:A-int-">UHFBlockWriteDataRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockWriteDataSendData-byte:A-char-int-int-byte:A-char-int-char-byte:A-">UHFBlockWriteDataSendData(byte[], char, int, int, byte[], char, int, char, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBlockWriteDataSendData-byte:A-char-int-int-byte:A-char-int-char-byte:A-">UHFBlockWriteDataSendData(byte[], char, int, int, byte[], char, int, char, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTAuthentication-char:A-char-int-int-char:A-char-int-int-char:A-char:A-">UHFBTAuthentication(char[], char, int, int, char[], char, int, int, char[], char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTAuthentication-char:A-char-int-int-char:A-char-int-int-char:A-char:A-">UHFBTAuthentication(char[], char, int, int, char[], char, int, int, char[], char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTDeleteAllTagToFlash--">UHFBTDeleteAllTagToFlash()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTDeleteAllTagToFlash--">UHFBTDeleteAllTagToFlash()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTDeleteAllTagToFlashRecvData-byte:A-int-">UHFBTDeleteAllTagToFlashRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTDeleteAllTagToFlashRecvData-byte:A-int-">UHFBTDeleteAllTagToFlashRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTDeleteAllTagToFlashSendData--">UHFBTDeleteAllTagToFlashSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTDeleteAllTagToFlashSendData--">UHFBTDeleteAllTagToFlashSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTEncReadUser-int-int-byte:A-">UHFBTEncReadUser(int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTEncReadUser-int-int-byte:A-">UHFBTEncReadUser(int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTEncWriteUser-int-int-byte:A-">UHFBTEncWriteUser(int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTEncWriteUser-int-int-byte:A-">UHFBTEncWriteUser(int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTEndUpdate--">UHFBTEndUpdate()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTEndUpdate--">UHFBTEndUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTEraseData-char:A-char-int-char-char:A-char-int-int-">UHFBTEraseData(char[], char, int, char, char[], char, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTEraseData-char:A-char-int-char-char:A-char-int-int-">UHFBTEraseData(char[], char, int, char, char[], char, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTFreHopSet-int-">UHFBTFreHopSet(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTFreHopSet-int-">UHFBTFreHopSet(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetAllTagNumFromFlash--">UHFBTGetAllTagNumFromFlash()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetAllTagNumFromFlash--">UHFBTGetAllTagNumFromFlash()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetAllTagNumFromFlashRecvData-byte:A-int-">UHFBTGetAllTagNumFromFlashRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetAllTagNumFromFlashRecvData-byte:A-int-">UHFBTGetAllTagNumFromFlashRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetAllTagNumFromFlashSendData--">UHFBTGetAllTagNumFromFlashSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetAllTagNumFromFlashSendData--">UHFBTGetAllTagNumFromFlashSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetBarcode-byte:A-">UHFBTGetBarcode(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetBarcode-byte:A-">UHFBTGetBarcode(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetBattery--">UHFBTGetBattery()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetBattery--">UHFBTGetBattery()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetCW--">UHFBTGetCW()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetCW--">UHFBTGetCW()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetGen2-byte:A-">UHFBTGetGen2(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetGen2-byte:A-">UHFBTGetGen2(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetPower--">UHFBTGetPower()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetPower--">UHFBTGetPower()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetPowerValueRecvData-byte:A-int-">UHFBTGetPowerValueRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetPowerValueRecvData-byte:A-int-">UHFBTGetPowerValueRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetSendCmd-byte:A-">UHFBTGetSendCmd(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetSendCmd-byte:A-">UHFBTGetSendCmd(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetTag-byte:A-int-">UHFBTGetTag(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetTag-byte:A-int-">UHFBTGetTag(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetTagDataFromFlash-byte:A-">UHFBTGetTagDataFromFlash(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetTagDataFromFlash-byte:A-">UHFBTGetTagDataFromFlash(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetTagDataFromFlashRecvData-byte:A-int-">UHFBTGetTagDataFromFlashRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetTagDataFromFlashRecvData-byte:A-int-">UHFBTGetTagDataFromFlashRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetTagDataFromFlashSendData--">UHFBTGetTagDataFromFlashSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTGetTagDataFromFlashSendData--">UHFBTGetTagDataFromFlashSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTInventorySingle-byte:A-">UHFBTInventorySingle(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTInventorySingle-byte:A-">UHFBTInventorySingle(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTKeydataMac-byte:A-byte:A-">UHFBTKeydataMac(byte[], byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTKeydataMac-byte:A-byte:A-">UHFBTKeydataMac(byte[], byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTKill-char:A-char-int-int-char:A-">UHFBTKill(char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTKill-char:A-char-int-int-char:A-">UHFBTKill(char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTLockMemEx-char:A-char-int-int-char:A-char:A-">UHFBTLockMemEx(char[], char, int, int, char[], char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTLockMemEx-char:A-char-int-int-char:A-char:A-">UHFBTLockMemEx(char[], char, int, int, char[], char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTOpen2DRecvData-byte:A-int-">UHFBTOpen2DRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTOpen2DRecvData-byte:A-int-">UHFBTOpen2DRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTR2000Temperature-byte:A-">UHFBTR2000Temperature(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTR2000Temperature-byte:A-">UHFBTR2000Temperature(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTR2000Version-byte:A-">UHFBTR2000Version(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTR2000Version-byte:A-">UHFBTR2000Version(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTReadData-char:A-char-int-int-char:A-char-int-char-">UHFBTReadData(char[], char, int, int, char[], char, int, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTReadData-char:A-char-int-int-char:A-char-int-char-">UHFBTReadData(char[], char, int, int, char[], char, int, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTReadEpcTidUserMode-int-int-byte:A-">UHFBTReadEpcTidUserMode(int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTReadEpcTidUserMode-int-int-byte:A-">UHFBTReadEpcTidUserMode(int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTReBootAPP-char-">UHFBTReBootAPP(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTReBootAPP-char-">UHFBTReBootAPP(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetBeep-byte-">UHFBTSetBeep(byte)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTSetBeep-byte-">UHFBTSetBeep(byte)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetBeepRecvData-byte:A-int-">UHFBTSetBeepRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTSetBeepRecvData-byte:A-int-">UHFBTSetBeepRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetCW-int-">UHFBTSetCW(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTSetCW-int-">UHFBTSetCW(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetEpcTidUserMode-int-int-int-int-">UHFBTSetEpcTidUserMode(int, int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTSetEpcTidUserMode-int-int-int-int-">UHFBTSetEpcTidUserMode(int, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetGen2-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">UHFBTSetGen2(char, char, char, char, char, char, char, char, char, char, char, char, char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTSetGen2-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">UHFBTSetGen2(char, char, char, char, char, char, char, char, char, char, char, char, char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetPower-byte-">UHFBTSetPower(byte)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTSetPower-byte-">UHFBTSetPower(byte)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetProtocolType-int-">UHFBTSetProtocolType(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTSetProtocolType-int-">UHFBTSetProtocolType(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetR6Workmode-int-">UHFBTSetR6Workmode(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTSetR6Workmode-int-">UHFBTSetR6Workmode(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTStartInventory--">UHFBTStartInventory()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTStartInventory--">UHFBTStartInventory()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTStartUpdate--">UHFBTStartUpdate()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTStartUpdate--">UHFBTStartUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSTM32Version-byte:A-">UHFBTSTM32Version(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTSTM32Version-byte:A-">UHFBTSTM32Version(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTStopInventory--">UHFBTStopInventory()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTStopInventory--">UHFBTStopInventory()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTUHFGBTagLock-char:A-char-int-int-char:A-int-int-int-">UHFBTUHFGBTagLock(char[], char, int, int, char[], int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTUHFGBTagLock-char:A-char-int-int-char:A-int-int-int-">UHFBTUHFGBTagLock(char[], char, int, int, char[], int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTUHFGetProtocolType--">UHFBTUHFGetProtocolType()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTUHFGetProtocolType--">UHFBTUHFGetProtocolType()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTUpdateData-byte:A-">UHFBTUpdateData(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTUpdateData-byte:A-">UHFBTUpdateData(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTWriteData-char:A-char-int-int-char:A-char-int-int-char:A-">UHFBTWriteData(char[], char, int, int, char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFBTWriteData-char:A-char-int-int-char:A-char-int-int-char:A-">UHFBTWriteData(char[], char, int, int, char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFCheckOpMode-int-int-int-byte:A-">UHFCheckOpMode(int, int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFCheckOpMode-int-int-int-byte:A-">UHFCheckOpMode(int, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFCloseAndDisconnect--">UHFCloseAndDisconnect()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFCloseAndDisconnect--">UHFCloseAndDisconnect()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFCSYX.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFCSYX</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>
<div class="block">MQTT是长沙盈芯，定制接口</div>
</dd>
<dt><a href="../com/rscja/custom/UHFCSYX.IUHFInventoryCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFCSYX.IUHFInventoryCallback</span></a> - Interface in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFCSYX.TagAuthenticationResponseInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFCSYX.TagAuthenticationResponseInfo</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFCSYX_A4NetWork.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFCSYX_A4NetWork</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFCSYX_A4NetWork.html#UHFCSYX_A4NetWork--">UHFCSYX_A4NetWork()</a></span> - Constructor for class com.rscja.custom.<a href="../com/rscja/custom/UHFCSYX_A4NetWork.html" title="class in com.rscja.custom">UHFCSYX_A4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/custom/UHFCSYX_qcom.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">UHFCSYX_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFCSYXForURx.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFCSYXForURx</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">UHFDataHandleBase</span></a> - Class in <a href="../com/rscja/team/qcom/uhfhandler/package-summary.html">com.rscja.team.qcom.uhfhandler</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#UHFDataHandleBase--">UHFDataHandleBase()</a></span> - Constructor for class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">UHFDataHandleBase.CMDInfo</span></a> - Class in <a href="../com/rscja/team/qcom/uhfhandler/package-summary.html">com.rscja.team.qcom.uhfhandler</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFDeactivate-int-char:A-char-int-int-char:A-">UHFDeactivate(int, char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFDeactivate-int-char:A-char-int-int-char:A-">UHFDeactivate(int, char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFDecryptSM4-char-char:A-byte:A-">UHFDecryptSM4(char, char[], byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFDecryptSM4-char-char:A-byte:A-">UHFDecryptSM4(char, char[], byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFDecSM4-byte:A-int-">UHFDecSM4(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFDecSM4-byte:A-int-">UHFDecSM4(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFDecUSER-int-int-">UHFDecUSER(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFDecUSER-int-int-">UHFDecUSER(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFDwell-int-int-">UHFDwell(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFDwell-int-int-">UHFDwell(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFEncryptSM4-char-char:A-byte:A-">UHFEncryptSM4(char, char[], byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFEncryptSM4-char-char:A-byte:A-">UHFEncryptSM4(char, char[], byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFEncSM4-byte:A-int-">UHFEncSM4(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFEncSM4-byte:A-int-">UHFEncSM4(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFEncUSER-int-int-byte:A-">UHFEncUSER(int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFEncUSER-int-int-byte:A-">UHFEncUSER(int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFEraseData-char:A-char-int-char-char:A-">UHFEraseData(char[], char, int, char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFEraseData-char:A-char-int-char-char:A-">UHFEraseData(char[], char, int, char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFEraseDataEx-char:A-char-int-char-char:A-char-int-int-">UHFEraseDataEx(char[], char, int, char, char[], char, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFEraseDataEx-char:A-char-int-char-char:A-char-int-int-">UHFEraseDataEx(char[], char, int, char, char[], char, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFEraseDataSingle-char:A-char-int-char-">UHFEraseDataSingle(char[], char, int, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFEraseDataSingle-char:A-char-int-char-">UHFEraseDataSingle(char[], char, int, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFFlafCrcOff--">UHFFlafCrcOff()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFFlafCrcOff--">UHFFlafCrcOff()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFFlagCrcOn--">UHFFlagCrcOn()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFFlagCrcOn--">UHFFlagCrcOn()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFFree-java.lang.String-">UHFFree(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFFree-java.lang.String-">UHFFree(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGBTagLock-char:A-char-int-int-char:A-int-int-int-">UHFGBTagLock(char[], char, int, int, char[], int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGBTagLock-char:A-char-int-int-char:A-int-int-int-">UHFGBTagLock(char[], char, int, int, char[], int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGBTagLockRecvData-byte:A-int-">UHFGBTagLockRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGBTagLockRecvData-byte:A-int-">UHFGBTagLockRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGBTagLockSendData-byte:A-char-int-int-byte:A-char-char-char-">UHFGBTagLockSendData(byte[], char, int, int, byte[], char, char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGBTagLockSendData-byte:A-char-int-int-byte:A-char-char-char-">UHFGBTagLockSendData(byte[], char, int, int, byte[], char, char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetANT--">UHFGetANT()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetANT-byte:A-">UHFGetANT(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：获取天线设置
 输出：buf--2bytes, 共16bits,</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetANTConnectState-byte:A-">UHFGetANTConnectState(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：获取天线设置
 输出：buf--2bytes, 共16bits,</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetANTPower-byte:A-int:A-">UHFGetANTPower(byte[], int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetANTRecvData-byte:A-int-">UHFGetANTRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetANTRecvData-byte:A-int-">UHFGetANTRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetANTSendData--">UHFGetANTSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：获取天线设置
 输出：buf--2bytes, 共16bits,</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetANTSendData--">UHFGetANTSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：获取天线设置
 输出：buf--2bytes, 共16bits,</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetANTState-byte:A-">UHFGetANTState(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetANTWorkTime-byte-int:A-">UHFGetANTWorkTime(byte, int[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：获取天线工作时间
 输入：antnum -- 天线号
 输出：WorkTime -- 工作时间 ，单位ms, 范围 10-65535ms
 返回：0：获取成功     -1：获取失败</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetANTWorkTime-byte-int:A-">UHFGetANTWorkTime(byte, int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：获取天线工作时间
 输入：antnum -- 天线号
 输出：WorkTime -- 工作时间 ，单位ms, 范围 10-65535ms
 返回：0：获取成功     -1：获取失败</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetBeep--">UHFGetBeep()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetBeep--">UHFGetBeep()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetBID--">UHFGetBID()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetBID--">UHFGetBID()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetBTFrequency--">UHFGetBTFrequency()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetBTFrequency--">UHFGetBTFrequency()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetCalibrationData-char:A-char-char:A-">UHFGetCalibrationData(char[], char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetCalibrationData-char:A-char-char:A-">UHFGetCalibrationData(char[], char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetCW--">UHFGetCW()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetCW--">UHFGetCW()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetCWRecvData-byte:A-int-">UHFGetCWRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetCWRecvData-byte:A-int-">UHFGetCWRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetCWSendData--">UHFGetCWSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetCWSendData--">UHFGetCWSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetDestIp-byte:A-int:A-">UHFGetDestIp(byte[], int[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetDestIP-byte:A-int:A-">UHFGetDestIP(byte[], int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetEPCTIDMode--">UHFGetEPCTIDMode()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetEPCTIDMode--">UHFGetEPCTIDMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetEPCTIDModeRecvData-byte:A-int-">UHFGetEPCTIDModeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetEPCTIDModeRecvData-byte:A-int-">UHFGetEPCTIDModeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetEPCTIDModeSendData-char-char-">UHFGetEPCTIDModeSendData(char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetEPCTIDModeSendData-char-char-">UHFGetEPCTIDModeSendData(char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetEPCTIDUSERAddrLength--">UHFGetEPCTIDUSERAddrLength()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：设置EPC+TID+USER 区地址和长度

 输出：
     char[0] : 状态，0成功
     char[1] : 数据长度，5
     char[2]  盘点模式： 0x00为盘点EPC； 0x01为盘点EPC+TID； 0x02为EPC+TID+USER
     char[3]  TID地址：盘点TID的起始地址，单位word（2 Byte），模式为0x01时有效

     char[4]  TID长度：盘点TID的长度，单位word（2 Byte），模式为0x01时有效
     char[5]  USER地址：盘点USER区的起始地址，单位word（2 Byte），模式为0x02时有效
     char[6]  USER长度：盘点USER区的长度，单位word（2 Byte），模式为0x02时有效</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetEPCTIDUSERAddrLength--">UHFGetEPCTIDUSERAddrLength()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：设置EPC+TID+USER 区地址和长度

 输出：
     char[0] : 状态，0成功
     char[1] : 数据长度，5
     char[2]  盘点模式： 0x00为盘点EPC； 0x01为盘点EPC+TID； 0x02为EPC+TID+USER
     char[3]  TID地址：盘点TID的起始地址，单位word（2 Byte），模式为0x01时有效

     char[4]  TID长度：盘点TID的长度，单位word（2 Byte），模式为0x01时有效
     char[5]  USER地址：盘点USER区的起始地址，单位word（2 Byte），模式为0x02时有效
     char[6]  USER长度：盘点USER区的长度，单位word（2 Byte），模式为0x02时有效</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetEPCTIDUSERMode--">UHFGetEPCTIDUSERMode()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetEPCTIDUSERMode--">UHFGetEPCTIDUSERMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetFastInventoryMode-int-int-">UHFGetFastInventoryMode(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetFastInventoryMode-int-int-">UHFGetFastInventoryMode(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetFrequency--">UHFGetFrequency()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetFrequency--">UHFGetFrequency()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetFrequency_Ex--">UHFGetFrequency_Ex()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetFrequency_Ex--">UHFGetFrequency_Ex()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetGen2RecvData-byte:A-int-">UHFGetGen2RecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetGen2RecvData-byte:A-int-">UHFGetGen2RecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetGen2SendData--">UHFGetGen2SendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetGen2SendData--">UHFGetGen2SendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetHardwareVersionType--">UHFGetHardwareVersionType()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetHardwareVersionType--">UHFGetHardwareVersionType()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetHwType--">UHFGetHwType()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetHwType--">UHFGetHwType()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetHwTypeM3--">UHFGetHwTypeM3()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetHwTypeM3--">UHFGetHwTypeM3()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetIoControl--">UHFGetIoControl()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：获取继电器和 IO 控制输出设置状态
     返回值：null -- 执行失败
     data：2字节，output1: 0:低电平   1：高电平 output2: 0:低电平   1：高电平</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetIoControl-byte:A-">UHFGetIoControl(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：获取继电器和 IO 控制输出设置状态
     返回值：null -- 执行失败
     data：2字节，output1: 0:低电平   1：高电平 output2: 0:低电平   1：高电平</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetIp-byte:A-int:A-byte:A-byte:A-">UHFGetIp(byte[], int[], byte[], byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetIp-byte:A-int:A-byte:A-byte:A-">UHFGetIp(byte[], int[], byte[], byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetIpRecvData-byte:A-int-">UHFGetIpRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetIpRecvData-byte:A-int-">UHFGetIpRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetIpSendData--">UHFGetIpSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetIpSendData--">UHFGetIpSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetMode--">UHFGetMode()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetMode--">UHFGetMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetMultiDataReceived--">UHFGetMultiDataReceived()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetMultiDataReceived--">UHFGetMultiDataReceived()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetOnChipRSSI-char:A-char-char:A-">UHFGetOnChipRSSI(char[], char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetOnChipRSSI-char:A-char-char:A-">UHFGetOnChipRSSI(char[], char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetOnChipRSSIAndTempCode-char:A-char-char:A-">UHFGetOnChipRSSIAndTempCode(char[], char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetOnChipRSSIAndTempCode-char:A-char-char:A-">UHFGetOnChipRSSIAndTempCode(char[], char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetParam-char-char:A-">UHFGetParam(char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetParam-char-char:A-">UHFGetParam(char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetPower--">UHFGetPower()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetPower--">UHFGetPower()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetPowerRecvData-byte:A-int-">UHFGetPowerRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetPowerRecvData-byte:A-int-">UHFGetPowerRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetPowerSendData--">UHFGetPowerSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetPowerSendData--">UHFGetPowerSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetProtocolType--">UHFGetProtocolType()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetProtocolType--">UHFGetProtocolType()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetProtocolTypeRecvData-byte:A-int-">UHFGetProtocolTypeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetProtocolTypeRecvData-byte:A-int-">UHFGetProtocolTypeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetProtocolTypeSendData--">UHFGetProtocolTypeSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetProtocolTypeSendData--">UHFGetProtocolTypeSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetPwm--">UHFGetPwm()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetPwm--">UHFGetPwm()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetReaderBeepRecvData-byte:A-int-">UHFGetReaderBeepRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetReaderBeepRecvData-byte:A-int-">UHFGetReaderBeepRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetReaderBeepSendData--">UHFGetReaderBeepSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetReaderBeepSendData--">UHFGetReaderBeepSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetReceived--">UHFGetReceived()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetReceived--">UHFGetReceived()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetReceived_EX--">UHFGetReceived_EX()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetReceived_EX--">UHFGetReceived_EX()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetReceived_EX2-char:A-">UHFGetReceived_EX2(char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetReceived_EX2-byte:A-">UHFGetReceived_EX2(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetReceived_EX_R2000--">UHFGetReceived_EX_R2000()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetReceived_EX_R2000--">UHFGetReceived_EX_R2000()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetRegionRecvData-byte:A-int-">UHFGetRegionRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetRegionRecvData-byte:A-int-">UHFGetRegionRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetRegionSendData--">UHFGetRegionSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetRegionSendData--">UHFGetRegionSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetRFLink--">UHFGetRFLink()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetRFLink--">UHFGetRFLink()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetRSSI--">UHFGetRSSI()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetRSSI--">UHFGetRSSI()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSensorCode-char:A-char-char:A-">UHFGetSensorCode(char[], char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">zjx 20191127  温度标签增加通讯命令   -------- start --------</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetSensorCode-char:A-char-char:A-">UHFGetSensorCode(char[], char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">zjx 20191127  温度标签增加通讯命令   -------- start --------</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSingelMode--">UHFGetSingelMode()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetSingelMode--">UHFGetSingelMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSleepTimeRecvData-byte:A-int-">UHFGetSleepTimeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetSleepTimeRecvData-byte:A-int-">UHFGetSleepTimeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSleepTimeSendData--">UHFGetSleepTimeSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetSleepTimeSendData--">UHFGetSleepTimeSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGETSM4-byte:A-byte:A-byte:A-">UHFGETSM4(byte[], byte[], byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSM4--">UHFGetSM4()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGETSM4-byte:A-byte:A-byte:A-">UHFGETSM4(byte[], byte[], byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetSM4--">UHFGetSM4()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSoftwareVersionRecvData-byte:A-int-">UHFGetSoftwareVersionRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetSoftwareVersionRecvData-byte:A-int-">UHFGetSoftwareVersionRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSoftwareVersionSendData--">UHFGetSoftwareVersionSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetSoftwareVersionSendData--">UHFGetSoftwareVersionSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSTM32VersionRecvData-byte:A-int-">UHFGetSTM32VersionRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetSTM32VersionRecvData-byte:A-int-">UHFGetSTM32VersionRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSTM32VersionSendData--">UHFGetSTM32VersionSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetSTM32VersionSendData--">UHFGetSTM32VersionSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTagfocusRecvData-byte:A-int-">UHFGetTagfocusRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetTagfocusRecvData-byte:A-int-">UHFGetTagfocusRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTagfocusSendData--">UHFGetTagfocusSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetTagfocusSendData--">UHFGetTagfocusSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTagsDataRecvData-byte:A-int-">UHFGetTagsDataRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetTagsDataRecvData-byte:A-int-">UHFGetTagsDataRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTagsDataSendData--">UHFGetTagsDataSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetTagsDataSendData--">UHFGetTagsDataSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTemperatureRecvData-byte:A-int-">UHFGetTemperatureRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetTemperatureRecvData-byte:A-int-">UHFGetTemperatureRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTemperatureSendData--">UHFGetTemperatureSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetTemperatureSendData--">UHFGetTemperatureSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTempertureCode-char:A-char-char:A-">UHFGetTempertureCode(char[], char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetTempertureCode-char:A-char-char:A-">UHFGetTempertureCode(char[], char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTempProtectVal--">UHFGetTempProtectVal()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetTempProtectVal--">UHFGetTempProtectVal()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/interfaces/IRFIDWithUHFJieCe.html#uhfGetTempTagReceived--">uhfGetTempTagReceived()</a></span> - Method in interface com.rscja.custom.interfaces.<a href="../com/rscja/custom/interfaces/IRFIDWithUHFJieCe.html" title="interface in com.rscja.custom.interfaces">IRFIDWithUHFJieCe</a></dt>
<dd>
<div class="block">获取温度标签的所有信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/RFIDWithUHFJieCe.html#uhfGetTempTagReceived--">uhfGetTempTagReceived()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/RFIDWithUHFJieCe.html" title="class in com.rscja.custom">RFIDWithUHFJieCe</a></dt>
<dd>
<div class="block">获取温度标签的所有信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTempTagReceived-char:A-">UHFGetTempTagReceived(char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/RFIDWithUHFJieCe_qcom.html#uhfGetTempTagReceived--">uhfGetTempTagReceived()</a></span> - Method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/RFIDWithUHFJieCe_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFJieCe_qcom</a></dt>
<dd>
<div class="block">获取温度标签的所有信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetTempTagReceived-char:A-">UHFGetTempTagReceived(char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTempTagReceived2-char:A-">UHFGetTempTagReceived2(char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetTempTagReceived2-char:A-">UHFGetTempTagReceived2(char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/interfaces/IRFIDWithUHFJieCe.html#uhfGetTempTagReceived_OnChipRSSI_TempeCode--">uhfGetTempTagReceived_OnChipRSSI_TempeCode()</a></span> - Method in interface com.rscja.custom.interfaces.<a href="../com/rscja/custom/interfaces/IRFIDWithUHFJieCe.html" title="interface in com.rscja.custom.interfaces">IRFIDWithUHFJieCe</a></dt>
<dd>
<div class="block">获取温度标签数据,不包含calibrationData 、sensorCode的信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/RFIDWithUHFJieCe.html#uhfGetTempTagReceived_OnChipRSSI_TempeCode--">uhfGetTempTagReceived_OnChipRSSI_TempeCode()</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/RFIDWithUHFJieCe.html" title="class in com.rscja.custom">RFIDWithUHFJieCe</a></dt>
<dd>
<div class="block">获取温度标签数据,不包含calibrationData 、sensorCode的信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/RFIDWithUHFJieCe_qcom.html#uhfGetTempTagReceived_OnChipRSSI_TempeCode--">uhfGetTempTagReceived_OnChipRSSI_TempeCode()</a></span> - Method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/RFIDWithUHFJieCe_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFJieCe_qcom</a></dt>
<dd>
<div class="block">获取温度标签数据,不包含calibrationData 、sensorCode的信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetTriggerWorkModePara-byte:A-int:A-int:A-byte:A-">UHFGetTriggerWorkModePara(byte[], int[], int[], byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetVersion--">UHFGetVersion()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetVersion--">UHFGetVersion()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetWorkMode--">UHFGetWorkMode()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFGetWorkMode--">UHFGetWorkMode()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInit-java.lang.String-">UHFInit(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInit-java.lang.String-">UHFInit(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInitRegFile-int-int-int-byte:A-">UHFInitRegFile(int, int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInitRegFile-int-int-int-byte:A-">UHFInitRegFile(int, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventory-char-char-">UHFInventory(char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventory-char-char-">UHFInventory(char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventory_EX-char-char-">UHFInventory_EX(char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventory_EX-char-char-">UHFInventory_EX(char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventory_EX_BankPtrCnt-char-char-char-char-char-">UHFInventory_EX_BankPtrCnt(char, char, char, char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventory_EX_BankPtrCnt-char-char-char-char-char-">UHFInventory_EX_BankPtrCnt(char, char, char, char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventory_EX_cnt-char-char-char-">UHFInventory_EX_cnt(char, char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventory_EX_cnt-char-char-char-">UHFInventory_EX_cnt(char, char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventoryBank-char:A-char-int-int-">UHFInventoryBank(char[], char, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventoryBank-char:A-char-int-int-">UHFInventoryBank(char[], char, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventoryBID-char-char-">UHFInventoryBID(char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventoryBID-char-char-">UHFInventoryBID(char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventoryRecvData-byte:A-int-">UHFInventoryRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventoryRecvData-byte:A-int-">UHFInventoryRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySendData--">UHFInventorySendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventorySendData--">UHFInventorySendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingle--">UHFInventorySingle()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventorySingle--">UHFInventorySingle()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingle_EX--">UHFInventorySingle_EX()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventorySingle_EX--">UHFInventorySingle_EX()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingle_R2000--">UHFInventorySingle_R2000()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventorySingle_R2000--">UHFInventorySingle_R2000()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingle_sf--">UHFInventorySingle_sf()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventorySingle_sf--">UHFInventorySingle_sf()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingle_tc-char-">UHFInventorySingle_tc(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventorySingle_tc-char-">UHFInventorySingle_tc(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingleEPCTIDUSER-int-int-">UHFInventorySingleEPCTIDUSER(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventorySingleEPCTIDUSER-int-int-">UHFInventorySingleEPCTIDUSER(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingleRecvData-byte:A-int-">UHFInventorySingleRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventorySingleRecvData-byte:A-int-">UHFInventorySingleRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingleSendData--">UHFInventorySingleSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventorySingleSendData--">UHFInventorySingleSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventoryTempTag-char-char:A-">UHFInventoryTempTag(char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventoryTempTag-char-char:A-">UHFInventoryTempTag(char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventoryTempTag2-char-char:A-">UHFInventoryTempTag2(char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventoryTempTag2-char-char:A-">UHFInventoryTempTag2(char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventoryTID-char-char-">UHFInventoryTID(char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFInventoryTID-char-char-">UHFInventoryTID(char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">uhf进入boot模式 ，开始升级之前需要调用此函数 <br>
 UHF enter boot mode, need to call this formula before upgrade</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">uhf进入boot模式 ，开始升级之前需要调用此函数 <br>
 UHF enter boot mode, need to call this formula before upgrade</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">uhf进入boot模式 ，开始升级之前需要调用此函数 <br>
 UHF enter boot mode, need to call this formula before upgrade</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">uhf固件进入boot模式准备升级,调用此函数后需要延时2秒再调用 <a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#uhfStartUpdate--"><code>RFIDWithUHFA4NetWork.uhfStartUpdate()</code></a> (uhf firmware enters boot mode to prepare for upgrade)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">uhf固件进入boot模式准备升级,调用此函数后需要延时2秒再调用 <a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#uhfStartUpdate--"><code>RFIDWithUHFA4RS232.uhfStartUpdate()</code></a> (uhf firmware enters boot mode to prepare for upgrade)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">uhf固件进入boot模式准备升级,调用此函数后需要延时2秒再调用 <a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#uhfStartUpdate--"><code>RFIDWithUHFA8NetWork.uhfStartUpdate()</code></a> (uhf firmware enters boot mode to prepare for upgrade)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">uhf固件进入boot模式准备升级,调用此函数后需要延时2秒再调用 <a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#uhfStartUpdate--"><code>RFIDWithUHFA8RS232.uhfStartUpdate()</code></a> (uhf firmware enters boot mode to prepare for upgrade)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">uhf进入boot模式 ，开始升级之前需要调用此函数 <br>
 UHF enter boot mode, need to call this formula before upgrade</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFJump2Boot--">UHFJump2Boot()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFJump2Boot--">UHFJump2Boot()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">uhf进入boot模式 ，开始升级之前需要调用此函数 <br>
 UHF enter boot mode, need to call this formula before upgrade</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#uhfJump2Boot--">uhfJump2Boot()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#uhfJump2Boot-int-">uhfJump2Boot(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFJump2BootRecvData-byte:A-int-">UHFJump2BootRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFJump2BootRecvData-byte:A-int-">UHFJump2BootRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#uhfJump2BootSendData-char-">uhfJump2BootSendData(char)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFJump2BootSendData-char-">UHFJump2BootSendData(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFJump2BootSendData-char-">UHFJump2BootSendData(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#uhfJump2BootSendData-char-">uhfJump2BootSendData(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUhfBle.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFURx.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>
<div class="block">升级STM32主板需要调用此函数进入boot模式<br>
 Upgrade STM32 mainboard that need to call this function to enter boot mode.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>
<div class="block">升级STM32主板需要调用此函数进入boot模式<br>
 Upgrade STM32 mainboard that need to call this function to enter boot mode.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFJump2BootSTM32--">UHFJump2BootSTM32()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFJump2BootSTM32--">UHFJump2BootSTM32()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>
<div class="block">升级STM32主板需要调用此函数进入boot模式<br>
 Upgrade STM32 mainboard that need to call this function to enter boot mode.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#uhfJump2BootSTM32--">uhfJump2BootSTM32()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>
<div class="block">升级STM32主板需要调用此函数进入boot模式<br>
 Upgrade STM32 mainboard that need to call this function to enter boot mode.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#uhfJumpToMainboardBoot--">uhfJumpToMainboardBoot()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">升级STM32主板需要调用此函数进入boot模式<br>
 Upgrade STM32 mainboard that need to call this function to enter boot mode.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#uhfJumpToMainboardBoot--">uhfJumpToMainboardBoot()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#uhfJumpToMainboardBoot--">uhfJumpToMainboardBoot()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFKillTag-char:A-char:A-">UHFKillTag(char[], char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFKillTag-char:A-char:A-">UHFKillTag(char[], char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFKillTagEx-char:A-char-int-int-char:A-">UHFKillTagEx(char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFKillTagEx-char:A-char-int-int-char:A-">UHFKillTagEx(char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFKillTagRecvData-byte:A-int-">UHFKillTagRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFKillTagRecvData-byte:A-int-">UHFKillTagRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFKillTagSendData-byte:A-char-int-int-byte:A-">UHFKillTagSendData(byte[], char, int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFKillTagSendData-byte:A-char-int-int-byte:A-">UHFKillTagSendData(byte[], char, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFKillTagSingle-char:A-">UHFKillTagSingle(char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFKillTagSingle-char:A-">UHFKillTagSingle(char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFLedOnOff-java.lang.String-int-int-">UHFLedOnOff(String, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFLedOnOff-java.lang.String-int-int-">UHFLedOnOff(String, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/UhfLocation_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UhfLocation_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UhfLocation_qcom.html#UhfLocation_qcom-android.content.Context-com.rscja.deviceapi.interfaces.IUHF-">UhfLocation_qcom(Context, IUHF)</a></span> - Constructor for class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UhfLocation_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UhfLocation_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFLockMem-char:A-char:A-char:A-">UHFLockMem(char[], char[], char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFLockMem-char:A-char:A-char:A-">UHFLockMem(char[], char[], char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFLockMemEx-char:A-char-int-int-char:A-char:A-">UHFLockMemEx(char[], char, int, int, char[], char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFLockMemEx-char:A-char-int-int-char:A-char:A-">UHFLockMemEx(char[], char, int, int, char[], char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFLockMemSingle-char:A-char:A-">UHFLockMemSingle(char[], char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFLockMemSingle-char:A-char:A-">UHFLockMemSingle(char[], char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFLockTagRecvData-byte:A-int-">UHFLockTagRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFLockTagRecvData-byte:A-int-">UHFLockTagRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFLockTagSendData-byte:A-char-int-int-byte:A-byte:A-">UHFLockTagSendData(byte[], char, int, int, byte[], byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFLockTagSendData-byte:A-char-int-int-byte:A-byte:A-">UHFLockTagSendData(byte[], char, int, int, byte[], byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFOpenAndConnect-java.lang.String-">UHFOpenAndConnect(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFOpenAndConnect-java.lang.String-">UHFOpenAndConnect(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFOpenAndConnect_Ex-java.lang.String-">UHFOpenAndConnect_Ex(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFOpenAndConnect_Ex-java.lang.String-">UHFOpenAndConnect_Ex(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfPowerOff_11--">uhfPowerOff_11()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfPowerOn_11--">uhfPowerOn_11()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/socket/SocketManageA4.html#uhfProtocolParse">uhfProtocolParse</a></span> - Variable in class com.rscja.team.qcom.socket.<a href="../com/rscja/team/qcom/socket/SocketManageA4.html" title="class in com.rscja.team.qcom.socket">SocketManageA4</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/UHFProtocolParseBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">UHFProtocolParseBase</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UHFProtocolParseBase.html#UHFProtocolParseBase--">UHFProtocolParseBase()</a></span> - Constructor for class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UHFProtocolParseBase.html" title="class in com.rscja.deviceapi">UHFProtocolParseBase</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.UHFProtocolParseBase.html#UHFProtocolParseBase--">UHFProtocolParseBase()</a></span> - Constructor for class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Barcode1D_qcom.UHFProtocolParseBase.html" title="class in com.rscja.team.qcom.deviceapi">Barcode1D_qcom.UHFProtocolParseBase</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseBLE.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFProtocolParseBLE</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseBLE.html#UHFProtocolParseBLE--">UHFProtocolParseBLE()</a></span> - Constructor for class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseBLE.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseBLE</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseBleByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFProtocolParseBleByJava_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFProtocolParseByJava</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>
<div class="block">java 解析R2\R5\R6 uhf协议</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#UHFProtocolParseByJava--">UHFProtocolParseByJava()</a></span> - Constructor for class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA4_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UHFProtocolParseUrA4_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/rs232utils/package-summary.html">com.rscja.team.qcom.rs232utils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA4_qcom.html#UHFProtocolParseUrA4_qcom--">UHFProtocolParseUrA4_qcom()</a></span> - Constructor for class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA4_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrA4_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA8_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UHFProtocolParseUrA8_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/rs232utils/package-summary.html">com.rscja.team.qcom.rs232utils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA8_qcom.html#UHFProtocolParseUrA8_qcom--">UHFProtocolParseUrA8_qcom()</a></span> - Constructor for class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA8_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrA8_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UHFProtocolParseUrAxBase_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/rs232utils/package-summary.html">com.rscja.team.qcom.rs232utils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#UHFProtocolParseUrAxBase_qcom--">UHFProtocolParseUrAxBase_qcom()</a></span> - Constructor for class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFProtocolParseUrxByJava_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>
<div class="block">java 解析UR4 协议</div>
</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUSBByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFProtocolParseUSBByJava_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>
<div class="block">R6、R3 usb通讯特有数据解析</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUSBByJava_qcom.html#UHFProtocolParseUSBByJava_qcom--">UHFProtocolParseUSBByJava_qcom()</a></span> - Constructor for class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseUSBByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUSBByJava_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UHFProtocolParseUtils_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/rs232utils/package-summary.html">com.rscja.team.qcom.rs232utils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html#UHFProtocolParseUtils_qcom--">UHFProtocolParseUtils_qcom()</a></span> - Constructor for class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUtils_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.CMDInfo.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UHFProtocolParseUtils_qcom.CMDInfo</span></a> - Class in <a href="../com/rscja/team/qcom/rs232utils/package-summary.html">com.rscja.team.qcom.rs232utils</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse"><span class="typeNameLink">UHFProtocolProtocolParseBase</span></a> - Class in <a href="../com/rscja/team/qcom/uhfparse/package-summary.html">com.rscja.team.qcom.uhfparse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#UHFProtocolProtocolParseBase--">UHFProtocolProtocolParseBase()</a></span> - Constructor for class com.rscja.team.qcom.uhfparse.<a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfparse"><span class="typeNameLink">UHFProtocolProtocolParseBase.CMDInfo</span></a> - Class in <a href="../com/rscja/team/qcom/uhfparse/package-summary.html">com.rscja.team.qcom.uhfparse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/UhfRadarLocation_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UhfRadarLocation_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UhfRadarLocation_qcom.html#UhfRadarLocation_qcom-android.content.Context-com.rscja.deviceapi.interfaces.IUHF-">UhfRadarLocation_qcom(Context, IUHF)</a></span> - Constructor for class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UhfRadarLocation_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UhfRadarLocation_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadData-char:A-char-int-int-char:A-">UHFReadData(char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFReadData-char:A-char-int-int-char:A-">UHFReadData(char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadData_EX-char:A-char-int-char-char:A-">UHFReadData_EX(char[], char, int, char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFReadData_EX-char:A-char-int-char-char:A-">UHFReadData_EX(char[], char, int, char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadData_Ex2-char:A-char-int-int-char:A-char-int-int-">UHFReadData_Ex2(char[], char, int, int, char[], char, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFReadData_Ex2-char:A-char-int-int-char:A-char-int-int-">UHFReadData_Ex2(char[], char, int, int, char[], char, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadDataRecvData-byte:A-int-">UHFReadDataRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFReadDataRecvData-byte:A-int-">UHFReadDataRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadDataSendData-byte:A-char-int-int-byte:A-char-int-char-">UHFReadDataSendData(byte[], char, int, int, byte[], char, int, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFReadDataSendData-byte:A-char-int-int-byte:A-char-int-char-">UHFReadDataSendData(byte[], char, int, int, byte[], char, int, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadDataSingle-char:A-char-int-char-">UHFReadDataSingle(char[], char, int, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFReadDataSingle-char:A-char-int-char-">UHFReadDataSingle(char[], char, int, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadMultiTemp-int-int-int-byte:A-int-int-int:A-int:A-float:A-">UHFReadMultiTemp(int, int, int, byte[], int, int, int[], int[], float[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFReadMultiTemp-int-int-int-byte:A-int-int-int:A-int:A-float:A-">UHFReadMultiTemp(int, int, int, byte[], int, int, int[], int[], float[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadQTData-char:A-char-int-char-char:A-">UHFReadQTData(char[], char, int, char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFReadQTData-char:A-char-int-char-char:A-">UHFReadQTData(char[], char, int, char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadQTData_Ex-char:A-char-int-int-char:A-char-int-char-">UHFReadQTData_Ex(char[], char, int, int, char[], char, int, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFReadQTData_Ex-char:A-char-int-int-char:A-char-int-char-">UHFReadQTData_Ex(char[], char, int, int, char[], char, int, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadQTDataSingle-char:A-char-int-char-">UHFReadQTDataSingle(char[], char, int, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFReadQTDataSingle-char:A-char-int-char-">UHFReadQTDataSingle(char[], char, int, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadTagTemp-int-int-int-byte:A-float:A-">UHFReadTagTemp(int, int, int, byte[], float[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFReadTagTemp-int-int-int-byte:A-float:A-">UHFReadTagTemp(int, int, int, byte[], float[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadTagVoltage-int-int-int-byte:A-float:A-">UHFReadTagVoltage(int, int, int, byte[], float[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFReadTagVoltage-int-int-int-byte:A-float:A-">UHFReadTagVoltage(int, int, int, byte[], float[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/uhfhandler/UHFRxBLEDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">UHFRxBLEDataHandle</span></a> - Class in <a href="../com/rscja/team/qcom/uhfhandler/package-summary.html">com.rscja.team.qcom.uhfhandler</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFRxBLEDataHandle.html#UHFRxBLEDataHandle-com.rscja.deviceapi.interfaces.IBluetoothReader-com.rscja.deviceapi.interfaces.KeyEventCallback-">UHFRxBLEDataHandle(IBluetoothReader, KeyEventCallback)</a></span> - Constructor for class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFRxBLEDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">UHFRxBLEDataHandle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/uhfhandler/UHFRxUsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">UHFRxUsbDataHandle</span></a> - Class in <a href="../com/rscja/team/qcom/uhfhandler/package-summary.html">com.rscja.team.qcom.uhfhandler</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFRxUsbDataHandle.html#UHFRxUsbDataHandle--">UHFRxUsbDataHandle()</a></span> - Constructor for class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFRxUsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">UHFRxUsbDataHandle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetANT-int-char:A-">UHFSetANT(int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetANT-byte-byte:A-">UHFSetANT(byte, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：天线设置
 输入：saveflag -- 1:掉电保存，  0：不保存
 输入：buf--2bytes, 共16bits, 每bit 置1选择对应天线</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetANT-int-char:A-">UHFSetANT(int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetANT-byte-byte:A-">UHFSetANT(byte, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：天线设置
 输入：saveflag -- 1:掉电保存，  0：不保存
 输入：buf--2bytes, 共16bits, 每bit 置1选择对应天线</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetANTPower-int-byte-int-int-">UHFSetANTPower(int, byte, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetANTRecvData-byte:A-int-">UHFSetANTRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetANTRecvData-byte:A-int-">UHFSetANTRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetANTSendData-char-byte:A-int-">UHFSetANTSendData(char, byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：天线设置
 输入：saveflag -- 1:掉电保存，  0：不保存
 输入：buf--2bytes, 共16bits, 每bit 置1选择对应天线</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetANTSendData-char-byte:A-int-">UHFSetANTSendData(char, byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：天线设置
 输入：saveflag -- 1:掉电保存，  0：不保存
 输入：buf--2bytes, 共16bits, 每bit 置1选择对应天线</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetANTWorkTime-byte-byte-int-">UHFSetANTWorkTime(byte, byte, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：设置天线工作时间
 输入：antnum -- 天线号
 输入：saveflag -- 1:掉电保存， 0：不保存
 输入：WorkTime -- 工作时间 ，单位ms, 范围 10-65535ms
 返回：0：设置成功     -1：设置失败</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetANTWorkTime-byte-byte-int-">UHFSetANTWorkTime(byte, byte, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：设置天线工作时间
 输入：antnum -- 天线号
 输入：saveflag -- 1:掉电保存， 0：不保存
 输入：WorkTime -- 工作时间 ，单位ms, 范围 10-65535ms
 返回：0：设置成功     -1：设置失败</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetBeep-int-">UHFSetBeep(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetBeep-int-">UHFSetBeep(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetBTFrequency-byte-">UHFSetBTFrequency(byte)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetBTFrequency-byte-">UHFSetBTFrequency(byte)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetCW-char-">UHFSetCW(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetCW-char-">UHFSetCW(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetCWRecvData-byte:A-int-">UHFSetCWRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetCWRecvData-byte:A-int-">UHFSetCWRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetCWSendData-char-">UHFSetCWSendData(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetCWSendData-char-">UHFSetCWSendData(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetDestIp-byte:A-int-">UHFSetDestIp(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetDestIp-byte:A-int-">UHFSetDestIp(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetEPCTIDMode-char-">UHFSetEPCTIDMode(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetEPCTIDMode-char-">UHFSetEPCTIDMode(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetEPCTIDModeRecvData-byte:A-int-">UHFSetEPCTIDModeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetEPCTIDModeRecvData-byte:A-int-">UHFSetEPCTIDModeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetEPCTIDModeSendData-char-char-char-char-">UHFSetEPCTIDModeSendData(char, char, char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetEPCTIDModeSendData-char-char-char-char-">UHFSetEPCTIDModeSendData(char, char, char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetEPCTIDUSERAddrLength-char-char-char-char-char-">UHFSetEPCTIDUSERAddrLength(char, char, char, char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：设置EPC+TID+USER 区地址和长度
 输入：mode       盘点模式： 0x00为盘点EPC； 0x01为盘点EPC+TID； 0x02为EPC+TID+USER
     TID_addr    TID地址：盘点TID的起始地址，单位word（2 Byte），模式为0x01时有效

     TID_length  TID长度：盘点TID的长度，单位word（2 Byte），模式为0x01时有效
     USER_addr   USER地址：盘点USER区的起始地址，单位word（2 Byte），模式为0x02时有效
     USER_length USER长度：盘点USER区的长度，单位word（2 Byte），模式为0x02时有效</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetEPCTIDUSERAddrLength-char-char-char-char-char-">UHFSetEPCTIDUSERAddrLength(char, char, char, char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：设置EPC+TID+USER 区地址和长度
 输入：mode       盘点模式： 0x00为盘点EPC； 0x01为盘点EPC+TID； 0x02为EPC+TID+USER
     TID_addr    TID地址：盘点TID的起始地址，单位word（2 Byte），模式为0x01时有效

     TID_length  TID长度：盘点TID的长度，单位word（2 Byte），模式为0x01时有效
     USER_addr   USER地址：盘点USER区的起始地址，单位word（2 Byte），模式为0x02时有效
     USER_length USER长度：盘点USER区的长度，单位word（2 Byte），模式为0x02时有效</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetEPCUserMode-char-int-int-int-">UHFSetEPCUserMode(char, int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetEPCUserMode-char-int-int-int-">UHFSetEPCUserMode(char, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFastID-char-">UHFSetFastID(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetFastID-char-">UHFSetFastID(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFastInventoryMode-int-int-int-">UHFSetFastInventoryMode(int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetFastInventoryMode-int-int-int-">UHFSetFastInventoryMode(int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFilter-char-char-int-int-char:A-">UHFSetFilter(char, char, int, int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetFilter-char-char-int-int-char:A-">UHFSetFilter(char, char, int, int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFilter_Ex-char-char-int-int-char:A-">UHFSetFilter_Ex(char, char, int, int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetFilter_Ex-char-char-int-int-char:A-">UHFSetFilter_Ex(char, char, int, int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFilterRecvData-byte:A-int-">UHFSetFilterRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetFilterRecvData-byte:A-int-">UHFSetFilterRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFilterSendData-char-char-int-int-byte:A-">UHFSetFilterSendData(char, char, int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetFilterSendData-char-char-int-int-byte:A-">UHFSetFilterSendData(char, char, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFrequency-char-char-char:A-char-char-char-">UHFSetFrequency(char, char, char[], char, char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetFrequency-char-char-char:A-char-char-char-">UHFSetFrequency(char, char, char[], char, char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFrequency_EX-char-">UHFSetFrequency_EX(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetFrequency_EX-char-">UHFSetFrequency_EX(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetGen2RecvData-byte:A-int-">UHFSetGen2RecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetGen2RecvData-byte:A-int-">UHFSetGen2RecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetGen2SendData-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">UHFSetGen2SendData(char, char, char, char, char, char, char, char, char, char, char, char, char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetGen2SendData-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">UHFSetGen2SendData(char, char, char, char, char, char, char, char, char, char, char, char, char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetInventoryMode-int-int-int-int-int-int-int-int-">UHFSetInventoryMode(int, int, int, int, int, int, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetInventoryMode-int-int-int-int-int-int-int-int-">UHFSetInventoryMode(int, int, int, int, int, int, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetIOControl-byte-byte-byte-">UHFSetIOControl(byte, byte, byte)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：继电器和 IO 控制输出设置
 输入：output1:    0:低电平   1：高电平

     output2:    0:低电平   1：高电平

     outStatus： 0：断开    1：闭合

     返回值：0：设置成功     -1：设置失败</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetIOControl-byte-byte-byte-">UHFSetIOControl(byte, byte, byte)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：继电器和 IO 控制输出设置
 输入：output1:    0:低电平   1：高电平

     output2:    0:低电平   1：高电平

     outStatus： 0：断开    1：闭合

     返回值：0：设置成功     -1：设置失败</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetIp-byte:A-int-byte:A-byte:A-">UHFSetIp(byte[], int, byte[], byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetIp-byte:A-int-byte:A-byte:A-">UHFSetIp(byte[], int, byte[], byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetIpRecvData-byte:A-int-">UHFSetIpRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetIpRecvData-byte:A-int-">UHFSetIpRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetIpSendData-byte:A-byte:A-">UHFSetIpSendData(byte[], byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetIpSendData-byte:A-byte:A-">UHFSetIpSendData(byte[], byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetJumpFrequencyRecvData-byte:A-int-">UHFSetJumpFrequencyRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetJumpFrequencyRecvData-byte:A-int-">UHFSetJumpFrequencyRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetJumpFrequencySendData-char-int:A-">UHFSetJumpFrequencySendData(char, int[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetJumpFrequencySendData-char-int:A-">UHFSetJumpFrequencySendData(char, int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetMode-char-">UHFSetMode(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetMode-char-">UHFSetMode(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetParam-char-char:A-char:A-">UHFSetParam(char, char[], char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetParam-char-char:A-char:A-">UHFSetParam(char, char[], char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetPower-char-">UHFSetPower(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetPower-char-">UHFSetPower(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetPowerOnDynamic-char-">UHFSetPowerOnDynamic(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetPowerOnDynamic-char-">UHFSetPowerOnDynamic(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetPowerRecvData-byte:A-int-">UHFSetPowerRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetPowerRecvData-byte:A-int-">UHFSetPowerRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetPowerSendData-char-char-">UHFSetPowerSendData(char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetPowerSendData-char-char-">UHFSetPowerSendData(char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetProtocolType-int-">UHFSetProtocolType(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetProtocolType-int-">UHFSetProtocolType(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetProtocolTypeRecvData-byte:A-int-">UHFSetProtocolTypeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetProtocolTypeRecvData-byte:A-int-">UHFSetProtocolTypeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetProtocolTypeSendData-char-">UHFSetProtocolTypeSendData(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetProtocolTypeSendData-char-">UHFSetProtocolTypeSendData(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetPwm-int-int-">UHFSetPwm(int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetPwm-int-int-">UHFSetPwm(int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetReaderBeepRecvData-byte:A-int-">UHFSetReaderBeepRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetReaderBeepRecvData-byte:A-int-">UHFSetReaderBeepRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetReaderBeepSendData-char-">UHFSetReaderBeepSendData(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetReaderBeepSendData-char-">UHFSetReaderBeepSendData(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetRegionRecvData-byte:A-int-">UHFSetRegionRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetRegionRecvData-byte:A-int-">UHFSetRegionRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetRegionSendData-char-char-">UHFSetRegionSendData(char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetRegionSendData-char-char-">UHFSetRegionSendData(char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetRFLink-char-">UHFSetRFLink(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetRFLink-char-">UHFSetRFLink(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSingelMode-char-">UHFSetSingelMode(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetSingelMode-char-">UHFSetSingelMode(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSleepTimeRecvData-byte:A-int-">UHFSetSleepTimeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetSleepTimeRecvData-byte:A-int-">UHFSetSleepTimeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSleepTimeSendData-char-">UHFSetSleepTimeSendData(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetSleepTimeSendData-char-">UHFSetSleepTimeSendData(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSETSM4-char-char:A-char:A-">UHFSETSM4(char, char[], char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSM4-char-byte:A-byte:A-">UHFSetSM4(char, byte[], byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSETSM4-char-char:A-char:A-">UHFSETSM4(char, char[], char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetSM4-char-byte:A-byte:A-">UHFSetSM4(char, byte[], byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSoftReset--">UHFSetSoftReset()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：设置软件复位</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetSoftReset--">UHFSetSoftReset()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">功能：设置软件复位</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSoftResetRecvData-byte:A-int-">UHFSetSoftResetRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetSoftResetRecvData-byte:A-int-">UHFSetSoftResetRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSoftResetSendData--">UHFSetSoftResetSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">20200519 begin</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetSoftResetSendData--">UHFSetSoftResetSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>
<div class="block">20200519 begin</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetTagFocus-char-">UHFSetTagFocus(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetTagFocus-char-">UHFSetTagFocus(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetTagfocusRecvData-byte:A-int-">UHFSetTagfocusRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetTagfocusRecvData-byte:A-int-">UHFSetTagfocusRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetTagfocusSendData-char-">UHFSetTagfocusSendData(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetTagfocusSendData-char-">UHFSetTagfocusSendData(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetTempProtectVal-char-">UHFSetTempProtectVal(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetTempProtectVal-char-">UHFSetTempProtectVal(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetTriggerWorkModePara-byte-int-int-byte-">UHFSetTriggerWorkModePara(byte, int, int, byte)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetWorkMode-byte-">UHFSetWorkMode(byte)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFSetWorkMode-byte-">UHFSetWorkMode(byte)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFSFForUrxNetwork.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFSFForUrxNetwork</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFSFForUrxNetwork.html#UHFSFForUrxNetwork--">UHFSFForUrxNetwork()</a></span> - Constructor for class com.rscja.custom.<a href="../com/rscja/custom/UHFSFForUrxNetwork.html" title="class in com.rscja.custom">UHFSFForUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFSFForUrxNetwork.AntInfo</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStartLogging-int-int-int-byte:A-float-float-int-int-">UHFStartLogging(int, int, int, byte[], float, float, int, int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFStartLogging-int-int-int-byte:A-float-float-int-int-">UHFStartLogging(int, int, int, byte[], float, float, int, int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStartReadDataFromMultiTag-char-char:A-char-char-char-">UHFStartReadDataFromMultiTag(char, char[], char, char, char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFStartReadDataFromMultiTag-char-char:A-char-char-char-">UHFStartReadDataFromMultiTag(char, char[], char, char, char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">开始升级uhf模块  <br>
 Start upgrade UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">开始升级uhf模块  <br>
 Start upgrade UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">开始升级UHF固件,调用此函数后需要延时2秒再调用 <a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#uhfUpdating-byte:A-"><code>RFIDWithUHFA4NetWork.uhfUpdating(byte[] buff)</code></a>  (Start to upgrade UHF firmware)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">开始升级UHF固件,调用此函数后需要延时2秒再调用 <a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#uhfUpdating-byte:A-"><code>RFIDWithUHFA4RS232.uhfUpdating(byte[] buff)</code></a>  (Start to upgrade UHF firmware)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">开始升级UHF固件,调用此函数后需要延时2秒再调用 <a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#uhfUpdating-byte:A-"><code>RFIDWithUHFA8NetWork.uhfUpdating(byte[] buff)</code></a>  (Start to upgrade UHF firmware)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">开始升级UHF固件,调用此函数后需要延时2秒再调用 <a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#uhfUpdating-byte:A-"><code>RFIDWithUHFA8RS232.uhfUpdating(byte[] buff)</code></a>  (Start to upgrade UHF firmware)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">开始升级uhf模块  <br>
 Start upgrade UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStartUpdate--">UHFStartUpdate()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFStartUpdate--">UHFStartUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">开始升级uhf模块  <br>
 Start upgrade UHF module</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#uhfStartUpdate--">uhfStartUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStartUpdateRecvData-byte:A-int-">UHFStartUpdateRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFStartUpdateRecvData-byte:A-int-">UHFStartUpdateRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#uhfStartUpdateSendData--">uhfStartUpdateSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStartUpdateSendData--">UHFStartUpdateSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFStartUpdateSendData--">UHFStartUpdateSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#uhfStartUpdateSendData--">uhfStartUpdateSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopGet--">UHFStopGet()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFStopGet--">UHFStopGet()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopInventoryRecvData-byte:A-int-">UHFStopInventoryRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFStopInventoryRecvData-byte:A-int-">UHFStopInventoryRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopInventorySendData--">UHFStopInventorySendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFStopInventorySendData--">UHFStopInventorySendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopLogging-int-int-int-byte:A-long-">UHFStopLogging(int, int, int, byte[], long)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFStopLogging-int-int-int-byte:A-long-">UHFStopLogging(int, int, int, byte[], long)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">停止升级uhf模块，uhf模块升级完成后需要调用此函数<br>
 Stop upgrade UHF module, call this formula after module has been upgraded.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">停止升级uhf模块，uhf模块升级完成后需要调用此函数<br>
 Stop upgrade UHF module, call this formula after module has been upgraded.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">停止升级UHF固件(Stop upgrading UHF firmware)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">停止升级UHF固件(Stop upgrading UHF firmware)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">停止升级UHF固件(Stop upgrading UHF firmware)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">停止升级UHF固件(Stop upgrading UHF firmware)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">停止升级uhf模块，uhf模块升级完成后需要调用此函数<br>
 Stop upgrade UHF module, call this formula after module has been upgraded.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopUpdate--">UHFStopUpdate()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFStopUpdate--">UHFStopUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">停止升级uhf模块，uhf模块升级完成后需要调用此函数<br>
 Stop upgrade UHF module, call this formula after module has been upgraded.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#uhfStopUpdate--">uhfStopUpdate()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopUpdateRecvData-byte:A-int-">UHFStopUpdateRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFStopUpdateRecvData-byte:A-int-">UHFStopUpdateRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#UHFStopUpdateSendData--">UHFStopUpdateSendData()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopUpdateSendData--">UHFStopUpdateSendData()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFStopUpdateSendData--">UHFStopUpdateSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#UHFStopUpdateSendData--">UHFStopUpdateSendData()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">UHFTAGInfo</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html#UHFTAGInfo--">UHFTAGInfo()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">UHFTAGInfo.ChipInfo</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">UHFTAGInfo.ChipInfo.Builder</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFTamperAPI.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTamperAPI</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>
<div class="block">阿联酋Acube 定制接口</div>
</dd>
<dt><a href="../com/rscja/custom/UHFTamperAPI.TamperInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTamperAPI.TamperInfo</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/custom/UHFTamperAPI_qcom.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">UHFTamperAPI_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFTCPTagsDataParseRecvData-byte:A-int-">UHFTCPTagsDataParseRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFTCPTagsDataParseRecvData-byte:A-int-">UHFTCPTagsDataParseRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFTemperatureSensors.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureSensors</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFTemperatureSensors.IUHFInventoryTemperatureTagCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFTemperatureSensors.IUHFInventoryTemperatureTagCallback</span></a> - Interface in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureSensors.TemperatureTag</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFTemperatureTag.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag.InventoryTemperatureTagCallback</span></a> - Interface in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN51.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag.RFIDWithUHFBLEN51</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN52.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag.RFIDWithUHFBLEN52</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag.TemperatureTagInfo</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFTemperatureTagsAPI.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFTemperatureTagsAPI.IUHFInventoryTempCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI.IUHFInventoryTempCallback</span></a> - Interface in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI.MultipleTemperatureInfo</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI.TagState</span></a> - Enum in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>
<div class="block">温度标签状态枚举值</div>
</dd>
<dt><a href="../com/rscja/custom/UHFTemperatureTagsAPI.TempertureInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI.TempertureInfo</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>
<div class="block">温度标签实体对象</div>
</dd>
<dt><a href="../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html" title="class in com.rscja.team.mtk.custom"><span class="typeNameLink">UHFTemperatureTagsAPI_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/custom/package-summary.html">com.rscja.team.mtk.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/custom/UHFTemperatureTagsAPI_qcom.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">UHFTemperatureTagsAPI_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/custom/UHFTemperatureTagsBLEAPI.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">UHFTemperatureTagsBLEAPI</span></a> - Class in <a href="../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFToBoot-int-">UHFToBoot(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFToBoot-int-">UHFToBoot(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFUartFoxconn.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFUartFoxconn</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html" title="class in com.rscja.team.mtk.custom"><span class="typeNameLink">UHFUartFoxconn_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/custom/package-summary.html">com.rscja.team.mtk.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/custom/UHFUartFoxconn_qcom.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">UHFUartFoxconn_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/rs232utils/UhfUartManage_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UhfUartManage_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/rs232utils/package-summary.html">com.rscja.team.qcom.rs232utils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UhfUartManage_qcom.html#UhfUartManage_qcom-java.lang.String-int-">UhfUartManage_qcom(String, int)</a></span> - Constructor for class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UhfUartManage_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UhfUartManage_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFUartTemperatureTag.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFUartTemperatureTag</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFUartTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFUartTemperatureTag.InventoryTemperatureTagCallback</span></a> - Interface in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFUartTemperatureTag.TemperatureTagInfo</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/rs232utils/UhfUartUR4Manage_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UhfUartUR4Manage_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/rs232utils/package-summary.html">com.rscja.team.qcom.rs232utils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UhfUartUR4Manage_qcom.html#UhfUartUR4Manage_qcom--">UhfUartUR4Manage_qcom()</a></span> - Constructor for class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UhfUartUR4Manage_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UhfUartUR4Manage_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/rs232utils/UhfUartUR4Manage_qcom.CheckConnectState.html" title="interface in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UhfUartUR4Manage_qcom.CheckConnectState</span></a> - Interface in <a href="../com/rscja/team/qcom/rs232utils/package-summary.html">com.rscja.team.qcom.rs232utils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">升级uhf模块，发送uhf固件数据 <br>
 Upgrade UHF module, send UHF firmware data</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">升级uhf模块，发送uhf固件数据 <br>
 Upgrade UHF module, send UHF firmware data</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">发送uhf升级包 (Send uhf upgrade package)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">发送uhf升级包 (Send uhf upgrade package)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">发送uhf升级包 (Send uhf upgrade package)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">发送uhf升级包 (Send uhf upgrade package)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">升级uhf模块，发送uhf固件数据 <br>
 Upgrade UHF module, send UHF firmware data</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFUpdating-byte:A-int-">UHFUpdating(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFUpdating-byte:A-int-">UHFUpdating(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">升级uhf模块，发送uhf固件数据 <br>
 Upgrade UHF module, send UHF firmware data</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#uhfUpdating-byte:A-">uhfUpdating(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFUpdatingRecvData-byte:A-int-">UHFUpdatingRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFUpdatingRecvData-byte:A-int-">UHFUpdatingRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#uhfUpdatingSendData-byte:A-">uhfUpdatingSendData(byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFUpdatingSendData-byte:A-">UHFUpdatingSendData(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFUpdatingSendData-byte:A-">UHFUpdatingSendData(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#uhfUpdatingSendData-byte:A-">uhfUpdatingSendData(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/socket/SocketManageUR4.html#uhfur4DataHandle">uhfur4DataHandle</a></span> - Variable in class com.rscja.team.qcom.socket.<a href="../com/rscja/team/qcom/socket/SocketManageUR4.html" title="class in com.rscja.team.qcom.socket">SocketManageUR4</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/uhfhandler/UHFUR4DataHandle.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">UHFUR4DataHandle</span></a> - Class in <a href="../com/rscja/team/qcom/uhfhandler/package-summary.html">com.rscja.team.qcom.uhfhandler</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFUR4DataHandle.html#UHFUR4DataHandle--">UHFUR4DataHandle()</a></span> - Constructor for class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFUR4DataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">UHFUR4DataHandle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/uhfhandler/UHFUrAxDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">UHFUrAxDataHandle</span></a> - Class in <a href="../com/rscja/team/qcom/uhfhandler/package-summary.html">com.rscja.team.qcom.uhfhandler</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/uhfhandler/UHFUrAxDataHandle.html#UHFUrAxDataHandle--">UHFUrAxDataHandle()</a></span> - Constructor for class com.rscja.team.qcom.uhfhandler.<a href="../com/rscja/team/qcom/uhfhandler/UHFUrAxDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">UHFUrAxDataHandle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/UHFUrxAutoInventoryTagFactory_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFUrxAutoInventoryTagFactory_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/UHFUrxNetWorkAutoInventoryTag_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFUrxNetWorkAutoInventoryTag_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UHFUrxNetWorkAutoInventoryTag_qcom.html#UHFUrxNetWorkAutoInventoryTag_qcom-java.lang.String-int-">UHFUrxNetWorkAutoInventoryTag_qcom(String, int)</a></span> - Constructor for class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UHFUrxNetWorkAutoInventoryTag_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFUrxNetWorkAutoInventoryTag_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFUSBGetTagsDataRecvData-byte:A-int-">UHFUSBGetTagsDataRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFUSBGetTagsDataRecvData-byte:A-int-">UHFUSBGetTagsDataRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/utility/UhfUtils.html" title="class in com.rscja.utility"><span class="typeNameLink">UhfUtils</span></a> - Class in <a href="../com/rscja/utility/package-summary.html">com.rscja.utility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/UhfUtils.html#UhfUtils--">UhfUtils()</a></span> - Constructor for class com.rscja.utility.<a href="../com/rscja/utility/UhfUtils.html" title="class in com.rscja.utility">UhfUtils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFVerifyVoltage--">UHFVerifyVoltage()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#UHFVerifyVoltage--">UHFVerifyVoltage()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/scanner/OnUhfWorkStateListener.html#uhfWorkState-boolean-">uhfWorkState(boolean)</a></span> - Method in interface com.rscja.scanner.<a href="../com/rscja/scanner/OnUhfWorkStateListener.html" title="interface in com.rscja.scanner">OnUhfWorkStateListener</a></dt>
<dd>
<div class="block">UHF 是否正在盘点<br>
 Working status of UHF</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteCalibrationData-char:A-char-char:A-char:A-">UHFWriteCalibrationData(char[], char, char[], char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFWriteCalibrationData-char:A-char-char:A-char:A-">UHFWriteCalibrationData(char[], char, char[], char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteData-char:A-char-int-char-char:A-char:A-">UHFWriteData(char[], char, int, char, char[], char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFWriteData-char:A-char-int-char-char:A-char:A-">UHFWriteData(char[], char, int, char, char[], char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteData_Ex2-char:A-char-int-int-char:A-char-int-int-char:A-">UHFWriteData_Ex2(char[], char, int, int, char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFWriteData_Ex2-char:A-char-int-int-char:A-char-int-int-char:A-">UHFWriteData_Ex2(char[], char, int, int, char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteDataRecvData-byte:A-int-">UHFWriteDataRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFWriteDataRecvData-byte:A-int-">UHFWriteDataRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteDataSendData-byte:A-char-int-int-byte:A-char-int-char-byte:A-">UHFWriteDataSendData(byte[], char, int, int, byte[], char, int, char, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFWriteDataSendData-byte:A-char-int-int-byte:A-char-int-char-byte:A-">UHFWriteDataSendData(byte[], char, int, int, byte[], char, int, char, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteDataSingle-char:A-char-int-char-char:A-">UHFWriteDataSingle(char[], char, int, char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFWriteDataSingle-char:A-char-int-char-char:A-">UHFWriteDataSingle(char[], char, int, char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteQTData-char:A-char-int-char-char:A-char:A-">UHFWriteQTData(char[], char, int, char, char[], char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFWriteQTData-char:A-char-int-char-char:A-char:A-">UHFWriteQTData(char[], char, int, char, char[], char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteQTData_Ex-char:A-char-int-int-char:A-char-int-int-char:A-">UHFWriteQTData_Ex(char[], char, int, int, char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFWriteQTData_Ex-char:A-char-int-int-char:A-char-int-int-char:A-">UHFWriteQTData_Ex(char[], char, int, int, char[], char, int, int, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteQTDataSingle-char:A-char-int-char-char:A-">UHFWriteQTDataSingle(char[], char, int, char, char[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFWriteQTDataSingle-char:A-char-int-char-char:A-">UHFWriteQTDataSingle(char[], char, int, char, char[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteScreenBlock-byte:A-byte-int-int-byte:A-byte-int-int-byte:A-">UHFWriteScreenBlock(byte[], byte, int, int, byte[], byte, int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHFWriteScreenBlock-byte:A-byte-int-int-byte:A-byte-int-int-byte:A-">UHFWriteScreenBlock(byte[], byte, int, int, byte[], byte, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFXSAPI.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFXSAPI</span></a> - Class in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/custom/UHFXSAPI.Bank.html" title="enum in com.rscja.custom"><span class="typeNameLink">UHFXSAPI.Bank</span></a> - Enum in <a href="../com/rscja/custom/package-summary.html">com.rscja.custom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHTSetR6WorkmodeRecvData-byte:A-int-">UHTSetR6WorkmodeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHTSetR6WorkmodeRecvData-byte:A-int-">UHTSetR6WorkmodeRecvData(byte[], int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHTSetR6WorkmodeSendData-char-">UHTSetR6WorkmodeSendData(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UHTSetR6WorkmodeSendData-char-">UHTSetR6WorkmodeSendData(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Um7ProtocolConfig-char-">Um7ProtocolConfig(char)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Um7ProtocolConfig-char-">Um7ProtocolConfig(char)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html#UNKNOWN">UNKNOWN</a></span> - Static variable in class com.rscja.barcode.<a href="../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/utility/BatteryManage.html#unregisterReceiver--">unregisterReceiver()</a></span> - Method in class com.rscja.team.mtk.utility.<a href="../com/rscja/team/mtk/utility/BatteryManage.html" title="class in com.rscja.team.mtk.utility">BatteryManage</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#unvarnished_transfer-byte:A-">unvarnished_transfer(byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.html#unvarnished_transfer-byte:A-">unvarnished_transfer(byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html#unvarnished_transfer-byte:A-">unvarnished_transfer(byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443A_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#unvarnished_transfer-byte:A-">unvarnished_transfer(byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443A_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#upChar-com.rscja.deviceapi.Fingerprint.BufferEnum-">upChar(Fingerprint.BufferEnum)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">上传指定缓冲区中的指纹特征数据<br>
 Upload fingerprint feature data of specified buffer zone<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#upChar-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">upChar(FingerprintWithZAZ.BufferEnum, int[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>
<div class="block">将暂存在RamBuffer中的指纹模板上传到主机</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#upChar-com.rscja.deviceapi.Fingerprint.BufferEnum-">upChar(Fingerprint.BufferEnum)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">上传指定缓冲区中的指纹特征数据<br>
 Upload fingerprint feature data of specified buffer zone<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#upChar-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">upChar(FingerprintWithZAZ.BufferEnum, int[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>
<div class="block">将暂存在RamBuffer中的指纹模板上传到主机</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#upChar-com.rscja.deviceapi.Fingerprint.BufferEnum-">upChar(Fingerprint.BufferEnum)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">上传指定缓冲区中的指纹特征数据<br>
 Upload fingerprint feature data of specified buffer zone<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#upChar-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">upChar(FingerprintWithZAZ.BufferEnum, int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>
<div class="block">将暂存在RamBuffer中的指纹模板上传到主机</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#update-java.util.Observable-java.lang.Object-">update(Observable, Object)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/socket/SocketManageA4.html#update-com.rscja.deviceapi.interfaces.ConnectionStatus-">update(ConnectionStatus)</a></span> - Method in class com.rscja.team.qcom.socket.<a href="../com/rscja/team/qcom/socket/SocketManageA4.html" title="class in com.rscja.team.qcom.socket">SocketManageA4</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/socket/SocketManageUR4.html#update-com.rscja.deviceapi.interfaces.ConnectionStatus-">update(ConnectionStatus)</a></span> - Method in class com.rscja.team.qcom.socket.<a href="../com/rscja/team/qcom/socket/SocketManageUR4.html" title="class in com.rscja.team.qcom.socket">SocketManageUR4</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Update_ReadInfo--">Update_ReadInfo()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#Update_ReadInfo--">Update_ReadInfo()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IReader.html#UPDATE_STM32">UPDATE_STM32</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#UPDATE_UHF">UPDATE_UHF</a></span> - Static variable in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IPSAM.html#Upgrade-int-int-int-byte:A-">Upgrade(int, int, int, byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a></dt>
<dd>
<div class="block">固件升级<br>
 Firmware upgrade<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/PSAM.html#Upgrade-int-int-int-byte:A-">Upgrade(int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/PSAM.html" title="class in com.rscja.deviceapi">PSAM</a></dt>
<dd>
<div class="block">固件升级<br>
 Firmware upgrade<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/PSAM_mtk.html#Upgrade-int-int-int-byte:A-">Upgrade(int, int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/PSAM_mtk.html" title="class in com.rscja.team.mtk.deviceapi">PSAM_mtk</a></dt>
<dd>
<div class="block">固件升级<br>
 Firmware upgrade<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/PSAM_qcom.html#Upgrade-int-int-int-byte:A-">Upgrade(int, int, int, byte[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/PSAM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">PSAM_qcom</a></dt>
<dd>
<div class="block">固件升级<br>
 Firmware upgrade<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IPrinter.html#upgradeFont-int-int-int-byte:A-">upgradeFont(int, int, int, byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.html#upgradeFont-int-int-int-byte:A-">upgradeFont(int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html#upgradeFont-int-int-int-byte:A-">upgradeFont(int, int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Printer_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IPrinter.html#upgradeFW-int-int-int-byte:A-">upgradeFW(int, int, int, byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Printer.html#upgradeFW-int-int-int-byte:A-">upgradeFW(int, int, int, byte[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html#upgradeFW-int-int-int-byte:A-">upgradeFW(int, int, int, byte[])</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Printer_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#upImage-int-java.lang.String-">upImage(int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">上传指纹图像文件<br>
 Upload finerprint image file<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#upImage-int-java.lang.String-">upImage(int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">上传指纹图像文件<br>
 Upload finerprint image file<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#upImage-int-java.lang.String-">upImage(int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">上传指纹图像文件<br>
 Upload finerprint image file<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/FingerprintWithZAZ.html#upImageData-int-java.lang.String-int:A-">upImageData(int, String, int[])</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></dt>
<dd>
<div class="block">上传指纹图像到外部文件</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#upImageData-int-java.lang.String-int:A-">upImageData(int, String, int[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></dt>
<dd>
<div class="block">上传指纹图像到外部文件</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#upImageData-int-java.lang.String-int:A-">upImageData(int, String, int[])</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dt>
<dd>
<div class="block">上传指纹图像到外部文件</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/Fingerprint.html#upImageISO-int-java.lang.String-">upImageISO(int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></dt>
<dd>
<div class="block">上传指纹ISO图像文件<br>
 Upload fingerprint ISO image file<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IFingerprint.html#upImageISO-int-java.lang.String-">upImageISO(int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dt>
<dd>
<div class="block">上传指纹ISO图像文件<br>
 Upload fingerprint ISO image file<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html#upImageISO-int-java.lang.String-">upImageISO(int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a></dt>
<dd>
<div class="block">上传指纹ISO图像文件<br>
 Upload fingerprint ISO image file<br></div>
</dd>
<dt><a href="../com/rscja/team/qcom/usb/UrxUsb_qcom.html" title="class in com.rscja.team.qcom.usb"><span class="typeNameLink">UrxUsb_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/usb/package-summary.html">com.rscja.team.qcom.usb</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/UrxUsb_qcom.html#UrxUsb_qcom-com.rscja.team.qcom.usb.UsbBase_qcom-">UrxUsb_qcom(UsbBase_qcom)</a></span> - Constructor for class com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/UrxUsb_qcom.html" title="class in com.rscja.team.qcom.usb">UrxUsb_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb"><span class="typeNameLink">UsbBase_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/usb/package-summary.html">com.rscja.team.qcom.usb</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/UsbBase_qcom.html#UsbBase_qcom--">UsbBase_qcom()</a></span> - Constructor for class com.rscja.team.qcom.usb.<a href="../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/usb/UsbBase_qcom.DataCallback.html" title="interface in com.rscja.team.qcom.usb"><span class="typeNameLink">UsbBase_qcom.DataCallback</span></a> - Interface in <a href="../com/rscja/team/qcom/usb/package-summary.html">com.rscja.team.qcom.usb</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/UsbFingerprint.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">UsbFingerprint</span></a> - Class in <a href="../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">UsbFingerprint_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/deviceapi/UsbFingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UsbFingerprint_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html" title="class in com.rscja.team.qcom.usb.pl2302"><span class="typeNameLink">UsbPL2302</span></a> - Class in <a href="../com/rscja/team/qcom/usb/pl2302/package-summary.html">com.rscja.team.qcom.usb.pl2302</a></dt>
<dd>
<div class="block">Created by xxj on 01/15.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html#UsbPL2302--">UsbPL2302()</a></span> - Constructor for class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbPL2302</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302"><span class="typeNameLink">UsbSerialPort_qcom</span></a> - Interface in <a href="../com/rscja/team/qcom/usb/pl2302/package-summary.html">com.rscja.team.qcom.usb.pl2302</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302"><span class="typeNameLink">UsbSerialPortImpl_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/usb/pl2302/package-summary.html">com.rscja.team.qcom.usb.pl2302</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html#UsbSerialPortImpl_qcom-android.hardware.usb.UsbDeviceConnection-">UsbSerialPortImpl_qcom(UsbDeviceConnection)</a></span> - Constructor for class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbSerialPortImpl_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUsbFingerprint.html#UsbToFingerprint--">UsbToFingerprint()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUsbFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IUsbFingerprint</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UsbFingerprint.html#UsbToFingerprint--">UsbToFingerprint()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UsbFingerprint.html" title="class in com.rscja.deviceapi">UsbFingerprint</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UsbToFingerprint-java.lang.String-">UsbToFingerprint(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html#UsbToFingerprint--">UsbToFingerprint()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html" title="class in com.rscja.team.mtk.deviceapi">UsbFingerprint_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UsbToFingerprint-java.lang.String-">UsbToFingerprint(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UsbFingerprint_qcom.html#UsbToFingerprint--">UsbToFingerprint()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UsbFingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UsbFingerprint_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUsbFingerprint.html#UsbToHost--">UsbToHost()</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUsbFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IUsbFingerprint</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/UsbFingerprint.html#UsbToHost--">UsbToHost()</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/UsbFingerprint.html" title="class in com.rscja.deviceapi">UsbFingerprint</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UsbToHost-java.lang.String-">UsbToHost(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html#UsbToHost--">UsbToHost()</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html" title="class in com.rscja.team.mtk.deviceapi">UsbFingerprint_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html#UsbToHost-java.lang.String-">UsbToHost(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi">DeviceAPI</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/UsbFingerprint_qcom.html#UsbToHost--">UsbToHost()</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/UsbFingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UsbFingerprint_qcom</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-20.html">Prev Letter</a></li>
<li><a href="index-22.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-21.html" target="_top">Frames</a></li>
<li><a href="index-21.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
