<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>W-Index</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="W-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-22.html">Prev Letter</a></li>
<li><a href="index-24.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-23.html" target="_top">Frames</a></li>
<li><a href="index-23.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;<a name="I:W">
<!--   -->
</a>
<h2 class="title">W</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPOEntity.html#WiegandData0">WiegandData0</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/GPOEntity.html#WiegandData1">WiegandData1</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">WifiConfig</span></a> - Class in <a href="../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/WifiConfig.html#WifiConfig--">WifiConfig()</a></span> - Constructor for class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IHF15693.html#write-com.rscja.deviceapi.entity.HF15693RequestEntity-int-byte:A-">write(HF15693RequestEntity, int, byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces">IHF15693</a></dt>
<dd>
<div class="block">写卡(Write card)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#write-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-int-int-java.lang.String-">write(String, RFIDWithISO14443A.KeyType, int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></dt>
<dd>
<div class="block">写卡，此函数包括寻卡和密钥验证异常<br>
 Card writing, this formula includes card searching and key verification err<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#write-int-java.lang.String-">write(int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></dt>
<dd>
<div class="block">写卡，适用于Urltra light标签<br>
 Card writing, used for Urltra light tag<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#write-int-java.lang.String-">write(int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></dt>
<dd>
<div class="block">写卡<br>
 card writing<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#write-com.rscja.deviceapi.entity.ISO15693Entity-int-java.lang.String-">write(ISO15693Entity, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#write-int-int-java.lang.String-">write(int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></dt>
<dd>
<div class="block">写卡<br>
 card writing<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.html#write-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-int-int-java.lang.String-">write(String, RFIDWithISO14443A.KeyType, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A</a></dt>
<dd>
<div class="block">写卡，此函数包括寻卡和密钥验证异常<br>
 Card writing, this formula includes card searching and key verification err<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO14443A.html#write-int-java.lang.String-">write(int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A</a></dt>
<dd>
<div class="block">写卡，适用于Urltra light标签<br>
 Card writing, used for Urltra light tag<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO15693.html#write-int-java.lang.String-">write(int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi">RFIDWithISO15693</a></dt>
<dd>
<div class="block">写卡<br>
 card writing<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO15693.html#write-com.rscja.deviceapi.entity.ISO15693Entity-int-java.lang.String-">write(ISO15693Entity, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi">RFIDWithISO15693</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO15693.html#write-int-int-java.lang.String-">write(int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi">RFIDWithISO15693</a></dt>
<dd>
<div class="block">写卡<br>
 card writing<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html#write-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-int-int-java.lang.String-">write(String, RFIDWithISO14443A.KeyType, int, int, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443A_mtk</a></dt>
<dd>
<div class="block">写卡，此函数包括寻卡和密钥验证异常<br>
 Card writing, this formula includes card searching and key verification err<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html#write-int-java.lang.String-">write(int, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443A_mtk</a></dt>
<dd>
<div class="block">写卡，适用于Urltra light标签<br>
 Card writing, used for Urltra light tag<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html#write-int-java.lang.String-">write(int, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk</a></dt>
<dd>
<div class="block">写卡<br>
 card writing<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html#write-com.rscja.deviceapi.entity.ISO15693Entity-int-java.lang.String-">write(ISO15693Entity, int, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html#write-int-int-java.lang.String-">write(int, int, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk</a></dt>
<dd>
<div class="block">写卡<br>
 card writing<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#write-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-int-int-java.lang.String-">write(String, RFIDWithISO14443A.KeyType, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443A_qcom</a></dt>
<dd>
<div class="block">写卡，此函数包括寻卡和密钥验证异常<br>
 Card writing, this formula includes card searching and key verification err<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#write-int-java.lang.String-">write(int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443A_qcom</a></dt>
<dd>
<div class="block">写卡，适用于Urltra light标签<br>
 Card writing, used for Urltra light tag<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#write-int-java.lang.String-">write(int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a></dt>
<dd>
<div class="block">写卡<br>
 card writing<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#write-com.rscja.deviceapi.entity.ISO15693Entity-int-java.lang.String-">write(ISO15693Entity, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#write-int-int-java.lang.String-">write(int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a></dt>
<dd>
<div class="block">写卡<br>
 card writing<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/r1/hf/HF15693.html#write-com.rscja.deviceapi.entity.HF15693RequestEntity-int-byte:A-">write(HF15693RequestEntity, int, byte[])</a></span> - Method in class com.rscja.team.qcom.r1.hf.<a href="../com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf">HF15693</a></dt>
<dd>
<div class="block">写卡(Write card)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#write-byte:A-int-">write(byte[], int)</a></span> - Method in interface com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></dt>
<dd>
<div class="block">Writes as many bytes as possible from the source buffer.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html#write-byte:A-int-">write(byte[], int)</a></span> - Method in class com.rscja.team.qcom.usb.pl2302.<a href="../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbSerialPortImpl_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IHF15693.html#writeAFI-com.rscja.deviceapi.entity.HF15693RequestEntity-byte-">writeAFI(HF15693RequestEntity, byte)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces">IHF15693</a></dt>
<dd>
<div class="block">写入AFI(Write AFi)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#writeAFI-int-">writeAFI(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></dt>
<dd>
<div class="block">写入AFI<br>
 Write AFi<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO15693.html#writeAFI-int-">writeAFI(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi">RFIDWithISO15693</a></dt>
<dd>
<div class="block">写入AFI<br>
 Write AFi<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html#writeAFI-int-">writeAFI(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk</a></dt>
<dd>
<div class="block">写入AFI<br>
 Write AFi<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#writeAFI-int-">writeAFI(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a></dt>
<dd>
<div class="block">写入AFI<br>
 Write AFi<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/r1/hf/HF15693.html#writeAFI-com.rscja.deviceapi.entity.HF15693RequestEntity-byte-">writeAFI(HF15693RequestEntity, byte)</a></span> - Method in class com.rscja.team.qcom.r1.hf.<a href="../com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf">HF15693</a></dt>
<dd>
<div class="block">写入AFI(Write AFi)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IHF14443A.html#writeBlock-byte-byte:A-">writeBlock(byte, byte[])</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IHF14443A.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443A</a></dt>
<dd>
<div class="block">写卡(Write card)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/r1/hf/HF14443A.html#writeBlock-byte-byte:A-">writeBlock(byte, byte[])</a></span> - Method in class com.rscja.team.qcom.r1.hf.<a href="../com/rscja/team/qcom/r1/hf/HF14443A.html" title="class in com.rscja.team.qcom.r1.hf">HF14443A</a></dt>
<dd>
<div class="block">写卡(Write card)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/interfaces/IRFIDWithUHFJieCe.html#writeCalibrationData-java.lang.String-int-int-java.lang.String-">writeCalibrationData(String, int, int, String)</a></span> - Method in interface com.rscja.custom.interfaces.<a href="../com/rscja/custom/interfaces/IRFIDWithUHFJieCe.html" title="interface in com.rscja.custom.interfaces">IRFIDWithUHFJieCe</a></dt>
<dd>
<div class="block">写Calibration数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/RFIDWithUHFJieCe.html#writeCalibrationData-java.lang.String-int-int-java.lang.String-">writeCalibrationData(String, int, int, String)</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/RFIDWithUHFJieCe.html" title="class in com.rscja.custom">RFIDWithUHFJieCe</a></dt>
<dd>
<div class="block">写Calibration数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/RFIDWithUHFJieCe_qcom.html#writeCalibrationData-java.lang.String-int-int-java.lang.String-">writeCalibrationData(String, int, int, String)</a></span> - Method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/RFIDWithUHFJieCe_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFJieCe_qcom</a></dt>
<dd>
<div class="block">写Calibration数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">向标签写入数据 <br>
 Write data in tag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">将数据写入指定标签<br>
 Write data</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#writeData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeData(String, int, int, int, String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">向标签写入数据 <br>
 Write data in tag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">将数据写入指定标签<br>
 Write data</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">将数据写入标签（write data to label）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">将数据写入指定标签（write data to label）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">将数据写入标签（write data to label）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">将数据写入指定标签（write data to label）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">将数据写入标签（write data to label）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">将数据写入指定标签（write data to label）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">将数据写入标签（write data to label）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">将数据写入指定标签（write data to label）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi">RFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html#writeData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeData(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi">RFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">向标签写入数据 <br>
 Write data in tag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">将数据写入指定标签<br>
 Write data</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeData(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">向标签写入数据 <br>
 Write data in tag</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">将数据写入指定标签<br>
 Write data</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData(String, int, int, int, String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">将数据写入到EPC,而且自动适配盘点的EPC长度<br>
 Write data to epc</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">将数据写入到EPC,而且自动适配盘点的EPC长度<br>
 Write data to epc</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">将数据写入到EPC,而且自动适配盘点的EPC长度<br>
 Write data to epc</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">将数据写入到EPC,而且自动适配盘点的EPC长度<br>
 Write data to epc</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">写标签到EPC (Write the label to the epc)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">写标签到EPC (Write the label to the epc)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">写标签到EPC (Write the label to the epc)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">写标签到EPC (Write the label to the epc)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">写标签到EPC (Write the label to the epc)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">写标签到EPC (Write the label to the epc)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">写标签到EPC (Write the label to the epc)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">写标签到EPC (Write the label to the epc)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc(String, int, int, int, String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#writeDataWith4305Card-int-java.lang.String-">writeDataWith4305Card(int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithLF</a></dt>
<dd>
<div class="block">写4305卡的某一页（0~31）数据<br>
 write data in card 4305 at random page (0-31)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithLF.html#writeDataWith4305Card-int-java.lang.String-">writeDataWith4305Card(int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi">RFIDWithLF</a></dt>
<dd>
<div class="block">写4305卡的某一页（0~31）数据<br>
 write data in card 4305 at random page (0-31)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html#writeDataWith4305Card-int-java.lang.String-">writeDataWith4305Card(int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithLF_qcom</a></dt>
<dd>
<div class="block">写4305卡的某一页（0~31）数据<br>
 write data in card 4305 at random page (0-31)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#writeDataWithHitagS-int-java.lang.String-">writeDataWithHitagS(int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithLF</a></dt>
<dd>
<div class="block">写Hitag S卡中的某一页的数据<br>
 Write data of random page in Hitag S card<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithLF.html#writeDataWithHitagS-int-java.lang.String-">writeDataWithHitagS(int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi">RFIDWithLF</a></dt>
<dd>
<div class="block">写Hitag S卡中的某一页的数据<br>
 Write data of random page in Hitag S card<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html#writeDataWithHitagS-int-java.lang.String-">writeDataWithHitagS(int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithLF_qcom</a></dt>
<dd>
<div class="block">写Hitag S卡中的某一页的数据<br>
 Write data of random page in Hitag S card<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IHF15693.html#writeDsfid-com.rscja.deviceapi.entity.HF15693RequestEntity-byte-">writeDsfid(HF15693RequestEntity, byte)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces">IHF15693</a></dt>
<dd>
<div class="block">写入Dsfid(Write Dsfid)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#writeDSFID-int-">writeDSFID(int)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></dt>
<dd>
<div class="block">写入DSFID<br>
 Write DSFID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithISO15693.html#writeDSFID-int-">writeDSFID(int)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi">RFIDWithISO15693</a></dt>
<dd>
<div class="block">写入DSFID<br>
 Write DSFID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html#writeDSFID-int-">writeDSFID(int)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk</a></dt>
<dd>
<div class="block">写入DSFID<br>
 Write DSFID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#writeDSFID-int-">writeDSFID(int)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a></dt>
<dd>
<div class="block">写入DSFID<br>
 Write DSFID<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/r1/hf/HF15693.html#writeDsfid-com.rscja.deviceapi.entity.HF15693RequestEntity-byte-">writeDsfid(HF15693RequestEntity, byte)</a></span> - Method in class com.rscja.team.qcom.r1.hf.<a href="../com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf">HF15693</a></dt>
<dd>
<div class="block">写入Dsfid(Write Dsfid)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/utility/FileUtils.html#writeFile-java.lang.String-java.lang.String-boolean-">writeFile(String, String, boolean)</a></span> - Static method in class com.rscja.team.mtk.utility.<a href="../com/rscja/team/mtk/utility/FileUtils.html" title="class in com.rscja.team.mtk.utility">FileUtils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/FileUtility.html#WriteFile-java.io.File-java.lang.String-boolean-">WriteFile(File, String, boolean)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/FileUtility.html" title="class in com.rscja.utility">FileUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/utility/FileUtility.html#WriteFile-java.io.File-byte:A-boolean-">WriteFile(File, byte[], boolean)</a></span> - Static method in class com.rscja.utility.<a href="../com/rscja/utility/FileUtility.html" title="class in com.rscja.utility">FileUtility</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#writeMacSendData-byte:A-">writeMacSendData(byte[])</a></span> - Method in class com.rscja.team.qcom.rs232utils.<a href="../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/service/BLEService_qcom.html#writeRXCharacteristic-byte:A-java.lang.String:A-">writeRXCharacteristic(byte[], String[])</a></span> - Method in class com.rscja.team.qcom.service.<a href="../com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#writeScannerState-int-">writeScannerState(int)</a></span> - Static method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html" title="class in com.rscja.team.mtk.barcode">Barcode2DSoftCommon_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#writeScannerStateNew-int-">writeScannerStateNew(int)</a></span> - Method in class com.rscja.team.mtk.barcode.<a href="../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html" title="class in com.rscja.team.mtk.barcode">Barcode2DSoftCommon_mtk</a></dt>
<dd>
<div class="block">写入扫描头是否在工作的状态值<br>
 Write in status value that scanning module is in working or not<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/interfaces/IUHFUartFoxconn.html#writeScreenBlockData-java.lang.String-int-int-java.lang.String-">writeScreenBlockData(String, int, int, String)</a></span> - Method in interface com.rscja.custom.interfaces.<a href="../com/rscja/custom/interfaces/IUHFUartFoxconn.html" title="interface in com.rscja.custom.interfaces">IUHFUartFoxconn</a></dt>
<dd>
<div class="block">按块写无源电子标签带水墨屏显示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/interfaces/IUHFUartFoxconn.html#writeScreenBlockData-java.lang.String-int-int-int-java.lang.String-int-int-java.lang.String-">writeScreenBlockData(String, int, int, int, String, int, int, String)</a></span> - Method in interface com.rscja.custom.interfaces.<a href="../com/rscja/custom/interfaces/IUHFUartFoxconn.html" title="interface in com.rscja.custom.interfaces">IUHFUartFoxconn</a></dt>
<dd>
<div class="block">按块写无源电子标签带水墨屏显示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFUartFoxconn.html#writeScreenBlockData-java.lang.String-int-int-java.lang.String-">writeScreenBlockData(String, int, int, String)</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFUartFoxconn.html" title="class in com.rscja.custom">UHFUartFoxconn</a></dt>
<dd>
<div class="block">按块写无源电子标签带水墨屏显示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/custom/UHFUartFoxconn.html#writeScreenBlockData-java.lang.String-int-int-int-java.lang.String-int-int-java.lang.String-">writeScreenBlockData(String, int, int, int, String, int, int, String)</a></span> - Method in class com.rscja.custom.<a href="../com/rscja/custom/UHFUartFoxconn.html" title="class in com.rscja.custom">UHFUartFoxconn</a></dt>
<dd>
<div class="block">按块写无源电子标签带水墨屏显示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html#writeScreenBlockData-java.lang.String-int-int-java.lang.String-">writeScreenBlockData(String, int, int, String)</a></span> - Method in class com.rscja.team.mtk.custom.<a href="../com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html" title="class in com.rscja.team.mtk.custom">UHFUartFoxconn_mtk</a></dt>
<dd>
<div class="block">按块写无源电子标签带水墨屏显示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html#writeScreenBlockData-java.lang.String-int-int-int-java.lang.String-int-int-java.lang.String-">writeScreenBlockData(String, int, int, int, String, int, int, String)</a></span> - Method in class com.rscja.team.mtk.custom.<a href="../com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html" title="class in com.rscja.team.mtk.custom">UHFUartFoxconn_mtk</a></dt>
<dd>
<div class="block">按块写无源电子标签带水墨屏显示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/UHFUartFoxconn_qcom.html#writeScreenBlockData-java.lang.String-int-int-java.lang.String-">writeScreenBlockData(String, int, int, String)</a></span> - Method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/UHFUartFoxconn_qcom.html" title="class in com.rscja.team.qcom.custom">UHFUartFoxconn_qcom</a></dt>
<dd>
<div class="block">按块写无源电子标签带水墨屏显示</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/custom/UHFUartFoxconn_qcom.html#writeScreenBlockData-java.lang.String-int-int-int-java.lang.String-int-int-java.lang.String-">writeScreenBlockData(String, int, int, int, String, int, int, String)</a></span> - Method in class com.rscja.team.qcom.custom.<a href="../com/rscja/team/qcom/custom/UHFUartFoxconn_qcom.html" title="class in com.rscja.team.qcom.custom">UHFUartFoxconn_qcom</a></dt>
<dd>
<div class="block">按块写无源电子标签带水墨屏显示</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-22.html">Prev Letter</a></li>
<li><a href="index-24.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-23.html" target="_top">Frames</a></li>
<li><a href="index-23.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
