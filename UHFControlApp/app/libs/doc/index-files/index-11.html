<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>K-Index</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="K-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-10.html">Prev Letter</a></li>
<li><a href="index-12.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-11.html" target="_top">Frames</a></li>
<li><a href="index-11.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;<a name="I:K">
<!--   -->
</a>
<h2 class="title">K</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/entity/BarcodeResult.html#K">K</a></span> - Static variable in class com.rscja.deviceapi.entity.<a href="../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_barcodeBroadcastAction">key_barcodeBroadcastAction</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_BarcodeNotRepeat">key_BarcodeNotRepeat</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_cbBarcode2D_s">key_cbBarcode2D_s</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_cbEnter">key_cbEnter</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_cbERKOS">key_cbERKOS</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_cbSpace">key_cbSpace</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_cbTab">key_cbTab</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_Continuous">key_Continuous</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ContinuousIntervalTime">key_ContinuousIntervalTime</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ContinuousIntervalTimeUHF">key_ContinuousIntervalTimeUHF</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ContinuousMode">key_ContinuousMode</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ContinuousTimeOut">key_ContinuousTimeOut</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ContinuousTimeOutUHF">key_ContinuousTimeOutUHF</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ContinuousUHF">key_ContinuousUHF</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_debug">key_debug</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_endIndex">key_endIndex</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_etBroadcastKey">key_etBroadcastKey</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_failureBroadcast">key_failureBroadcast</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_failureSound">key_failureSound</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_filterChars">key_filterChars</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_firstInit">key_firstInit</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_firstSetScanP">key_firstSetScanP</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_format_Barcode">key_format_Barcode</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_format_RFID">key_format_RFID</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_GS1Parsing">key_GS1Parsing</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_IlluminationPowerLevel">key_IlluminationPowerLevel</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_InterceptScanKey">key_InterceptScanKey</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_LF_Last4Bytes">key_LF_Last4Bytes</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_Mirror">key_Mirror</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_prefix">key_prefix</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ScanAuxiliaryLight">key_ScanAuxiliaryLight</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_Scanner_Enable">key_Scanner_Enable</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_scanOnRelease">key_scanOnRelease</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_sort">key_sort</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_Sound">key_Sound</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_startIndex">key_startIndex</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_suffix">key_suffix</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_target">key_target</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_TimeOut">key_TimeOut</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_Vibrate">key_Vibrate</a></span> - Static variable in class com.rscja.team.qcom.scanner.utility.<a href="../com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html" title="class in com.rscja.team.mtk.barcode.barcode2d"><span class="typeNameLink">KeyboardEmulator2DDecoder_mtk</span></a> - Class in <a href="../com/rscja/team/mtk/barcode/barcode2d/package-summary.html">com.rscja.team.mtk.barcode.barcode2d</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d"><span class="typeNameLink">KeyboardEmulator2DDecoder_qcom</span></a> - Class in <a href="../com/rscja/team/qcom/barcode/barcode2d/package-summary.html">com.rscja.team.qcom.barcode.barcode2d</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/rscja/deviceapi/interfaces/KeyEventCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">KeyEventCallback</span></a> - Interface in <a href="../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></dt>
<dd>
<div class="block">接收扫描按键的回调
 Receive call back of SCAN Key</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">销毁指定标签,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Kill specific tag, default password(0x00 0x00 0x00 0x00)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/BleDevice.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></dt>
<dd>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Kill specific tag, default password(0x00 0x00 0x00 0x00)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IBleDevice.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#killTag-java.lang.String-java.lang.String-">killTag(String, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">销毁指定标签,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Kill specific tag, default password(0x00 0x00 0x00 0x00)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in interface com.rscja.deviceapi.interfaces.<a href="../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></dt>
<dd>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Kill specific tag, default password(0x00 0x00 0x00 0x00)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Destroy the  label, the default password (0x00 0x00 0x00 0x00) cannot be destroyed<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></dt>
<dd>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Destroy the specified label, the default password (0x00 0x00 0x00 0x00) cannot be destroyed<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Destroy the  label, the default password (0x00 0x00 0x00 0x00) cannot be destroyed<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></dt>
<dd>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Destroy the specified label, the default password (0x00 0x00 0x00 0x00) cannot be destroyed<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Destroy the  label, the default password (0x00 0x00 0x00 0x00) cannot be destroyed<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></dt>
<dd>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Destroy the specified label, the default password (0x00 0x00 0x00 0x00) cannot be destroyed<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Destroy the  label, the default password (0x00 0x00 0x00 0x00) cannot be destroyed<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></dt>
<dd>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Destroy the specified label, the default password (0x00 0x00 0x00 0x00) cannot be destroyed<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi">RFIDWithUHFRLM</a></dt>
<dd>
<div class="block">销毁指定标签（不指定UII）,默认密码不能执行销毁<br>
 destroy specified tag (non-specified UII), default code cannot execute erase<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html#killTag-java.lang.String-java.lang.String-">killTag(String, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi">RFIDWithUHFRLM</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUART.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">销毁指定标签,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Kill specific tag, default password(0x00 0x00 0x00 0x00)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></dt>
<dd>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Kill specific tag, default password(0x00 0x00 0x00 0x00)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.deviceapi.<a href="../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.team.mtk.deviceapi.<a href="../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.ble.<a href="../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BleDevice_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a></dt>
<dd>
<div class="block">销毁指定标签（不指定UII）,默认密码不能执行销毁<br>
 destroy specified tag (non-specified UII), default code cannot execute erase<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#killTag-java.lang.String-java.lang.String-">killTag(String, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">销毁指定标签,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Kill specific tag, default password(0x00 0x00 0x00 0x00)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></dt>
<dd>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Kill specific tag, default password(0x00 0x00 0x00 0x00)<br></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#killTag-java.lang.String-">killTag(String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag(String, int, int, int, String)</a></span> - Method in class com.rscja.team.qcom.deviceapi.<a href="../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a href="index-25.html">Z</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="../overview-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-10.html">Prev Letter</a></li>
<li><a href="index-12.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-11.html" target="_top">Frames</a></li>
<li><a href="index-11.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
