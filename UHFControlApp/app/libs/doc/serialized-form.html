<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Serialized Form</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Serialized Form";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-files/index-1.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?serialized-form.html" target="_top">Frames</a></li>
<li><a href="serialized-form.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Serialized Form" class="title">Serialized Form</h1>
</div>
<div class="serializedFormContainer">
<ul class="blockList">
<li class="blockList">
<h2 title="Package">Package&nbsp;com.rscja.deviceapi.exception</h2>
<ul class="blockList">
<li class="blockList"><a name="com.rscja.deviceapi.exception.ConfigurationException">
<!--   -->
</a>
<h3>Class <a href="com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">com.rscja.deviceapi.exception.ConfigurationException</a> extends java.lang.Exception implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>-3812256749292167834L</dd>
</dl>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.exception.DeviceNotConnectException">
<!--   -->
</a>
<h3>Class <a href="com/rscja/deviceapi/exception/DeviceNotConnectException.html" title="class in com.rscja.deviceapi.exception">com.rscja.deviceapi.exception.DeviceNotConnectException</a> extends java.lang.Exception implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>5820880406463066217L</dd>
</dl>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.exception.FingerprintAlreadyEnrolledException">
<!--   -->
</a>
<h3>Class <a href="com/rscja/deviceapi/exception/FingerprintAlreadyEnrolledException.html" title="class in com.rscja.deviceapi.exception">com.rscja.deviceapi.exception.FingerprintAlreadyEnrolledException</a> extends java.lang.Exception implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1588495819796022955L</dd>
</dl>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.exception.FingerprintInvalidIDException">
<!--   -->
</a>
<h3>Class <a href="com/rscja/deviceapi/exception/FingerprintInvalidIDException.html" title="class in com.rscja.deviceapi.exception">com.rscja.deviceapi.exception.FingerprintInvalidIDException</a> extends java.lang.Exception implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1588495819796022955L</dd>
</dl>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.exception.LowBatteryException">
<!--   -->
</a>
<h3>Class <a href="com/rscja/deviceapi/exception/LowBatteryException.html" title="class in com.rscja.deviceapi.exception">com.rscja.deviceapi.exception.LowBatteryException</a> extends java.lang.Exception implements Serializable</h3>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.exception.PrinterBarcodeInvalidException">
<!--   -->
</a>
<h3>Class <a href="com/rscja/deviceapi/exception/PrinterBarcodeInvalidException.html" title="class in com.rscja.deviceapi.exception">com.rscja.deviceapi.exception.PrinterBarcodeInvalidException</a> extends java.lang.Exception implements Serializable</h3>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.exception.PrinterLowPager">
<!--   -->
</a>
<h3>Class <a href="com/rscja/deviceapi/exception/PrinterLowPager.html" title="class in com.rscja.deviceapi.exception">com.rscja.deviceapi.exception.PrinterLowPager</a> extends java.lang.Exception implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>-2275129636038603532L</dd>
</dl>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.exception.PSAMException">
<!--   -->
</a>
<h3>Class <a href="com/rscja/deviceapi/exception/PSAMException.html" title="class in com.rscja.deviceapi.exception">com.rscja.deviceapi.exception.PSAMException</a> extends java.lang.Exception implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.exception.RFIDArgumentException">
<!--   -->
</a>
<h3>Class <a href="com/rscja/deviceapi/exception/RFIDArgumentException.html" title="class in com.rscja.deviceapi.exception">com.rscja.deviceapi.exception.RFIDArgumentException</a> extends java.lang.Exception implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.exception.RFIDNotFoundException">
<!--   -->
</a>
<h3>Class <a href="com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">com.rscja.deviceapi.exception.RFIDNotFoundException</a> extends java.lang.Exception implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>5718113445422324844L</dd>
</dl>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.exception.RFIDReadFailureException">
<!--   -->
</a>
<h3>Class <a href="com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">com.rscja.deviceapi.exception.RFIDReadFailureException</a> extends java.lang.Exception implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>2402758762233751986L</dd>
</dl>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.exception.RFIDVerificationException">
<!--   -->
</a>
<h3>Class <a href="com/rscja/deviceapi/exception/RFIDVerificationException.html" title="class in com.rscja.deviceapi.exception">com.rscja.deviceapi.exception.RFIDVerificationException</a> extends java.lang.Exception implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>-1076138928893434443L</dd>
</dl>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-files/index-1.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?serialized-form.html" target="_top">Frames</a></li>
<li><a href="serialized-form.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
