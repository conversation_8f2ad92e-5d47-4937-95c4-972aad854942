<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Sat Aug 17 14:14:42 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Overview</title>
<meta name="date" content="2024-08-17">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Overview";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li class="navBarCell1Rev">Overview</li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-files/index-1.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-summary.html" target="_top">Frames</a></li>
<li><a href="overview-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer">
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Packages table, listing packages, and an explanation">
<caption><span>Packages</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/package-summary.html">com.rscja</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/barcode/package-summary.html">com.rscja.barcode</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/custom/package-summary.html">com.rscja.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/custom/interfaces/package-summary.html">com.rscja.custom.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/deviceapi/enums/package-summary.html">com.rscja.deviceapi.enums</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/scanner/package-summary.html">com.rscja.scanner</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/scanner/led/package-summary.html">com.rscja.scanner.led</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/scanner/utility/package-summary.html">com.rscja.scanner.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/system/package-summary.html">com.rscja.system</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/mtk/package-summary.html">com.rscja.team.mtk</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/mtk/barcode/package-summary.html">com.rscja.team.mtk.barcode</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/mtk/barcode/barcode2d/package-summary.html">com.rscja.team.mtk.barcode.barcode2d</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/mtk/barcode/symbol/package-summary.html">com.rscja.team.mtk.barcode.symbol</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/mtk/custom/package-summary.html">com.rscja.team.mtk.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/mtk/scanner/led/package-summary.html">com.rscja.team.mtk.scanner.led</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/mtk/scanner/utility/package-summary.html">com.rscja.team.mtk.scanner.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/mtk/system/package-summary.html">com.rscja.team.mtk.system</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/mtk/utility/package-summary.html">com.rscja.team.mtk.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/qcom/package-summary.html">com.rscja.team.qcom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/qcom/barcode/package-summary.html">com.rscja.team.qcom.barcode</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/qcom/barcode/barcode2d/package-summary.html">com.rscja.team.qcom.barcode.barcode2d</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/qcom/barcode/symbol/package-summary.html">com.rscja.team.qcom.barcode.symbol</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/qcom/ble/package-summary.html">com.rscja.team.qcom.ble</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/qcom/http/package-summary.html">com.rscja.team.qcom.http</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/qcom/r1/package-summary.html">com.rscja.team.qcom.r1</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/qcom/r1/hf/package-summary.html">com.rscja.team.qcom.r1.hf</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/qcom/r1/psam/package-summary.html">com.rscja.team.qcom.r1.psam</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/qcom/rs232utils/package-summary.html">com.rscja.team.qcom.rs232utils</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/qcom/scanner/led/package-summary.html">com.rscja.team.qcom.scanner.led</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/qcom/scanner/utility/package-summary.html">com.rscja.team.qcom.scanner.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/qcom/service/package-summary.html">com.rscja.team.qcom.service</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/qcom/socket/package-summary.html">com.rscja.team.qcom.socket</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/qcom/system/package-summary.html">com.rscja.team.qcom.system</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/qcom/uhfhandler/package-summary.html">com.rscja.team.qcom.uhfhandler</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/qcom/uhfparse/package-summary.html">com.rscja.team.qcom.uhfparse</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/qcom/urax/package-summary.html">com.rscja.team.qcom.urax</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/qcom/usb/package-summary.html">com.rscja.team.qcom.usb</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/team/qcom/usb/pl2302/package-summary.html">com.rscja.team.qcom.usb.pl2302</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/rscja/team/qcom/utility/package-summary.html">com.rscja.team.qcom.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/rscja/utility/package-summary.html">com.rscja.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li class="navBarCell1Rev">Overview</li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-files/index-1.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-summary.html" target="_top">Frames</a></li>
<li><a href="overview-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->







<!-- ======== END OF BOTTOM NAVBAR ======= -->
<li><a href="com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></li>
 &nbsp&nbsp&nbsp&nbsp  UHF模块手持机，串口通信操作类(UHF module handheld, serial communication operation interface)</p> 
 

<li><a href="com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></li>
 &nbsp&nbsp&nbsp&nbsp  UHF模块低功耗蓝牙操作类(UHF module operation type)</p>
 

<li><a href="com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></li>
 &nbsp&nbsp&nbsp&nbsp  UHF模块手持机，USB通信操作类(UHF module handheld, USB operation interface)</p>
 

<li><a href="com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></li>
 &nbsp&nbsp&nbsp&nbsp  UHF模块 A4操作类(嵌入式开发) (UHF module operation type（Embedded development）)</p>
 

<li><a href="com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></li>
 &nbsp&nbsp&nbsp&nbsp  操作URA4设备以及UHF模块相关接口(通过其他android设备控制A4) (Operate URA4 devices and related interfaces of UHF modules)</p>
 

<li><a href="com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></li>
 &nbsp&nbsp&nbsp&nbsp  操作URA4设备以及UHF模块相关接口 (通过其他android设备控制A4) (Operate URA4 devices and related interfaces of UHF modules)</p>
 

<li><a href="com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a></li>
 &nbsp&nbsp&nbsp&nbsp  UHF模块 A8操作类(嵌入式开发) (UHF module operation type （Embedded development）)</p>
 

<li><a href="com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></li>
 &nbsp&nbsp&nbsp&nbsp  操作URA8设备以及UHF模块相关接口(通过其他android设备控制A8) (Operate URA8 devices and related interfaces of UHF modules)</p>
 

<li><a href="com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></li>
 &nbsp&nbsp&nbsp&nbsp  操作URA8设备以及UHF模块相关接口(通过其他android设备控制A8)(Operate URA8 devices and related interfaces of UHF modules) </p>
 
 
<li><a href="com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></li>
 &nbsp&nbsp&nbsp&nbsp  UHF模块URx网口通信操作类(URx network operation of UHF module)</p>
 

<li><a href="com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></li>
 &nbsp&nbsp&nbsp&nbsp  UHF模块URx串口通信操作类(URx module,serial communication operation interface)</p>
 

<!-- ======= 该类待补充 ====== -->
<li><a href="com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></li>
 &nbsp&nbsp&nbsp&nbsp  UHF模块URx串口通信操作类(URx module,serial communication operation interface)</p>
 

<li><a href="com/rscja/deviceapi/FingerprintWithMorpho.html" title="class in com.rscja.deviceapi">FingerprintWithMorpho</a></li>
 &nbsp&nbsp&nbsp&nbsp  Morpho指纹识别模块操作类(Morpho fingerprint indentify module operation type)</p>
 

 <li><a href="com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></li>
 &nbsp&nbsp&nbsp&nbsp  TCS1指纹识别模块操作类(TCS1 fingerprint indentify module operation type)</p>
 

<li><a href="com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></li>
 &nbsp&nbsp&nbsp&nbsp  TLK1NC指纹识别模块操作类(TLK1NC fingerprint indentify module operation type)</p>
 
<li><a href="com/rscja/deviceapi/PSAM.html" title="class in com.rscja.deviceapi">PSAM</a></li>
 &nbsp&nbsp&nbsp&nbsp  PSAM操作类(PSAM operation type)</p>
 

  <li><a href="com/rscja/barcode/BarcodeFactory.html" title="class in com.rscja.barcode">BarcodeFactory</a></li>
 &nbsp&nbsp&nbsp&nbsp  2D读头操作类(Barcode 2D operation interface)</p> 
 
 
 
</body>
</html>
