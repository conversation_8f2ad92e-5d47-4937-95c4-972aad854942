<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Constant Field Values</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Constant Field Values";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-files/index-1.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">Frames</a></li>
<li><a href="constant-values.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Constant Field Values" class="title">Constant Field Values</h1>
<h2 title="Contents">Contents</h2>
<ul>
<li><a href="#com.rscja">com.rscja.*</a></li>
</ul>
</div>
<div class="constantValuesContainer"><a name="com.rscja">
<!--   -->
</a>
<h2 title="com.rscja">com.rscja.*</h2>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.<a href="com/rscja/CWDeviceInfo.html" title="class in com.rscja">CWDeviceInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.C60_MTK_6765_110">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#C60_MTK_6765_110">C60_MTK_6765_110</a></code></td>
<td class="colLast"><code>"C60_6765_110"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.C60_QCM2150_100">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#C60_QCM2150_100">C60_QCM2150_100</a></code></td>
<td class="colLast"><code>"C60_QCM2150_100"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.C60_SMD450_100">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#C60_SMD450_100">C60_SMD450_100</a></code></td>
<td class="colLast"><code>"C60_SMD450_100"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.C6000_6762">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#C6000_6762">C6000_6762</a></code></td>
<td class="colLast"><code>"C6000_6762"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.C61_SMD450_90">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#C61_SMD450_90">C61_SMD450_90</a></code></td>
<td class="colLast"><code>"C61_SMD450_90"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.C61P_SM6115_110">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#C61P_SM6115_110">C61P_SM6115_110</a></code></td>
<td class="colLast"><code>"C61P_SM6115_110"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.C66_8953">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#C66_8953">C66_8953</a></code></td>
<td class="colLast"><code>"C66_8953"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.C66_SMD450_90">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#C66_SMD450_90">C66_SMD450_90</a></code></td>
<td class="colLast"><code>"C66_SMD450_90"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.C66P_SM6115_110">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#C66P_SM6115_110">C66P_SM6115_110</a></code></td>
<td class="colLast"><code>"C66P_SM6115_110"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.C90_6762_10">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#C90_6762_10">C90_6762_10</a></code></td>
<td class="colLast"><code>"C90"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.CW102_6761">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#CW102_6761">CW102_6761</a></code></td>
<td class="colLast"><code>"CW102_6761"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.CW107_6762">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#CW107_6762">CW107_6762</a></code></td>
<td class="colLast"><code>"CW107_6762"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.CW108_8781">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#CW108_8781">CW108_8781</a></code></td>
<td class="colLast"><code>"CW108_8781"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.CW108_8791">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#CW108_8791">CW108_8791</a></code></td>
<td class="colLast"><code>"CW108_8791"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.CW95_6762">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#CW95_6762">CW95_6762</a></code></td>
<td class="colLast"><code>"CW95_6762"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.H100_8953">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#H100_8953">H100_8953</a></code></td>
<td class="colLast"><code>"H100_8953"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.MC50_4350_120">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#MC50_4350_120">MC50_4350_120</a></code></td>
<td class="colLast"><code>"MC50_4350_120"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.MC51_4350_120">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#MC51_4350_120">MC51_4350_120</a></code></td>
<td class="colLast"><code>"MC51_4350_140"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.P80_8786_130">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#P80_8786_130">P80_8786_130</a></code></td>
<td class="colLast"><code>"P80_8786_130"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.P80_8953">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#P80_8953">P80_8953</a></code></td>
<td class="colLast"><code>"P80_8953"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.P80_8953_90">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#P80_8953_90">P80_8953_90</a></code></td>
<td class="colLast"><code>"P80_8953_90"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.PLATFORM_MTK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#PLATFORM_MTK">PLATFORM_MTK</a></code></td>
<td class="colLast"><code>"mtk"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.PLATFORM_QCOM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#PLATFORM_QCOM">PLATFORM_QCOM</a></code></td>
<td class="colLast"><code>"qualcomm"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.TEAM_MTK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#TEAM_MTK">TEAM_MTK</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.CWDeviceInfo.TEAM_QCOM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/CWDeviceInfo.html#TEAM_QCOM">TEAM_QCOM</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.barcode.<a href="com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_CW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_CW">MANUFACTOR_CW</a></code></td>
<td class="colLast"><code>"CW"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_HONYWELL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_HONYWELL">MANUFACTOR_HONYWELL</a></code></td>
<td class="colLast"><code>"HONYWELL"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_IA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_IA">MANUFACTOR_IA</a></code></td>
<td class="colLast"><code>"COASIA"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_IDATA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_IDATA">MANUFACTOR_IDATA</a></code></td>
<td class="colLast"><code>"IDATA"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_MOBYDATA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_MOBYDATA">MANUFACTOR_MOBYDATA</a></code></td>
<td class="colLast"><code>"MOBYDATA"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_NEWLAND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_NEWLAND">MANUFACTOR_NEWLAND</a></code></td>
<td class="colLast"><code>"NEWLAND"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_ZEBRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_ZEBRA">MANUFACTOR_ZEBRA</a></code></td>
<td class="colLast"><code>"Zebra"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.MODEL_CW_CW9281">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#MODEL_CW_CW9281">MODEL_CW_CW9281</a></code></td>
<td class="colLast"><code>"cw9281"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_HONYWELL_3601">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_3601">Model_HONYWELL_3601</a></code></td>
<td class="colLast"><code>"HONYWELL_3601"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_HONYWELL_3603">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_3603">Model_HONYWELL_3603</a></code></td>
<td class="colLast"><code>"HONYWELL_3603"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_HONYWELL_6603">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_6603">Model_HONYWELL_6603</a></code></td>
<td class="colLast"><code>"N6603"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_HONYWELL_EX30">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_EX30">Model_HONYWELL_EX30</a></code></td>
<td class="colLast"><code>"EX30"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_HONYWELL_N5703">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_N5703">Model_HONYWELL_N5703</a></code></td>
<td class="colLast"><code>"N5703"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_HONYWELL_N6703">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_N6703">Model_HONYWELL_N6703</a></code></td>
<td class="colLast"><code>"N6703"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_100S">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_100S">Model_IA_100S</a></code></td>
<td class="colLast"><code>"ia100s"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_101S">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_101S">Model_IA_101S</a></code></td>
<td class="colLast"><code>"IA_101S"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_166S">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_166S">Model_IA_166S</a></code></td>
<td class="colLast"><code>"IA_166S"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_171S">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_171S">Model_IA_171S</a></code></td>
<td class="colLast"><code>"IA_171S"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_181S">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_181S">Model_IA_181S</a></code></td>
<td class="colLast"><code>"IA_181S"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_400S">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_400S">Model_IA_400S</a></code></td>
<td class="colLast"><code>"IA_400S"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_417S">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_417S">Model_IA_417S</a></code></td>
<td class="colLast"><code>"ia417s"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_IDATA_DS7000">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IDATA_DS7000">Model_IDATA_DS7000</a></code></td>
<td class="colLast"><code>"ds7000p"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_MOBYDATA_E3200">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_MOBYDATA_E3200">Model_MOBYDATA_E3200</a></code></td>
<td class="colLast"><code>"e3200"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_MOTO_5500">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_MOTO_5500">Model_MOTO_5500</a></code></td>
<td class="colLast"><code>"SE5500"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_NEWLAND_CM30">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_NEWLAND_CM30">Model_NEWLAND_CM30</a></code></td>
<td class="colLast"><code>"cm30_newland"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_NEWLAND_CM47">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_NEWLAND_CM47">Model_NEWLAND_CM47</a></code></td>
<td class="colLast"><code>"cm47_newland"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_NEWLAND_CM60">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_NEWLAND_CM60">Model_NEWLAND_CM60</a></code></td>
<td class="colLast"><code>"cm60_newland"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_2100">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_2100">Model_ZEBRA_2100</a></code></td>
<td class="colLast"><code>"SE2100"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_4100">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4100">Model_ZEBRA_4100</a></code></td>
<td class="colLast"><code>"SE4100"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_4710">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4710">Model_ZEBRA_4710</a></code></td>
<td class="colLast"><code>"SE4710"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_4720">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4720">Model_ZEBRA_4720</a></code></td>
<td class="colLast"><code>"SE4720"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_4750">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4750">Model_ZEBRA_4750</a></code></td>
<td class="colLast"><code>"SE4750"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_4770">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4770">Model_ZEBRA_4770</a></code></td>
<td class="colLast"><code>"SE4770"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_4850">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4850">Model_ZEBRA_4850</a></code></td>
<td class="colLast"><code>"SE4850"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.ZEBRA_ENGINE_SUFFIX_DP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#ZEBRA_ENGINE_SUFFIX_DP">ZEBRA_ENGINE_SUFFIX_DP</a></code></td>
<td class="colLast"><code>"DP"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.ZEBRA_ENGINE_SUFFIX_MR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#ZEBRA_ENGINE_SUFFIX_MR">ZEBRA_ENGINE_SUFFIX_MR</a></code></td>
<td class="colLast"><code>"MR"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.Barcode2DSHardwareInfo.ZEBRA_ENGINE_SUFFIX_SR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html#ZEBRA_ENGINE_SUFFIX_SR">ZEBRA_ENGINE_SUFFIX_SR</a></code></td>
<td class="colLast"><code>"SR"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.barcode.<a href="com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeDecoder.DECODE_CANCEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeDecoder.html#DECODE_CANCEL">DECODE_CANCEL</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeDecoder.DECODE_ENGINE_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeDecoder.html#DECODE_ENGINE_ERROR">DECODE_ENGINE_ERROR</a></code></td>
<td class="colLast"><code>-3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeDecoder.DECODE_FAILURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeDecoder.html#DECODE_FAILURE">DECODE_FAILURE</a></code></td>
<td class="colLast"><code>-2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeDecoder.DECODE_SUCCESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeDecoder.html#DECODE_SUCCESS">DECODE_SUCCESS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeDecoder.DECODE_TIMEOUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeDecoder.html#DECODE_TIMEOUT">DECODE_TIMEOUT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.barcode.<a href="com/rscja/barcode/BarcodeSymbolUtility.html" title="class in com.rscja.barcode">BarcodeSymbolUtility</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_AIM_128">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_AIM_128">INT_AIM_128</a></code></td>
<td class="colLast"><code>52</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_AUSPOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_AUSPOST">INT_AUSPOST</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_AZTEC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_AZTEC">INT_AZTEC</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_BOOKLAND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_BOOKLAND">INT_BOOKLAND</a></code></td>
<td class="colLast"><code>53</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_BPO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_BPO">INT_BPO</a></code></td>
<td class="colLast"><code>14</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CANPOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CANPOST">INT_CANPOST</a></code></td>
<td class="colLast"><code>15</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CHINAPOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CHINAPOST">INT_CHINAPOST</a></code></td>
<td class="colLast"><code>42</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CODABAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODABAR">INT_CODABAR</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CODABLOCK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODABLOCK">INT_CODABLOCK</a></code></td>
<td class="colLast"><code>18</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CODABLOCK_A">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODABLOCK_A">INT_CODABLOCK_A</a></code></td>
<td class="colLast"><code>35</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CODE_16K">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE_16K">INT_CODE_16K</a></code></td>
<td class="colLast"><code>48</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CODE11">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE11">INT_CODE11</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CODE128">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE128">INT_CODE128</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CODE32">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE32">INT_CODE32</a></code></td>
<td class="colLast"><code>28</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CODE39">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE39">INT_CODE39</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CODE39_FULL_ASCII">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE39_FULL_ASCII">INT_CODE39_FULL_ASCII</a></code></td>
<td class="colLast"><code>59</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CODE49">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE49">INT_CODE49</a></code></td>
<td class="colLast"><code>36</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CODE93">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE93">INT_CODE93</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_COUPON_CODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_COUPON_CODE">INT_COUPON_CODE</a></code></td>
<td class="colLast"><code>55</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_CUSTOM_GS1_DATAMATRIX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_CUSTOM_GS1_DATAMATRIX">INT_CUSTOM_GS1_DATAMATRIX</a></code></td>
<td class="colLast"><code>10001</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_DATAMATRIX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_DATAMATRIX">INT_DATAMATRIX</a></code></td>
<td class="colLast"><code>43</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_DISCRETE_2_OF_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_DISCRETE_2_OF_5">INT_DISCRETE_2_OF_5</a></code></td>
<td class="colLast"><code>44</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_DUTCHPOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_DUTCHPOST">INT_DUTCHPOST</a></code></td>
<td class="colLast"><code>21</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_EAN_128">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_EAN_128">INT_EAN_128</a></code></td>
<td class="colLast"><code>46</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_EAN13">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_EAN13">INT_EAN13</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_EAN8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_EAN8">INT_EAN8</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_GRIDMATRIX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_GRIDMATRIX">INT_GRIDMATRIX</a></code></td>
<td class="colLast"><code>29</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_GS1_128">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_GS1_128">INT_GS1_128</a></code></td>
<td class="colLast"><code>31</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_GS1_DATA_MATRIX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_GS1_DATA_MATRIX">INT_GS1_DATA_MATRIX</a></code></td>
<td class="colLast"><code>60</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_GS1_DataBar">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_GS1_DataBar">INT_GS1_DataBar</a></code></td>
<td class="colLast"><code>38</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_GS1_DataBar_Expanded">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_GS1_DataBar_Expanded">INT_GS1_DataBar_Expanded</a></code></td>
<td class="colLast"><code>40</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_GS1_DataBar_Limited">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_GS1_DataBar_Limited">INT_GS1_DataBar_Limited</a></code></td>
<td class="colLast"><code>39</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_GS1_QR_CODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_GS1_QR_CODE">INT_GS1_QR_CODE</a></code></td>
<td class="colLast"><code>61</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_HANXIN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_HANXIN">INT_HANXIN</a></code></td>
<td class="colLast"><code>30</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_IATA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_IATA">INT_IATA</a></code></td>
<td class="colLast"><code>17</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_INFOMAIL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_INFOMAIL">INT_INFOMAIL</a></code></td>
<td class="colLast"><code>37</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_INTERLEAVED_2_OF_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_INTERLEAVED_2_OF_5">INT_INTERLEAVED_2_OF_5</a></code></td>
<td class="colLast"><code>45</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_ISBT_128">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_ISBT_128">INT_ISBT_128</a></code></td>
<td class="colLast"><code>54</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_ISSN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_ISSN">INT_ISSN</a></code></td>
<td class="colLast"><code>58</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_JAPOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_JAPOST">INT_JAPOST</a></code></td>
<td class="colLast"><code>19</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_KOREAPOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_KOREAPOST">INT_KOREAPOST</a></code></td>
<td class="colLast"><code>26</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_MACRO_PDF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_MACRO_PDF">INT_MACRO_PDF</a></code></td>
<td class="colLast"><code>56</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_MACRO_QR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_MACRO_QR">INT_MACRO_QR</a></code></td>
<td class="colLast"><code>57</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_MATRIX25">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_MATRIX25">INT_MATRIX25</a></code></td>
<td class="colLast"><code>27</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_MAXICODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_MAXICODE">INT_MAXICODE</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_MICRO_QR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_MICRO_QR">INT_MICRO_QR</a></code></td>
<td class="colLast"><code>51</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_MICROPDF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_MICROPDF">INT_MICROPDF</a></code></td>
<td class="colLast"><code>10</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_MSI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_MSI">INT_MSI</a></code></td>
<td class="colLast"><code>23</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_NEC_2_OF_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_NEC_2_OF_5">INT_NEC_2_OF_5</a></code></td>
<td class="colLast"><code>41</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_PDF417">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_PDF417">INT_PDF417</a></code></td>
<td class="colLast"><code>11</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_PLANET">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_PLANET">INT_PLANET</a></code></td>
<td class="colLast"><code>20</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_POSTAL_4I">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_POSTAL_4I">INT_POSTAL_4I</a></code></td>
<td class="colLast"><code>34</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_QR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_QR">INT_QR</a></code></td>
<td class="colLast"><code>25</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_TELEPEN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_TELEPEN">INT_TELEPEN</a></code></td>
<td class="colLast"><code>22</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_TLCODE39">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_TLCODE39">INT_TLCODE39</a></code></td>
<td class="colLast"><code>24</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_TRIOPTIC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_TRIOPTIC">INT_TRIOPTIC</a></code></td>
<td class="colLast"><code>50</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_UPC_D">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_UPC_D">INT_UPC_D</a></code></td>
<td class="colLast"><code>49</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_UPC_E1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_UPC_E1">INT_UPC_E1</a></code></td>
<td class="colLast"><code>47</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_UPCA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_UPCA">INT_UPCA</a></code></td>
<td class="colLast"><code>12</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_UPCE0">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_UPCE0">INT_UPCE0</a></code></td>
<td class="colLast"><code>13</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_US_POSTALS1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_US_POSTALS1">INT_US_POSTALS1</a></code></td>
<td class="colLast"><code>33</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.INT_USPS4CB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#INT_USPS4CB">INT_USPS4CB</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_AIM_128">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_AIM_128">STR_AIM_128</a></code></td>
<td class="colLast"><code>"AIM_128"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_AUSPOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_AUSPOST">STR_AUSPOST</a></code></td>
<td class="colLast"><code>"AUSPOST"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_AZTEC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_AZTEC">STR_AZTEC</a></code></td>
<td class="colLast"><code>"AZTEC"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_BOOKLAND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_BOOKLAND">STR_BOOKLAND</a></code></td>
<td class="colLast"><code>"BOOKLAND"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_BPO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_BPO">STR_BPO</a></code></td>
<td class="colLast"><code>"BPO"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CANPOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CANPOST">STR_CANPOST</a></code></td>
<td class="colLast"><code>"CANPOST"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CHINAPOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CHINAPOST">STR_CHINAPOST</a></code></td>
<td class="colLast"><code>"CHINAPOST"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CODABAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODABAR">STR_CODABAR</a></code></td>
<td class="colLast"><code>"CODABAR"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CODABLOCK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODABLOCK">STR_CODABLOCK</a></code></td>
<td class="colLast"><code>"CODABLOCK"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CODABLOCK_A">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODABLOCK_A">STR_CODABLOCK_A</a></code></td>
<td class="colLast"><code>"CODABLOCK_A"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CODE_16K">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE_16K">STR_CODE_16K</a></code></td>
<td class="colLast"><code>"CODE_16K"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CODE11">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE11">STR_CODE11</a></code></td>
<td class="colLast"><code>"CODE11"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CODE128">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE128">STR_CODE128</a></code></td>
<td class="colLast"><code>"CODE128"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CODE32">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE32">STR_CODE32</a></code></td>
<td class="colLast"><code>"CODE32"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CODE39">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE39">STR_CODE39</a></code></td>
<td class="colLast"><code>"CODE39"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CODE39_FULL_ASCII">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE39_FULL_ASCII">STR_CODE39_FULL_ASCII</a></code></td>
<td class="colLast"><code>"CODE39_FULL_ASCII1"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CODE49">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE49">STR_CODE49</a></code></td>
<td class="colLast"><code>"CODE49"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CODE93">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE93">STR_CODE93</a></code></td>
<td class="colLast"><code>"CODE93"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_COUPON_CODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_COUPON_CODE">STR_COUPON_CODE</a></code></td>
<td class="colLast"><code>"COUPON_CODE"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_CUSTOM_GS1_DATAMATRIX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_CUSTOM_GS1_DATAMATRIX">STR_CUSTOM_GS1_DATAMATRIX</a></code></td>
<td class="colLast"><code>"GS1_DATAMATRIX"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_DATAMATRIX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_DATAMATRIX">STR_DATAMATRIX</a></code></td>
<td class="colLast"><code>"DATAMATRIX"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_DISCRETE_2_OF_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_DISCRETE_2_OF_5">STR_DISCRETE_2_OF_5</a></code></td>
<td class="colLast"><code>"DISCRETE_2_OF_5"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_DUTCHPOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_DUTCHPOST">STR_DUTCHPOST</a></code></td>
<td class="colLast"><code>"DUTCHPOST"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_EAN_128">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_EAN_128">STR_EAN_128</a></code></td>
<td class="colLast"><code>"EAN128"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_EAN13">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_EAN13">STR_EAN13</a></code></td>
<td class="colLast"><code>"EAN13"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_EAN8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_EAN8">STR_EAN8</a></code></td>
<td class="colLast"><code>"EAN8"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_GRIDMATRIX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_GRIDMATRIX">STR_GRIDMATRIX</a></code></td>
<td class="colLast"><code>"GRIDMATRIX"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_GS1_128">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_GS1_128">STR_GS1_128</a></code></td>
<td class="colLast"><code>"GS1_128"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_GS1_DATA_MATRIX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_GS1_DATA_MATRIX">STR_GS1_DATA_MATRIX</a></code></td>
<td class="colLast"><code>"GS1_DATA_MATRIX"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_GS1_DataBar">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_GS1_DataBar">STR_GS1_DataBar</a></code></td>
<td class="colLast"><code>"GS1_DataBar"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_GS1_DataBar_Expanded">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_GS1_DataBar_Expanded">STR_GS1_DataBar_Expanded</a></code></td>
<td class="colLast"><code>"GS1_DataBar_Expanded"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_GS1_DataBar_Limited">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_GS1_DataBar_Limited">STR_GS1_DataBar_Limited</a></code></td>
<td class="colLast"><code>"GS1_DataBar_Limited"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_GS1_QR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_GS1_QR">STR_GS1_QR</a></code></td>
<td class="colLast"><code>"GS1_QR"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_HANXIN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_HANXIN">STR_HANXIN</a></code></td>
<td class="colLast"><code>"HANXIN"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_IATA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_IATA">STR_IATA</a></code></td>
<td class="colLast"><code>"IATA"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_INFOMAIL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_INFOMAIL">STR_INFOMAIL</a></code></td>
<td class="colLast"><code>"INFOMAIL"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_INTERLEAVED_2_OF_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_INTERLEAVED_2_OF_5">STR_INTERLEAVED_2_OF_5</a></code></td>
<td class="colLast"><code>"INTERLEAVED_2_OF_5"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_ISBT_128">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_ISBT_128">STR_ISBT_128</a></code></td>
<td class="colLast"><code>"ISBT_128"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_ISSN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_ISSN">STR_ISSN</a></code></td>
<td class="colLast"><code>"ISSN"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_JAPOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_JAPOST">STR_JAPOST</a></code></td>
<td class="colLast"><code>"JAPOST"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_KOREAPOST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_KOREAPOST">STR_KOREAPOST</a></code></td>
<td class="colLast"><code>"KOREAPOST"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_MACRO_PDF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_MACRO_PDF">STR_MACRO_PDF</a></code></td>
<td class="colLast"><code>"MACRO_PDF"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_MACRO_QR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_MACRO_QR">STR_MACRO_QR</a></code></td>
<td class="colLast"><code>"MACRO_QR"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_MATRIX25">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_MATRIX25">STR_MATRIX25</a></code></td>
<td class="colLast"><code>"MATRIX25"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_MAXICODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_MAXICODE">STR_MAXICODE</a></code></td>
<td class="colLast"><code>"MAXICODE"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_MICRO_QR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_MICRO_QR">STR_MICRO_QR</a></code></td>
<td class="colLast"><code>"MICRO_QR"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_MICROPDF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_MICROPDF">STR_MICROPDF</a></code></td>
<td class="colLast"><code>"MICROPDF"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_MSI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_MSI">STR_MSI</a></code></td>
<td class="colLast"><code>"MSI"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_NEC_2_OF_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_NEC_2_OF_5">STR_NEC_2_OF_5</a></code></td>
<td class="colLast"><code>"NEC_2_OF_5"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_PDF417">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_PDF417">STR_PDF417</a></code></td>
<td class="colLast"><code>"PDF417"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_PLANET">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_PLANET">STR_PLANET</a></code></td>
<td class="colLast"><code>"PLANET"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_POSTAL_4I">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_POSTAL_4I">STR_POSTAL_4I</a></code></td>
<td class="colLast"><code>"POSTAL_4I"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_QR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_QR">STR_QR</a></code></td>
<td class="colLast"><code>"QR"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_TELEPEN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_TELEPEN">STR_TELEPEN</a></code></td>
<td class="colLast"><code>"TELEPEN"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_TLCODE39">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_TLCODE39">STR_TLCODE39</a></code></td>
<td class="colLast"><code>"TLCODE39"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_TRIOPTIC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_TRIOPTIC">STR_TRIOPTIC</a></code></td>
<td class="colLast"><code>"TRIOPTIC"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_UPC_D">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_UPC_D">STR_UPC_D</a></code></td>
<td class="colLast"><code>"UPC_D"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_UPC_E1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_UPC_E1">STR_UPC_E1</a></code></td>
<td class="colLast"><code>"UPC_E1"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_UPCA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_UPCA">STR_UPCA</a></code></td>
<td class="colLast"><code>"UPCA"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_UPCE0">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_UPCE0">STR_UPCE0</a></code></td>
<td class="colLast"><code>"UPCE0"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_US_POSTALS1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_US_POSTALS1">STR_US_POSTALS1</a></code></td>
<td class="colLast"><code>"US_POSTALS1"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeSymbolUtility.STR_USPS4CB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeSymbolUtility.html#STR_USPS4CB">STR_USPS4CB</a></code></td>
<td class="colLast"><code>"USPS4CB"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.barcode.<a href="com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.ACTION_SCAN_KEYBOARD_HELPER_PARAM_RESPONSE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#ACTION_SCAN_KEYBOARD_HELPER_PARAM_RESPONSE">ACTION_SCAN_KEYBOARD_HELPER_PARAM_RESPONSE</a></code></td>
<td class="colLast"><code>"android.intent.action.SCAN_KEYBOARD_HELPER_PARAM_RESPONSE"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_BARCODE_1D">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_BARCODE_1D">SCANNER_BARCODE_1D</a></code></td>
<td class="colLast"><code>"scanner_cbBarcode1D"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_BARCODE_2D">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_BARCODE_2D">SCANNER_BARCODE_2D</a></code></td>
<td class="colLast"><code>"scanner_cbBarcode2D_s"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_BARCODENOTREPEAT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_BARCODENOTREPEAT">SCANNER_BARCODENOTREPEAT</a></code></td>
<td class="colLast"><code>"scanner_BarcodeNotRepeat"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_BROADCAST_ACTION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_BROADCAST_ACTION">SCANNER_BROADCAST_ACTION</a></code></td>
<td class="colLast"><code>"scanner_etBroadcast"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_BROADCAST_EXTRA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_BROADCAST_EXTRA">SCANNER_BROADCAST_EXTRA</a></code></td>
<td class="colLast"><code>"scanner_etBroadcastKey"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_CONTINUOUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_CONTINUOUS">SCANNER_CONTINUOUS</a></code></td>
<td class="colLast"><code>"scanner_Continuous"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_CONTINUOUS_INTREVALTIME">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_CONTINUOUS_INTREVALTIME">SCANNER_CONTINUOUS_INTREVALTIME</a></code></td>
<td class="colLast"><code>"scanner_ContinuousIntervalTime"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_CONTINUOUSMODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_CONTINUOUSMODE">SCANNER_CONTINUOUSMODE</a></code></td>
<td class="colLast"><code>"ContinuousMode"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_CONTINUOUSTIMEOUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_CONTINUOUSTIMEOUT">SCANNER_CONTINUOUSTIMEOUT</a></code></td>
<td class="colLast"><code>"scanner_ContinuousTimeOut"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_ENDINDEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_ENDINDEX">SCANNER_ENDINDEX</a></code></td>
<td class="colLast"><code>"scanner_etEndIndex"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_ENTER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_ENTER">SCANNER_ENTER</a></code></td>
<td class="colLast"><code>"scanner_cbEnter"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_FAILUREBROADCAST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_FAILUREBROADCAST">SCANNER_FAILUREBROADCAST</a></code></td>
<td class="colLast"><code>"scanner_failureBroadcast"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_FAILURESOUND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_FAILURESOUND">SCANNER_FAILURESOUND</a></code></td>
<td class="colLast"><code>"scanner_failureSound"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_FILTERCHARS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_FILTERCHARS">SCANNER_FILTERCHARS</a></code></td>
<td class="colLast"><code>"scanner_FilterChars"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_FORMAT_BARCODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_FORMAT_BARCODE">SCANNER_FORMAT_BARCODE</a></code></td>
<td class="colLast"><code>"scanner_format_barcode"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_ILLUMINATIONPOWERLEVEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_ILLUMINATIONPOWERLEVEL">SCANNER_ILLUMINATIONPOWERLEVEL</a></code></td>
<td class="colLast"><code>"scanner_IlluminationPowerLevel"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_KEYBORADHELPER_OPEN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_KEYBORADHELPER_OPEN">SCANNER_KEYBORADHELPER_OPEN</a></code></td>
<td class="colLast"><code>"scanner_cbOpen"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_OUTPUTMODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_OUTPUTMODE">SCANNER_OUTPUTMODE</a></code></td>
<td class="colLast"><code>"scanner_target"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_PREFIX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_PREFIX">SCANNER_PREFIX</a></code></td>
<td class="colLast"><code>"scanner_etPrefix"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_RELEASESCAN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_RELEASESCAN">SCANNER_RELEASESCAN</a></code></td>
<td class="colLast"><code>"scanner_cbERKOS"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_SCANKEYCODE_1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_SCANKEYCODE_1">SCANNER_SCANKEYCODE_1</a></code></td>
<td class="colLast"><code>"scanner_scanKeyCode_1"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_SCANKEYCODE_3">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_SCANKEYCODE_3">SCANNER_SCANKEYCODE_3</a></code></td>
<td class="colLast"><code>"scanner_scanKeyCode_3"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_SOUND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_SOUND">SCANNER_SOUND</a></code></td>
<td class="colLast"><code>"scanner_Sound"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_STARTINDEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_STARTINDEX">SCANNER_STARTINDEX</a></code></td>
<td class="colLast"><code>"scanner_etStartIndex"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_SUFFIX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_SUFFIX">SCANNER_SUFFIX</a></code></td>
<td class="colLast"><code>"scanner_etSuffix"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_TAB">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_TAB">SCANNER_TAB</a></code></td>
<td class="colLast"><code>"scanner_cbTab"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_TIMEOUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_TIMEOUT">SCANNER_TIMEOUT</a></code></td>
<td class="colLast"><code>"scanner_TimeOut"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.BarcodeUtility.SCANNER_VIBRATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/BarcodeUtility.html#SCANNER_VIBRATE">SCANNER_VIBRATE</a></code></td>
<td class="colLast"><code>"scanner_Vibrate"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.barcode.<a href="com/rscja/barcode/ConstantUtil.html" title="class in com.rscja.barcode">ConstantUtil</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.ConstantUtil.BARCODE_BYTE_DATA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/ConstantUtil.html#BARCODE_BYTE_DATA">BARCODE_BYTE_DATA</a></code></td>
<td class="colLast"><code>"barcodeByteData"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.ConstantUtil.BARCODE_CODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/ConstantUtil.html#BARCODE_CODE">BARCODE_CODE</a></code></td>
<td class="colLast"><code>"barcodeCode"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.ConstantUtil.BARCODE_DECODE_TIME">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/ConstantUtil.html#BARCODE_DECODE_TIME">BARCODE_DECODE_TIME</a></code></td>
<td class="colLast"><code>"barcodeDecodeTime"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.ConstantUtil.BARCODE_HEX_DATA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/ConstantUtil.html#BARCODE_HEX_DATA">BARCODE_HEX_DATA</a></code></td>
<td class="colLast"><code>"barcodeHexData"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.ConstantUtil.BARCODE_NAME">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/ConstantUtil.html#BARCODE_NAME">BARCODE_NAME</a></code></td>
<td class="colLast"><code>"barcodeNAME"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.ConstantUtil.BARCODE_RESULT_CODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/ConstantUtil.html#BARCODE_RESULT_CODE">BARCODE_RESULT_CODE</a></code></td>
<td class="colLast"><code>"barcodeResultCode"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.barcode.ConstantUtil.BARCODE_STRING_DATA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/ConstantUtil.html#BARCODE_STRING_DATA">BARCODE_STRING_DATA</a></code></td>
<td class="colLast"><code>"barcodeStringData"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.barcode.ConstantUtil.RESULT_STATUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/barcode/ConstantUtil.html#RESULT_STATUS">RESULT_STATUS</a></code></td>
<td class="colLast"><code>"resultStatus"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.RFIDWithUHFUrxUsbToUart.ACTION_USB_PERMISSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#ACTION_USB_PERMISSION">ACTION_USB_PERMISSION</a></code></td>
<td class="colLast"><code>"com.rscja.USB_PERMISSION"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.RFIDWithUHFUSB.ACTION_USB_PERMISSION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/RFIDWithUHFUSB.html#ACTION_USB_PERMISSION">ACTION_USB_PERMISSION</a></code></td>
<td class="colLast"><code>"com.rscja.USB_PERMISSION"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.deviceapi.<a href="com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.UhfBase.ErrorCode.ERRCODE_FAILURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/UhfBase.ErrorCode.html#ERRCODE_FAILURE">ERRCODE_FAILURE</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.UhfBase.ErrorCode.ERRCODE_SUCCESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/UhfBase.ErrorCode.html#ERRCODE_SUCCESS">ERRCODE_SUCCESS</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_INSUFFICIENT_PRIVILEGES">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_INSUFFICIENT_PRIVILEGES">ERROR_INSUFFICIENT_PRIVILEGES</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_MEMORY_LOCK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_MEMORY_LOCK">ERROR_MEMORY_LOCK</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_MEMORY_OVERRUN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_MEMORY_OVERRUN">ERROR_MEMORY_OVERRUN</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_NO_ENOUGH_POWER_ON_TAG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_NO_ENOUGH_POWER_ON_TAG">ERROR_NO_ENOUGH_POWER_ON_TAG</a></code></td>
<td class="colLast"><code>11</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_NO_TAG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_NO_TAG">ERROR_NO_TAG</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_OPERATION_FAILED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_OPERATION_FAILED">ERROR_OPERATION_FAILED</a></code></td>
<td class="colLast"><code>255</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_PASSWORD_IS_INCORRECT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_PASSWORD_IS_INCORRECT">ERROR_PASSWORD_IS_INCORRECT</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_RECV_FAIL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_RECV_FAIL">ERROR_RECV_FAIL</a></code></td>
<td class="colLast"><code>253</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_RESPONSE_BUFFER_OVERFLOW">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_RESPONSE_BUFFER_OVERFLOW">ERROR_RESPONSE_BUFFER_OVERFLOW</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_SEND_FAIL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_SEND_FAIL">ERROR_SEND_FAIL</a></code></td>
<td class="colLast"><code>252</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_TAG_NO_REPLY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_TAG_NO_REPLY">ERROR_TAG_NO_REPLY</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.A">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#A">A</a></code></td>
<td class="colLast"><code>"UPC-A, UPC-E, UPC-E1, EAN-8, EAN-13"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.B">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#B">B</a></code></td>
<td class="colLast"><code>"Code 39, Code 32"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.C">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#C">C</a></code></td>
<td class="colLast"><code>"Codabar"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.D">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#D">D</a></code></td>
<td class="colLast"><code>"Code 128, ISBT 128, ISBT 128 Concatenated"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.E">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#E">E</a></code></td>
<td class="colLast"><code>"Code 93"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.F">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#F">F</a></code></td>
<td class="colLast"><code>"Interleaved 2 of 5"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.G">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#G">G</a></code></td>
<td class="colLast"><code>"Discrete 2 of 5, or Discrete 2 of 5 IATA"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.H">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#H">H</a></code></td>
<td class="colLast"><code>"Code 11"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.J">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#J">J</a></code></td>
<td class="colLast"><code>"MSI"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.K">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#K">K</a></code></td>
<td class="colLast"><code>"GS1-128"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.L">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#L">L</a></code></td>
<td class="colLast"><code>"Bookland EAN"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.M">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#M">M</a></code></td>
<td class="colLast"><code>"Trioptic Code 39"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.N">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#N">N</a></code></td>
<td class="colLast"><code>"Coupon Code"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.P00">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#P00">P00</a></code></td>
<td class="colLast"><code>"Data Matrix"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.P01">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#P01">P01</a></code></td>
<td class="colLast"><code>"QR Code, MicroQR"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.P02">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#P02">P02</a></code></td>
<td class="colLast"><code>"Maxicode"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.P03">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#P03">P03</a></code></td>
<td class="colLast"><code>"US Postnet"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.P04">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#P04">P04</a></code></td>
<td class="colLast"><code>"US Planet"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.P05">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#P05">P05</a></code></td>
<td class="colLast"><code>"Japan Postal"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.P06">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#P06">P06</a></code></td>
<td class="colLast"><code>"UK Postal"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.P08">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#P08">P08</a></code></td>
<td class="colLast"><code>"Netherlands KIX Code"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.P09">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#P09">P09</a></code></td>
<td class="colLast"><code>"Australia Post"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.P0A">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#P0A">P0A</a></code></td>
<td class="colLast"><code>"USPS 4CB/One Code/Intelligent Mail"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.P0B">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#P0B">P0B</a></code></td>
<td class="colLast"><code>"UPU FICS Postal"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.P0H">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#P0H">P0H</a></code></td>
<td class="colLast"><code>"Han Xin"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.P0X">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#P0X">P0X</a></code></td>
<td class="colLast"><code>"Signature Capture"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.R">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#R">R</a></code></td>
<td class="colLast"><code>"GS1 DataBar Family"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.S">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#S">S</a></code></td>
<td class="colLast"><code>"Matrix 2 of 5"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.T">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#T">T</a></code></td>
<td class="colLast"><code>"UCC Composite, TLC 39"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.U">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#U">U</a></code></td>
<td class="colLast"><code>"Chinese 2 of 5"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.V">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#V">V</a></code></td>
<td class="colLast"><code>"Korean 3 of 5"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.X">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#X">X</a></code></td>
<td class="colLast"><code>"ISSN EAN, PDF417, Macro PDF417, Micro PDF417"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.BarcodeResult.Z">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/BarcodeResult.html#Z">Z</a></code></td>
<td class="colLast"><code>"Aztec, Aztec Rune"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity">GPIStateEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.GPIStateEntity.GPI1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/GPIStateEntity.html#GPI1">GPI1</a></code></td>
<td class="colLast"><code>"GPI1"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.GPIStateEntity.GPI2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/GPIStateEntity.html#GPI2">GPI2</a></code></td>
<td class="colLast"><code>"GPI2"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.GPIStateEntity.GPI3">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/GPIStateEntity.html#GPI3">GPI3</a></code></td>
<td class="colLast"><code>"GPI3"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.GPIStateEntity.GPI4">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/GPIStateEntity.html#GPI4">GPI4</a></code></td>
<td class="colLast"><code>"GPI4"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.GPOEntity.GPO1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/GPOEntity.html#GPO1">GPO1</a></code></td>
<td class="colLast"><code>"GPO1"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.GPOEntity.GPO2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/GPOEntity.html#GPO2">GPO2</a></code></td>
<td class="colLast"><code>"GPO2"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.GPOEntity.GPO3">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/GPOEntity.html#GPO3">GPO3</a></code></td>
<td class="colLast"><code>"GPO3"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.GPOEntity.GPO4">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/GPOEntity.html#GPO4">GPO4</a></code></td>
<td class="colLast"><code>"GPO4"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.GPOEntity.WiegandData0">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/GPOEntity.html#WiegandData0">WiegandData0</a></code></td>
<td class="colLast"><code>"WiegandData0"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.GPOEntity.WiegandData1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/GPOEntity.html#WiegandData1">WiegandData1</a></code></td>
<td class="colLast"><code>"WiegandData1"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.entity.UHFTAGInfo.EXTRADATA_EPCAREA">
<!--   -->
</a><code>public&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/entity/UHFTAGInfo.html#EXTRADATA_EPCAREA">EXTRADATA_EPCAREA</a></code></td>
<td class="colLast"><code>"epcArea"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcodePictureCallback.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodePictureCallback</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IBarcodePictureCallback.IMG_FORMAT_BMP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;byte</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IBarcodePictureCallback.html#IMG_FORMAT_BMP">IMG_FORMAT_BMP</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IBarcodePictureCallback.IMG_FORMAT_JPEG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;byte</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IBarcodePictureCallback.html#IMG_FORMAT_JPEG">IMG_FORMAT_JPEG</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IBarcodePictureCallback.IMG_FORMAT_TIFF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;byte</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IBarcodePictureCallback.html#IMG_FORMAT_TIFF">IMG_FORMAT_TIFF</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IBluetoothReader.VERSION_BT_FIRMWARE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_FIRMWARE">VERSION_BT_FIRMWARE</a></code></td>
<td class="colLast"><code>"FIRMWARE"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IBluetoothReader.VERSION_BT_HARDWARE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_HARDWARE">VERSION_BT_HARDWARE</a></code></td>
<td class="colLast"><code>"HARDWARE"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IBluetoothReader.VERSION_BT_SOFTWARE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_SOFTWARE">VERSION_BT_SOFTWARE</a></code></td>
<td class="colLast"><code>"SOFTWARE"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IReader.UPDATE_STM32">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;char</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IReader.html#UPDATE_STM32">UPDATE_STM32</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.Bank_EPC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC">Bank_EPC</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.Bank_RESERVED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED">Bank_RESERVED</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.Bank_TID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID">Bank_TID</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.Bank_USER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER">Bank_USER</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.LockBank_ACCESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#LockBank_ACCESS">LockBank_ACCESS</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.LockBank_EPC">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#LockBank_EPC">LockBank_EPC</a></code></td>
<td class="colLast"><code>48</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.LockBank_KILL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#LockBank_KILL">LockBank_KILL</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.LockBank_TID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#LockBank_TID">LockBank_TID</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.LockBank_USER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#LockBank_USER">LockBank_USER</a></code></td>
<td class="colLast"><code>80</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.LockMode_LOCK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#LockMode_LOCK">LockMode_LOCK</a></code></td>
<td class="colLast"><code>16</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.LockMode_OPEN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#LockMode_OPEN">LockMode_OPEN</a></code></td>
<td class="colLast"><code>32</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.LockMode_PLOCK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#LockMode_PLOCK">LockMode_PLOCK</a></code></td>
<td class="colLast"><code>48</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.LockMode_POPEN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#LockMode_POPEN">LockMode_POPEN</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.deviceapi.interfaces.IUHF.UPDATE_UHF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;char</code></td>
<td><code><a href="com/rscja/deviceapi/interfaces/IUHF.html#UPDATE_UHF">UPDATE_UHF</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.scanner.<a href="com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FORMAT_ASCII">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FORMAT_ASCII">FORMAT_ASCII</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FORMAT_DECIMAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FORMAT_DECIMAL">FORMAT_DECIMAL</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FORMAT_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FORMAT_DEFAULT">FORMAT_DEFAULT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FORMAT_GB18030">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FORMAT_GB18030">FORMAT_GB18030</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FORMAT_GB2312">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FORMAT_GB2312">FORMAT_GB2312</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FORMAT_GBK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FORMAT_GBK">FORMAT_GBK</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FORMAT_HEX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FORMAT_HEX">FORMAT_HEX</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FORMAT_UNICODE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FORMAT_UNICODE">FORMAT_UNICODE</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FORMAT_UTF8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FORMAT_UTF8">FORMAT_UTF8</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FUNCTION_14443A">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FUNCTION_14443A">FUNCTION_14443A</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FUNCTION_15693">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FUNCTION_15693">FUNCTION_15693</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FUNCTION_1D">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FUNCTION_1D">FUNCTION_1D</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FUNCTION_2D">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FUNCTION_2D">FUNCTION_2D</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FUNCTION_2D_H">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FUNCTION_2D_H">FUNCTION_2D_H</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FUNCTION_LF_ANIMAL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FUNCTION_LF_ANIMAL">FUNCTION_LF_ANIMAL</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FUNCTION_LF_EM4450">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FUNCTION_LF_EM4450">FUNCTION_LF_EM4450</a></code></td>
<td class="colLast"><code>9</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FUNCTION_LF_HDX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FUNCTION_LF_HDX">FUNCTION_LF_HDX</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FUNCTION_LF_HID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FUNCTION_LF_HID">FUNCTION_LF_HID</a></code></td>
<td class="colLast"><code>10</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FUNCTION_LF_HITAG">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FUNCTION_LF_HITAG">FUNCTION_LF_HITAG</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FUNCTION_LF_ID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FUNCTION_LF_ID">FUNCTION_LF_ID</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FUNCTION_LF_NEEDLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FUNCTION_LF_NEEDLE">FUNCTION_LF_NEEDLE</a></code></td>
<td class="colLast"><code>12</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.scanner.IScanner.FUNCTION_UHF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/scanner/IScanner.html#FUNCTION_UHF">FUNCTION_UHF</a></code></td>
<td class="colLast"><code>11</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.team.mtk.<a href="com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.mtk.DeviceConfiguration_mtk.C6000_6762">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/mtk/DeviceConfiguration_mtk.html#C6000_6762">C6000_6762</a></code></td>
<td class="colLast"><code>"C6000_6762"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.mtk.DeviceConfiguration_mtk.C90_6762_10">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/mtk/DeviceConfiguration_mtk.html#C90_6762_10">C90_6762_10</a></code></td>
<td class="colLast"><code>"C90"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.team.mtk.<a href="com/rscja/team/mtk/DeviceConfiguration_mtk.Platform.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk.Platform</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.mtk.DeviceConfiguration_mtk.Platform.MTK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/mtk/DeviceConfiguration_mtk.Platform.html#MTK">MTK</a></code></td>
<td class="colLast"><code>"mtk"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.mtk.DeviceConfiguration_mtk.Platform.QUACOMM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/mtk/DeviceConfiguration_mtk.Platform.html#QUACOMM">QUACOMM</a></code></td>
<td class="colLast"><code>"qualcomm"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.team.mtk.barcode.<a href="com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeDecoder_mtk</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeDecoder_mtk.DECODE_CANCEL">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#DECODE_CANCEL">DECODE_CANCEL</a></code></td>
<td class="colLast"><code>-1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeDecoder_mtk.DECODE_ENGINE_ERROR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#DECODE_ENGINE_ERROR">DECODE_ENGINE_ERROR</a></code></td>
<td class="colLast"><code>-3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeDecoder_mtk.DECODE_FAILURE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#DECODE_FAILURE">DECODE_FAILURE</a></code></td>
<td class="colLast"><code>-2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeDecoder_mtk.DECODE_SUCCESS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#DECODE_SUCCESS">DECODE_SUCCESS</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeDecoder_mtk.DECODE_TIMEOUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#DECODE_TIMEOUT">DECODE_TIMEOUT</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.team.mtk.barcode.<a href="com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeSymbolUtility_mtk</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.INT_GM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#INT_GM">INT_GM</a></code></td>
<td class="colLast"><code>56</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.INT_INDUSTRIAL_2_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#INT_INDUSTRIAL_2_5">INT_INDUSTRIAL_2_5</a></code></td>
<td class="colLast"><code>55</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.INT_ISBT_128">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#INT_ISBT_128">INT_ISBT_128</a></code></td>
<td class="colLast"><code>53</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.INT_OCR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#INT_OCR">INT_OCR</a></code></td>
<td class="colLast"><code>57</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.INT_STANDARD_2_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#INT_STANDARD_2_5">INT_STANDARD_2_5</a></code></td>
<td class="colLast"><code>54</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.STR_GM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#STR_GM">STR_GM</a></code></td>
<td class="colLast"><code>"GM"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.STR_INDUSTRIAL_2_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#STR_INDUSTRIAL_2_5">STR_INDUSTRIAL_2_5</a></code></td>
<td class="colLast"><code>"INDUSTRIAL_2_5"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.STR_ISBT_128">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#STR_ISBT_128">STR_ISBT_128</a></code></td>
<td class="colLast"><code>"ISBT_128"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.STR_OCR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#STR_OCR">STR_OCR</a></code></td>
<td class="colLast"><code>"OCR"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.STR_STANDARD_2_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#STR_STANDARD_2_5">STR_STANDARD_2_5</a></code></td>
<td class="colLast"><code>"STANDARD_2_5"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.team.qcom.<a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.C60_MTK_6765_110">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html#C60_MTK_6765_110">C60_MTK_6765_110</a></code></td>
<td class="colLast"><code>"C60_6765_110"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.C60_QCM2150_100">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html#C60_QCM2150_100">C60_QCM2150_100</a></code></td>
<td class="colLast"><code>"C60_QCM2150_100"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.C60_SMD450_100">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html#C60_SMD450_100">C60_SMD450_100</a></code></td>
<td class="colLast"><code>"C60_SMD450_100"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.C61_SMD450_90">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html#C61_SMD450_90">C61_SMD450_90</a></code></td>
<td class="colLast"><code>"C61_SMD450_90"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.C61P_SM6115_110">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html#C61P_SM6115_110">C61P_SM6115_110</a></code></td>
<td class="colLast"><code>"C61P_SM6115_110"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.C66_8953">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html#C66_8953">C66_8953</a></code></td>
<td class="colLast"><code>"C66_8953"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.C66_SMD450_90">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html#C66_SMD450_90">C66_SMD450_90</a></code></td>
<td class="colLast"><code>"C66_SMD450_90"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.C66P_SM6115_110">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html#C66P_SM6115_110">C66P_SM6115_110</a></code></td>
<td class="colLast"><code>"C66P_SM6115_110"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.H100_8953">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html#H100_8953">H100_8953</a></code></td>
<td class="colLast"><code>"H100_8953"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.MC50_4350_120">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html#MC50_4350_120">MC50_4350_120</a></code></td>
<td class="colLast"><code>"MC50_4350_120"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.P80_8786_130">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html#P80_8786_130">P80_8786_130</a></code></td>
<td class="colLast"><code>"P80_8786_130"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.P80_8953">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html#P80_8953">P80_8953</a></code></td>
<td class="colLast"><code>"P80_8953"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.P80_8953_90">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html#P80_8953_90">P80_8953_90</a></code></td>
<td class="colLast"><code>"P80_8953_90"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.team.qcom.<a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom.Platform</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.CPU_MTK_6735">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#CPU_MTK_6735">CPU_MTK_6735</a></code></td>
<td class="colLast"><code>"6735"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.CPU_MTK_6763">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#CPU_MTK_6763">CPU_MTK_6763</a></code></td>
<td class="colLast"><code>"6763"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.CPU_MTK_6765">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#CPU_MTK_6765">CPU_MTK_6765</a></code></td>
<td class="colLast"><code>"6765"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.CPU_MTK_8786">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#CPU_MTK_8786">CPU_MTK_8786</a></code></td>
<td class="colLast"><code>"8786"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.CPU_QUACOMM_4350">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#CPU_QUACOMM_4350">CPU_QUACOMM_4350</a></code></td>
<td class="colLast"><code>"4350"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.CPU_QUACOMM_8909">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#CPU_QUACOMM_8909">CPU_QUACOMM_8909</a></code></td>
<td class="colLast"><code>"8909"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.CPU_QUACOMM_8953">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#CPU_QUACOMM_8953">CPU_QUACOMM_8953</a></code></td>
<td class="colLast"><code>"8953"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.CPU_QUACOMM_QCM2150">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#CPU_QUACOMM_QCM2150">CPU_QUACOMM_QCM2150</a></code></td>
<td class="colLast"><code>"qcm2150"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.CPU_QUACOMM_SM6115">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#CPU_QUACOMM_SM6115">CPU_QUACOMM_SM6115</a></code></td>
<td class="colLast"><code>"sm6115"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.CPU_QUACOMM_SMD450">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#CPU_QUACOMM_SMD450">CPU_QUACOMM_SMD450</a></code></td>
<td class="colLast"><code>"smd450"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.CPU_RK_3568">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#CPU_RK_3568">CPU_RK_3568</a></code></td>
<td class="colLast"><code>"3568"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.MTK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#MTK">MTK</a></code></td>
<td class="colLast"><code>"mtk"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.QUACOMM">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#QUACOMM">QUACOMM</a></code></td>
<td class="colLast"><code>"qualcomm"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.DeviceConfiguration_qcom.Platform.RK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html#RK">RK</a></code></td>
<td class="colLast"><code>"rockchip"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.team.qcom.barcode.barcode2d.<a href="com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d">KeyboardEmulator2DDecoder_qcom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.barcode.barcode2d.KeyboardEmulator2DDecoder_qcom.ACTION_GET_LAST_DEC_IMAGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html#ACTION_GET_LAST_DEC_IMAGE">ACTION_GET_LAST_DEC_IMAGE</a></code></td>
<td class="colLast"><code>"com.rscja.scanner.action.GET_LAST_DEC_IMAGE"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.barcode.barcode2d.KeyboardEmulator2DDecoder_qcom.ACTION_SCAN_PARAMETER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html#ACTION_SCAN_PARAMETER">ACTION_SCAN_PARAMETER</a></code></td>
<td class="colLast"><code>"com.rscja.scanner.barcode.ZEBRA_PARAMETER_RESPONSE"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.barcode.barcode2d.KeyboardEmulator2DDecoder_qcom.DEC_IMAGE_KEY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html#DEC_IMAGE_KEY">DEC_IMAGE_KEY</a></code></td>
<td class="colLast"><code>"imageData"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.team.qcom.r1.hf.<a href="com/rscja/team/qcom/r1/hf/HFBase.html" title="class in com.rscja.team.qcom.r1.hf">HFBase</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_15693_SELECT">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_15693_SELECT">COMMAND_15693_SELECT</a></code></td>
<td class="colLast"><code>165</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_ACTIVATEIDLE">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ACTIVATEIDLE">COMMAND_ACTIVATEIDLE</a></code></td>
<td class="colLast"><code>65</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_ANTICOLL">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ANTICOLL">COMMAND_ANTICOLL</a></code></td>
<td class="colLast"><code>66</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_ANTICOLLINVENTORY">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ANTICOLLINVENTORY">COMMAND_ANTICOLLINVENTORY</a></code></td>
<td class="colLast"><code>174</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_AUTHENTICATION">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_AUTHENTICATION">COMMAND_AUTHENTICATION</a></code></td>
<td class="colLast"><code>69</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_COS_COMMAND">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_COS_COMMAND">COMMAND_COS_COMMAND</a></code></td>
<td class="colLast"><code>103</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_DECREMENT">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_DECREMENT">COMMAND_DECREMENT</a></code></td>
<td class="colLast"><code>74</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_FM1216RESET">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_FM1216RESET">COMMAND_FM1216RESET</a></code></td>
<td class="colLast"><code>57</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_GET_MUL_BLOCK">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_GET_MUL_BLOCK">COMMAND_GET_MUL_BLOCK</a></code></td>
<td class="colLast"><code>172</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_GET_SYSTEM_INFO">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_GET_SYSTEM_INFO">COMMAND_GET_SYSTEM_INFO</a></code></td>
<td class="colLast"><code>171</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_HALT">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_HALT">COMMAND_HALT</a></code></td>
<td class="colLast"><code>68</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_HALT_TYPEB">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_HALT_TYPEB">COMMAND_HALT_TYPEB</a></code></td>
<td class="colLast"><code>99</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_INCREMENT">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_INCREMENT">COMMAND_INCREMENT</a></code></td>
<td class="colLast"><code>75</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_INITVAL">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_INITVAL">COMMAND_INITVAL</a></code></td>
<td class="colLast"><code>72</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_INVENTORY">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_INVENTORY">COMMAND_INVENTORY</a></code></td>
<td class="colLast"><code>160</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_LOCK_AFI">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_LOCK_AFI">COMMAND_LOCK_AFI</a></code></td>
<td class="colLast"><code>168</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_LOCK_BLOCK">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_LOCK_BLOCK">COMMAND_LOCK_BLOCK</a></code></td>
<td class="colLast"><code>164</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_LOCK_DSFID">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_LOCK_DSFID">COMMAND_LOCK_DSFID</a></code></td>
<td class="colLast"><code>170</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_READ">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_READ">COMMAND_READ</a></code></td>
<td class="colLast"><code>70</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_READ_SM">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_READ_SM">COMMAND_READ_SM</a></code></td>
<td class="colLast"><code>162</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_READVAL">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_READVAL">COMMAND_READVAL</a></code></td>
<td class="colLast"><code>73</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_REQUESTA">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_REQUESTA">COMMAND_REQUESTA</a></code></td>
<td class="colLast"><code>64</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_RESET_TO_READY">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_RESET_TO_READY">COMMAND_RESET_TO_READY</a></code></td>
<td class="colLast"><code>166</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_RESTORE">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_RESTORE">COMMAND_RESTORE</a></code></td>
<td class="colLast"><code>76</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_SELECT">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_SELECT">COMMAND_SELECT</a></code></td>
<td class="colLast"><code>67</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_TRANSFER">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TRANSFER">COMMAND_TRANSFER</a></code></td>
<td class="colLast"><code>77</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_TRANSFERCOMMAND">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TRANSFERCOMMAND">COMMAND_TRANSFERCOMMAND</a></code></td>
<td class="colLast"><code>173</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_TYPEARATS">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TYPEARATS">COMMAND_TYPEARATS</a></code></td>
<td class="colLast"><code>80</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_TYPEAREST">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TYPEAREST">COMMAND_TYPEAREST</a></code></td>
<td class="colLast"><code>81</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_TYPEB_GET_UID">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TYPEB_GET_UID">COMMAND_TYPEB_GET_UID</a></code></td>
<td class="colLast"><code>101</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_TYPEBREST">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TYPEBREST">COMMAND_TYPEBREST</a></code></td>
<td class="colLast"><code>100</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_ULANTICOLL">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ULANTICOLL">COMMAND_ULANTICOLL</a></code></td>
<td class="colLast"><code>78</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_ULWRITE">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ULWRITE">COMMAND_ULWRITE</a></code></td>
<td class="colLast"><code>79</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_WRITE">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_WRITE">COMMAND_WRITE</a></code></td>
<td class="colLast"><code>71</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_WRITE_AFI">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_WRITE_AFI">COMMAND_WRITE_AFI</a></code></td>
<td class="colLast"><code>167</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_WRITE_DSFID">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_WRITE_DSFID">COMMAND_WRITE_DSFID</a></code></td>
<td class="colLast"><code>169</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.r1.hf.HFBase.COMMAND_WRITE_SM">
<!--   -->
</a><code>public&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_WRITE_SM">COMMAND_WRITE_SM</a></code></td>
<td class="colLast"><code>163</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.team.qcom.scanner.utility.<a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility">ScannerParameterUtility_qcom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_barcodeBroadcastAction">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_barcodeBroadcastAction">key_barcodeBroadcastAction</a></code></td>
<td class="colLast"><code>"BarcodeBroadcastAction"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_BarcodeNotRepeat">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_BarcodeNotRepeat">key_BarcodeNotRepeat</a></code></td>
<td class="colLast"><code>"BarcodeNotRepeat"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_cbBarcode2D_s">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_cbBarcode2D_s">key_cbBarcode2D_s</a></code></td>
<td class="colLast"><code>"Barcode2D"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_cbEnter">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_cbEnter">key_cbEnter</a></code></td>
<td class="colLast"><code>"Enter"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_cbERKOS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_cbERKOS">key_cbERKOS</a></code></td>
<td class="colLast"><code>"ReleaseScanKeySotpScan"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_cbSpace">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_cbSpace">key_cbSpace</a></code></td>
<td class="colLast"><code>"Space"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_cbTab">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_cbTab">key_cbTab</a></code></td>
<td class="colLast"><code>"Tab"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_Continuous">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_Continuous">key_Continuous</a></code></td>
<td class="colLast"><code>"BarcodeContinuousScan"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_ContinuousIntervalTime">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ContinuousIntervalTime">key_ContinuousIntervalTime</a></code></td>
<td class="colLast"><code>"BarcodeContinuousScanIntervalTime"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_ContinuousIntervalTimeUHF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ContinuousIntervalTimeUHF">key_ContinuousIntervalTimeUHF</a></code></td>
<td class="colLast"><code>"UHFContinuousScanIntervalTime"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_ContinuousMode">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ContinuousMode">key_ContinuousMode</a></code></td>
<td class="colLast"><code>"BarcodeContinuousScanMode"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_ContinuousTimeOut">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ContinuousTimeOut">key_ContinuousTimeOut</a></code></td>
<td class="colLast"><code>"BarcodeContinuousScanTimeOut"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_ContinuousTimeOutUHF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ContinuousTimeOutUHF">key_ContinuousTimeOutUHF</a></code></td>
<td class="colLast"><code>"UHFContinuousScanTimeOut"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_ContinuousUHF">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ContinuousUHF">key_ContinuousUHF</a></code></td>
<td class="colLast"><code>"UHFContinuous"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_debug">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_debug">key_debug</a></code></td>
<td class="colLast"><code>"Scanner_Debug"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_endIndex">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_endIndex">key_endIndex</a></code></td>
<td class="colLast"><code>"EndIndex"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_etBroadcastKey">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_etBroadcastKey">key_etBroadcastKey</a></code></td>
<td class="colLast"><code>"BarcodeBroadcastDataKey"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_failureBroadcast">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_failureBroadcast">key_failureBroadcast</a></code></td>
<td class="colLast"><code>"ScanFailureBroadcast"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_failureSound">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_failureSound">key_failureSound</a></code></td>
<td class="colLast"><code>"ScanFailureSound"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_filterChars">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_filterChars">key_filterChars</a></code></td>
<td class="colLast"><code>"FilterChars"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_firstInit">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_firstInit">key_firstInit</a></code></td>
<td class="colLast"><code>"Scanner_FirstInit"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_firstSetScanP">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_firstSetScanP">key_firstSetScanP</a></code></td>
<td class="colLast"><code>"Scanner_FirstSetScanP"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_format_Barcode">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_format_Barcode">key_format_Barcode</a></code></td>
<td class="colLast"><code>"BarcodeFormat"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_format_RFID">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_format_RFID">key_format_RFID</a></code></td>
<td class="colLast"><code>"RFIDFormat"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_GS1Parsing">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_GS1Parsing">key_GS1Parsing</a></code></td>
<td class="colLast"><code>"GS1Parsing"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_IlluminationPowerLevel">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_IlluminationPowerLevel">key_IlluminationPowerLevel</a></code></td>
<td class="colLast"><code>"IlluminationPowerLevel"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_InterceptScanKey">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_InterceptScanKey">key_InterceptScanKey</a></code></td>
<td class="colLast"><code>"InterceptScanKey"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_LF_Last4Bytes">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_LF_Last4Bytes">key_LF_Last4Bytes</a></code></td>
<td class="colLast"><code>"RFID_LF_Last4Bytes"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_Mirror">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_Mirror">key_Mirror</a></code></td>
<td class="colLast"><code>"CoAsiaMirror"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_prefix">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_prefix">key_prefix</a></code></td>
<td class="colLast"><code>"Prefix"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_ScanAuxiliaryLight">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_ScanAuxiliaryLight">key_ScanAuxiliaryLight</a></code></td>
<td class="colLast"><code>"ScanAuxiliaryLight"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_Scanner_Enable">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_Scanner_Enable">key_Scanner_Enable</a></code></td>
<td class="colLast"><code>"Scanner_Enable"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_scanOnRelease">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_scanOnRelease">key_scanOnRelease</a></code></td>
<td class="colLast"><code>"ScanOnRelease"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_sort">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_sort">key_sort</a></code></td>
<td class="colLast"><code>"Scanner_Sort"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_Sound">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_Sound">key_Sound</a></code></td>
<td class="colLast"><code>"SuccessSound"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_startIndex">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_startIndex">key_startIndex</a></code></td>
<td class="colLast"><code>"StartIndex"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_suffix">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_suffix">key_suffix</a></code></td>
<td class="colLast"><code>"Suffix"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_target">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_target">key_target</a></code></td>
<td class="colLast"><code>"OutputMode"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_TimeOut">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_TimeOut">key_TimeOut</a></code></td>
<td class="colLast"><code>"ScanTimeOut"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.scanner.utility.ScannerParameterUtility_qcom.key_Vibrate">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html#key_Vibrate">key_Vibrate</a></code></td>
<td class="colLast"><code>"Vibrate"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.team.qcom.service.<a href="com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service">BLEService_qcom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.service.BLEService_qcom.ACTION_DATA_AVAILABLE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/service/BLEService_qcom.html#ACTION_DATA_AVAILABLE">ACTION_DATA_AVAILABLE</a></code></td>
<td class="colLast"><code>"com.nordicsemi.nrfUART.ACTION_DATA_AVAILABLE"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.service.BLEService_qcom.ACTION_GATT_DISCONNECTED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/service/BLEService_qcom.html#ACTION_GATT_DISCONNECTED">ACTION_GATT_DISCONNECTED</a></code></td>
<td class="colLast"><code>"com.nordicsemi.nrfUART.ACTION_GATT_DISCONNECTED"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.service.BLEService_qcom.ACTION_GATT_SERVICES_DISCOVERED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/service/BLEService_qcom.html#ACTION_GATT_SERVICES_DISCOVERED">ACTION_GATT_SERVICES_DISCOVERED</a></code></td>
<td class="colLast"><code>"com.nordicsemi.nrfUART.ACTION_GATT_SERVICES_DISCOVERED"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.service.BLEService_qcom.ACTION_SEARCH_DEVICES">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/service/BLEService_qcom.html#ACTION_SEARCH_DEVICES">ACTION_SEARCH_DEVICES</a></code></td>
<td class="colLast"><code>"com.nordicsemi.nrfUART.ACTION_SEARCH_DEVICES"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.service.BLEService_qcom.BT_DEVICE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/service/BLEService_qcom.html#BT_DEVICE">BT_DEVICE</a></code></td>
<td class="colLast"><code>"device"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.service.BLEService_qcom.BT_RECORD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/service/BLEService_qcom.html#BT_RECORD">BT_RECORD</a></code></td>
<td class="colLast"><code>"record"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.service.BLEService_qcom.BT_RSSI">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/service/BLEService_qcom.html#BT_RSSI">BT_RSSI</a></code></td>
<td class="colLast"><code>"rssi"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.service.BLEService_qcom.DEVICE_DOES_NOT_SUPPORT_UART">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/service/BLEService_qcom.html#DEVICE_DOES_NOT_SUPPORT_UART">DEVICE_DOES_NOT_SUPPORT_UART</a></code></td>
<td class="colLast"><code>"com.nordicsemi.nrfUART.DEVICE_DOES_NOT_SUPPORT_UART"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.service.BLEService_qcom.EXTRA_DATA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/rscja/team/qcom/service/BLEService_qcom.html#EXTRA_DATA">EXTRA_DATA</a></code></td>
<td class="colLast"><code>"com.nordicsemi.nrfUART.EXTRA_DATA"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>com.rscja.team.qcom.usb.pl2302.<a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.DATABITS_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#DATABITS_5">DATABITS_5</a></code></td>
<td class="colLast"><code>5</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.DATABITS_6">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#DATABITS_6">DATABITS_6</a></code></td>
<td class="colLast"><code>6</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.DATABITS_7">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#DATABITS_7">DATABITS_7</a></code></td>
<td class="colLast"><code>7</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.DATABITS_8">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#DATABITS_8">DATABITS_8</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.FLOWCONTROL_NONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_NONE">FLOWCONTROL_NONE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.FLOWCONTROL_RTSCTS_IN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_RTSCTS_IN">FLOWCONTROL_RTSCTS_IN</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.FLOWCONTROL_RTSCTS_OUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_RTSCTS_OUT">FLOWCONTROL_RTSCTS_OUT</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.FLOWCONTROL_XONXOFF_IN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_XONXOFF_IN">FLOWCONTROL_XONXOFF_IN</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.FLOWCONTROL_XONXOFF_OUT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_XONXOFF_OUT">FLOWCONTROL_XONXOFF_OUT</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.PARITY_EVEN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_EVEN">PARITY_EVEN</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.PARITY_MARK">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_MARK">PARITY_MARK</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.PARITY_NONE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_NONE">PARITY_NONE</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.PARITY_ODD">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_ODD">PARITY_ODD</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.PARITY_SPACE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_SPACE">PARITY_SPACE</a></code></td>
<td class="colLast"><code>4</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.STOPBITS_1">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#STOPBITS_1">STOPBITS_1</a></code></td>
<td class="colLast"><code>1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.STOPBITS_1_5">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#STOPBITS_1_5">STOPBITS_1_5</a></code></td>
<td class="colLast"><code>3</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.STOPBITS_2">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#STOPBITS_2">STOPBITS_2</a></code></td>
<td class="colLast"><code>2</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-files/index-1.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">Frames</a></li>
<li><a href="constant-values.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
