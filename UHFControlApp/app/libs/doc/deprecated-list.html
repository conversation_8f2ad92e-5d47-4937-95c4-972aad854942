<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Deprecated List</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Deprecated List";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Deprecated</li>
<li><a href="index-files/index-1.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">Frames</a></li>
<li><a href="deprecated-list.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Deprecated API" class="title">Deprecated API</h1>
<h2 title="Contents">Contents</h2>
<ul>
<li><a href="#field">Deprecated Fields</a></li>
<li><a href="#method">Deprecated Methods</a></li>
</ul>
</div>
<div class="contentContainer"><a name="field">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="Deprecated Fields table, listing deprecated fields, and an explanation">
<caption><span>Deprecated Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#CameraStateFile">com.rscja.team.mtk.barcode.Barcode2DSoftCommon_mtk.CameraStateFile</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#ScannerCameraIdFile">com.rscja.team.mtk.barcode.Barcode2DSoftCommon_mtk.ScannerCameraIdFile</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#ScannerStateFile">com.rscja.team.mtk.barcode.Barcode2DSoftCommon_mtk.ScannerStateFile</a></td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="method">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="Deprecated Methods table, listing deprecated methods, and an explanation">
<caption><span>Deprecated Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#decryption-byte:A-int-">com.rscja.team.qcom.deviceapi.FingerprintWithFIPS_qcom.decryption(byte[], int)</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#decryption-byte:A-int-">com.rscja.team.mtk.deviceapi.FingerprintWithFIPS_mtk.decryption(byte[], int)</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#decryption-byte:A-int-byte:A-">com.rscja.team.qcom.deviceapi.FingerprintWithFIPS_qcom.decryption(byte[], int, byte[])</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#decryption-byte:A-int-byte:A-">com.rscja.team.mtk.deviceapi.FingerprintWithFIPS_mtk.decryption(byte[], int, byte[])</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#decryption-byte:A-int-java.lang.String-">com.rscja.team.qcom.deviceapi.FingerprintWithFIPS_qcom.decryption(byte[], int, String)</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#decryption-byte:A-int-java.lang.String-">com.rscja.team.mtk.deviceapi.FingerprintWithFIPS_mtk.decryption(byte[], int, String)</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#Encryption-byte:A-int-">com.rscja.team.qcom.deviceapi.FingerprintWithFIPS_qcom.Encryption(byte[], int)</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#Encryption-byte:A-int-">com.rscja.team.mtk.deviceapi.FingerprintWithFIPS_mtk.Encryption(byte[], int)</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html#Encryption-byte:A-int-java.lang.String-">com.rscja.team.qcom.deviceapi.FingerprintWithFIPS_qcom.Encryption(byte[], int, String)</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html#Encryption-byte:A-int-java.lang.String-">com.rscja.team.mtk.deviceapi.FingerprintWithFIPS_mtk.Encryption(byte[], int, String)</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/utility/StringUtility.html#isHexNumber-java.lang.String-">com.rscja.utility.StringUtility.isHexNumber(String)</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#readCameraState--">com.rscja.team.mtk.barcode.Barcode2DSoftCommon_mtk.readCameraState()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#readTagFromBuffer--">com.rscja.deviceapi.RFIDWithUHFA8NetWork.readTagFromBuffer()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/deviceapi/RFIDWithUHFUSB.html#readTagFromBuffer--">com.rscja.deviceapi.RFIDWithUHFUSB.readTagFromBuffer()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/deviceapi/RFIDWithUHFUrxUart.html#readTagFromBuffer--">com.rscja.deviceapi.RFIDWithUHFUrxUart.readTagFromBuffer()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#readTagFromBuffer--">com.rscja.deviceapi.RFIDWithUHFUrxUsbToUart.readTagFromBuffer()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#readTagFromBuffer--">com.rscja.deviceapi.RFIDWithUHFUrxNetwork.readTagFromBuffer()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#readTagFromBuffer--">com.rscja.deviceapi.RFIDWithUHFA4NetWork.readTagFromBuffer()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/deviceapi/RFIDWithUHFBLE.html#readTagFromBuffer--">com.rscja.deviceapi.RFIDWithUHFBLE.readTagFromBuffer()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/deviceapi/RFIDWithUHFUART.html#readTagFromBuffer--">com.rscja.deviceapi.RFIDWithUHFUART.readTagFromBuffer()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/deviceapi/RFIDWithUHFA4RS232.html#readTagFromBuffer--">com.rscja.deviceapi.RFIDWithUHFA4RS232.readTagFromBuffer()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/deviceapi/RFIDWithUHFA8RS232.html#readTagFromBuffer--">com.rscja.deviceapi.RFIDWithUHFA8RS232.readTagFromBuffer()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/deviceapi/interfaces/IUHF.html#readTagFromBuffer--">com.rscja.deviceapi.interfaces.IUHF.readTagFromBuffer()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/deviceapi/RFIDWithUHFBLE.html#readTagFromBufferList_EpcTidUser--">com.rscja.deviceapi.RFIDWithUHFBLE.readTagFromBufferList_EpcTidUser()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/deviceapi/interfaces/IUhfBle.html#readTagFromBufferList_EpcTidUser--">com.rscja.deviceapi.interfaces.IUhfBle.readTagFromBufferList_EpcTidUser()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#readTagFromBufferList_EpcTidUser--">com.rscja.team.qcom.deviceapi.RFIDWithUHFBLE_qcom.readTagFromBufferList_EpcTidUser()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/deviceapi/RFIDWithUHFUSB.html#readTagFromBufferList--">com.rscja.deviceapi.RFIDWithUHFUSB.readTagFromBufferList()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/deviceapi/RFIDWithUHFBLE.html#readTagFromBufferList--">com.rscja.deviceapi.RFIDWithUHFBLE.readTagFromBufferList()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/deviceapi/interfaces/IUhfReader.html#readTagFromBufferList--">com.rscja.deviceapi.interfaces.IUhfReader.readTagFromBufferList()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/deviceapi/FingerprintWithMorpho.html#stopEnroll--">com.rscja.deviceapi.FingerprintWithMorpho.stopEnroll()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#stopEnroll--">com.rscja.deviceapi.interfaces.IFingerprintWithMorpho.stopEnroll()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html#stopEnroll--">com.rscja.team.qcom.deviceapi.FingerprintWithMorpho_qcom.stopEnroll()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#stopEnroll--">com.rscja.team.mtk.deviceapi.FingerprintWithMorpho_mtk.stopEnroll()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/deviceapi/FingerprintWithMorpho.html#stopIdentification--">com.rscja.deviceapi.FingerprintWithMorpho.stopIdentification()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#stopIdentification--">com.rscja.deviceapi.interfaces.IFingerprintWithMorpho.stopIdentification()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html#stopIdentification--">com.rscja.team.qcom.deviceapi.FingerprintWithMorpho_qcom.stopIdentification()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#stopIdentification--">com.rscja.team.mtk.deviceapi.FingerprintWithMorpho_mtk.stopIdentification()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/deviceapi/FingerprintWithMorpho.html#stopPtCapture--">com.rscja.deviceapi.FingerprintWithMorpho.stopPtCapture()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#stopPtCapture--">com.rscja.deviceapi.interfaces.IFingerprintWithMorpho.stopPtCapture()</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html#stopPtCapture--">com.rscja.team.qcom.deviceapi.FingerprintWithMorpho_qcom.stopPtCapture()</a></td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#stopPtCapture--">com.rscja.team.mtk.deviceapi.FingerprintWithMorpho_mtk.stopPtCapture()</a></td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li><a href="overview-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Deprecated</li>
<li><a href="index-files/index-1.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">Frames</a></li>
<li><a href="deprecated-list.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
