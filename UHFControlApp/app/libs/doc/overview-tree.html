<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Class Hierarchy</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Class Hierarchy";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-files/index-1.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-tree.html" target="_top">Frames</a></li>
<li><a href="overview-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For All Packages</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="com/rscja/package-tree.html">com.rscja</a>, </li>
<li><a href="com/rscja/barcode/package-tree.html">com.rscja.barcode</a>, </li>
<li><a href="com/rscja/custom/package-tree.html">com.rscja.custom</a>, </li>
<li><a href="com/rscja/custom/interfaces/package-tree.html">com.rscja.custom.interfaces</a>, </li>
<li><a href="com/rscja/deviceapi/package-tree.html">com.rscja.deviceapi</a>, </li>
<li><a href="com/rscja/deviceapi/entity/package-tree.html">com.rscja.deviceapi.entity</a>, </li>
<li><a href="com/rscja/deviceapi/enums/package-tree.html">com.rscja.deviceapi.enums</a>, </li>
<li><a href="com/rscja/deviceapi/exception/package-tree.html">com.rscja.deviceapi.exception</a>, </li>
<li><a href="com/rscja/deviceapi/interfaces/package-tree.html">com.rscja.deviceapi.interfaces</a>, </li>
<li><a href="com/rscja/scanner/package-tree.html">com.rscja.scanner</a>, </li>
<li><a href="com/rscja/scanner/led/package-tree.html">com.rscja.scanner.led</a>, </li>
<li><a href="com/rscja/scanner/utility/package-tree.html">com.rscja.scanner.utility</a>, </li>
<li><a href="com/rscja/system/package-tree.html">com.rscja.system</a>, </li>
<li><a href="com/rscja/team/mtk/package-tree.html">com.rscja.team.mtk</a>, </li>
<li><a href="com/rscja/team/mtk/barcode/package-tree.html">com.rscja.team.mtk.barcode</a>, </li>
<li><a href="com/rscja/team/mtk/barcode/barcode2d/package-tree.html">com.rscja.team.mtk.barcode.barcode2d</a>, </li>
<li><a href="com/rscja/team/mtk/barcode/symbol/package-tree.html">com.rscja.team.mtk.barcode.symbol</a>, </li>
<li><a href="com/rscja/team/mtk/custom/package-tree.html">com.rscja.team.mtk.custom</a>, </li>
<li><a href="com/rscja/team/mtk/deviceapi/package-tree.html">com.rscja.team.mtk.deviceapi</a>, </li>
<li><a href="com/rscja/team/mtk/scanner/led/package-tree.html">com.rscja.team.mtk.scanner.led</a>, </li>
<li><a href="com/rscja/team/mtk/scanner/utility/package-tree.html">com.rscja.team.mtk.scanner.utility</a>, </li>
<li><a href="com/rscja/team/mtk/system/package-tree.html">com.rscja.team.mtk.system</a>, </li>
<li><a href="com/rscja/team/mtk/utility/package-tree.html">com.rscja.team.mtk.utility</a>, </li>
<li><a href="com/rscja/team/qcom/package-tree.html">com.rscja.team.qcom</a>, </li>
<li><a href="com/rscja/team/qcom/barcode/package-tree.html">com.rscja.team.qcom.barcode</a>, </li>
<li><a href="com/rscja/team/qcom/barcode/barcode2d/package-tree.html">com.rscja.team.qcom.barcode.barcode2d</a>, </li>
<li><a href="com/rscja/team/qcom/barcode/symbol/package-tree.html">com.rscja.team.qcom.barcode.symbol</a>, </li>
<li><a href="com/rscja/team/qcom/ble/package-tree.html">com.rscja.team.qcom.ble</a>, </li>
<li><a href="com/rscja/team/qcom/custom/package-tree.html">com.rscja.team.qcom.custom</a>, </li>
<li><a href="com/rscja/team/qcom/deviceapi/package-tree.html">com.rscja.team.qcom.deviceapi</a>, </li>
<li><a href="com/rscja/team/qcom/http/package-tree.html">com.rscja.team.qcom.http</a>, </li>
<li><a href="com/rscja/team/qcom/r1/package-tree.html">com.rscja.team.qcom.r1</a>, </li>
<li><a href="com/rscja/team/qcom/r1/hf/package-tree.html">com.rscja.team.qcom.r1.hf</a>, </li>
<li><a href="com/rscja/team/qcom/r1/psam/package-tree.html">com.rscja.team.qcom.r1.psam</a>, </li>
<li><a href="com/rscja/team/qcom/rs232utils/package-tree.html">com.rscja.team.qcom.rs232utils</a>, </li>
<li><a href="com/rscja/team/qcom/scanner/led/package-tree.html">com.rscja.team.qcom.scanner.led</a>, </li>
<li><a href="com/rscja/team/qcom/scanner/utility/package-tree.html">com.rscja.team.qcom.scanner.utility</a>, </li>
<li><a href="com/rscja/team/qcom/serialportapi/package-tree.html">com.rscja.team.qcom.serialportapi</a>, </li>
<li><a href="com/rscja/team/qcom/service/package-tree.html">com.rscja.team.qcom.service</a>, </li>
<li><a href="com/rscja/team/qcom/socket/package-tree.html">com.rscja.team.qcom.socket</a>, </li>
<li><a href="com/rscja/team/qcom/system/package-tree.html">com.rscja.team.qcom.system</a>, </li>
<li><a href="com/rscja/team/qcom/uhfhandler/package-tree.html">com.rscja.team.qcom.uhfhandler</a>, </li>
<li><a href="com/rscja/team/qcom/uhfparse/package-tree.html">com.rscja.team.qcom.uhfparse</a>, </li>
<li><a href="com/rscja/team/qcom/urax/package-tree.html">com.rscja.team.qcom.urax</a>, </li>
<li><a href="com/rscja/team/qcom/usb/package-tree.html">com.rscja.team.qcom.usb</a>, </li>
<li><a href="com/rscja/team/qcom/usb/pl2302/package-tree.html">com.rscja.team.qcom.usb.pl2302</a>, </li>
<li><a href="com/rscja/team/qcom/utility/package-tree.html">com.rscja.team.qcom.utility</a>, </li>
<li><a href="com/rscja/utility/package-tree.html">com.rscja.utility</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">AnimalEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/AntennaConnectState.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">AntennaConnectState</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">AntennaPowerEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">AntennaState</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/Barcode1D.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Barcode1D</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcode1D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode1D</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/Barcode1D_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Barcode1D_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcode1D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode1D</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/Barcode1D_qcom.UHFProtocolParseBase.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Barcode1D_qcom.UHFProtocolParseBase</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/Barcode2D.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Barcode2D</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Barcode2D_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a>)</li>
<li type="circle">com.rscja.team.mtk.barcode.barcode2d.<a href="com/rscja/team/mtk/barcode/barcode2d/Barcode2DFactory_mtk.html" title="class in com.rscja.team.mtk.barcode.barcode2d"><span class="typeNameLink">Barcode2DFactory_mtk</span></a></li>
<li type="circle">com.rscja.team.qcom.barcode.barcode2d.<a href="com/rscja/team/qcom/barcode/barcode2d/Barcode2DFactory_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d"><span class="typeNameLink">Barcode2DFactory_qcom</span></a></li>
<li type="circle">com.rscja.barcode.<a href="com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode"><span class="typeNameLink">Barcode2DSHardwareInfo</span></a> (implements com.rscja.barcode.<a href="com/rscja/barcode/IBarcode2DSHardwareInfo.html" title="interface in com.rscja.barcode">IBarcode2DSHardwareInfo</a>)</li>
<li type="circle">com.rscja.team.mtk.barcode.<a href="com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Barcode2DSHardwareInfo_mtk</span></a> (implements com.rscja.barcode.<a href="com/rscja/barcode/IBarcode2DSHardwareInfo.html" title="interface in com.rscja.barcode">IBarcode2DSHardwareInfo</a>)</li>
<li type="circle">com.rscja.team.qcom.barcode.<a href="com/rscja/team/qcom/barcode/Barcode2DSHardwareInfo_qcom.html" title="class in com.rscja.team.qcom.barcode"><span class="typeNameLink">Barcode2DSHardwareInfo_qcom</span></a> (implements com.rscja.barcode.<a href="com/rscja/barcode/IBarcode2DSHardwareInfo.html" title="interface in com.rscja.barcode">IBarcode2DSHardwareInfo</a>)</li>
<li type="circle">com.rscja.team.mtk.barcode.<a href="com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Barcode2DSoftCommon_mtk</span></a></li>
<li type="circle">com.rscja.barcode.<a href="com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode"><span class="typeNameLink">BarcodeDecoder</span></a>
<ul>
<li type="circle">com.rscja.team.mtk.barcode.barcode2d.<a href="com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html" title="class in com.rscja.team.mtk.barcode.barcode2d"><span class="typeNameLink">KeyboardEmulator2DDecoder_mtk</span></a></li>
<li type="circle">com.rscja.team.qcom.barcode.barcode2d.<a href="com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d"><span class="typeNameLink">KeyboardEmulator2DDecoder_qcom</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.mtk.barcode.<a href="com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">BarcodeDecoder_mtk</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">BarcodeEntity</span></a></li>
<li type="circle">com.rscja.barcode.<a href="com/rscja/barcode/BarcodeFactory.html" title="class in com.rscja.barcode"><span class="typeNameLink">BarcodeFactory</span></a></li>
<li type="circle">com.rscja.team.mtk.barcode.<a href="com/rscja/team/mtk/barcode/BarcodeFactory_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">BarcodeFactory_mtk</span></a></li>
<li type="circle">com.rscja.team.qcom.barcode.<a href="com/rscja/team/qcom/barcode/BarcodeFactory_qcom.html" title="class in com.rscja.team.qcom.barcode"><span class="typeNameLink">BarcodeFactory_qcom</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">BarcodeResult</span></a></li>
<li type="circle">com.rscja.barcode.<a href="com/rscja/barcode/BarcodeSymbolUtility.html" title="class in com.rscja.barcode"><span class="typeNameLink">BarcodeSymbolUtility</span></a></li>
<li type="circle">com.rscja.team.mtk.barcode.<a href="com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">BarcodeSymbolUtility_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeSymbolUtility</a>)</li>
<li type="circle">com.rscja.team.qcom.barcode.<a href="com/rscja/team/qcom/barcode/BarcodeSymbolUtility_qcom.html" title="class in com.rscja.team.qcom.barcode"><span class="typeNameLink">BarcodeSymbolUtility_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeSymbolUtility</a>)</li>
<li type="circle">com.rscja.barcode.<a href="com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode"><span class="typeNameLink">BarcodeUtility</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a>)</li>
<li type="circle">com.rscja.team.mtk.barcode.<a href="com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">BarcodeUtility_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a>)</li>
<li type="circle">com.rscja.team.qcom.barcode.<a href="com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode"><span class="typeNameLink">BarcodeUtility_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a>)</li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/BatteryEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">BatteryEntity</span></a></li>
<li type="circle">com.rscja.team.mtk.utility.<a href="com/rscja/team/mtk/utility/BatteryManage.html" title="class in com.rscja.team.mtk.utility"><span class="typeNameLink">BatteryManage</span></a></li>
<li type="circle">com.rscja.utility.<a href="com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility"><span class="typeNameLink">BatteryUtils</span></a></li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">BleDevice_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/BleDevice.BleDeviceInfo.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">BleDevice.BleDeviceInfo</span></a></li>
<li type="circle">com.rscja.team.qcom.service.<a href="com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service"><span class="typeNameLink">BLEService_qcom</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">BluetoothReader</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>)
<ul>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFBLE</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">BluetoothReader_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>)
<ul>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFBLE_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTemperatureTag.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag</span></a></li>
<li type="circle">com.rscja.team.qcom.custom.<a href="com/rscja/team/qcom/custom/UHFTemperatureTagsBLEAPI.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">UHFTemperatureTagsBLEAPI</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a>)</li>
</ul>
</li>
</ul>
</li>
<li type="circle">android.content.BroadcastReceiver
<ul>
<li type="circle">com.rscja.team.qcom.service.<a href="com/rscja/team/qcom/service/BLEService_qcom.BluetoothStateReceiver.html" title="class in com.rscja.team.qcom.service"><span class="typeNameLink">BLEService_qcom.BluetoothStateReceiver</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/CardWithBYL.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">CardWithBYL</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ICardWithBYL.html" title="interface in com.rscja.deviceapi.interfaces">ICardWithBYL</a>)</li>
<li type="circle">com.rscja.team.qcom.utility.<a href="com/rscja/team/qcom/utility/CheckUtils.html" title="class in com.rscja.team.qcom.utility"><span class="typeNameLink">CheckUtils</span></a></li>
<li type="circle">com.rscja.team.qcom.barcode.symbol.<a href="com/rscja/team/qcom/barcode/symbol/CoAsiaBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol"><span class="typeNameLink">CoAsiaBarcodeSymbol_qcom</span></a> (implements com.rscja.team.qcom.barcode.symbol.<a href="com/rscja/team/qcom/barcode/symbol/IBarcodeSymbol_qcom.html" title="interface in com.rscja.team.qcom.barcode.symbol">IBarcodeSymbol_qcom</a>)</li>
<li type="circle">com.rscja.barcode.<a href="com/rscja/barcode/ConstantUtil.html" title="class in com.rscja.barcode"><span class="typeNameLink">ConstantUtil</span></a></li>
<li type="circle">com.rscja.<a href="com/rscja/CWDeviceInfo.html" title="class in com.rscja"><span class="typeNameLink">CWDeviceInfo</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">DESFireFile</span></a></li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/Device_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Device_qcom</span></a>
<ul>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/Barcode1D_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Barcode1D_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcode1D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode1D</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/Barcode2D_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Barcode2D_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">CardWithBYL_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ICardWithBYL.html" title="interface in com.rscja.deviceapi.interfaces">ICardWithBYL</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Fingerprint_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">FingerprintWithFIPS_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">FingerprintWithMorpho_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">FingerprintWithZAZ_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/LedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">LedLight_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ILedLight.html" title="interface in com.rscja.deviceapi.interfaces">ILedLight</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/PSAM_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">PSAM_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDBase_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDBase</a>)
<ul>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithISO14443A_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithISO14443A4CPU_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithISO14443A4CPU_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443A4CPU.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A4CPU</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithISO14443B_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithISO14443B_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443B</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithISO15693_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithLF_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithLF</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">ScanerLedLight_qcom</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">DeviceAPI</span></a></li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">DeviceAPI</span></a></li>
<li type="circle">com.rscja.team.mtk.<a href="com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk"><span class="typeNameLink">DeviceConfiguration_mtk</span></a></li>
<li type="circle">com.rscja.team.mtk.<a href="com/rscja/team/mtk/DeviceConfiguration_mtk.Platform.html" title="class in com.rscja.team.mtk"><span class="typeNameLink">DeviceConfiguration_mtk.Platform</span></a></li>
<li type="circle">com.rscja.team.qcom.<a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom"><span class="typeNameLink">DeviceConfiguration_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.<a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html" title="class in com.rscja.team.qcom"><span class="typeNameLink">DeviceConfiguration_qcom.Platform</span></a></li>
<li type="circle">com.rscja.team.mtk.barcode.symbol.<a href="com/rscja/team/mtk/barcode/symbol/DlBarcodeSymbol_mtk.html" title="class in com.rscja.team.mtk.barcode.symbol"><span class="typeNameLink">DlBarcodeSymbol_mtk</span></a> (implements com.rscja.team.mtk.barcode.symbol.<a href="com/rscja/team/mtk/barcode/symbol/IBarcodeSymbol_mtk.html" title="interface in com.rscja.team.mtk.barcode.symbol">IBarcodeSymbol_mtk</a>)</li>
<li type="circle">com.rscja.team.qcom.barcode.symbol.<a href="com/rscja/team/qcom/barcode/symbol/DlBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol"><span class="typeNameLink">DlBarcodeSymbol_qcom</span></a> (implements com.rscja.team.qcom.barcode.symbol.<a href="com/rscja/team/qcom/barcode/symbol/IBarcodeSymbol_qcom.html" title="interface in com.rscja.team.qcom.barcode.symbol">IBarcodeSymbol_qcom</a>)</li>
<li type="circle">com.rscja.utility.<a href="com/rscja/utility/FileUtility.html" title="class in com.rscja.utility"><span class="typeNameLink">FileUtility</span></a></li>
<li type="circle">com.rscja.team.mtk.utility.<a href="com/rscja/team/mtk/utility/FileUtils.html" title="class in com.rscja.team.mtk.utility"><span class="typeNameLink">FileUtils</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Fingerprint</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a>)</li>
<li type="circle">com.rscja.utility.<a href="com/rscja/utility/FingerprintPictureUtility.html" title="class in com.rscja.utility"><span class="typeNameLink">FingerprintPictureUtility</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintSM206B.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">FingerprintSM206B</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">FingerprintSM206B_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">FingerprintWithFIPS_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithFIPS.FingerprintInfo.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.FingerprintInfo</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithMorpho.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithMorpho</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">FingerprintWithMorpho_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithTLK1NC</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">FingerprintWithTLK1NC_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">FingerprintWithTLK1NC_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithZAZ</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a>)</li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Gen2Entity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/GPIOInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">GPIOInfo</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">GPIStateEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">GPOEntity</span></a></li>
<li type="circle">com.rscja.team.qcom.utility.<a href="com/rscja/team/qcom/utility/GyroAngle.html" title="class in com.rscja.team.qcom.utility"><span class="typeNameLink">GyroAngle</span></a></li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">HardwareInterface_qcom</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/HF14443RequestEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">HF14443RequestEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">HF15693RequestEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/HF15693RequestEntity.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">HF15693RequestEntity.Builder</span></a></li>
<li type="circle">com.rscja.team.qcom.r1.hf.<a href="com/rscja/team/qcom/r1/hf/HFBase.html" title="class in com.rscja.team.qcom.r1.hf"><span class="typeNameLink">HFBase</span></a>
<ul>
<li type="circle">com.rscja.team.qcom.r1.hf.<a href="com/rscja/team/qcom/r1/hf/HF14443A.html" title="class in com.rscja.team.qcom.r1.hf"><span class="typeNameLink">HF14443A</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHF14443A.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443A</a>)</li>
<li type="circle">com.rscja.team.qcom.r1.hf.<a href="com/rscja/team/qcom/r1/hf/HF14443B.html" title="class in com.rscja.team.qcom.r1.hf"><span class="typeNameLink">HF14443B</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443B</a>)</li>
<li type="circle">com.rscja.team.qcom.r1.hf.<a href="com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf"><span class="typeNameLink">HF15693</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces">IHF15693</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.barcode.symbol.<a href="com/rscja/team/qcom/barcode/symbol/HoneywellBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol"><span class="typeNameLink">HoneywellBarcodeSymbol_qcom</span></a> (implements com.rscja.team.qcom.barcode.symbol.<a href="com/rscja/team/qcom/barcode/symbol/IBarcodeSymbol_qcom.html" title="interface in com.rscja.team.qcom.barcode.symbol">IBarcodeSymbol_qcom</a>)</li>
<li type="circle">com.rscja.team.qcom.http.<a href="com/rscja/team/qcom/http/HttpUtils_qcom.html" title="class in com.rscja.team.qcom.http"><span class="typeNameLink">HttpUtils_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.barcode.symbol.<a href="com/rscja/team/qcom/barcode/symbol/IdataBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol"><span class="typeNameLink">IdataBarcodeSymbol_qcom</span></a> (implements com.rscja.team.qcom.barcode.symbol.<a href="com/rscja/team/qcom/barcode/symbol/IBarcodeSymbol_qcom.html" title="interface in com.rscja.team.qcom.barcode.symbol">IBarcodeSymbol_qcom</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/Infrared.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Infrared</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces">IInfrared</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/Infrared_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Infrared_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces">IInfrared</a>)</li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">InventoryModeEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/InventoryModeEntity.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">InventoryModeEntity.Builder</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/InventoryParameter.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">InventoryParameter</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/InventoryParameter.ResultData.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">InventoryParameter.ResultData</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/LedLight.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">LedLight</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ILedLight.html" title="interface in com.rscja.deviceapi.interfaces">ILedLight</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/LedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">LedLight_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ILedLight.html" title="interface in com.rscja.deviceapi.interfaces">ILedLight</a>)</li>
<li type="circle">com.rscja.team.mtk.utility.<a href="com/rscja/team/mtk/utility/LogUtility_mtk.html" title="class in com.rscja.team.mtk.utility"><span class="typeNameLink">LogUtility_mtk</span></a></li>
<li type="circle">com.rscja.team.qcom.utility.<a href="com/rscja/team/qcom/utility/LogUtility_qcom.html" title="class in com.rscja.team.qcom.utility"><span class="typeNameLink">LogUtility_qcom</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/LowBatteryEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">LowBatteryEntity</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/M775Authenticate.AuthenticateInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">M775Authenticate.AuthenticateInfo</span></a></li>
<li type="circle">com.rscja.team.qcom.barcode.symbol.<a href="com/rscja/team/qcom/barcode/symbol/MobyDataBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol"><span class="typeNameLink">MobyDataBarcodeSymbol_qcom</span></a> (implements com.rscja.team.qcom.barcode.symbol.<a href="com/rscja/team/qcom/barcode/symbol/IBarcodeSymbol_qcom.html" title="interface in com.rscja.team.qcom.barcode.symbol">IBarcodeSymbol_qcom</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/Module.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Module</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/Module_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Module_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/Module_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Module_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a>)</li>
<li type="circle">com.rscja.team.qcom.http.<a href="com/rscja/team/qcom/http/NetResult_qcom.html" title="class in com.rscja.team.qcom.http"><span class="typeNameLink">NetResult_qcom</span></a></li>
<li type="circle">com.rscja.utility.<a href="com/rscja/utility/NetUtils.html" title="class in com.rscja.utility"><span class="typeNameLink">NetUtils</span></a></li>
<li type="circle">com.rscja.utility.<a href="com/rscja/utility/NumberTool.html" title="class in com.rscja.utility"><span class="typeNameLink">NumberTool</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Printer</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Printer_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/PSAM.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">PSAM</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a>)</li>
<li type="circle">com.rscja.team.qcom.r1.psam.<a href="com/rscja/team/qcom/r1/psam/PSAM.html" title="class in com.rscja.team.qcom.r1.psam"><span class="typeNameLink">PSAM</span></a> (implements com.rscja.team.qcom.r1.<a href="com/rscja/team/qcom/r1/IPSAM.html" title="interface in com.rscja.team.qcom.r1">IPSAM</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/PSAM_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">PSAM_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a>)</li>
<li type="circle">com.rscja.team.qcom.urax.<a href="com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax"><span class="typeNameLink">QcomURA4Gpio</span></a> (implements com.rscja.team.qcom.urax.<a href="com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a>)</li>
<li type="circle">com.rscja.team.qcom.urax.<a href="com/rscja/team/qcom/urax/QcomURAxDevice.html" title="class in com.rscja.team.qcom.urax"><span class="typeNameLink">QcomURAxDevice</span></a> (implements com.rscja.team.qcom.urax.<a href="com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax">IURAxDevice</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">R1HFAndPsamManage</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/RadarLocationEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">RadarLocationEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/RadarLocationEntity.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">RadarLocationEntity.Builder</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">ReaderIPEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDBase</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDBase</a>)
<ul>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithISO14443A</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithISO14443A4CPU.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithISO14443A4CPU</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443A4CPU.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A4CPU</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithISO14443B.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithISO14443B</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443B</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithISO15693</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDBase_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDBase</a>)
<ul>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDWithISO14443A_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/RFIDWithISO14443A4CPU_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDWithISO14443A4CPU_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443A4CPU.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A4CPU</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDWithISO14443B_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443B</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDWithISO15693_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithLF</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithLF</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFA4NetWork</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4NetWork</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFA4RS232</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4RS232</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFA8NetWork</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8NetWork</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFA8RS232</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8RS232</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFBLEManage.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFBLEManage</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">RFIDWithUHFJieCe.TemperatureTagInfo</span></a></li>
<li type="circle">com.rscja.team.qcom.urax.<a href="com/rscja/team/qcom/urax/RKURA4C8Device.html" title="class in com.rscja.team.qcom.urax"><span class="typeNameLink">RKURA4C8Device</span></a> (implements com.rscja.team.qcom.urax.<a href="com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax">IURAxDevice</a>)</li>
<li type="circle">com.rscja.team.qcom.urax.<a href="com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax"><span class="typeNameLink">RKURA4Gpio</span></a> (implements com.rscja.team.qcom.urax.<a href="com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a>)</li>
<li type="circle">com.rscja.team.qcom.urax.<a href="com/rscja/team/qcom/urax/RKURAxDevice.html" title="class in com.rscja.team.qcom.urax"><span class="typeNameLink">RKURAxDevice</span></a> (implements com.rscja.team.qcom.urax.<a href="com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax">IURAxDevice</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/ScanerLedLight.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">ScanerLedLight</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces">IScanerLedLight</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">ScanerLedLight_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces">IScanerLedLight</a>)</li>
<li type="circle">com.rscja.scanner.led.<a href="com/rscja/scanner/led/ScanLed.html" title="class in com.rscja.scanner.led"><span class="typeNameLink">ScanLed</span></a>
<ul>
<li type="circle">com.rscja.team.qcom.scanner.led.<a href="com/rscja/team/qcom/scanner/led/C60_6765_11_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led"><span class="typeNameLink">C60_6765_11_ScanLed_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.scanner.led.<a href="com/rscja/team/qcom/scanner/led/C60_qcm2150_10_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led"><span class="typeNameLink">C60_qcm2150_10_ScanLed_qcom</span></a></li>
<li type="circle">com.rscja.team.mtk.scanner.led.<a href="com/rscja/team/mtk/scanner/led/C6000_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led"><span class="typeNameLink">C6000_6762_ScanLed_mtk</span></a></li>
<li type="circle">com.rscja.team.qcom.scanner.led.<a href="com/rscja/team/qcom/scanner/led/C61_smd450_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led"><span class="typeNameLink">C61_smd450_90_ScanLed_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.scanner.led.<a href="com/rscja/team/qcom/scanner/led/C66_smd450_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led"><span class="typeNameLink">C66_smd450_90_ScanLed_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.scanner.led.<a href="com/rscja/team/qcom/scanner/led/C66m_sm6115_10_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led"><span class="typeNameLink">C66m_sm6115_10_ScanLed_qcom</span></a></li>
<li type="circle">com.rscja.team.mtk.scanner.led.<a href="com/rscja/team/mtk/scanner/led/C7X_6765_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led"><span class="typeNameLink">C7X_6765_ScanLed_mtk</span></a></li>
<li type="circle">com.rscja.team.mtk.scanner.led.<a href="com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led"><span class="typeNameLink">C90_6762_ScanLed_mtk</span></a></li>
<li type="circle">com.rscja.team.qcom.scanner.led.<a href="com/rscja/team/qcom/scanner/led/MC50_4350_12_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led"><span class="typeNameLink">MC50_4350_12_ScanLed_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.scanner.led.<a href="com/rscja/team/qcom/scanner/led/P80_8786_130_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led"><span class="typeNameLink">P80_8786_130_ScanLed_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.scanner.led.<a href="com/rscja/team/qcom/scanner/led/P80_8953_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led"><span class="typeNameLink">P80_8953_90_ScanLed_qcom</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.mtk.scanner.led.<a href="com/rscja/team/mtk/scanner/led/ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led"><span class="typeNameLink">ScanLed_mtk</span></a></li>
<li type="circle">com.rscja.scanner.led.<a href="com/rscja/scanner/led/ScanLedManage.html" title="class in com.rscja.scanner.led"><span class="typeNameLink">ScanLedManage</span></a></li>
<li type="circle">com.rscja.team.mtk.scanner.led.<a href="com/rscja/team/mtk/scanner/led/ScanLedManage_mtk.html" title="class in com.rscja.team.mtk.scanner.led"><span class="typeNameLink">ScanLedManage_mtk</span></a></li>
<li type="circle">com.rscja.team.qcom.scanner.led.<a href="com/rscja/team/qcom/scanner/led/ScanLedManage_qcom.html" title="class in com.rscja.team.qcom.scanner.led"><span class="typeNameLink">ScanLedManage_qcom</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">ScannerParameterEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/ScannerParameterEntity.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">ScannerParameterEntity.Builder</span></a></li>
<li type="circle">com.rscja.team.qcom.scanner.utility.<a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility"><span class="typeNameLink">ScannerParameterUtility_qcom</span></a></li>
<li type="circle">com.rscja.scanner.utility.<a href="com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility"><span class="typeNameLink">ScannerUtility</span></a> (implements com.rscja.scanner.<a href="com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a>)</li>
<li type="circle">com.rscja.team.mtk.scanner.utility.<a href="com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility"><span class="typeNameLink">ScannerUtility_mtk</span></a> (implements com.rscja.scanner.<a href="com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a>)</li>
<li type="circle">com.rscja.team.qcom.scanner.utility.<a href="com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility"><span class="typeNameLink">ScannerUtility_qcom</span></a> (implements com.rscja.scanner.<a href="com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a>)</li>
<li type="circle">com.rscja.team.qcom.serialportapi.<a href="com/rscja/team/qcom/serialportapi/SerialPort.html" title="class in com.rscja.team.qcom.serialportapi"><span class="typeNameLink">SerialPort</span></a></li>
<li type="circle">com.rscja.team.qcom.serialportapi.<a href="com/rscja/team/qcom/serialportapi/SerialportAPI.html" title="class in com.rscja.team.qcom.serialportapi"><span class="typeNameLink">SerialportAPI</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">SimpleRFIDEntity</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">ISO15693Entity</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.socket.<a href="com/rscja/team/qcom/socket/SocketManageA4.html" title="class in com.rscja.team.qcom.socket"><span class="typeNameLink">SocketManageA4</span></a></li>
<li type="circle">com.rscja.team.qcom.socket.<a href="com/rscja/team/qcom/socket/SocketManageUR4.html" title="class in com.rscja.team.qcom.socket"><span class="typeNameLink">SocketManageUR4</span></a></li>
<li type="circle">com.rscja.team.qcom.socket.<a href="com/rscja/team/qcom/socket/SocketTcpIpBase.html" title="class in com.rscja.team.qcom.socket"><span class="typeNameLink">SocketTcpIpBase</span></a>
<ul>
<li type="circle">com.rscja.team.qcom.socket.<a href="com/rscja/team/qcom/socket/SocketUdpClient.html" title="class in com.rscja.team.qcom.socket"><span class="typeNameLink">SocketUdpClient</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.socket.<a href="com/rscja/team/qcom/socket/SocketUdpClientUR4.html" title="class in com.rscja.team.qcom.socket"><span class="typeNameLink">SocketUdpClientUR4</span></a></li>
<li type="circle">com.rscja.utility.<a href="com/rscja/utility/StringUtility.html" title="class in com.rscja.utility"><span class="typeNameLink">StringUtility</span></a></li>
<li type="circle">com.rscja.team.qcom.system.<a href="com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html" title="class in com.rscja.team.qcom.system"><span class="typeNameLink">SysInterfacesOfC66P_qcom</span></a></li>
<li type="circle">com.rscja.team.mtk.system.<a href="com/rscja/team/mtk/system/SysInterfacesOfC7XA11_mtk.html" title="class in com.rscja.team.mtk.system"><span class="typeNameLink">SysInterfacesOfC7XA11_mtk</span></a></li>
<li type="circle">com.rscja.system.<a href="com/rscja/system/SystemInterfacesFactory.html" title="class in com.rscja.system"><span class="typeNameLink">SystemInterfacesFactory</span></a></li>
<li type="circle">com.rscja.team.mtk.system.<a href="com/rscja/team/mtk/system/SystemInterfacesFactory_mtk.html" title="class in com.rscja.team.mtk.system"><span class="typeNameLink">SystemInterfacesFactory_mtk</span></a></li>
<li type="circle">com.rscja.team.qcom.system.<a href="com/rscja/team/qcom/system/SystemInterfacesFactory_qcom.html" title="class in com.rscja.team.qcom.system"><span class="typeNameLink">SystemInterfacesFactory_qcom</span></a></li>
<li type="circle">com.rscja.team.mtk.system.<a href="com/rscja/team/mtk/system/SystemPropValues_mtk.html" title="class in com.rscja.team.mtk.system"><span class="typeNameLink">SystemPropValues_mtk</span></a></li>
<li type="circle">com.rscja.team.qcom.system.<a href="com/rscja/team/qcom/system/SystemPropValues_qcom.html" title="class in com.rscja.team.qcom.system"><span class="typeNameLink">SystemPropValues_qcom</span></a></li>
<li type="circle">com.rscja.team.mtk.system.<a href="com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html" title="class in com.rscja.team.mtk.system"><span class="typeNameLink">SystemSettingsDBData_mtk</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">TagInfoRule</span></a></li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/TagLocate_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">TagLocate_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ITagLocate.html" title="interface in com.rscja.deviceapi.interfaces">ITagLocate</a>)</li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/TagLocationEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">TagLocationEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/TagLocationInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">TagLocationInfo</span></a></li>
<li type="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li type="circle">java.lang.Exception
<ul>
<li type="circle">com.rscja.deviceapi.exception.<a href="com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">ConfigurationException</span></a></li>
<li type="circle">com.rscja.deviceapi.exception.<a href="com/rscja/deviceapi/exception/DeviceNotConnectException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">DeviceNotConnectException</span></a></li>
<li type="circle">com.rscja.deviceapi.exception.<a href="com/rscja/deviceapi/exception/FingerprintAlreadyEnrolledException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">FingerprintAlreadyEnrolledException</span></a></li>
<li type="circle">com.rscja.deviceapi.exception.<a href="com/rscja/deviceapi/exception/FingerprintInvalidIDException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">FingerprintInvalidIDException</span></a></li>
<li type="circle">com.rscja.deviceapi.exception.<a href="com/rscja/deviceapi/exception/LowBatteryException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">LowBatteryException</span></a></li>
<li type="circle">com.rscja.deviceapi.exception.<a href="com/rscja/deviceapi/exception/PrinterBarcodeInvalidException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">PrinterBarcodeInvalidException</span></a></li>
<li type="circle">com.rscja.deviceapi.exception.<a href="com/rscja/deviceapi/exception/PrinterLowPager.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">PrinterLowPager</span></a></li>
<li type="circle">com.rscja.deviceapi.exception.<a href="com/rscja/deviceapi/exception/PSAMException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">PSAMException</span></a></li>
<li type="circle">com.rscja.deviceapi.exception.<a href="com/rscja/deviceapi/exception/RFIDArgumentException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">RFIDArgumentException</span></a></li>
<li type="circle">com.rscja.deviceapi.exception.<a href="com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">RFIDNotFoundException</span></a></li>
<li type="circle">com.rscja.deviceapi.exception.<a href="com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">RFIDReadFailureException</span></a></li>
<li type="circle">com.rscja.deviceapi.exception.<a href="com/rscja/deviceapi/exception/RFIDVerificationException.html" title="class in com.rscja.deviceapi.exception"><span class="typeNameLink">RFIDVerificationException</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">UhfBase</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">BleDevice</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a>)</li>
<li type="circle">com.rscja.team.qcom.ble.<a href="com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble"><span class="typeNameLink">EmptyUhfBle</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a>)
<ul>
<li type="circle">com.rscja.team.qcom.ble.<a href="com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html" title="class in com.rscja.team.qcom.ble"><span class="typeNameLink">RFIDWithUHFBLEN51_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;T&gt;, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN51.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag.RFIDWithUHFBLEN51</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.ble.<a href="com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html" title="class in com.rscja.team.qcom.ble"><span class="typeNameLink">RFIDWithUHFBLEN52_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;T&gt;, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN52.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag.RFIDWithUHFBLEN52</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFA4NetWork_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4NetWork</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFCSYX_A4NetWork.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFCSYX_A4NetWork</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFA4RS232_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4RS232</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFA8NetWork_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8NetWork</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFA8RS232_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8RS232</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFUART</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/M775Authenticate.html" title="class in com.rscja.custom"><span class="typeNameLink">M775Authenticate</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IM775Authenticate.html" title="interface in com.rscja.custom.interfaces">IM775Authenticate</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFAxBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFAxBase</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/RFIDWithUHFJieCe.html" title="class in com.rscja.custom"><span class="typeNameLink">RFIDWithUHFJieCe</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IRFIDWithUHFJieCe.html" title="interface in com.rscja.custom.interfaces">IRFIDWithUHFJieCe</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFRLM</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a>)</li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFCSYX.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFCSYX</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFCSYX.html" title="interface in com.rscja.custom.interfaces">IUHFCSYX</a>)</li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTamperAPI.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTamperAPI</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFTamperAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTamperAPI</a>)</li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTemperatureTagsAPI.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a>)</li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFUartFoxconn.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFUartFoxconn</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFUartFoxconn.html" title="interface in com.rscja.custom.interfaces">IUHFUartFoxconn</a>)</li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFXSAPI.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFXSAPI</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFInventoryCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFInventoryCallback</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDWithUHFUART_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>)
<ul>
<li type="circle">com.rscja.team.mtk.custom.<a href="com/rscja/team/mtk/custom/M775Authenticate_mtk.html" title="class in com.rscja.team.mtk.custom"><span class="typeNameLink">M775Authenticate_mtk</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IM775Authenticate.html" title="interface in com.rscja.custom.interfaces">IM775Authenticate</a>)</li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTemperatureSensors.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureSensors</span></a></li>
<li type="circle">com.rscja.team.mtk.custom.<a href="com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html" title="class in com.rscja.team.mtk.custom"><span class="typeNameLink">UHFTemperatureTagsAPI_mtk</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a>)</li>
<li type="circle">com.rscja.team.mtk.custom.<a href="com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html" title="class in com.rscja.team.mtk.custom"><span class="typeNameLink">UHFUartFoxconn_mtk</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFUartFoxconn.html" title="interface in com.rscja.custom.interfaces">IUHFUartFoxconn</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFUART_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>)
<ul>
<li type="circle">com.rscja.team.qcom.custom.<a href="com/rscja/team/qcom/custom/M775Authenticate_qcom.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">M775Authenticate_qcom</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IM775Authenticate.html" title="interface in com.rscja.custom.interfaces">IM775Authenticate</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFAxBase_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)
<ul>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFA4</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFA4_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>)
<ul>
<li type="circle">com.rscja.team.qcom.custom.<a href="com/rscja/team/qcom/custom/UHFTemperatureTagsAPI_qcom.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">UHFTemperatureTagsAPI_qcom</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFA8</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFA8_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.custom.<a href="com/rscja/team/qcom/custom/RFIDWithUHFJieCe_qcom.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">RFIDWithUHFJieCe_qcom</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IRFIDWithUHFJieCe.html" title="interface in com.rscja.custom.interfaces">IRFIDWithUHFJieCe</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFRLM_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a>)</li>
<li type="circle">com.rscja.team.qcom.custom.<a href="com/rscja/team/qcom/custom/RFIDWithUHFShuangYingDianZi_qcom.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">RFIDWithUHFShuangYingDianZi_qcom</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IRFIDWithUHFShuangYingDianZi.html" title="interface in com.rscja.custom.interfaces">IRFIDWithUHFShuangYingDianZi</a>)</li>
<li type="circle">com.rscja.team.qcom.custom.<a href="com/rscja/team/qcom/custom/RFIDWithUHFUARTUAE_qcom.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">RFIDWithUHFUARTUAE_qcom</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IRFIDWithUHFUARTUAE.html" title="interface in com.rscja.custom.interfaces">IRFIDWithUHFUARTUAE</a>)</li>
<li type="circle">com.rscja.team.qcom.custom.<a href="com/rscja/team/qcom/custom/UHFCSYX_qcom.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">UHFCSYX_qcom</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFCSYX.html" title="interface in com.rscja.custom.interfaces">IUHFCSYX</a>)</li>
<li type="circle">com.rscja.team.qcom.custom.<a href="com/rscja/team/qcom/custom/UHFTamperAPI_qcom.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">UHFTamperAPI_qcom</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFTamperAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTamperAPI</a>)</li>
<li type="circle">com.rscja.team.qcom.custom.<a href="com/rscja/team/qcom/custom/UHFUartFoxconn_qcom.html" title="class in com.rscja.team.qcom.custom"><span class="typeNameLink">UHFUartFoxconn_qcom</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFUartFoxconn.html" title="interface in com.rscja.custom.interfaces">IUHFUartFoxconn</a>)</li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFUartTemperatureTag.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFUartTemperatureTag</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFUrxNetwork</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxNetwork</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFUrxNetwork_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxNetwork</a>, java.util.Observer)
<ul>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFSFForUrxNetwork.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFSFForUrxNetwork</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFUrxUart</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxUart</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFUrxUart_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxUart</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFCSYXForURx.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFCSYXForURx</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFUrxUart2_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxUart</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFUrxUsbToUart</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUsbToUart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxUsbToUart</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFUrxUsbToUart_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUsbToUart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxUsbToUart</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFUSB</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUSB</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFUSB_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUSB</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">UhfBase.ErrorCode</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFCSYX.TagAuthenticationResponseInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFCSYX.TagAuthenticationResponseInfo</span></a></li>
<li type="circle">com.rscja.team.qcom.uhfhandler.<a href="com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">UHFDataHandleBase</span></a>
<ul>
<li type="circle">com.rscja.team.qcom.uhfhandler.<a href="com/rscja/team/qcom/uhfhandler/HFR1UsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">HFR1UsbDataHandle</span></a></li>
<li type="circle">com.rscja.team.qcom.uhfhandler.<a href="com/rscja/team/qcom/uhfhandler/UHFRxBLEDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">UHFRxBLEDataHandle</span></a></li>
<li type="circle">com.rscja.team.qcom.uhfhandler.<a href="com/rscja/team/qcom/uhfhandler/UHFRxUsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">UHFRxUsbDataHandle</span></a></li>
<li type="circle">com.rscja.team.qcom.uhfhandler.<a href="com/rscja/team/qcom/uhfhandler/UHFUR4DataHandle.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">UHFUR4DataHandle</span></a></li>
<li type="circle">com.rscja.team.qcom.uhfhandler.<a href="com/rscja/team/qcom/uhfhandler/UHFUrAxDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">UHFUrAxDataHandle</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.uhfhandler.<a href="com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">UHFDataHandleBase.CMDInfo</span></a></li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/UhfLocation_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UhfLocation_qcom</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/UHFProtocolParseBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">UHFProtocolParseBase</span></a></li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/UHFProtocolParseBLE.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFProtocolParseBLE</span></a> (implements com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFProtocolParseBLE.html" title="interface in com.rscja.custom.interfaces">IUHFProtocolParseBLE</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFProtocolParseByJava</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a>)
<ul>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/UHFProtocolParseBleByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFProtocolParseBleByJava_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.rs232utils.<a href="com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UHFProtocolParseUrAxBase_qcom</span></a>
<ul>
<li type="circle">com.rscja.team.qcom.rs232utils.<a href="com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA4_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UHFProtocolParseUrA4_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.rs232utils.<a href="com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA8_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UHFProtocolParseUrA8_qcom</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFProtocolParseUrxByJava_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/UHFProtocolParseUSBByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFProtocolParseUSBByJava_qcom</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.rs232utils.<a href="com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UHFProtocolParseUtils_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.rs232utils.<a href="com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.CMDInfo.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UHFProtocolParseUtils_qcom.CMDInfo</span></a></li>
<li type="circle">com.rscja.team.qcom.uhfparse.<a href="com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse"><span class="typeNameLink">UHFProtocolProtocolParseBase</span></a>
<ul>
<li type="circle">com.rscja.team.qcom.uhfparse.<a href="com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html" title="class in com.rscja.team.qcom.uhfparse"><span class="typeNameLink">UHFBLEProtocolParse</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.uhfparse.<a href="com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfparse"><span class="typeNameLink">UHFProtocolProtocolParseBase.CMDInfo</span></a></li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/UhfRadarLocation_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UhfRadarLocation_qcom</span></a> (implements android.hardware.SensorEventListener)</li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFSFForUrxNetwork.AntInfo</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">UHFTAGInfo</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">UHFTAGInfo.ChipInfo</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">UHFTAGInfo.ChipInfo.Builder</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTamperAPI.TamperInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTamperAPI.TamperInfo</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureSensors.TemperatureTag</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag.TemperatureTagInfo</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI.MultipleTemperatureInfo</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTemperatureTagsAPI.TempertureInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI.TempertureInfo</span></a></li>
<li type="circle">com.rscja.team.qcom.rs232utils.<a href="com/rscja/team/qcom/rs232utils/UhfUartManage_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UhfUartManage_qcom</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFUartTemperatureTag.TemperatureTagInfo</span></a></li>
<li type="circle">com.rscja.team.qcom.rs232utils.<a href="com/rscja/team/qcom/rs232utils/UhfUartUR4Manage_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UhfUartUR4Manage_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/UHFUrxAutoInventoryTagFactory_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFUrxAutoInventoryTagFactory_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/UHFUrxNetWorkAutoInventoryTag_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UHFUrxNetWorkAutoInventoryTag_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFUrxAutoInventoryTag.html" title="interface in com.rscja.deviceapi.interfaces">IUHFUrxAutoInventoryTag</a>)</li>
<li type="circle">com.rscja.utility.<a href="com/rscja/utility/UhfUtils.html" title="class in com.rscja.utility"><span class="typeNameLink">UhfUtils</span></a></li>
<li type="circle">com.rscja.team.qcom.usb.<a href="com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb"><span class="typeNameLink">UsbBase_qcom</span></a>
<ul>
<li type="circle">com.rscja.team.qcom.usb.<a href="com/rscja/team/qcom/usb/RxUsb_qcom.html" title="class in com.rscja.team.qcom.usb"><span class="typeNameLink">RxUsb_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.usb.<a href="com/rscja/team/qcom/usb/UrxUsb_qcom.html" title="class in com.rscja.team.qcom.usb"><span class="typeNameLink">UrxUsb_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.usb.pl2302.<a href="com/rscja/team/qcom/usb/pl2302/UsbPL2302.html" title="class in com.rscja.team.qcom.usb.pl2302"><span class="typeNameLink">UsbPL2302</span></a>
<ul>
<li type="circle">com.rscja.team.qcom.usb.<a href="com/rscja/team/qcom/usb/R1HFUSB.html" title="class in com.rscja.team.qcom.usb"><span class="typeNameLink">R1HFUSB</span></a> (implements com.rscja.team.qcom.usb.<a href="com/rscja/team/qcom/usb/UsbBase_qcom.DataCallback.html" title="interface in com.rscja.team.qcom.usb">UsbBase_qcom.DataCallback</a>)</li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/UsbFingerprint.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">UsbFingerprint</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUsbFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IUsbFingerprint</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">UsbFingerprint_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUsbFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IUsbFingerprint</a>)</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/UsbFingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">UsbFingerprint_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUsbFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IUsbFingerprint</a>)</li>
<li type="circle">com.rscja.team.qcom.usb.pl2302.<a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302"><span class="typeNameLink">UsbSerialPortImpl_qcom</span></a> (implements com.rscja.team.qcom.usb.pl2302.<a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302">UsbSerialPort_qcom</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/VersionInfo.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">VersionInfo</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">WifiConfig</span></a></li>
<li type="circle">com.rscja.team.qcom.barcode.symbol.<a href="com/rscja/team/qcom/barcode/symbol/ZebraBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol"><span class="typeNameLink">ZebraBarcodeSymbol_qcom</span></a> (implements com.rscja.team.qcom.barcode.symbol.<a href="com/rscja/team/qcom/barcode/symbol/IBarcodeSymbol_qcom.html" title="interface in com.rscja.team.qcom.barcode.symbol">IBarcodeSymbol_qcom</a>)</li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li type="circle">com.rscja.team.mtk.barcode.<a href="com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.DecodeCallback.html" title="interface in com.rscja.team.mtk.barcode"><span class="typeNameLink">BarcodeDecoder_mtk.DecodeCallback</span></a></li>
<li type="circle">com.rscja.barcode.<a href="com/rscja/barcode/BarcodeDecoder.DecodeCallback.html" title="interface in com.rscja.barcode"><span class="typeNameLink">BarcodeDecoder.DecodeCallback</span></a></li>
<li type="circle">com.rscja.barcode.<a href="com/rscja/barcode/BarcodeDecoder.IBarcodeImageCallback.html" title="interface in com.rscja.barcode"><span class="typeNameLink">BarcodeDecoder.IBarcodeImageCallback</span></a></li>
<li type="circle">com.rscja.team.qcom.service.<a href="com/rscja/team/qcom/service/BLEService_qcom.IDataCallBack.html" title="interface in com.rscja.team.qcom.service"><span class="typeNameLink">BLEService_qcom.IDataCallBack</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/BluetoothReader.DecodeCallback.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">BluetoothReader.DecodeCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/BluetoothReader.OnDataChangeListener.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">BluetoothReader.OnDataChangeListener</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ConnectionStatusCallback</span></a>&lt;T&gt;</li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithFIPS.EnrollCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.EnrollCallBack</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithFIPS.GRABCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.GRABCallBack</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithFIPS.IdentificationCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.IdentificationCallBack</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithFIPS.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.PtCaptureCallBack</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithFIPS.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.TemplateVerifyCallBack</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithMorpho.EnrollCallBack</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithMorpho.GrabCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithMorpho.GrabCallBack</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithMorpho.IdentificationCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithMorpho.IdentificationCallBack</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithMorpho.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithMorpho.PtCaptureCallBack</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithMorpho.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithMorpho.TemplateVerifyCallBack</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithTLK1NC.IUPImageCallback.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithTLK1NC.IUPImageCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcode1D.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcode1D</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcode2D</span></a></li>
<li type="circle">com.rscja.barcode.<a href="com/rscja/barcode/IBarcode2DSHardwareInfo.html" title="interface in com.rscja.barcode"><span class="typeNameLink">IBarcode2DSHardwareInfo</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcodePhoto.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcodePhoto</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcodePictureCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcodePictureCallback</span></a></li>
<li type="circle">com.rscja.team.mtk.barcode.symbol.<a href="com/rscja/team/mtk/barcode/symbol/IBarcodeSymbol_mtk.html" title="interface in com.rscja.team.mtk.barcode.symbol"><span class="typeNameLink">IBarcodeSymbol_mtk</span></a></li>
<li type="circle">com.rscja.team.qcom.barcode.symbol.<a href="com/rscja/team/qcom/barcode/symbol/IBarcodeSymbol_qcom.html" title="interface in com.rscja.team.qcom.barcode.symbol"><span class="typeNameLink">IBarcodeSymbol_qcom</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcodeSymbolUtility</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcodeUtility</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBarcodeVideoCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcodeVideoCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBleDevice</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBluetoothData.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBluetoothData</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ICardWithBYL.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ICardWithBYL</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IConnectionStatusChangedListener.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IConnectionStatusChangedListener</span></a></li>
<li type="circle">com.rscja.team.qcom.http.<a href="com/rscja/team/qcom/http/IDownLoadProgress_qcom.html" title="interface in com.rscja.team.qcom.http"><span class="typeNameLink">IDownLoadProgress_qcom</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IFingerprint</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IFingerprintSM206B</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IFingerprintWithFIPS</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IFingerprintWithMorpho</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IFingerprintWithTLK1NC</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IFingerprintWithZAZ</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IGPIStateCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IGPIStateCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IHandheldRFID</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUSB</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUhfBle</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHF14443A.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IHF14443A</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IHF14443B</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IHF15693</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IInfrared</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ILedLight.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ILedLight</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IModule</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IMultipleAntenna</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4RS232</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA4</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4RS232</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4RS232</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8RS232</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA8</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8RS232</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8RS232</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxNetwork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxUart</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUsbToUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxUsbToUart</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA4</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4RS232</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA8</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8RS232</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFURx</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxNetwork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxUart</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUsbToUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxUsbToUart</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IPrinter</span></a></li>
<li type="circle">com.rscja.team.qcom.r1.<a href="com/rscja/team/qcom/r1/IPSAM.html" title="interface in com.rscja.team.qcom.r1"><span class="typeNameLink">IPSAM</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IPSAM</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IReader</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBluetoothReader</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUhfBle</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUSB</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUhfBle</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUhfReader</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUSB</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUhfBle</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>)</li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDBase</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithISO14443A</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443A4CPU.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithISO14443A4CPU</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithISO14443B</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithISO15693</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithLF</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IScanerLedLight</span></a></li>
<li type="circle">com.rscja.scanner.<a href="com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner"><span class="typeNameLink">IScanner</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ISingleAntenna</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUSB</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUhfBle</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.system.<a href="com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system"><span class="typeNameLink">ISystemInterfaces</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ITagLocate.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ITagLocate</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ITagLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ITagLocationCallback</span></a>&lt;T&gt;</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHF</span></a>
<ul>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IM775Authenticate.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IM775Authenticate</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4RS232</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8RS232</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IRFIDWithUHFJieCe.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IRFIDWithUHFJieCe</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFRLM</span></a></li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IRFIDWithUHFShuangYingDianZi.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IRFIDWithUHFShuangYingDianZi</span></a></li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IRFIDWithUHFUARTUAE.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IRFIDWithUHFUARTUAE</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxNetwork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxUart</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUsbToUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxUsbToUart</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUSB</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA4</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4RS232</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA8</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8RS232</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUhfBle</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFCSYX.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IUHFCSYX</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFOfAndroidUart</span></a>
<ul>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IM775Authenticate.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IM775Authenticate</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IRFIDWithUHFJieCe.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IRFIDWithUHFJieCe</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFRLM</span></a></li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IRFIDWithUHFShuangYingDianZi.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IRFIDWithUHFShuangYingDianZi</span></a></li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IRFIDWithUHFUARTUAE.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IRFIDWithUHFUARTUAE</span></a></li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFCSYX.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IUHFCSYX</span></a></li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFTamperAPI.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IUHFTamperAPI</span></a></li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFUartFoxconn.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IUHFUartFoxconn</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFTamperAPI.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IUHFTamperAPI</span></a></li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFUartFoxconn.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IUHFUartFoxconn</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFURx</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxNetwork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxUart</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUsbToUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxUsbToUart</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFInventoryCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFInventoryCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFLocationCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFProtocolParse</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFProtocolParseUrx</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFProtocolParseBLE.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IUHFProtocolParseBLE</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFRadarLocationCallback</span></a></li>
<li type="circle">com.rscja.custom.interfaces.<a href="com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">IUHFTemperatureTagsAPI</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFURAxExtend</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4RS232</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8RS232</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA4</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4RS232</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA8</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8RS232</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFUrxAutoInventoryTag.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFUrxAutoInventoryTag</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUpgradeProgress.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUpgradeProgress</span></a></li>
<li type="circle">com.rscja.team.qcom.http.<a href="com/rscja/team/qcom/http/IUploadProgress_qcom.html" title="interface in com.rscja.team.qcom.http"><span class="typeNameLink">IUploadProgress_qcom</span></a></li>
<li type="circle">com.rscja.team.qcom.urax.<a href="com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax"><span class="typeNameLink">IURA4Gpio</span></a></li>
<li type="circle">com.rscja.team.qcom.urax.<a href="com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax"><span class="typeNameLink">IURAxDevice</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IURAxOfAndroidUart</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/IUsbFingerprint.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUsbFingerprint</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/KeyEventCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">KeyEventCallback</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/M775Authenticate.IUHFInventoryCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">M775Authenticate.IUHFInventoryCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/OnLowBatteryListener.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">OnLowBatteryListener</span></a></li>
<li type="circle">com.rscja.scanner.<a href="com/rscja/scanner/OnUhfWorkStateListener.html" title="interface in com.rscja.scanner"><span class="typeNameLink">OnUhfWorkStateListener</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/Printer.PrinterStatusCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">Printer.PrinterStatusCallBack</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ScanBTCallback</span></a></li>
<li type="circle">com.rscja.team.qcom.socket.<a href="com/rscja/team/qcom/socket/SocketManageUR4.CheckConnectState.html" title="interface in com.rscja.team.qcom.socket"><span class="typeNameLink">SocketManageUR4.CheckConnectState</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFCSYX.IUHFInventoryCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFCSYX.IUHFInventoryCallback</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTemperatureSensors.IUHFInventoryTemperatureTagCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFTemperatureSensors.IUHFInventoryTemperatureTagCallback</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag.InventoryTemperatureTagCallback</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTemperatureTagsAPI.IUHFInventoryTempCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI.IUHFInventoryTempCallback</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFUartTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFUartTemperatureTag.InventoryTemperatureTagCallback</span></a></li>
<li type="circle">com.rscja.team.qcom.rs232utils.<a href="com/rscja/team/qcom/rs232utils/UhfUartUR4Manage_qcom.CheckConnectState.html" title="interface in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">UhfUartUR4Manage_qcom.CheckConnectState</span></a></li>
<li type="circle">com.rscja.team.qcom.usb.<a href="com/rscja/team/qcom/usb/UsbBase_qcom.DataCallback.html" title="interface in com.rscja.team.qcom.usb"><span class="typeNameLink">UsbBase_qcom.DataCallback</span></a></li>
<li type="circle">com.rscja.team.qcom.usb.pl2302.<a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302"><span class="typeNameLink">UsbSerialPort_qcom</span></a></li>
</ul>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithISO15693.TagType.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithISO15693.TagType</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithISO14443A.KeyType.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithISO14443A.KeyType</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithISO14443A.TagType.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithISO14443A.TagType</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithISO14443A.DESFireFileTypekEnum</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithISO14443A.DESFireEncryptionTypekEnum</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithFIPS.DataFormat.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithFIPS.DataFormat</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithTLK1NC.BufferEnum</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">FingerprintWithZAZ.BufferEnum</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Fingerprint.BufferEnum</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/Printer.PrinterStatus.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Printer.PrinterStatus</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="com/rscja/deviceapi/Printer.BarcodeType.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Printer.BarcodeType</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="com/rscja/deviceapi/entity/HF15693RequestEntity.TagType.html" title="enum in com.rscja.deviceapi.entity"><span class="typeNameLink">HF15693RequestEntity.TagType</span></a></li>
<li type="circle">com.rscja.deviceapi.enums.<a href="com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums"><span class="typeNameLink">AntennaEnum</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ConnectionStatus</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFXSAPI.Bank.html" title="enum in com.rscja.custom"><span class="typeNameLink">UHFXSAPI.Bank</span></a></li>
<li type="circle">com.rscja.custom.<a href="com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI.TagState</span></a></li>
<li type="circle">com.rscja.barcode.<a href="com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode"><span class="typeNameLink">BarcodeUtility.ModuleType</span></a></li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">HardwareInterface_qcom.FunctionEnum</span></a></li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.TagType.html" title="enum in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDWithISO15693_mtk.TagType</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-files/index-1.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-tree.html" target="_top">Frames</a></li>
<li><a href="overview-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
