<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>ScanLed</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ScanLed";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":6,"i5":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ScanLed.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../com/rscja/scanner/led/ScanLedManage.html" title="class in com.rscja.scanner.led"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/scanner/led/ScanLed.html" target="_top">Frames</a></li>
<li><a href="ScanLed.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.scanner.led</div>
<h2 title="Class ScanLed" class="title">Class ScanLed</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.scanner.led.ScanLed</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/rscja/team/qcom/scanner/led/C60_6765_11_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C60_6765_11_ScanLed_qcom</a>, <a href="../../../../com/rscja/team/qcom/scanner/led/C60_qcm2150_10_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C60_qcm2150_10_ScanLed_qcom</a>, <a href="../../../../com/rscja/team/mtk/scanner/led/C6000_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C6000_6762_ScanLed_mtk</a>, <a href="../../../../com/rscja/team/qcom/scanner/led/C61_smd450_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C61_smd450_90_ScanLed_qcom</a>, <a href="../../../../com/rscja/team/qcom/scanner/led/C66_smd450_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C66_smd450_90_ScanLed_qcom</a>, <a href="../../../../com/rscja/team/qcom/scanner/led/C66m_sm6115_10_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">C66m_sm6115_10_ScanLed_qcom</a>, <a href="../../../../com/rscja/team/mtk/scanner/led/C7X_6765_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C7X_6765_ScanLed_mtk</a>, <a href="../../../../com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C90_6762_ScanLed_mtk</a>, <a href="../../../../com/rscja/team/qcom/scanner/led/MC50_4350_12_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">MC50_4350_12_ScanLed_qcom</a>, <a href="../../../../com/rscja/team/qcom/scanner/led/P80_8786_130_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">P80_8786_130_ScanLed_qcom</a>, <a href="../../../../com/rscja/team/qcom/scanner/led/P80_8953_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led">P80_8953_90_ScanLed_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">ScanLed</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/led/ScanLed.html#ScanLed--">ScanLed</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/led/ScanLed.html#blink--">blink</a></span>()</code>
<div class="block">亮0.5S后熄灭<br>
 light up for 0.5s then OFF<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/led/ScanLed.html#blink-int-int-">blink</a></span>(int&nbsp;lightTime,
     int&nbsp;interval)</code>
<div class="block">控制亮灯后自动熄灭</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/led/ScanLed.html#free--">free</a></span>()</code>
<div class="block">释放设备资源<br>
 release device source<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/led/ScanLed.html#init-android.content.Context-">init</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">初始化设备资源<br>
 initialize device source<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/led/ScanLed.html#off--">off</a></span>()</code>
<div class="block">关闭LED灯<br>
 Switch off LED light<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/led/ScanLed.html#on--">on</a></span>()</code>
<div class="block">打开LED灯<br>
 Switch on LED light<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ScanLed--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ScanLed</h4>
<pre>public&nbsp;ScanLed()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="init-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;void&nbsp;init(android.content.Context&nbsp;context)</pre>
<div class="block">初始化设备资源<br>
 initialize device source<br></div>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;void&nbsp;free()</pre>
<div class="block">释放设备资源<br>
 release device source<br></div>
</li>
</ul>
<a name="on--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>on</h4>
<pre>public abstract&nbsp;void&nbsp;on()</pre>
<div class="block">打开LED灯<br>
 Switch on LED light<br></div>
</li>
</ul>
<a name="off--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>off</h4>
<pre>public abstract&nbsp;void&nbsp;off()</pre>
<div class="block">关闭LED灯<br>
 Switch off LED light<br></div>
</li>
</ul>
<a name="blink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blink</h4>
<pre>public&nbsp;void&nbsp;blink()</pre>
<div class="block">亮0.5S后熄灭<br>
 light up for 0.5s then OFF<br></div>
</li>
</ul>
<a name="blink-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>blink</h4>
<pre>public&nbsp;void&nbsp;blink(int&nbsp;lightTime,
                  int&nbsp;interval)</pre>
<div class="block">控制亮灯后自动熄灭</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>lightTime</code> - 亮度时间(不低于50ms),到达此时间之后自动熄灭,单位:毫秒</dd>
<dd><code>interval</code> - 间隔时间(不低于50ms),单位:毫秒</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ScanLed.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../com/rscja/scanner/led/ScanLedManage.html" title="class in com.rscja.scanner.led"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/scanner/led/ScanLed.html" target="_top">Frames</a></li>
<li><a href="ScanLed.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
