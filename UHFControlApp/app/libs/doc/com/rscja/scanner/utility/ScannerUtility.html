<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>ScannerUtility</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ScannerUtility";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":9,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ScannerUtility.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/scanner/utility/ScannerUtility.html" target="_top">Frames</a></li>
<li><a href="ScannerUtility.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.scanner.utility</div>
<h2 title="Class ScannerUtility" class="title">Class ScannerUtility</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.scanner.utility.ScannerUtility</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ScannerUtility</span>
extends java.lang.Object
implements <a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></pre>
<div class="block">键盘助手工具类，用于控制键盘助手<br>
 keyboardemulator tool-kits, control keyboardemulator</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Administrator</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.scanner.IScanner">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.scanner.<a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></h3>
<code><a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_ASCII">FORMAT_ASCII</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_DECIMAL">FORMAT_DECIMAL</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_DEFAULT">FORMAT_DEFAULT</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_GB18030">FORMAT_GB18030</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_GB2312">FORMAT_GB2312</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_GBK">FORMAT_GBK</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_HEX">FORMAT_HEX</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_UNICODE">FORMAT_UNICODE</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_UTF8">FORMAT_UTF8</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_14443A">FUNCTION_14443A</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_15693">FUNCTION_15693</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_1D">FUNCTION_1D</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_2D">FUNCTION_2D</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_2D_H">FUNCTION_2D_H</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_LF_ANIMAL">FUNCTION_LF_ANIMAL</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_LF_EM4450">FUNCTION_LF_EM4450</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_LF_HDX">FUNCTION_LF_HDX</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_LF_HID">FUNCTION_LF_HID</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_LF_HITAG">FUNCTION_LF_HITAG</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_LF_ID">FUNCTION_LF_ID</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_LF_NEEDLE">FUNCTION_LF_NEEDLE</a>, <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_UHF">FUNCTION_UHF</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#close-android.content.Context-">close</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">关闭键盘助手总开关<br>
 Switch off keyboardemulator</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#disableFunction-android.content.Context-int-">disableFunction</a></span>(android.content.Context&nbsp;context,
               int&nbsp;function)</code>
<div class="block">禁用指定功能模块<br>
 Disable specific function module</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#enableAuxiliaryLight-android.content.Context-boolean-">enableAuxiliaryLight</a></span>(android.content.Context&nbsp;context,
                    boolean&nbsp;enable)</code>
<div class="block">开启扫描辅助灯(C7x才有此功能)<br>
 switch on scanning aux.light for C7x series</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#enableBarcodeNotRepeat-android.content.Context-boolean-">enableBarcodeNotRepeat</a></span>(android.content.Context&nbsp;context,
                      boolean&nbsp;enable)</code>
<div class="block">不输出重复标签(前后两次标签不重复)<br>
 donot outout repeated tags</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#enableBlockScankey-android.content.Context-boolean-">enableBlockScankey</a></span>(android.content.Context&nbsp;context,
                  boolean&nbsp;enable)</code>
<div class="block">拦截扫描按键 (备注：键盘助手v2.3.5 之后的版本才支持)<br>
 Block scan button (Comment: Supports after keyboardemualator v2.3.5 has been released)</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#enableEnter-android.content.Context-boolean-">enableEnter</a></span>(android.content.Context&nbsp;context,
           boolean&nbsp;enter)</code>
<div class="block">是否启用回车<br>
 Enter ON/OFF</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#enableFunction-android.content.Context-int-">enableFunction</a></span>(android.content.Context&nbsp;context,
              int&nbsp;function)</code>
<div class="block">启用指定功能模块<br>
 Enable specific function module</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#enablePlayFailureSound-android.content.Context-boolean-">enablePlayFailureSound</a></span>(android.content.Context&nbsp;context,
                      boolean&nbsp;failureSound)</code>
<div class="block">扫描失败是否播放提示音<br>
 scan failure sound ON/OFF</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#enablePlaySuccessSound-android.content.Context-boolean-">enablePlaySuccessSound</a></span>(android.content.Context&nbsp;context,
                      boolean&nbsp;successSound)</code>
<div class="block">扫描成功是否播放提示音<br>
 Scan success sound ON/OFF</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#enableScanOnRelease-android.content.Context-boolean-">enableScanOnRelease</a></span>(android.content.Context&nbsp;context,
                   boolean&nbsp;enable)</code>
<div class="block">释放扫描按键开始扫描<br></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#enableTAB-android.content.Context-boolean-">enableTAB</a></span>(android.content.Context&nbsp;context,
         boolean&nbsp;tab)</code>
<div class="block">是否启用TAB<br>
 TAB ON/OFF</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#enableVibrate-android.content.Context-boolean-">enableVibrate</a></span>(android.content.Context&nbsp;context,
             boolean&nbsp;vibrate)</code>
<div class="block">扫描成功是否震动提示<br>
 scan success vibrate ON/OFF</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#filterCharacter-android.content.Context-java.lang.String-">filterCharacter</a></span>(android.content.Context&nbsp;context,
               java.lang.String&nbsp;chars)</code>
<div class="block">过滤字符串<br>
 Filter string</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#getLastDecImage-android.content.Context-">getLastDecImage</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#getParam_zebra-android.content.Context-int-">getParam_zebra</a></span>(android.content.Context&nbsp;context,
              int&nbsp;paramId)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#getScannerInerface--">getScannerInerface</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#getScannerParameter-android.content.Context-">getScannerParameter</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#interceptTrimLeft-android.content.Context-int-">interceptTrimLeft</a></span>(android.content.Context&nbsp;context,
                 int&nbsp;num)</code>
<div class="block">截取左边字符串<br>
 Capture string on left</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#interceptTrimRight-android.content.Context-int-">interceptTrimRight</a></span>(android.content.Context&nbsp;context,
                  int&nbsp;num)</code>
<div class="block">截取右边字符串<br>
 capture string on right</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#isUhfWorking-android.content.Context-">isUhfWorking</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">UHF 是否正在盘点<br>
 Working status of UHF</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#open-android.content.Context-">open</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">打开键盘助手总开关<br>
 Switch on keyboardemulator</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#resetScan-android.content.Context-">resetScan</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">键盘助手恢复出厂设置<br>
 Restore factory setup</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setBarcodeContinuousMode-android.content.Context-int-">setBarcodeContinuousMode</a></span>(android.content.Context&nbsp;context,
                        int&nbsp;mode)</code>
<div class="block">设置扫描头连续扫描模式 (备注：键盘助手v2.3.5 之后的版本才支持)<br>
 Setup UHF mode (Comment: Supports after keyboardemualator v2.3.5 has been released)</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setBarcodeEncodingFormat-android.content.Context-int-">setBarcodeEncodingFormat</a></span>(android.content.Context&nbsp;context,
                        int&nbsp;format)</code>
<div class="block">设置条码编码格式<br>
 Setup barcode decoding format</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setContinuousScan-android.content.Context-boolean-">setContinuousScan</a></span>(android.content.Context&nbsp;context,
                 boolean&nbsp;isContinuous)</code>
<div class="block">设置条码连续扫描<br>
 Setup barcode continuous scanning</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setContinuousScanIntervalTime-android.content.Context-int-">setContinuousScanIntervalTime</a></span>(android.content.Context&nbsp;context,
                             int&nbsp;intervalTime)</code>
<div class="block">设置条码连续扫描间隔时间<br>
 Setup barcode continuous scanning intervals</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setContinuousScanIntervalTimeRFID-android.content.Context-int-">setContinuousScanIntervalTimeRFID</a></span>(android.content.Context&nbsp;context,
                                 int&nbsp;intervalTime)</code>
<div class="block">设置UHF连续扫描间隔时间<br>
 Setup UHF continuous scanning intervals</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setContinuousScanRFID-android.content.Context-boolean-">setContinuousScanRFID</a></span>(android.content.Context&nbsp;context,
                     boolean&nbsp;isContinuous)</code>
<div class="block">设置UHF连续扫描<br>
 Setup UHF continuous scann</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setContinuousScanTimeOut-android.content.Context-int-">setContinuousScanTimeOut</a></span>(android.content.Context&nbsp;context,
                        int&nbsp;timeOut)</code>
<div class="block">设置条码连续扫描超时时间<br>
 Setup barcode continuous scanning time-out interval</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setContinuousScanTimeOutRFID-android.content.Context-int-">setContinuousScanTimeOutRFID</a></span>(android.content.Context&nbsp;context,
                            int&nbsp;timeOut)</code>
<div class="block">设置UHF连续扫描超时时间<br>
 Setup UHF continuous scanning time-out interval</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setGroupSeparator-android.content.Context-boolean-">setGroupSeparator</a></span>(android.content.Context&nbsp;context,
                 boolean&nbsp;disable)</code>
<div class="block">是否去掉分组符<br>
 Delete D GS</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setOnUhfWorkStateListener-android.content.Context-com.rscja.scanner.OnUhfWorkStateListener-">setOnUhfWorkStateListener</a></span>(android.content.Context&nbsp;context,
                         <a href="../../../../com/rscja/scanner/OnUhfWorkStateListener.html" title="interface in com.rscja.scanner">OnUhfWorkStateListener</a>&nbsp;onUhfWorkStateListener)</code>
<div class="block">设置UHF工作状态发送改变的回调接口
 Register a callback to be invoked when this UHF working state changes.</br></div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setOutputMode-android.content.Context-int-">setOutputMode</a></span>(android.content.Context&nbsp;context,
             int&nbsp;outputMode)</code>
<div class="block">输出模式<br>
 Output mode</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setParam_zebra-android.content.Context-int-int-">setParam_zebra</a></span>(android.content.Context&nbsp;context,
              int&nbsp;paramId,
              int&nbsp;paramValue)</code>
<div class="block">设置斑马扫描头参数,扫描头上电之后设置一次即可,扫描头断电之后失效。(备注：键盘助手v2.2.0.3 之后的版本才支持)<br>
 Setup zebra scanner parameters, after scanner has powered on to setup for once,</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setPrefix-android.content.Context-java.lang.String-">setPrefix</a></span>(android.content.Context&nbsp;context,
         java.lang.String&nbsp;prefix)</code>
<div class="block">设置前缀<br>
 Setup prefix</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setReleaseScan-android.content.Context-boolean-">setReleaseScan</a></span>(android.content.Context&nbsp;context,
              boolean&nbsp;enable)</code>
<div class="block">松开扫描按键是否停止扫描<br>
 Stop scan after release scan button</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setRFIDEncodingFormat-android.content.Context-int-">setRFIDEncodingFormat</a></span>(android.content.Context&nbsp;context,
                     int&nbsp;format)</code>
<div class="block">设置RFID编码格式<br>
 Setup RFID decoding format</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setScanFailureBroadcast-android.content.Context-boolean-">setScanFailureBroadcast</a></span>(android.content.Context&nbsp;context,
                       boolean&nbsp;enable)</code>
<div class="block">扫描失败是否发送广播,接收广播的action和扫描成功的action是同一个<br>
 Send broadcast when scan failure</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setScanKey-android.content.Context-int-int:A-">setScanKey</a></span>(android.content.Context&nbsp;context,
          int&nbsp;type,
          int[]&nbsp;scanKey)</code>
<div class="block">设置扫描或者读卡的按键值<br>
 Setup keycode for barcode scan or card reading</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setScanOutTime-android.content.Context-int-">setScanOutTime</a></span>(android.content.Context&nbsp;context,
              int&nbsp;time)</code>
<div class="block">设置扫码超时时间<br>
 Setup scan time-out duration</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setScanResultBroadcast-android.content.Context-java.lang.String-java.lang.String-">setScanResultBroadcast</a></span>(android.content.Context&nbsp;context,
                      java.lang.String&nbsp;broadcastAction,
                      java.lang.String&nbsp;data)</code>
<div class="block">设置条码扫描结果接收的广播<br>
 Setup barcode scanning result</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setScanResultBroadcastRFID-android.content.Context-java.lang.String-java.lang.String-">setScanResultBroadcastRFID</a></span>(android.content.Context&nbsp;context,
                          java.lang.String&nbsp;broadcastAction,
                          java.lang.String&nbsp;data)</code>
<div class="block">设置RFID扫描结果接收广播<br>
 Setup RFID scanning result receive broadcast</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setSuffix-android.content.Context-java.lang.String-">setSuffix</a></span>(android.content.Context&nbsp;context,
         java.lang.String&nbsp;suffix)</code>
<div class="block">设置后缀<br>
 Setup suffix</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setUHFMode-android.content.Context-int-">setUHFMode</a></span>(android.content.Context&nbsp;context,
          int&nbsp;mode)</code>
<div class="block">设置uhf模式 (备注：键盘助手v2.3.5 之后的版本才支持)<br>
 Setup UHF mode (Comment: Supports after keyboardemualator v2.3.5 has been released)</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setUHFPower-android.content.Context-int-">setUHFPower</a></span>(android.content.Context&nbsp;context,
           int&nbsp;power)</code>
<div class="block">设置uhf功率<br>
 Setup UHF output power</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#setVirtualScanButton-android.content.Context-int-">setVirtualScanButton</a></span>(android.content.Context&nbsp;context,
                    int&nbsp;buttonSize)</code>
<div class="block">设置虚拟扫描按钮<br></div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#startScan-android.content.Context-int-">startScan</a></span>(android.content.Context&nbsp;context,
         int&nbsp;function)</code>
<div class="block">开始扫描或者读卡<br>
 start scanning or card-reading</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/scanner/utility/ScannerUtility.html#stopScan-android.content.Context-int-">stopScan</a></span>(android.content.Context&nbsp;context,
        int&nbsp;function)</code>
<div class="block">停止扫描<br>
 Stop scanning</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getScannerInerface--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScannerInerface</h4>
<pre>public static&nbsp;<a href="../../../../com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility">ScannerUtility</a>&nbsp;getScannerInerface()</pre>
</li>
</ul>
<a name="open-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;void&nbsp;open(android.content.Context&nbsp;context)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#open-android.content.Context-">IScanner</a></code></span></div>
<div class="block">打开键盘助手总开关<br>
 Switch on keyboardemulator</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#open-android.content.Context-">open</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
</dl>
</li>
</ul>
<a name="close-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;void&nbsp;close(android.content.Context&nbsp;context)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#close-android.content.Context-">IScanner</a></code></span></div>
<div class="block">关闭键盘助手总开关<br>
 Switch off keyboardemulator</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#close-android.content.Context-">close</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
</dl>
</li>
</ul>
<a name="enableFunction-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableFunction</h4>
<pre>public&nbsp;void&nbsp;enableFunction(android.content.Context&nbsp;context,
                           int&nbsp;function)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#enableFunction-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">启用指定功能模块<br>
 Enable specific function module</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#enableFunction-android.content.Context-int-">enableFunction</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>function</code> - 0: 1D， 1: 硬解码(hard_decoding)，2: 软解码(soft_decoding) , 11：UHF <br>
                 <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_1D"><code>IScanner.FUNCTION_1D</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_2D_H"><code>IScanner.FUNCTION_2D_H</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_2D"><code>IScanner.FUNCTION_2D</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_UHF"><code>IScanner.FUNCTION_UHF</code></a> <br></dd>
</dl>
</li>
</ul>
<a name="disableFunction-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disableFunction</h4>
<pre>public&nbsp;void&nbsp;disableFunction(android.content.Context&nbsp;context,
                            int&nbsp;function)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#disableFunction-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">禁用指定功能模块<br>
 Disable specific function module</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#disableFunction-android.content.Context-int-">disableFunction</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>function</code> - 0: 1D， 1: 硬解码(hard_decoding)，2: 软解码(soft_decoding) , 11：UHF <br>
                 <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_1D"><code>IScanner.FUNCTION_1D</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_2D_H"><code>IScanner.FUNCTION_2D_H</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_2D"><code>IScanner.FUNCTION_2D</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_UHF"><code>IScanner.FUNCTION_UHF</code></a> <br></dd>
</dl>
</li>
</ul>
<a name="startScan-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startScan</h4>
<pre>public&nbsp;void&nbsp;startScan(android.content.Context&nbsp;context,
                      int&nbsp;function)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#startScan-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">开始扫描或者读卡<br>
 start scanning or card-reading</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#startScan-android.content.Context-int-">startScan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>function</code> - 0: 1D， 1: 硬解码(hard_decoding)，2: 软解码(soft_decoding) , 11：UHF <br>
                 <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_1D"><code>IScanner.FUNCTION_1D</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_2D_H"><code>IScanner.FUNCTION_2D_H</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_2D"><code>IScanner.FUNCTION_2D</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_UHF"><code>IScanner.FUNCTION_UHF</code></a> <br></dd>
</dl>
</li>
</ul>
<a name="stopScan-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopScan</h4>
<pre>public&nbsp;void&nbsp;stopScan(android.content.Context&nbsp;context,
                     int&nbsp;function)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#stopScan-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">停止扫描<br>
 Stop scanning</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#stopScan-android.content.Context-int-">stopScan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>function</code> - 0: 1D， 1: 硬解码(hard_decoding)，2: 软解码(soft_decoding) , 11：UHF <br>
                 <a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_1D"><code>IScanner.FUNCTION_1D</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_2D_H"><code>IScanner.FUNCTION_2D_H</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_2D"><code>IScanner.FUNCTION_2D</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FUNCTION_UHF"><code>IScanner.FUNCTION_UHF</code></a> <br></dd>
</dl>
</li>
</ul>
<a name="enablePlaySuccessSound-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enablePlaySuccessSound</h4>
<pre>public&nbsp;void&nbsp;enablePlaySuccessSound(android.content.Context&nbsp;context,
                                   boolean&nbsp;successSound)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#enablePlaySuccessSound-android.content.Context-boolean-">IScanner</a></code></span></div>
<div class="block">扫描成功是否播放提示音<br>
 Scan success sound ON/OFF</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#enablePlaySuccessSound-android.content.Context-boolean-">enablePlaySuccessSound</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>successSound</code> - true:播放声音(play sound)，false不播放声音(donot play sound)</dd>
</dl>
</li>
</ul>
<a name="enablePlayFailureSound-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enablePlayFailureSound</h4>
<pre>public&nbsp;void&nbsp;enablePlayFailureSound(android.content.Context&nbsp;context,
                                   boolean&nbsp;failureSound)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#enablePlayFailureSound-android.content.Context-boolean-">IScanner</a></code></span></div>
<div class="block">扫描失败是否播放提示音<br>
 scan failure sound ON/OFF</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#enablePlayFailureSound-android.content.Context-boolean-">enablePlayFailureSound</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>failureSound</code> - true:播放声音(play sound)，false不播放声音(donot play sound)</dd>
</dl>
</li>
</ul>
<a name="enableVibrate-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableVibrate</h4>
<pre>public&nbsp;void&nbsp;enableVibrate(android.content.Context&nbsp;context,
                          boolean&nbsp;vibrate)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#enableVibrate-android.content.Context-boolean-">IScanner</a></code></span></div>
<div class="block">扫描成功是否震动提示<br>
 scan success vibrate ON/OFF</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#enableVibrate-android.content.Context-boolean-">enableVibrate</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>vibrate</code> - true:表示震动(vibrate)，false表示不震动(NO vibrate)</dd>
</dl>
</li>
</ul>
<a name="setOutputMode-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutputMode</h4>
<pre>public&nbsp;void&nbsp;setOutputMode(android.content.Context&nbsp;context,
                          int&nbsp;outputMode)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setOutputMode-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">输出模式<br>
 Output mode</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setOutputMode-android.content.Context-int-">setOutputMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>outputMode</code> - 0:扫描到光标位置(scan content to cursor)    1:剪切板(clipboard)   2:广播(broadcast)    3:模拟键盘(analog keyboard)</dd>
</dl>
</li>
</ul>
<a name="setBarcodeEncodingFormat-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeEncodingFormat</h4>
<pre>public&nbsp;void&nbsp;setBarcodeEncodingFormat(android.content.Context&nbsp;context,
                                     int&nbsp;format)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setBarcodeEncodingFormat-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">设置条码编码格式<br>
 Setup barcode decoding format</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setBarcodeEncodingFormat-android.content.Context-int-">setBarcodeEncodingFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>format</code> - 0: Default   1: ASCII    2:GB2312   3:UTF8   4:Unicode    10:GBK,    11:GB18030 <br>
                <a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_DEFAULT"><code>IScanner.FORMAT_DEFAULT</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_ASCII"><code>IScanner.FORMAT_ASCII</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_GB2312"><code>IScanner.FORMAT_GB2312</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_UTF8"><code>IScanner.FORMAT_UTF8</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_UNICODE"><code>IScanner.FORMAT_UNICODE</code></a>、<a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_GBK"><code>IScanner.FORMAT_GBK</code></a> 、<a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_GB18030"><code>IScanner.FORMAT_GB18030</code></a> <br></dd>
</dl>
</li>
</ul>
<a name="setRFIDEncodingFormat-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRFIDEncodingFormat</h4>
<pre>public&nbsp;void&nbsp;setRFIDEncodingFormat(android.content.Context&nbsp;context,
                                  int&nbsp;format)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setRFIDEncodingFormat-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">设置RFID编码格式<br>
 Setup RFID decoding format</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setRFIDEncodingFormat-android.content.Context-int-">setRFIDEncodingFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>format</code> - 5:十六进制(Hex)  6: 十进制(decimalism)    <br>
                <a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_HEX"><code>IScanner.FORMAT_HEX</code></a> 、<a href="../../../../com/rscja/scanner/IScanner.html#FORMAT_DECIMAL"><code>IScanner.FORMAT_DECIMAL</code></a>   <br></dd>
</dl>
</li>
</ul>
<a name="enableEnter-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableEnter</h4>
<pre>public&nbsp;void&nbsp;enableEnter(android.content.Context&nbsp;context,
                        boolean&nbsp;enter)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#enableEnter-android.content.Context-boolean-">IScanner</a></code></span></div>
<div class="block">是否启用回车<br>
 Enter ON/OFF</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#enableEnter-android.content.Context-boolean-">enableEnter</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>enter</code> - true: 启用回车(Enter ON),  false:不启用回车(Enter OFF)</dd>
</dl>
</li>
</ul>
<a name="enableTAB-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableTAB</h4>
<pre>public&nbsp;void&nbsp;enableTAB(android.content.Context&nbsp;context,
                      boolean&nbsp;tab)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#enableTAB-android.content.Context-boolean-">IScanner</a></code></span></div>
<div class="block">是否启用TAB<br>
 TAB ON/OFF</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#enableTAB-android.content.Context-boolean-">enableTAB</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>tab</code> - true: 启用(Tab ON) ,   false:不启用(Tab OFF)</dd>
</dl>
</li>
</ul>
<a name="setSuffix-android.content.Context-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSuffix</h4>
<pre>public&nbsp;void&nbsp;setSuffix(android.content.Context&nbsp;context,
                      java.lang.String&nbsp;suffix)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setSuffix-android.content.Context-java.lang.String-">IScanner</a></code></span></div>
<div class="block">设置后缀<br>
 Setup suffix</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setSuffix-android.content.Context-java.lang.String-">setSuffix</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>suffix</code> - 后缀字符(suffix characters)</dd>
</dl>
</li>
</ul>
<a name="setPrefix-android.content.Context-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrefix</h4>
<pre>public&nbsp;void&nbsp;setPrefix(android.content.Context&nbsp;context,
                      java.lang.String&nbsp;prefix)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setPrefix-android.content.Context-java.lang.String-">IScanner</a></code></span></div>
<div class="block">设置前缀<br>
 Setup prefix</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setPrefix-android.content.Context-java.lang.String-">setPrefix</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>prefix</code> - 前缀字符(prefix characters)</dd>
</dl>
</li>
</ul>
<a name="interceptTrimLeft-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>interceptTrimLeft</h4>
<pre>public&nbsp;void&nbsp;interceptTrimLeft(android.content.Context&nbsp;context,
                              int&nbsp;num)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#interceptTrimLeft-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">截取左边字符串<br>
 Capture string on left</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#interceptTrimLeft-android.content.Context-int-">interceptTrimLeft</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>num</code> - 左边截取的字符数量(string capacity that captured on left)</dd>
</dl>
</li>
</ul>
<a name="interceptTrimRight-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>interceptTrimRight</h4>
<pre>public&nbsp;void&nbsp;interceptTrimRight(android.content.Context&nbsp;context,
                               int&nbsp;num)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#interceptTrimRight-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">截取右边字符串<br>
 capture string on right</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#interceptTrimRight-android.content.Context-int-">interceptTrimRight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>num</code> - 右边截取的字符数量(string capacity that captured on right)</dd>
</dl>
</li>
</ul>
<a name="setScanOutTime-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScanOutTime</h4>
<pre>public&nbsp;void&nbsp;setScanOutTime(android.content.Context&nbsp;context,
                           int&nbsp;time)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setScanOutTime-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">设置扫码超时时间<br>
 Setup scan time-out duration</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setScanOutTime-android.content.Context-int-">setScanOutTime</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>time</code> - 超时时间，单位秒(time-out duration, unit is sec.)</dd>
</dl>
</li>
</ul>
<a name="filterCharacter-android.content.Context-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filterCharacter</h4>
<pre>public&nbsp;void&nbsp;filterCharacter(android.content.Context&nbsp;context,
                            java.lang.String&nbsp;chars)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#filterCharacter-android.content.Context-java.lang.String-">IScanner</a></code></span></div>
<div class="block">过滤字符串<br>
 Filter string</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#filterCharacter-android.content.Context-java.lang.String-">filterCharacter</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>chars</code> - 过滤的字符(fialtered strings)</dd>
</dl>
</li>
</ul>
<a name="setContinuousScanRFID-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContinuousScanRFID</h4>
<pre>public&nbsp;void&nbsp;setContinuousScanRFID(android.content.Context&nbsp;context,
                                  boolean&nbsp;isContinuous)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setContinuousScanRFID-android.content.Context-boolean-">IScanner</a></code></span></div>
<div class="block">设置UHF连续扫描<br>
 Setup UHF continuous scann</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setContinuousScanRFID-android.content.Context-boolean-">setContinuousScanRFID</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>isContinuous</code> - true: 连续扫描(continous scann )    false：单次扫描(single scann)</dd>
</dl>
</li>
</ul>
<a name="setContinuousScanIntervalTimeRFID-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContinuousScanIntervalTimeRFID</h4>
<pre>public&nbsp;void&nbsp;setContinuousScanIntervalTimeRFID(android.content.Context&nbsp;context,
                                              int&nbsp;intervalTime)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setContinuousScanIntervalTimeRFID-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">设置UHF连续扫描间隔时间<br>
 Setup UHF continuous scanning intervals</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setContinuousScanIntervalTimeRFID-android.content.Context-int-">setContinuousScanIntervalTimeRFID</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>intervalTime</code> - 间隔时间，单位毫秒(time interval,unit is millisecond. )</dd>
</dl>
</li>
</ul>
<a name="setContinuousScanTimeOutRFID-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContinuousScanTimeOutRFID</h4>
<pre>public&nbsp;void&nbsp;setContinuousScanTimeOutRFID(android.content.Context&nbsp;context,
                                         int&nbsp;timeOut)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setContinuousScanTimeOutRFID-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">设置UHF连续扫描超时时间<br>
 Setup UHF continuous scanning time-out interval</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setContinuousScanTimeOutRFID-android.content.Context-int-">setContinuousScanTimeOutRFID</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>timeOut</code> - 超时时间，单位秒  (timeout, unit is sec.)</dd>
</dl>
</li>
</ul>
<a name="setUHFPower-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUHFPower</h4>
<pre>public&nbsp;void&nbsp;setUHFPower(android.content.Context&nbsp;context,
                        int&nbsp;power)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setUHFPower-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">设置uhf功率<br>
 Setup UHF output power</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setUHFPower-android.content.Context-int-">setUHFPower</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>power</code> - 功率值(Power value)</dd>
</dl>
</li>
</ul>
<a name="setContinuousScan-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContinuousScan</h4>
<pre>public&nbsp;void&nbsp;setContinuousScan(android.content.Context&nbsp;context,
                              boolean&nbsp;isContinuous)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setContinuousScan-android.content.Context-boolean-">IScanner</a></code></span></div>
<div class="block">设置条码连续扫描<br>
 Setup barcode continuous scanning</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setContinuousScan-android.content.Context-boolean-">setContinuousScan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>isContinuous</code> - true: 连续扫描(continous scann )    false：单次扫描(single scann)</dd>
</dl>
</li>
</ul>
<a name="setContinuousScanIntervalTime-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContinuousScanIntervalTime</h4>
<pre>public&nbsp;void&nbsp;setContinuousScanIntervalTime(android.content.Context&nbsp;context,
                                          int&nbsp;intervalTime)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setContinuousScanIntervalTime-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">设置条码连续扫描间隔时间<br>
 Setup barcode continuous scanning intervals</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setContinuousScanIntervalTime-android.content.Context-int-">setContinuousScanIntervalTime</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>intervalTime</code> - 间隔时间，单位毫秒(time interval,unit is millisecond. )</dd>
</dl>
</li>
</ul>
<a name="setContinuousScanTimeOut-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContinuousScanTimeOut</h4>
<pre>public&nbsp;void&nbsp;setContinuousScanTimeOut(android.content.Context&nbsp;context,
                                     int&nbsp;timeOut)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setContinuousScanTimeOut-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">设置条码连续扫描超时时间<br>
 Setup barcode continuous scanning time-out interval</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setContinuousScanTimeOut-android.content.Context-int-">setContinuousScanTimeOut</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>timeOut</code> - 超时时间，单位秒  (timeout, unit is sec.)</dd>
</dl>
</li>
</ul>
<a name="resetScan-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resetScan</h4>
<pre>public&nbsp;void&nbsp;resetScan(android.content.Context&nbsp;context)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#resetScan-android.content.Context-">IScanner</a></code></span></div>
<div class="block">键盘助手恢复出厂设置<br>
 Restore factory setup</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#resetScan-android.content.Context-">resetScan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
</dl>
</li>
</ul>
<a name="setScanKey-android.content.Context-int-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScanKey</h4>
<pre>public&nbsp;void&nbsp;setScanKey(android.content.Context&nbsp;context,
                       int&nbsp;type,
                       int[]&nbsp;scanKey)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setScanKey-android.content.Context-int-int:A-">IScanner</a></code></span></div>
<div class="block">设置扫描或者读卡的按键值<br>
 Setup keycode for barcode scan or card reading</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setScanKey-android.content.Context-int-int:A-">setScanKey</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>type</code> - 0: Barcode    1：RFID     2：LF     3：UHF</dd>
<dd><code>scanKey</code> - 按键值,条码和UHF支持两个按键值，其他的只支持一个按键值(scanKey keycode, barcode and UHF support two keycodes, others support one keycode.)</dd>
</dl>
</li>
</ul>
<a name="setScanFailureBroadcast-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScanFailureBroadcast</h4>
<pre>public&nbsp;void&nbsp;setScanFailureBroadcast(android.content.Context&nbsp;context,
                                    boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setScanFailureBroadcast-android.content.Context-boolean-">IScanner</a></code></span></div>
<div class="block">扫描失败是否发送广播,接收广播的action和扫描成功的action是同一个<br>
 Send broadcast when scan failure</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setScanFailureBroadcast-android.content.Context-boolean-">setScanFailureBroadcast</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>enable</code> - true:发送(send)    false：不发送(no send)</dd>
</dl>
</li>
</ul>
<a name="setScanResultBroadcast-android.content.Context-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScanResultBroadcast</h4>
<pre>public&nbsp;void&nbsp;setScanResultBroadcast(android.content.Context&nbsp;context,
                                   java.lang.String&nbsp;broadcastAction,
                                   java.lang.String&nbsp;data)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setScanResultBroadcast-android.content.Context-java.lang.String-java.lang.String-">IScanner</a></code></span></div>
<div class="block">设置条码扫描结果接收的广播<br>
 Setup barcode scanning result</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setScanResultBroadcast-android.content.Context-java.lang.String-java.lang.String-">setScanResultBroadcast</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>broadcastAction</code> - 广播的action名称 (broadcastAction designation of broadcastAction)</dd>
<dd><code>data</code> - 广播的Extra名称 (Extra designation of broadcast)</dd>
</dl>
</li>
</ul>
<a name="setScanResultBroadcastRFID-android.content.Context-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScanResultBroadcastRFID</h4>
<pre>public&nbsp;void&nbsp;setScanResultBroadcastRFID(android.content.Context&nbsp;context,
                                       java.lang.String&nbsp;broadcastAction,
                                       java.lang.String&nbsp;data)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setScanResultBroadcastRFID-android.content.Context-java.lang.String-java.lang.String-">IScanner</a></code></span></div>
<div class="block">设置RFID扫描结果接收广播<br>
 Setup RFID scanning result receive broadcast</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setScanResultBroadcastRFID-android.content.Context-java.lang.String-java.lang.String-">setScanResultBroadcastRFID</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>broadcastAction</code> - 广播的action名称 ( broadcast action designation)</dd>
<dd><code>data</code> - 广播的Extra名称 (Extra designation of broadcast)</dd>
</dl>
</li>
</ul>
<a name="setGroupSeparator-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGroupSeparator</h4>
<pre>public&nbsp;void&nbsp;setGroupSeparator(android.content.Context&nbsp;context,
                              boolean&nbsp;disable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setGroupSeparator-android.content.Context-boolean-">IScanner</a></code></span></div>
<div class="block">是否去掉分组符<br>
 Delete D GS</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setGroupSeparator-android.content.Context-boolean-">setGroupSeparator</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>disable</code> - true:禁用 (disable)   false：启用(enable)</dd>
</dl>
</li>
</ul>
<a name="setReleaseScan-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReleaseScan</h4>
<pre>public&nbsp;void&nbsp;setReleaseScan(android.content.Context&nbsp;context,
                           boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setReleaseScan-android.content.Context-boolean-">IScanner</a></code></span></div>
<div class="block">松开扫描按键是否停止扫描<br>
 Stop scan after release scan button</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setReleaseScan-android.content.Context-boolean-">setReleaseScan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>enable</code> - true:停止扫描(stop scan)    false：不停止扫描(continnue scan)</dd>
</dl>
</li>
</ul>
<a name="enableAuxiliaryLight-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableAuxiliaryLight</h4>
<pre>public&nbsp;void&nbsp;enableAuxiliaryLight(android.content.Context&nbsp;context,
                                 boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#enableAuxiliaryLight-android.content.Context-boolean-">IScanner</a></code></span></div>
<div class="block">开启扫描辅助灯(C7x才有此功能)<br>
 switch on scanning aux.light for C7x series</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#enableAuxiliaryLight-android.content.Context-boolean-">enableAuxiliaryLight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>enable</code> - true:开启扫描辅助灯(switch on scan aux.light)    false： 关闭扫描辅助灯(switch off aux.light)</dd>
</dl>
</li>
</ul>
<a name="enableBarcodeNotRepeat-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableBarcodeNotRepeat</h4>
<pre>public&nbsp;void&nbsp;enableBarcodeNotRepeat(android.content.Context&nbsp;context,
                                   boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#enableBarcodeNotRepeat-android.content.Context-boolean-">IScanner</a></code></span></div>
<div class="block">不输出重复标签(前后两次标签不重复)<br>
 donot outout repeated tags</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#enableBarcodeNotRepeat-android.content.Context-boolean-">enableBarcodeNotRepeat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>enable</code> - true:不输出重复标签 (donot output repeated tags)   false： 输出重复标签(output repeated tags)</dd>
</dl>
</li>
</ul>
<a name="setParam_zebra-android.content.Context-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParam_zebra</h4>
<pre>public&nbsp;void&nbsp;setParam_zebra(android.content.Context&nbsp;context,
                           int&nbsp;paramId,
                           int&nbsp;paramValue)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setParam_zebra-android.content.Context-int-int-">IScanner</a></code></span></div>
<div class="block">设置斑马扫描头参数,扫描头上电之后设置一次即可,扫描头断电之后失效。(备注：键盘助手v2.2.0.3 之后的版本才支持)<br>
 Setup zebra scanner parameters, after scanner has powered on to setup for once,</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setParam_zebra-android.content.Context-int-int-">setParam_zebra</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>paramId</code> - 参数ID (Param Id)</dd>
<dd><code>paramValue</code> - 参数value (Param Value)</dd>
</dl>
</li>
</ul>
<a name="getParam_zebra-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParam_zebra</h4>
<pre>public&nbsp;int&nbsp;getParam_zebra(android.content.Context&nbsp;context,
                          int&nbsp;paramId)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#getParam_zebra-android.content.Context-int-">getParam_zebra</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
</dl>
</li>
</ul>
<a name="setUHFMode-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUHFMode</h4>
<pre>public&nbsp;void&nbsp;setUHFMode(android.content.Context&nbsp;context,
                       int&nbsp;mode)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setUHFMode-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">设置uhf模式 (备注：键盘助手v2.3.5 之后的版本才支持)<br>
 Setup UHF mode (Comment: Supports after keyboardemualator v2.3.5 has been released)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setUHFMode-android.content.Context-int-">setUHFMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>mode</code> - 0:epc、 1:tid</dd>
</dl>
</li>
</ul>
<a name="setBarcodeContinuousMode-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeContinuousMode</h4>
<pre>public&nbsp;void&nbsp;setBarcodeContinuousMode(android.content.Context&nbsp;context,
                                     int&nbsp;mode)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#setBarcodeContinuousMode-android.content.Context-int-">IScanner</a></code></span></div>
<div class="block">设置扫描头连续扫描模式 (备注：键盘助手v2.3.5 之后的版本才支持)<br>
 Setup UHF mode (Comment: Supports after keyboardemualator v2.3.5 has been released)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setBarcodeContinuousMode-android.content.Context-int-">setBarcodeContinuousMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>mode</code> - 1:普通模式   2:斑马模式(斑马扫描头才支持此模式)<br></dd>
</dl>
</li>
</ul>
<a name="enableBlockScankey-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableBlockScankey</h4>
<pre>public&nbsp;void&nbsp;enableBlockScankey(android.content.Context&nbsp;context,
                               boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html#enableBlockScankey-android.content.Context-boolean-">IScanner</a></code></span></div>
<div class="block">拦截扫描按键 (备注：键盘助手v2.3.5 之后的版本才支持)<br>
 Block scan button (Comment: Supports after keyboardemualator v2.3.5 has been released)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#enableBlockScankey-android.content.Context-boolean-">enableBlockScankey</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>enable</code> - true:拦截扫描按键，不上报扫描按键值     false:不拦截扫描按键</dd>
</dl>
</li>
</ul>
<a name="isUhfWorking-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isUhfWorking</h4>
<pre>public&nbsp;boolean&nbsp;isUhfWorking(android.content.Context&nbsp;context)</pre>
<div class="block">UHF 是否正在盘点<br>
 Working status of UHF</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#isUhfWorking-android.content.Context-">isUhfWorking</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:working    false:non-working</dd>
</dl>
</li>
</ul>
<a name="setOnUhfWorkStateListener-android.content.Context-com.rscja.scanner.OnUhfWorkStateListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnUhfWorkStateListener</h4>
<pre>public&nbsp;void&nbsp;setOnUhfWorkStateListener(android.content.Context&nbsp;context,
                                      <a href="../../../../com/rscja/scanner/OnUhfWorkStateListener.html" title="interface in com.rscja.scanner">OnUhfWorkStateListener</a>&nbsp;onUhfWorkStateListener)</pre>
<div class="block">设置UHF工作状态发送改变的回调接口
 Register a callback to be invoked when this UHF working state changes.</br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setOnUhfWorkStateListener-android.content.Context-com.rscja.scanner.OnUhfWorkStateListener-">setOnUhfWorkStateListener</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dd><code>onUhfWorkStateListener</code> - Callback interface</dd>
</dl>
</li>
</ul>
<a name="getLastDecImage-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastDecImage</h4>
<pre>public&nbsp;void&nbsp;getLastDecImage(android.content.Context&nbsp;context)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#getLastDecImage-android.content.Context-">getLastDecImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
</dl>
</li>
</ul>
<a name="enableScanOnRelease-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableScanOnRelease</h4>
<pre>public&nbsp;void&nbsp;enableScanOnRelease(android.content.Context&nbsp;context,
                                boolean&nbsp;enable)</pre>
<div class="block">释放扫描按键开始扫描<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#enableScanOnRelease-android.content.Context-boolean-">enableScanOnRelease</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
</dl>
</li>
</ul>
<a name="setVirtualScanButton-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVirtualScanButton</h4>
<pre>public&nbsp;void&nbsp;setVirtualScanButton(android.content.Context&nbsp;context,
                                 int&nbsp;buttonSize)</pre>
<div class="block">设置虚拟扫描按钮<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#setVirtualScanButton-android.content.Context-int-">setVirtualScanButton</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
</dl>
</li>
</ul>
<a name="getScannerParameter-android.content.Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getScannerParameter</h4>
<pre>public&nbsp;<a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity</a>&nbsp;getScannerParameter(android.content.Context&nbsp;context)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/scanner/IScanner.html#getScannerParameter-android.content.Context-">getScannerParameter</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner">IScanner</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ScannerUtility.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/scanner/utility/ScannerUtility.html" target="_top">Frames</a></li>
<li><a href="ScannerUtility.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
