<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>com.rscja.deviceapi</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.rscja.deviceapi";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/custom/interfaces/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../com/rscja/deviceapi/entity/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;com.rscja.deviceapi</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/BluetoothReader.DecodeCallback.html" title="interface in com.rscja.deviceapi">BluetoothReader.DecodeCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/BluetoothReader.OnDataChangeListener.html" title="interface in com.rscja.deviceapi">BluetoothReader.OnDataChangeListener</a></td>
<td class="colLast">
<div class="block">接收蓝牙原始数据的接口 <br>
 Interface of receiving initial data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithFIPS.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.EnrollCallBack</a></td>
<td class="colLast">
<div class="block">采集指纹回调接口<br>
 call-back contact for acquiring fingerprint<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithFIPS.GRABCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.GRABCallBack</a></td>
<td class="colLast">
<div class="block">指纹图像回调接口<br>
 fingerprint image call-back contact<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithFIPS.IdentificationCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.IdentificationCallBack</a></td>
<td class="colLast">
<div class="block">验证指纹的回调接口<br>
 call-back contact of verify fingerprint<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithFIPS.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.PtCaptureCallBack</a></td>
<td class="colLast">
<div class="block">获取指纹模版数据回调接口<br>
 Acquire call-back contact fingerprint template data<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithFIPS.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.TemplateVerifyCallBack</a></td>
<td class="colLast">
<div class="block">指纹模版比对<br>
 fingerprint template comparison<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a></td>
<td class="colLast">
<div class="block">采集指纹回调接口<br>
 acquire fingerprint call-back contact<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithMorpho.GrabCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.GrabCallBack</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithMorpho.IdentificationCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.IdentificationCallBack</a></td>
<td class="colLast">
<div class="block">验证指纹的回调接口<br>
 call-back contact for fingerprint verification<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithMorpho.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.PtCaptureCallBack</a></td>
<td class="colLast">
<div class="block">设置获取指纹模版回调接口<br>
 setup fingerprint template acquire call-back contact<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithMorpho.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.TemplateVerifyCallBack</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.IUPImageCallback.html" title="interface in com.rscja.deviceapi">FingerprintWithTLK1NC.IUPImageCallback</a></td>
<td class="colLast">
<div class="block">指纹图像回调接口<br>
 fingerprint image call-back contact<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/Printer.PrinterStatusCallBack.html" title="interface in com.rscja.deviceapi">Printer.PrinterStatusCallBack</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/Barcode1D.html" title="class in com.rscja.deviceapi">Barcode1D</a></td>
<td class="colLast">
<div class="block">一维条码操作类<br>1D barcode operation class<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/Barcode2D.html" title="class in com.rscja.deviceapi">Barcode2D</a></td>
<td class="colLast">
<div class="block">二维条码操作类 <br>
 2D barcode operation class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></td>
<td class="colLast">
<div class="block">BLE蓝牙的操作对象<br>

 1.通过<code>#connect(ConnectionStatusCallback<Object> bleStatusCallback)</code> 来连接蓝牙，bleStatusCallback是蓝牙状态的连接回调<br>
 2.通过<a href="../../../com/rscja/deviceapi/BleDevice.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>BleDevice.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a> 标签数据回调接口。<br>
 3.通过<a href="../../../com/rscja/deviceapi/BleDevice.html#startInventoryTag--"><code>BleDevice.startInventoryTag()</code></a> 开始盘点，在盘点过程中，ble设备只会响应<a href="../../../com/rscja/deviceapi/BleDevice.html#stopInventory--"><code>BleDevice.stopInventory()</code></a> ()}函数，不能设置和获取ble设备的相关其他参数。<br>
 4.通过<a href="../../../com/rscja/deviceapi/BleDevice.html#stopInventory--"><code>BleDevice.stopInventory()</code></a> ()} 停止盘点<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/BleDevice.BleDeviceInfo.html" title="class in com.rscja.deviceapi">BleDevice.BleDeviceInfo</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></td>
<td class="colLast">
<div class="block">蓝牙读写器操作类：<br>
 Bluetoot reader operation
 第一步：调用<a href="../../../com/rscja/deviceapi/BluetoothReader.html#init-android.content.Context-"><code>BluetoothReader.init(Context context)</code></a> 函数初始化蓝牙相关服务，退出应用程序需要调用<a href="../../../com/rscja/deviceapi/BluetoothReader.html#free--"><code>BluetoothReader.free()</code></a>是否蓝牙相关资源<br>
 First step: Call <a href="../../../com/rscja/deviceapi/BluetoothReader.html#init-android.content.Context-"><code>BluetoothReader.init(Context context)</code></a>to initiate BT service, call <a href="../../../com/rscja/deviceapi/BluetoothReader.html#free--"><code>BluetoothReader.free()</code></a> to exit application.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/CardWithBYL.html" title="class in com.rscja.deviceapi">CardWithBYL</a></td>
<td class="colLast">
<div class="block">白玉兰公交卡操作类</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></td>
<td class="colLast">
<div class="block">指纹识别模块操作类,<br>
 Fingerprint identify module operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintSM206B.html" title="class in com.rscja.deviceapi">FingerprintSM206B</a></td>
<td class="colLast">
<div class="block">SM206B 指纹模块操作接口<br/>

  第一步:调用init(Context context) 函数初始化指纹模块<br/>
  第二步:调用指纹相关接口,比如：search()，getDeviceVersion()...<br/>
  第三步:调用free()释放指纹模块相关资源<br/>
  示例代码:<br/>
 public void Test() {<br/>
 <br>&emsp;     Context context;
 <br>&emsp;FingerprintSM206B fingerprintSM206B= FingerprintSM206B.getInstance();
 <br>&emsp;boolean result= fingerprintSM206B.init(context);
 <br>&emsp;if(!result){
 <br>&emsp;&emsp;//init fail
 <br>&emsp;&emsp;return;
 <br>&emsp;}
 <br>&emsp; fingerprintSM206B.getImage();
 <br>&emsp; fingerprintSM206B.getFingerTemplate();
 <br>&emsp; fingerprintSM206B.getDeviceVersion();
 <br>&emsp; //...........</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></td>
<td class="colLast">
<div class="block">FIPS指纹识别模块操作类,<br>
 FIPS fingerprint indentify module operation type,<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithFIPS.FingerprintInfo.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS.FingerprintInfo</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithMorpho.html" title="class in com.rscja.deviceapi">FingerprintWithMorpho</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></td>
<td class="colLast">
<div class="block">迪安杰</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></td>
<td class="colLast">
<div class="block">指昂</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/Infrared.html" title="class in com.rscja.deviceapi">Infrared</a></td>
<td class="colLast">
<div class="block">红外模块操作类
 Infared module operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/LedLight.html" title="class in com.rscja.deviceapi">LedLight</a></td>
<td class="colLast">
<div class="block">手柄LED灯控制类<br>
 Handdeld LED control type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/Module.html" title="class in com.rscja.deviceapi">Module</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></td>
<td class="colLast">
<div class="block">打印机操作类<br>
 Printer operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/PSAM.html" title="class in com.rscja.deviceapi">PSAM</a></td>
<td class="colLast">
<div class="block">PSAM操作类<br>
 PSAM operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi">R1HFAndPsamManage</a></td>
<td class="colLast">
<div class="block">R1设备高频和PSAM操作类<br>
 R1 device HF and PSAM operation interfaces<br><br>

 第一步:通过<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#connect-android.content.Context-"><code>R1HFAndPsamManage.connect(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#connect-android.content.Context-"><code>R1HFAndPsamManage.connect(Context context)</code></a><br><br>

 第二步:获取相关接口对象<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443A--"><code>R1HFAndPsamManage.getHF14443A()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443B--"><code>R1HFAndPsamManage.getHF14443B()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF15693--"><code>R1HFAndPsamManage.getHF15693()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getPSAM--"><code>R1HFAndPsamManage.getPSAM()</code></a><br>
 Step 2:Gets the associated interface object <a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443A--"><code>R1HFAndPsamManage.getHF14443A()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443B--"><code>R1HFAndPsamManage.getHF14443B()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF15693--"><code>R1HFAndPsamManage.getHF15693()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getPSAM--"><code>R1HFAndPsamManage.getPSAM()</code></a>  <br><br>
 <br>
 <br>
 Example Usage:<br>

   public void HFDemo() {
 &emsp;       R1HFAndPsamManage rfid = R1HFAndPsamManage.getInstance();
 &emsp;   if (!rfid.connect(context)) {
 &emsp;&emsp;        //fail
 &emsp;&emsp;         return;
 &emsp;    }
 &emsp;    IHF14443A ihf14443A = rfid.getHF14443A();
 &emsp;   IHF14443B ihf14443B = rfid.getHF14443B();
 &emsp;   IHF15693 ihf15693 = rfid.getHF15693();
 &emsp;  IPSAM ipsam = rfid.getPSAM();

 &emsp;  HF14443RequestEntity entity = ihf14443A.requestTypeA();
 &emsp;   if (entity == null) {
 &emsp;&emsp;        //"Card not found!"
 &emsp;&emsp;        return;
 &emsp;   }
 &emsp;   byte cMode =  0x60:A  ;  0x61:B
 &emsp;   byte cBlock;
 &emsp;    byte[] txtKey;//6bytes
 &emsp;  boolean reuslt = ihf14443A.authentication(cMode, cBlock, txtKey);
 &emsp;  if (!reuslt) {
 &emsp;&emsp;       //The key validation fail
 &emsp;&emsp;       return;
 &emsp;    }
 &emsp;  //................</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDBase.html" title="class in com.rscja.deviceapi">RFIDBase</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A</a></td>
<td class="colLast">
<div class="block">RFID模块ISO14443A协议操作类<br>
 RFID module ISO 14443A protocol operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A4CPU.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A4CPU</a></td>
<td class="colLast">
<div class="block">RFID模块ISO14443A CPU卡协议操作类<br>
 RFID module ISO14443A CPU card protocol operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithISO14443B.html" title="class in com.rscja.deviceapi">RFIDWithISO14443B</a></td>
<td class="colLast">
<div class="block">RFID模块ISO14443B协议操作类<br>
 RFID module ISO14443B protocol operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi">RFIDWithISO15693</a></td>
<td class="colLast">
<div class="block">RFID模块ISO15693协议操作类,<br>
 RFID module ISO15639 protocol operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi">RFIDWithLF</a></td>
<td class="colLast">
<div class="block">RFID低频（125K）操作类<br>
 RFID LF (125K) operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></td>
<td class="colLast">
<div class="block">UHF模块 A4操作类<br>
 UHF module operation type<br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4.html#init-android.content.Context-"><code>RFIDWithUHFA4.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4.html#init-android.content.Context-"><code>RFIDWithUHFA4.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点标签先调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>RFIDWithUHFA4.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>设置标签回调接口，有标签数据就会回调这个接口。
        然后在调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4.html#startInventoryTag--"><code>RFIDWithUHFA4.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4.html#stopInventory--"><code>RFIDWithUHFA4.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></td>
<td class="colLast">
<div class="block">操作URA4设备以及UHF模块相关接口。(通过其他android设备控制A4)<br>
 Operate URA4 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFA4NetWork.setIPAndPort(String ip, int port)</code></a>设置要连接的IP地址, 再通过 <a href="../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#init-android.content.Context-"><code>RFIDWithUHFA4NetWork.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA4NetWork.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFA4NetWork.setIPAndPort(String ip, int port)</code></a>-> <a href="../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#init-android.content.Context-"><code>RFIDWithUHFA4NetWork.init(Context context)</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></td>
<td class="colLast">
<div class="block">操作URA4设备以及UHF模块相关接口。(通过其他android设备控制A4)<br>
 Operate URA4 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA4RS232.setUart(String path)</code></a>设置要连接的IP地址, 再通过 <a href="../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#init-android.content.Context-"><code>RFIDWithUHFA4RS232.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA4RS232.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA4RS232.setUart(String path)</code></a>-> <a href="../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#init-android.content.Context-"><code>RFIDWithUHFA4RS232.init(Context context)</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a></td>
<td class="colLast">
<div class="block">UHF模块 A8操作类<br>
 UHF module operation type<br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8.html#init-android.content.Context-"><code>RFIDWithUHFA8.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8.html#init-android.content.Context-"><code>RFIDWithUHFA8.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点标签先调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>RFIDWithUHFA8.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>设置标签回调接口，有标签数据就会回调这个接口。
        然后在调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8.html#startInventoryTag--"><code>RFIDWithUHFA8.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8.html#stopInventory--"><code>RFIDWithUHFA8.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></td>
<td class="colLast">
<div class="block">操作URA8设备以及UHF模块相关接口。(通过其他android设备控制A8)<br>
 Operate URA8 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFA8NetWork.setIPAndPort(String ip, int port)</code></a>设置要连接的IP地址, 再通过 <a href="../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#init-android.content.Context-"><code>RFIDWithUHFA8NetWork.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA8NetWork.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFA8NetWork.setIPAndPort(String ip, int port)</code></a>-> <a href="../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#init-android.content.Context-"><code>RFIDWithUHFA8NetWork.init(Context context)</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></td>
<td class="colLast">
<div class="block">操作URA8设备以及UHF模块相关接口。(通过其他android设备控制A8)<br>
 Operate URA8 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA8RS232.setUart(String path)</code></a>设置要连接的IP地址, 再通过 <a href="../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#init-android.content.Context-"><code>RFIDWithUHFA8RS232.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA8RS232.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA8RS232.setUart(String path)</code></a>-> <a href="../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#init-android.content.Context-"><code>RFIDWithUHFA8RS232.init(Context context)</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFAxBase.html" title="class in com.rscja.deviceapi">RFIDWithUHFAxBase</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></td>
<td class="colLast">
<div class="block">UHF模块低功耗蓝牙操作类<br>
 UHF module operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLEManage</a></td>
<td class="colLast">
<div class="block">BLE设备读写器管理类，支持一对多连接<br>
 具体操作步骤:<br>
 1.通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#getInstance--"><code>RFIDWithUHFBLEManage.getInstance()</code></a> 函数获取 RFIDWithUHFBLEManage对象<br>
 2.调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#addBleDevice-java.lang.String-android.content.Context-"><code>RFIDWithUHFBLEManage.addBleDevice(String address, Context context)</code></a>添加BLE读写器设备，每个蓝牙地址作为设备的唯一标识<br>
 3.通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#getBleDeviceByMac-java.lang.String-"><code>RFIDWithUHFBLEManage.getBleDeviceByMac(String address)</code></a> 函数获取每一台蓝牙设备的操作对象 <code>com.rscja.deviceapi.BleDevice</code>。<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi">RFIDWithUHFRLM</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></td>
<td class="colLast">
<div class="block">UHF模块手持机，串口通信操作类<br>
 UHF module handheld, serial communication operation interface <br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点标签先调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>RFIDWithUHFUART.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>设置标签回调接口，有标签数据就会回调这个接口。
        然后在调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#startInventoryTag--"><code>RFIDWithUHFUART.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#stopInventory--"><code>RFIDWithUHFUART.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></td>
<td class="colLast">
<div class="block">UHF模块URx网口通信操作类<br>
 URx network operation of UHF module <br>

 第一步：先通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFUrxNetwork.setIPAndPort(String ip, int port)</code></a>设置要连接的IP地址, 再通过 <a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#init-android.content.Context-"><code>RFIDWithUHFUrxNetwork.init(Context context)</code></a>连接读写器串口。
 Step 1: Connect to the serial port of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFUrxNetwork.setIPAndPort(String ip, int port)</code></a>-> <a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#init-android.content.Context-"><code>RFIDWithUHFUrxNetwork.init(Context context)</code></a>.<br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#startInventoryTag--"><code>RFIDWithUHFUrxNetwork.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#stopInventory--"><code>RFIDWithUHFUrxNetwork.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></td>
<td class="colLast">
<div class="block">URx 模块，串口通信操作类<br>
 URx module , serial communication operation interface <br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#init-android.content.Context-"><code>RFIDWithUHFUrxUart.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#init-android.content.Context-"><code>RFIDWithUHFUrxUart.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#startInventoryTag--"><code>RFIDWithUHFUrxUart.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#stopInventory--"><code>RFIDWithUHFUrxUart.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></td>
<td class="colLast">
<div class="block">URx 模块，串口通信操作类<br>
 URx module , serial communication operation interface <br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#init-android.content.Context-"><code>RFIDWithUHFUrxUsbToUart.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#init-android.content.Context-"><code>RFIDWithUHFUrxUsbToUart.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#startInventoryTag--"><code>RFIDWithUHFUrxUsbToUart.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#stopInventory--"><code>RFIDWithUHFUrxUsbToUart.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></td>
<td class="colLast">
<div class="block">UHF模块手持机，USB通信操作类<br>
 UHF module handheld, USB operation interface <br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#init-android.content.Context-"><code>RFIDWithUHFUSB.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#init-android.content.Context-"><code>RFIDWithUHFUSB.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#startInventoryTag--"><code>RFIDWithUHFUSB.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#stopInventory--"><code>RFIDWithUHFUSB.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/ScanerLedLight.html" title="class in com.rscja.deviceapi">ScanerLedLight</a></td>
<td class="colLast">
<div class="block">扫描LED灯控制类（仅C6000有效）<br>
 Scanning LED light control type ( valid for C6000 only)<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/UHFProtocolParseBase.html" title="class in com.rscja.deviceapi">UHFProtocolParseBase</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/UsbFingerprint.html" title="class in com.rscja.deviceapi">UsbFingerprint</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/VersionInfo.html" title="class in com.rscja.deviceapi">VersionInfo</a></td>
<td class="colLast">
<div class="block">jar包信息类<br>
 jar pack infor type<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a></td>
<td class="colLast">
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithFIPS.DataFormat.html" title="enum in com.rscja.deviceapi">FingerprintWithFIPS.DataFormat</a></td>
<td class="colLast">
<div class="block">指纹数据格式<br>
 Fingerprint data format<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a></td>
<td class="colLast">
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a></td>
<td class="colLast">
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/Printer.BarcodeType.html" title="enum in com.rscja.deviceapi">Printer.BarcodeType</a></td>
<td class="colLast">
<div class="block">1D条码类型</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/Printer.PrinterStatus.html" title="enum in com.rscja.deviceapi">Printer.PrinterStatus</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a></td>
<td class="colLast">
<div class="block">DESFire卡加密类型<br>
 DESFire card encyption type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireFileTypekEnum</a></td>
<td class="colLast">
<div class="block">DESFire卡文件类型<br>
 DESFire card file typr<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.KeyType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.KeyType</a></td>
<td class="colLast">
<div class="block">密钥类型，适用于S50和S70标签。<br>
 key type, used for S50 and S70 tags.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.TagType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.TagType</a></td>
<td class="colLast">
<div class="block">M1标签类型定义<br>
 M1 tag type definition<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../com/rscja/deviceapi/RFIDWithISO15693.TagType.html" title="enum in com.rscja.deviceapi">RFIDWithISO15693.TagType</a></td>
<td class="colLast">
<div class="block">标签类型<br>
 Tag type<br></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/custom/interfaces/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../com/rscja/deviceapi/entity/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
