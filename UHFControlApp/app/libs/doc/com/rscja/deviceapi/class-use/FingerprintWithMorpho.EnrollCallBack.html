<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Interface com.rscja.deviceapi.FingerprintWithMorpho.EnrollCallBack</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface com.rscja.deviceapi.FingerprintWithMorpho.EnrollCallBack";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../overview-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/class-use/FingerprintWithMorpho.EnrollCallBack.html" target="_top">Frames</a></li>
<li><a href="FingerprintWithMorpho.EnrollCallBack.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface com.rscja.deviceapi.FingerprintWithMorpho.EnrollCallBack" class="title">Uses of Interface<br>com.rscja.deviceapi.FingerprintWithMorpho.EnrollCallBack</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.interfaces">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.deviceapi">com.rscja.team.mtk.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a> in <a href="../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> with parameters of type <a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithMorpho.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.html#setPtEnrollCallBack-com.rscja.deviceapi.FingerprintWithMorpho.EnrollCallBack-">setPtEnrollCallBack</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a>&nbsp;callBack)</code>
<div class="block">设置采集指纹回调接口<br>
 setup fingerprint acquire call-back contact<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.interfaces">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a> in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> with parameters of type <a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">IFingerprintWithMorpho.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#setPtEnrollCallBack-com.rscja.deviceapi.FingerprintWithMorpho.EnrollCallBack-">setPtEnrollCallBack</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a>&nbsp;callBack)</code>
<div class="block">设置采集指纹回调接口<br>
 setup fingerprint acquire call-back contact<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a> in <a href="../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a> with parameters of type <a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithMorpho_mtk.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#setPtEnrollCallBack-com.rscja.deviceapi.FingerprintWithMorpho.EnrollCallBack-">setPtEnrollCallBack</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a>&nbsp;callBack)</code>
<div class="block">设置采集指纹回调接口<br>
 setup fingerprint acquire call-back contact<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a> in <a href="../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> with parameters of type <a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithMorpho_qcom.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html#setPtEnrollCallBack-com.rscja.deviceapi.FingerprintWithMorpho.EnrollCallBack-">setPtEnrollCallBack</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a>&nbsp;callBack)</code>
<div class="block">设置采集指纹回调接口<br>
 setup fingerprint acquire call-back contact<br></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../overview-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/class-use/FingerprintWithMorpho.EnrollCallBack.html" target="_top">Frames</a></li>
<li><a href="FingerprintWithMorpho.EnrollCallBack.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
