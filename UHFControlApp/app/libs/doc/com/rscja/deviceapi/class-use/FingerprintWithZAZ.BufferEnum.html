<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Class com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../overview-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/class-use/FingerprintWithZAZ.BufferEnum.html" target="_top">Frames</a></li>
<li><a href="FingerprintWithZAZ.BufferEnum.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum" class="title">Uses of Class<br>com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.interfaces">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a> in <a href="../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> that return <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a></code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ.BufferEnum.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ.BufferEnum.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> with parameters of type <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.html#downChar-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-byte:A-int:A-">downChar</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
        byte[]&nbsp;templateData,
        int[]&nbsp;outErrCode)</code>
<div class="block">下载指纹模板数据到模块指定的RamBuffer</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.html#generate-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">generate</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
        int[]&nbsp;outErrCode)</code>
<div class="block">生成模版</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.html#loadChar-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">loadChar</a></span>(int&nbsp;templateIdStart,
        <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
        int[]&nbsp;outErrCode)</code>
<div class="block">读取模块中的指纹并暂存在RamBuffer中</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.html#match-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">match</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId1,
     <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId2,
     int[]&nbsp;outErrCode)</code>
<div class="block">指定的两个 Ram Buffer 中的 Template 之间进行比对</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.html#merge-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int-int:A-">merge</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
     int&nbsp;number,
     int[]&nbsp;outErrCode)</code>
<div class="block">合并模版</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.html#search-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int-int-int:A-int:A-int:A-">search</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
      int&nbsp;templateIdStart,
      int&nbsp;templateIdEnd,
      int[]&nbsp;outTemplateID,
      int[]&nbsp;outUpStatus,
      int[]&nbsp;outErrCode)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.html#storeChar-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">storeChar</a></span>(int&nbsp;templateId,
         <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
         int[]&nbsp;outErrCode)</code>
<div class="block">将保存于指定 Ram Buffer 中的模板保存于指定编号的模块指纹库中</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.html#upChar-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">upChar</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
      int[]&nbsp;outErrCode)</code>
<div class="block">将暂存在RamBuffer中的指纹模板上传到主机</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.html#verify-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-int:A-int:A-">verify</a></span>(int&nbsp;templateId,
      <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;buffer,
      int[]&nbsp;outTemplateID,
      int[]&nbsp;outUpStatus,
      int[]&nbsp;outErrCode)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.interfaces">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a> in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> with parameters of type <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">IFingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#downChar-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-byte:A-int:A-">downChar</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
        byte[]&nbsp;templateData,
        int[]&nbsp;outErrCode)</code>
<div class="block">下载指纹模板数据到模块指定的RamBuffer</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">IFingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#generate-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">generate</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
        int[]&nbsp;outErrCode)</code>
<div class="block">生成模版</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">IFingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#loadChar-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">loadChar</a></span>(int&nbsp;templateIdStart,
        <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
        int[]&nbsp;outErrCode)</code>
<div class="block">读取模块中的指纹并暂存在RamBuffer中</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">IFingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#match-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">match</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId1,
     <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId2,
     int[]&nbsp;outErrCode)</code>
<div class="block">指定的两个 Ram Buffer 中的 Template 之间进行比对</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">IFingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#merge-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int-int:A-">merge</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
     int&nbsp;number,
     int[]&nbsp;outErrCode)</code>
<div class="block">合并模版</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">IFingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#search-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int-int-int:A-int:A-int:A-">search</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
      int&nbsp;templateIdStart,
      int&nbsp;templateIdEnd,
      int[]&nbsp;outTemplateID,
      int[]&nbsp;outUpStatus,
      int[]&nbsp;outErrCode)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">IFingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#storeChar-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">storeChar</a></span>(int&nbsp;templateId,
         <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
         int[]&nbsp;outErrCode)</code>
<div class="block">将保存于指定 Ram Buffer 中的模板保存于指定编号的模块指纹库中</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><span class="typeNameLabel">IFingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#upChar-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">upChar</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
      int[]&nbsp;outErrCode)</code>
<div class="block">将暂存在RamBuffer中的指纹模板上传到主机</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">IFingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#verify-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-int:A-int:A-">verify</a></span>(int&nbsp;templateId,
      <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;buffer,
      int[]&nbsp;outTemplateID,
      int[]&nbsp;outUpStatus,
      int[]&nbsp;outErrCode)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a> in <a href="../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> with parameters of type <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ_qcom.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#downChar-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-byte:A-int:A-">downChar</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
        byte[]&nbsp;templateData,
        int[]&nbsp;outErrCode)</code>
<div class="block">下载指纹模板数据到模块指定的RamBuffer</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ_qcom.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#generate-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">generate</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
        int[]&nbsp;outErrCode)</code>
<div class="block">生成模版</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ_qcom.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#loadChar-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">loadChar</a></span>(int&nbsp;templateIdStart,
        <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
        int[]&nbsp;outErrCode)</code>
<div class="block">读取模块中的指纹并暂存在RamBuffer中</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ_qcom.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#match-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">match</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId1,
     <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId2,
     int[]&nbsp;outErrCode)</code>
<div class="block">指定的两个 Ram Buffer 中的 Template 之间进行比对</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ_qcom.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#merge-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int-int:A-">merge</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
     int&nbsp;number,
     int[]&nbsp;outErrCode)</code>
<div class="block">合并模版</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ_qcom.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#search-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int-int-int:A-int:A-int:A-">search</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
      int&nbsp;templateIdStart,
      int&nbsp;templateIdEnd,
      int[]&nbsp;outTemplateID,
      int[]&nbsp;outUpStatus,
      int[]&nbsp;outErrCode)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ_qcom.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#storeChar-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">storeChar</a></span>(int&nbsp;templateId,
         <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
         int[]&nbsp;outErrCode)</code>
<div class="block">将保存于指定 Ram Buffer 中的模板保存于指定编号的模块指纹库中</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ_qcom.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#upChar-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">upChar</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
      int[]&nbsp;outErrCode)</code>
<div class="block">将暂存在RamBuffer中的指纹模板上传到主机</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ_qcom.</span><code><span class="memberNameLink"><a href="../../../../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#verify-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-int:A-int:A-">verify</a></span>(int&nbsp;templateId,
      <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;buffer,
      int[]&nbsp;outTemplateID,
      int[]&nbsp;outUpStatus,
      int[]&nbsp;outErrCode)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../overview-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/class-use/FingerprintWithZAZ.BufferEnum.html" target="_top">Frames</a></li>
<li><a href="FingerprintWithZAZ.BufferEnum.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
