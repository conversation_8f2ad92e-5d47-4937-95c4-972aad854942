<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>PSAM</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PSAM";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":9,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PSAM.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/Printer.PrinterStatusCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/PSAM.html" target="_top">Frames</a></li>
<li><a href="PSAM.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Class PSAM" class="title">Class PSAM</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.PSAM</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">PSAM</span>
extends java.lang.Object
implements <a href="../../../com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a></pre>
<div class="block">PSAM操作类<br>
 PSAM operation type<br>
 <p>
 注意： <br>
 Attention: <br>
 1、使用前请确认您的机器已安装此模块。 <br>
 1. Make sure this module is installed before using your device.<br>
 2、要正常使用模块需要在\libs\armeabi\目录放置libDeviceAPI.so文件 <br>
 2. Put libDeviceAPI.so file in directory \libs\armeabi\ then module can be used normally.<br>
 3、在操作设备前需要调用 <b><a href="../../../com/rscja/deviceapi/PSAM.html#init--"><code>init()</code></a></b> 打开设备，使用完后调用 <b> <a href="../../../com/rscja/deviceapi/PSAM.html#free--"><code>free()</code></a></b> 关闭设备
 3. Call <b><a href="../../../com/rscja/deviceapi/PSAM.html#init--"><code>init()</code></a></b> to switch on device before operating the device, call <b> <a href="../../../com/rscja/deviceapi/PSAM.html#free--"><code>free()</code></a></b> to switch off device after using.<br></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>liuruifeng</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/PSAM.html#executeCmd-java.lang.String-java.lang.String-">executeCmd</a></span>(java.lang.String&nbsp;hexCmd,
          java.lang.String&nbsp;hexData)</code>
<div class="block">执行PSAM命令<br>
 Execute PSAM command<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/PSAM.html#free--">free</a></span>()</code>
<div class="block">释放PSAM<br>
 Release PSAM<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/deviceapi/PSAM.html" title="class in com.rscja.deviceapi">PSAM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/PSAM.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取PSAM操作实例<br>
 Acquire PSAM operation Instance<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/PSAM.html#init--">init</a></span>()</code>
<div class="block">初始化PSAM<br>
 Initialize PSAM<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/PSAM.html#isPowerOn--">isPowerOn</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/PSAM.html#Upgrade-int-int-int-byte:A-">Upgrade</a></span>(int&nbsp;packageCount,
       int&nbsp;index,
       int&nbsp;currSize,
       byte[]&nbsp;data)</code>
<div class="block">固件升级<br>
 Firmware upgrade<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/deviceapi/PSAM.html" title="class in com.rscja.deviceapi">PSAM</a>&nbsp;getInstance()</pre>
<div class="block">获取PSAM操作实例<br>
 Acquire PSAM operation Instance<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>PSAM操作实例<br>
 PSAM operation Instance<br></dd>
</dl>
</li>
</ul>
<a name="init--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;boolean&nbsp;init()</pre>
<div class="block">初始化PSAM<br>
 Initialize PSAM<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPSAM.html#init--">init</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;boolean&nbsp;free()</pre>
<div class="block">释放PSAM<br>
 Release PSAM<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPSAM.html#free--">free</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="executeCmd-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executeCmd</h4>
<pre>public&nbsp;java.lang.String&nbsp;executeCmd(java.lang.String&nbsp;hexCmd,
                                   java.lang.String&nbsp;hexData)</pre>
<div class="block">执行PSAM命令<br>
 Execute PSAM command<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPSAM.html#executeCmd-java.lang.String-java.lang.String-">executeCmd</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>hexCmd</code> - 十六进制命令code<br>
                nexdecimal command code<br></dd>
<dd><code>hexData</code> - 十六进制命令内容<br>
                hexdecimal command content<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>command return value<br></dd>
</dl>
</li>
</ul>
<a name="Upgrade-int-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Upgrade</h4>
<pre>public&nbsp;boolean&nbsp;Upgrade(int&nbsp;packageCount,
                       int&nbsp;index,
                       int&nbsp;currSize,
                       byte[]&nbsp;data)</pre>
<div class="block">固件升级<br>
 Firmware upgrade<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPSAM.html#Upgrade-int-int-int-byte:A-">Upgrade</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>packageCount</code> - 包的数量<br>
                     package amount<br></dd>
<dd><code>index</code> - 当前为第几个包<br>
                     current package<br></dd>
<dd><code>currSize</code> - 当前包大小<br>
                     current package size<br></dd>
<dd><code>data</code> - 包数据<br>
                     package data<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>public&nbsp;boolean&nbsp;isPowerOn()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPSAM.html#isPowerOn--">isPowerOn</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PSAM.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/Printer.PrinterStatusCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/PSAM.html" target="_top">Frames</a></li>
<li><a href="PSAM.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
