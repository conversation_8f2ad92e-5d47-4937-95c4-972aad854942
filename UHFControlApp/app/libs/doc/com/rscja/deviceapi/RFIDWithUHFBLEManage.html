<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>RFIDWithUHFBLEManage</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RFIDWithUHFBLEManage";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":9,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithUHFBLEManage.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/RFIDWithUHFBLEManage.html" target="_top">Frames</a></li>
<li><a href="RFIDWithUHFBLEManage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Class RFIDWithUHFBLEManage" class="title">Class RFIDWithUHFBLEManage</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.RFIDWithUHFBLEManage</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">RFIDWithUHFBLEManage</span>
extends java.lang.Object</pre>
<div class="block">BLE设备读写器管理类，支持一对多连接<br>
 具体操作步骤:<br>
 1.通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#getInstance--"><code>getInstance()</code></a> 函数获取 RFIDWithUHFBLEManage对象<br>
 2.调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#addBleDevice-java.lang.String-android.content.Context-"><code>addBleDevice(String address, Context context)</code></a>添加BLE读写器设备，每个蓝牙地址作为设备的唯一标识<br>
 3.通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#getBleDeviceByMac-java.lang.String-"><code>getBleDeviceByMac(String address)</code></a> 函数获取每一台蓝牙设备的操作对象 <code>com.rscja.deviceapi.BleDevice</code>。<br></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#RFIDWithUHFBLEManage--">RFIDWithUHFBLEManage</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#addBleDevice-java.lang.String-android.content.Context-">addBleDevice</a></span>(java.lang.String&nbsp;mac,
            android.content.Context&nbsp;context)</code>
<div class="block">增加蓝牙设备</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#connectAll--">connectAll</a></span>()</code>
<div class="block">连接所有蓝牙</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#disConnectAll--">disConnectAll</a></span>()</code>
<div class="block">断开所有蓝牙</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#getAllBleDevice--">getAllBleDevice</a></span>()</code>
<div class="block">获取所有的ble设备</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#getBleDeviceByMac-java.lang.String-">getBleDeviceByMac</a></span>(java.lang.String&nbsp;address)</code>
<div class="block">通过蓝牙地址获取蓝牙设备</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLEManage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#removeBleDevice-java.lang.String-">removeBleDevice</a></span>(java.lang.String&nbsp;mac)</code>
<div class="block">移除蓝牙设备</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-android.content.Context-">startScanBTDevices</a></span>(<a href="../../../com/rscja/deviceapi/interfaces/ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces">ScanBTCallback</a>&nbsp;scanBTCallback,
                  android.content.Context&nbsp;context)</code>
<div class="block">搜索蓝牙</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#stopScanBTDevices--">stopScanBTDevices</a></span>()</code>
<div class="block">停止搜索蓝牙</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="RFIDWithUHFBLEManage--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RFIDWithUHFBLEManage</h4>
<pre>public&nbsp;RFIDWithUHFBLEManage()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLEManage</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startScanBTDevices</h4>
<pre>public&nbsp;void&nbsp;startScanBTDevices(<a href="../../../com/rscja/deviceapi/interfaces/ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces">ScanBTCallback</a>&nbsp;scanBTCallback,
                               android.content.Context&nbsp;context)</pre>
<div class="block">搜索蓝牙</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scanBTCallback</code> - </dd>
</dl>
</li>
</ul>
<a name="stopScanBTDevices--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopScanBTDevices</h4>
<pre>public&nbsp;void&nbsp;stopScanBTDevices()</pre>
<div class="block">停止搜索蓝牙</div>
</li>
</ul>
<a name="getBleDeviceByMac-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBleDeviceByMac</h4>
<pre>public&nbsp;<a href="../../../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a>&nbsp;getBleDeviceByMac(java.lang.String&nbsp;address)</pre>
<div class="block">通过蓝牙地址获取蓝牙设备</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>address</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="addBleDevice-java.lang.String-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addBleDevice</h4>
<pre>public&nbsp;boolean&nbsp;addBleDevice(java.lang.String&nbsp;mac,
                            android.content.Context&nbsp;context)</pre>
<div class="block">增加蓝牙设备</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mac</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="removeBleDevice-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeBleDevice</h4>
<pre>public&nbsp;boolean&nbsp;removeBleDevice(java.lang.String&nbsp;mac)</pre>
<div class="block">移除蓝牙设备</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mac</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getAllBleDevice--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllBleDevice</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a>&gt;&nbsp;getAllBleDevice()</pre>
<div class="block">获取所有的ble设备</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="connectAll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>connectAll</h4>
<pre>public&nbsp;void&nbsp;connectAll()</pre>
<div class="block">连接所有蓝牙</div>
</li>
</ul>
<a name="disConnectAll--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>disConnectAll</h4>
<pre>public&nbsp;void&nbsp;disConnectAll()</pre>
<div class="block">断开所有蓝牙</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithUHFBLEManage.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/RFIDWithUHFBLEManage.html" target="_top">Frames</a></li>
<li><a href="RFIDWithUHFBLEManage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
