<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Class com.rscja.deviceapi.enums.AntennaEnum</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.rscja.deviceapi.enums.AntennaEnum";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/deviceapi/enums/class-use/AntennaEnum.html" target="_top">Frames</a></li>
<li><a href="AntennaEnum.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.rscja.deviceapi.enums.AntennaEnum" class="title">Uses of Class<br>com.rscja.deviceapi.enums.AntennaEnum</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.entity">com.rscja.deviceapi.entity</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.enums">com.rscja.deviceapi.enums</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.interfaces">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a> in <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA8NetWork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUart.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUsbToUart.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxNetwork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA4NetWork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFAxBase.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFAxBase.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA4RS232.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA8RS232.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA8NetWork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>
<div class="block">设置天线功率 (Set the antenna power)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUart.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUsbToUart.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxNetwork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA4NetWork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>
<div class="block">设置天线功率 (Set the antenna power)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFAxBase.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFAxBase.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA4RS232.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>
<div class="block">设置天线功率 (Set the antenna power)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA8RS232.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>
<div class="block">设置天线功率 (Set the antenna power)</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.entity">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a> in <a href="../../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> that return <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></code></td>
<td class="colLast"><span class="typeNameLabel">AntennaPowerEntity.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/entity/AntennaPowerEntity.html#getAnt--">getAnt</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></code></td>
<td class="colLast"><span class="typeNameLabel">AntennaState.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/entity/AntennaState.html#getAntennaName--">getAntennaName</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></code></td>
<td class="colLast"><span class="typeNameLabel">AntennaConnectState.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/entity/AntennaConnectState.html#getAntennaName--">getAntennaName</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AntennaPowerEntity.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/entity/AntennaPowerEntity.html#setAnt-com.rscja.deviceapi.enums.AntennaEnum-">setAnt</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/entity/AntennaConnectState.html#AntennaConnectState-com.rscja.deviceapi.enums.AntennaEnum-boolean-">AntennaConnectState</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;antName,
                   boolean&nbsp;isConnected)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/entity/AntennaState.html#AntennaState-com.rscja.deviceapi.enums.AntennaEnum-boolean-">AntennaState</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;antName,
            boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.enums">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a> in <a href="../../../../../com/rscja/deviceapi/enums/package-summary.html">com.rscja.deviceapi.enums</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/enums/package-summary.html">com.rscja.deviceapi.enums</a> that return <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></code></td>
<td class="colLast"><span class="typeNameLabel">AntennaEnum.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html#getValue-int-">getValue</a></span>(int&nbsp;value)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></code></td>
<td class="colLast"><span class="typeNameLabel">AntennaEnum.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">AntennaEnum.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.interfaces">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a> in <a href="../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">IMultipleAntenna.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>
<div class="block">获取单个天线的功率</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">IMultipleAntenna.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>
<div class="block">设置单个天线的功率</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a> in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUsbToUart_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUart_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUart2_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxNetwork_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFAxBase_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUsbToUart_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUart_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUart2_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxNetwork_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFAxBase_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/deviceapi/enums/class-use/AntennaEnum.html" target="_top">Frames</a></li>
<li><a href="AntennaEnum.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
