<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>RFIDWithISO14443A.DESFireEncryptionTypekEnum</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RFIDWithISO14443A.DESFireEncryptionTypekEnum";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" target="_top">Frames</a></li>
<li><a href="RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Enum RFIDWithISO14443A.DESFireEncryptionTypekEnum" class="title">Enum RFIDWithISO14443A.DESFireEncryptionTypekEnum</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Enum&lt;<a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.RFIDWithISO14443A.DESFireEncryptionTypekEnum</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable, java.lang.Comparable&lt;<a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a>&gt;</dd>
</dl>
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A</a></dd>
</dl>
<hr>
<br>
<pre>public static enum <span class="typeNameLabel">RFIDWithISO14443A.DESFireEncryptionTypekEnum</span>
extends java.lang.Enum&lt;<a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a>&gt;</pre>
<div class="block">DESFire卡加密类型<br>
 DESFire card encyption type<br></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>liuruifeng</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html#DES">DES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html#Transparent">Transparent</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html#Unknown">Unknown</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html#getValue--">getValue</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Enum</h3>
<code>compareTo, equals, getDeclaringClass, hashCode, name, ordinal, toString, valueOf</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="Unknown">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Unknown</h4>
<pre>public static final&nbsp;<a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a> Unknown</pre>
</li>
</ul>
<a name="Transparent">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Transparent</h4>
<pre>public static final&nbsp;<a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a> Transparent</pre>
</li>
</ul>
<a name="DES">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DES</h4>
<pre>public static final&nbsp;<a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a> DES</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (RFIDWithISO14443A.DESFireEncryptionTypekEnum c : RFIDWithISO14443A.DESFireEncryptionTypekEnum.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a>&nbsp;valueOf(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if this enum type has no constant with the specified name</dd>
<dd><code>java.lang.NullPointerException</code> - if the argument is null</dd>
</dl>
</li>
</ul>
<a name="getValue--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;byte&nbsp;getValue()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" target="_top">Frames</a></li>
<li><a href="RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
