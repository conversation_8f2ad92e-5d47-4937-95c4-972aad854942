<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>BluetoothReader</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BluetoothReader";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":9,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BluetoothReader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/BleDevice.BleDeviceInfo.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/BluetoothReader.DecodeCallback.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/BluetoothReader.html" target="_top">Frames</a></li>
<li><a href="BluetoothReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Class BluetoothReader" class="title">Class BluetoothReader</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.BluetoothReader</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">BluetoothReader</span>
extends java.lang.Object
implements <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></pre>
<div class="block">蓝牙读写器操作类：<br>
 Bluetoot reader operation
 第一步：调用<a href="../../../com/rscja/deviceapi/BluetoothReader.html#init-android.content.Context-"><code>init(Context context)</code></a> 函数初始化蓝牙相关服务，退出应用程序需要调用<a href="../../../com/rscja/deviceapi/BluetoothReader.html#free--"><code>free()</code></a>是否蓝牙相关资源<br>
 First step: Call <a href="../../../com/rscja/deviceapi/BluetoothReader.html#init-android.content.Context-"><code>init(Context context)</code></a>to initiate BT service, call <a href="../../../com/rscja/deviceapi/BluetoothReader.html#free--"><code>free()</code></a> to exit application.
 第二步：调用<a href="../../../com/rscja/deviceapi/BluetoothReader.html#connect-java.lang.String-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>connect(java.lang.String, com.rscja.deviceapi.interfaces.ConnectionStatusCallback&lt;java.lang.Object&gt;)</code></a>连接蓝牙，在此之前你可以调用<a href="../../../com/rscja/deviceapi/BluetoothReader.html#startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-"><code>startScanBTDevices(com.rscja.deviceapi.interfaces.ScanBTCallback)</code></a>搜索附件的符合要求的蓝牙设备<br>
 Second step: call <a href="../../../com/rscja/deviceapi/BluetoothReader.html#connect-java.lang.String-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>connect(java.lang.String, com.rscja.deviceapi.interfaces.ConnectionStatusCallback&lt;java.lang.Object&gt;)</code></a> to connect BT, user could call <a href="../../../com/rscja/deviceapi/BluetoothReader.html#startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-"><code>startScanBTDevices(com.rscja.deviceapi.interfaces.ScanBTCallback)</code></a> to search BT device nearby before connecting.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.DecodeCallback.html" title="interface in com.rscja.deviceapi">BluetoothReader.DecodeCallback</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.OnDataChangeListener.html" title="interface in com.rscja.deviceapi">BluetoothReader.OnDataChangeListener</a></span></code>
<div class="block">接收蓝牙原始数据的接口 <br>
 Interface of receiving initial data.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IBluetoothReader">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></h3>
<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_FIRMWARE">VERSION_BT_FIRMWARE</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_HARDWARE">VERSION_BT_HARDWARE</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_SOFTWARE">VERSION_BT_SOFTWARE</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IReader">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></h3>
<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#UPDATE_STM32">UPDATE_STM32</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#blinkOfLed-int-int-int-">blinkOfLed</a></span>(int&nbsp;duration,
          int&nbsp;interval,
          int&nbsp;count)</code>
<div class="block">led 闪烁</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#closeLed--">closeLed</a></span>()</code>
<div class="block">关闭led</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#connect-java.lang.String-">connect</a></span>(java.lang.String&nbsp;address)</code>
<div class="block">连接蓝牙(Connect Bluetooth)</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#connect-java.lang.String-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">connect</a></span>(java.lang.String&nbsp;address,
       <a href="../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;java.lang.Object&gt;&nbsp;btStatusCallback)</code>
<div class="block">连接蓝牙(Connect Bluetooth)</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#disconnect--">disconnect</a></span>()</code>
<div class="block">断开蓝牙(Disconnect Bluetooth)</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#free--">free</a></span>()</code>
<div class="block">释放蓝牙相关的资源(free Bluetooth resources)</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#getBattery--">getBattery</a></span>()</code>
<div class="block">获取电池电量<br>
 Acquire battery capacity</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#getBeep--">getBeep</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#getBleHardwareVersion--">getBleHardwareVersion</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.util.HashMap&lt;java.lang.String,java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#getBluetoothVersion--">getBluetoothVersion</a></span>()</code>
<div class="block">获取蓝牙版本号(acquire Bluetooth version)</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#getConnectStatus--">getConnectStatus</a></span>()</code>
<div class="block">获取蓝牙连接状态(Acquire Bluetooth connection status)</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#getInstance--">getInstance</a></span>()</code>
<div class="block">Get BluetoothReader</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#getParameter-byte:A-">getParameter</a></span>(byte[]&nbsp;ssiParameter)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#getSTM32Version--">getSTM32Version</a></span>()</code>
<div class="block">获取stm32版本号(Acquire stm32 version )</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#init-android.content.Context-">init</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">初始化蓝牙相关的服务(Intialize Bluetooth services)</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#openLed--">openLed</a></span>()</code>
<div class="block">打开led</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#scanBarcode--">scanBarcode</a></span>()</code>
<div class="block">扫描条码<br>
 Scan barcode</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#scanBarcodeToBytes--">scanBarcodeToBytes</a></span>()</code>
<div class="block">扫描条码<br>
 Scan barcode</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#sendData-byte:A-">sendData</a></span>(byte[]&nbsp;sendData)</code>
<div class="block">发送指令(send data)</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#setBarcodeTypeInSSIID-boolean-">setBarcodeTypeInSSIID</a></span>(boolean&nbsp;isContainBarcodeType)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#setBeep-boolean-">setBeep</a></span>(boolean&nbsp;isOpen)</code>
<div class="block">设置蜂鸣器开关<br>
 Setup buzzer ON/OFF</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a></span>(<a href="../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;java.lang.Object&gt;&nbsp;btStatusCallback)</code>
<div class="block">设置蓝牙连接状态的回调(Setup Bluetooth connection status call back)</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#setKeyEventCallback-com.rscja.deviceapi.interfaces.KeyEventCallback-">setKeyEventCallback</a></span>(<a href="../../../com/rscja/deviceapi/interfaces/KeyEventCallback.html" title="interface in com.rscja.deviceapi.interfaces">KeyEventCallback</a>&nbsp;KeyEventCallback)</code>
<div class="block">设置扫描按键回调<br>
 Setup SCAN button call back</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#setOnDataChangeListener-com.rscja.deviceapi.BluetoothReader.OnDataChangeListener-">setOnDataChangeListener</a></span>(<a href="../../../com/rscja/deviceapi/BluetoothReader.OnDataChangeListener.html" title="interface in com.rscja.deviceapi">BluetoothReader.OnDataChangeListener</a>&nbsp;onDataChangeListener)</code>
<div class="block">设置接收蓝牙原始数据的回调函数<br>
 setup call back function for receiving BT initial data.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#setParameter-byte:A-byte:A-">setParameter</a></span>(byte[]&nbsp;ssiParameter,
            byte[]&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#setRemoteBluetoothName-java.lang.String-">setRemoteBluetoothName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">修改蓝牙名称(Change Bluetooth device Name)</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#startScanBarcode--">startScanBarcode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-">startScanBTDevices</a></span>(<a href="../../../com/rscja/deviceapi/interfaces/ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces">ScanBTCallback</a>&nbsp;scanBTCallback)</code>
<div class="block">扫描蓝牙设备(scanning Bluetooth devices)</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#stopScanBarcode--">stopScanBarcode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#stopScanBTDevices--">stopScanBTDevices</a></span>()</code>
<div class="block">停止扫描蓝牙设备(Stop scanning Bluetooth devices)</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BluetoothReader.html#triggerBeep-int-">triggerBeep</a></span>(int&nbsp;durationTime)</code>
<div class="block">触发蜂鸣器(Trigger the buzzer)</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a>&nbsp;getInstance()</pre>
<div class="block">Get BluetoothReader</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>return BluetoothReader</dd>
</dl>
</li>
</ul>
<a name="init-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;boolean&nbsp;init(android.content.Context&nbsp;context)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#init-android.content.Context-">IBluetoothReader</a></code></span></div>
<div class="block">初始化蓝牙相关的服务(Intialize Bluetooth services)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#init-android.content.Context-">init</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - context</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;boolean&nbsp;free()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#free--">IBluetoothReader</a></code></span></div>
<div class="block">释放蓝牙相关的资源(free Bluetooth resources)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#free--">free</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startScanBTDevices</h4>
<pre>public&nbsp;void&nbsp;startScanBTDevices(<a href="../../../com/rscja/deviceapi/interfaces/ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces">ScanBTCallback</a>&nbsp;scanBTCallback)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-">IBluetoothReader</a></code></span></div>
<div class="block">扫描蓝牙设备(scanning Bluetooth devices)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-">startScanBTDevices</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scanBTCallback</code> - 扫描结果回调(scanning result call back)</dd>
</dl>
</li>
</ul>
<a name="stopScanBTDevices--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopScanBTDevices</h4>
<pre>public&nbsp;void&nbsp;stopScanBTDevices()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#stopScanBTDevices--">IBluetoothReader</a></code></span></div>
<div class="block">停止扫描蓝牙设备(Stop scanning Bluetooth devices)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#stopScanBTDevices--">stopScanBTDevices</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
</dl>
</li>
</ul>
<a name="setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConnectionStatusCallback</h4>
<pre>public&nbsp;void&nbsp;setConnectionStatusCallback(<a href="../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;java.lang.Object&gt;&nbsp;btStatusCallback)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">IBluetoothReader</a></code></span></div>
<div class="block">设置蓝牙连接状态的回调(Setup Bluetooth connection status call back)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>btStatusCallback</code> - 蓝牙连接状态回调接口(Bluetooth connection status call back)</dd>
</dl>
</li>
</ul>
<a name="connect-java.lang.String-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>connect</h4>
<pre>public&nbsp;void&nbsp;connect(java.lang.String&nbsp;address,
                    <a href="../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;java.lang.Object&gt;&nbsp;btStatusCallback)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#connect-java.lang.String-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">IBluetoothReader</a></code></span></div>
<div class="block">连接蓝牙(Connect Bluetooth)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#connect-java.lang.String-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">connect</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>address</code> - 蓝牙地址(Bluetooth address )</dd>
<dd><code>btStatusCallback</code> - 蓝牙连接状态回调接口(Bluetooth connection status call back)</dd>
</dl>
</li>
</ul>
<a name="setKeyEventCallback-com.rscja.deviceapi.interfaces.KeyEventCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setKeyEventCallback</h4>
<pre>public&nbsp;void&nbsp;setKeyEventCallback(<a href="../../../com/rscja/deviceapi/interfaces/KeyEventCallback.html" title="interface in com.rscja.deviceapi.interfaces">KeyEventCallback</a>&nbsp;KeyEventCallback)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#setKeyEventCallback-com.rscja.deviceapi.interfaces.KeyEventCallback-">IReader</a></code></span></div>
<div class="block">设置扫描按键回调<br>
 Setup SCAN button call back</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#setKeyEventCallback-com.rscja.deviceapi.interfaces.KeyEventCallback-">setKeyEventCallback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>KeyEventCallback</code> - 按键回调接口(Keyboard call back)</dd>
</dl>
</li>
</ul>
<a name="openLed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openLed</h4>
<pre>public&nbsp;boolean&nbsp;openLed()</pre>
<div class="block">打开led</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#openLed--">openLed</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success  false:false</dd>
</dl>
</li>
</ul>
<a name="closeLed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeLed</h4>
<pre>public&nbsp;boolean&nbsp;closeLed()</pre>
<div class="block">关闭led</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#closeLed--">closeLed</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success  false:false</dd>
</dl>
</li>
</ul>
<a name="blinkOfLed-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blinkOfLed</h4>
<pre>public&nbsp;boolean&nbsp;blinkOfLed(int&nbsp;duration,
                          int&nbsp;interval,
                          int&nbsp;count)</pre>
<div class="block">led 闪烁</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#blinkOfLed-int-int-int-">blinkOfLed</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - 持续发光时间   范围为1-255，单位为100ms( Duration of luminescence , value:1-255 , unit:100ms )</dd>
<dd><code>interval</code> - 间隔时间   范围为1-255，单位为100ms  (interval time, value:1-255,  unit:100ms)</dd>
<dd><code>count</code> - 闪烁次数  1-255 (count, value:1-255)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success  false:false</dd>
</dl>
</li>
</ul>
<a name="disconnect--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disconnect</h4>
<pre>public&nbsp;void&nbsp;disconnect()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#disconnect--">IBluetoothReader</a></code></span></div>
<div class="block">断开蓝牙(Disconnect Bluetooth)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#disconnect--">disconnect</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
</dl>
</li>
</ul>
<a name="getConnectStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConnectStatus</h4>
<pre>public&nbsp;<a href="../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;getConnectStatus()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getConnectStatus--">IBluetoothReader</a></code></span></div>
<div class="block">获取蓝牙连接状态(Acquire Bluetooth connection status)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getConnectStatus--">getConnectStatus</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回蓝牙连接状态(Return Bluetooth connection status)</dd>
</dl>
</li>
</ul>
<a name="getBattery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBattery</h4>
<pre>public&nbsp;int&nbsp;getBattery()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#getBattery--">IReader</a></code></span></div>
<div class="block">获取电池电量<br>
 Acquire battery capacity</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#getBattery--">getBattery</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回电量，-1表示获取电量失败(return battery capacity, -1 means acquire failure.)</dd>
</dl>
</li>
</ul>
<a name="setBeep-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBeep</h4>
<pre>public&nbsp;boolean&nbsp;setBeep(boolean&nbsp;isOpen)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#setBeep-boolean-">IReader</a></code></span></div>
<div class="block">设置蜂鸣器开关<br>
 Setup buzzer ON/OFF</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#setBeep-boolean-">setBeep</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>isOpen</code> - true:打开蜂鸣器(ON)， false:关闭蜂鸣器(OFF)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success)   false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="getBeep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBeep</h4>
<pre>public&nbsp;int&nbsp;getBeep()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBeep--">getBeep</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
</dl>
</li>
</ul>
<a name="triggerBeep-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>triggerBeep</h4>
<pre>public&nbsp;boolean&nbsp;triggerBeep(int&nbsp;durationTime)</pre>
<div class="block">触发蜂鸣器(Trigger the buzzer)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#triggerBeep-int-">triggerBeep</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>durationTime</code> - 持续时间, 范围1-255，单位为100ms (Duration, range 1-255,   Unit:100ms)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true success,false false</dd>
</dl>
</li>
</ul>
<a name="scanBarcode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scanBarcode</h4>
<pre>public&nbsp;java.lang.String&nbsp;scanBarcode()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#scanBarcode--">IReader</a></code></span></div>
<div class="block">扫描条码<br>
 Scan barcode</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#scanBarcode--">scanBarcode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回条码字符串数据(return barcode string data)</dd>
</dl>
</li>
</ul>
<a name="scanBarcodeToBytes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scanBarcodeToBytes</h4>
<pre>public&nbsp;byte[]&nbsp;scanBarcodeToBytes()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#scanBarcodeToBytes--">IReader</a></code></span></div>
<div class="block">扫描条码<br>
 Scan barcode</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#scanBarcodeToBytes--">scanBarcodeToBytes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回条码的原始byte数据(return initial byte data of barcode)</dd>
</dl>
</li>
</ul>
<a name="stopScanBarcode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopScanBarcode</h4>
<pre>public&nbsp;boolean&nbsp;stopScanBarcode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#stopScanBarcode--">stopScanBarcode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setBarcodeTypeInSSIID-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeTypeInSSIID</h4>
<pre>public&nbsp;boolean&nbsp;setBarcodeTypeInSSIID(boolean&nbsp;isContainBarcodeType)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#setBarcodeTypeInSSIID-boolean-">setBarcodeTypeInSSIID</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
</dl>
</li>
</ul>
<a name="getBarcodeTypeInSSIID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarcodeTypeInSSIID</h4>
<pre>public&nbsp;int&nbsp;getBarcodeTypeInSSIID()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
</dl>
</li>
</ul>
<a name="startScanBarcode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startScanBarcode</h4>
<pre>public&nbsp;<a href="../../../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a>&nbsp;startScanBarcode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#startScanBarcode--">startScanBarcode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
</dl>
</li>
</ul>
<a name="setParameter-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParameter</h4>
<pre>public&nbsp;boolean&nbsp;setParameter(byte[]&nbsp;ssiParameter,
                            byte[]&nbsp;value)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#setParameter-byte:A-byte:A-">setParameter</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
</dl>
</li>
</ul>
<a name="getParameter-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParameter</h4>
<pre>public&nbsp;byte[]&nbsp;getParameter(byte[]&nbsp;ssiParameter)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#getParameter-byte:A-">getParameter</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
</dl>
</li>
</ul>
<a name="setOnDataChangeListener-com.rscja.deviceapi.BluetoothReader.OnDataChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnDataChangeListener</h4>
<pre>public&nbsp;void&nbsp;setOnDataChangeListener(<a href="../../../com/rscja/deviceapi/BluetoothReader.OnDataChangeListener.html" title="interface in com.rscja.deviceapi">BluetoothReader.OnDataChangeListener</a>&nbsp;onDataChangeListener)</pre>
<div class="block">设置接收蓝牙原始数据的回调函数<br>
 setup call back function for receiving BT initial data.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#setOnDataChangeListener-com.rscja.deviceapi.BluetoothReader.OnDataChangeListener-">setOnDataChangeListener</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>onDataChangeListener</code> - OnDataChangeListener</dd>
</dl>
</li>
</ul>
<a name="sendData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sendData</h4>
<pre>public&nbsp;boolean&nbsp;sendData(byte[]&nbsp;sendData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#sendData-byte:A-">IBluetoothReader</a></code></span></div>
<div class="block">发送指令(send data)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#sendData-byte:A-">sendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="setRemoteBluetoothName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemoteBluetoothName</h4>
<pre>public&nbsp;boolean&nbsp;setRemoteBluetoothName(java.lang.String&nbsp;name)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#setRemoteBluetoothName-java.lang.String-">IBluetoothReader</a></code></span></div>
<div class="block">修改蓝牙名称(Change Bluetooth device Name)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#setRemoteBluetoothName-java.lang.String-">setRemoteBluetoothName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - 新的蓝牙名称(New Bluetooth name)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="getSTM32Version--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSTM32Version</h4>
<pre>public&nbsp;java.lang.String&nbsp;getSTM32Version()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#getSTM32Version--">IReader</a></code></span></div>
<div class="block">获取stm32版本号(Acquire stm32 version )</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#getSTM32Version--">getSTM32Version</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回stm32版本 (return stm32 version)</dd>
</dl>
</li>
</ul>
<a name="getBluetoothVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBluetoothVersion</h4>
<pre>public&nbsp;java.util.HashMap&lt;java.lang.String,java.lang.String&gt;&nbsp;getBluetoothVersion()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBluetoothVersion--">IBluetoothReader</a></code></span></div>
<div class="block">获取蓝牙版本号(acquire Bluetooth version)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBluetoothVersion--">getBluetoothVersion</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>key:<a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_FIRMWARE"><code>IBluetoothReader.VERSION_BT_FIRMWARE</code></a>  表示固件版本(Firmware Version), key:<a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_HARDWARE"><code>IBluetoothReader.VERSION_BT_HARDWARE</code></a>
 表示硬件版本(Hardware version) ， key:<a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_SOFTWARE"><code>IBluetoothReader.VERSION_BT_SOFTWARE</code></a>  表示软件版本(software version)</dd>
</dl>
</li>
</ul>
<a name="getBleHardwareVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBleHardwareVersion</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBleHardwareVersion()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBleHardwareVersion--">getBleHardwareVersion</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
</dl>
</li>
</ul>
<a name="connect-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>connect</h4>
<pre>public&nbsp;void&nbsp;connect(java.lang.String&nbsp;address)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#connect-java.lang.String-">IBluetoothReader</a></code></span></div>
<div class="block">连接蓝牙(Connect Bluetooth)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#connect-java.lang.String-">connect</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>address</code> - 蓝牙地址(Bluetooth address)</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BluetoothReader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/BleDevice.BleDeviceInfo.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/BluetoothReader.DecodeCallback.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/BluetoothReader.html" target="_top">Frames</a></li>
<li><a href="BluetoothReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
