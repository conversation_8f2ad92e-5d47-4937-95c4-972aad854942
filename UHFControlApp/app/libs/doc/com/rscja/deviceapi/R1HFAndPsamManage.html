<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>R1HFAndPsamManage</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="R1HFAndPsamManage";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":9,"i8":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/R1HFAndPsamManage.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/PSAM.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/RFIDBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/R1HFAndPsamManage.html" target="_top">Frames</a></li>
<li><a href="R1HFAndPsamManage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Class R1HFAndPsamManage" class="title">Class R1HFAndPsamManage</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.R1HFAndPsamManage</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">R1HFAndPsamManage</span>
extends java.lang.Object</pre>
<div class="block">R1设备高频和PSAM操作类<br>
 R1 device HF and PSAM operation interfaces<br><br>

 第一步:通过<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#connect-android.content.Context-"><code>connect(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#connect-android.content.Context-"><code>connect(Context context)</code></a><br><br>

 第二步:获取相关接口对象<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443A--"><code>getHF14443A()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443B--"><code>getHF14443B()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF15693--"><code>getHF15693()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getPSAM--"><code>getPSAM()</code></a><br>
 Step 2:Gets the associated interface object <a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443A--"><code>getHF14443A()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443B--"><code>getHF14443B()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF15693--"><code>getHF15693()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getPSAM--"><code>getPSAM()</code></a>  <br><br>
 <br>
 <br>
 Example Usage:<br>

   public void HFDemo() {
 &emsp;       R1HFAndPsamManage rfid = R1HFAndPsamManage.getInstance();
 &emsp;   if (!rfid.connect(context)) {
 &emsp;&emsp;        //fail
 &emsp;&emsp;         return;
 &emsp;    }
 &emsp;    IHF14443A ihf14443A = rfid.getHF14443A();
 &emsp;   IHF14443B ihf14443B = rfid.getHF14443B();
 &emsp;   IHF15693 ihf15693 = rfid.getHF15693();
 &emsp;  IPSAM ipsam = rfid.getPSAM();

 &emsp;  HF14443RequestEntity entity = ihf14443A.requestTypeA();
 &emsp;   if (entity == null) {
 &emsp;&emsp;        //"Card not found!"
 &emsp;&emsp;        return;
 &emsp;   }
 &emsp;   byte cMode =  0x60:A  ;  0x61:B
 &emsp;   byte cBlock;
 &emsp;    byte[] txtKey;//6bytes
 &emsp;  boolean reuslt = ihf14443A.authentication(cMode, cBlock, txtKey);
 &emsp;  if (!reuslt) {
 &emsp;&emsp;       //The key validation fail
 &emsp;&emsp;       return;
 &emsp;    }
 &emsp;  //................
 &emsp;   rfid.disconnect();
 &emsp; }</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#connect-android.content.Context-">connect</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">连接读写器(Connect to the reader)</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#disconnect--">disconnect</a></span>()</code>
<div class="block">断开连接(disconnect)</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getConnectionStatus--">getConnectionStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../com/rscja/deviceapi/interfaces/IHF14443A.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443A</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443A--">getHF14443A</a></span>()</code>
<div class="block">获取14443A操作对象 (Get the 14443A operation object)</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443B</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443B--">getHF14443B</a></span>()</code>
<div class="block">获取14443B操作对象 (Get the 14443B operation object)</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces">IHF15693</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF15693--">getHF15693</a></span>()</code>
<div class="block">获取15693操作对象 (Get the 15693 operation object)</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHFVersion--">getHFVersion</a></span>()</code>
<div class="block">获取高频版本号(Get the HF version)</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi">R1HFAndPsamManage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../com/rscja/team/qcom/r1/IPSAM.html" title="interface in com.rscja.team.qcom.r1">IPSAM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getPSAM--">getPSAM</a></span>()</code>
<div class="block">获取PSAM操作对象 (Get the PSAM operation object)</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi">R1HFAndPsamManage</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="getHF14443A--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHF14443A</h4>
<pre>public&nbsp;<a href="../../../com/rscja/deviceapi/interfaces/IHF14443A.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443A</a>&nbsp;getHF14443A()</pre>
<div class="block">获取14443A操作对象 (Get the 14443A operation object)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>IHF14443A</dd>
</dl>
</li>
</ul>
<a name="getHF14443B--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHF14443B</h4>
<pre>public&nbsp;<a href="../../../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443B</a>&nbsp;getHF14443B()</pre>
<div class="block">获取14443B操作对象 (Get the 14443B operation object)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>IHF14443B</dd>
</dl>
</li>
</ul>
<a name="getHF15693--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHF15693</h4>
<pre>public&nbsp;<a href="../../../com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces">IHF15693</a>&nbsp;getHF15693()</pre>
<div class="block">获取15693操作对象 (Get the 15693 operation object)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>IHF15693</dd>
</dl>
</li>
</ul>
<a name="getPSAM--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPSAM</h4>
<pre>public&nbsp;<a href="../../../com/rscja/team/qcom/r1/IPSAM.html" title="interface in com.rscja.team.qcom.r1">IPSAM</a>&nbsp;getPSAM()</pre>
<div class="block">获取PSAM操作对象 (Get the PSAM operation object)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>IPSAM</dd>
</dl>
</li>
</ul>
<a name="getConnectionStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConnectionStatus</h4>
<pre>public&nbsp;<a href="../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;getConnectionStatus()</pre>
</li>
</ul>
<a name="connect-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>connect</h4>
<pre>public&nbsp;boolean&nbsp;connect(android.content.Context&nbsp;context)</pre>
<div class="block">连接读写器(Connect to the reader)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success false:fail</dd>
</dl>
</li>
</ul>
<a name="disconnect--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disconnect</h4>
<pre>public&nbsp;void&nbsp;disconnect()</pre>
<div class="block">断开连接(disconnect)</div>
</li>
</ul>
<a name="getHFVersion--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getHFVersion</h4>
<pre>public&nbsp;java.lang.String&nbsp;getHFVersion()</pre>
<div class="block">获取高频版本号(Get the HF version)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回版本信息，null为失败 (return version info, null is failure)</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/R1HFAndPsamManage.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/PSAM.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/RFIDBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/R1HFAndPsamManage.html" target="_top">Frames</a></li>
<li><a href="R1HFAndPsamManage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
