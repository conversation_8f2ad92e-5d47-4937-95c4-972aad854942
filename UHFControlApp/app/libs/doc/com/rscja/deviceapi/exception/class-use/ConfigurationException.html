<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Class com.rscja.deviceapi.exception.ConfigurationException</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.rscja.deviceapi.exception.ConfigurationException";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/deviceapi/exception/class-use/ConfigurationException.html" target="_top">Frames</a></li>
<li><a href="ConfigurationException.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.rscja.deviceapi.exception.ConfigurationException" class="title">Uses of Class<br>com.rscja.deviceapi.exception.ConfigurationException</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.custom">com.rscja.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk">com.rscja.team.mtk</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.deviceapi">com.rscja.team.mtk.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom">com.rscja.team.qcom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.custom">com.rscja.team.qcom.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.rscja.custom">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a> in <a href="../../../../../com/rscja/custom/package-summary.html">com.rscja.custom</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/custom/package-summary.html">com.rscja.custom</a> that throw <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/custom/UHFTemperatureSensors.html" title="class in com.rscja.custom">UHFTemperatureSensors</a></code></td>
<td class="colLast"><span class="typeNameLabel">UHFTemperatureSensors.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFTemperatureSensors.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a> in <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> that throw <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi">RFIDWithISO15693</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithISO15693.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithISO15693.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO15693协议操作实例<br>
 Acquire ISO15693 protocol operation Instance<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/Module.html" title="class in com.rscja.deviceapi">Module</a></code></td>
<td class="colLast"><span class="typeNameLabel">Module.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/Module.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取操作实例<br>
 Acquire operation Instance<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithISO14443A.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443A.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO14443A协议操作实例<br>
 Acquire ISO14443A protocol operation Instance<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/Infrared.html" title="class in com.rscja.deviceapi">Infrared</a></code></td>
<td class="colLast"><span class="typeNameLabel">Infrared.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/Infrared.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/CardWithBYL.html" title="class in com.rscja.deviceapi">CardWithBYL</a></code></td>
<td class="colLast"><span class="typeNameLabel">CardWithBYL.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/CardWithBYL.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取RFID低频操作实例</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA8.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/FingerprintWithMorpho.html" title="class in com.rscja.deviceapi">FingerprintWithMorpho</a></code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithMorpho.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/FingerprintWithMorpho.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取指纹模块操作实例<br>
 acquire fingerprint module operation eample<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a></code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithFIPS.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/FingerprintWithFIPS.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取指纹模块操作实例<br>
 Acquire fingerprint module operation Instance<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithTLK1NC.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/Barcode1D.html" title="class in com.rscja.deviceapi">Barcode1D</a></code></td>
<td class="colLast"><span class="typeNameLabel">Barcode1D.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/Barcode1D.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取一维条码操作实例<br>
 Acquire 1D barcode operation Instance.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a></code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/FingerprintWithZAZ.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取指纹模块操作实例<br>
 acquire fingerprint module operation eample<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi">RFIDWithLF</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithLF.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithLF.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取RFID低频操作实例<br>
 Acquire RFID LF operation Instance<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></code></td>
<td class="colLast"><span class="typeNameLabel">Fingerprint.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/Fingerprint.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取指纹模块操作实例<br>
 Acquire fingerprint module operation Instance.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA4.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443B.html" title="class in com.rscja.deviceapi">RFIDWithISO14443B</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithISO14443B.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443B.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO14443B协议操作实例<br>
 Acquire ISO14443B protocol operation Instance<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/Barcode2D.html" title="class in com.rscja.deviceapi">Barcode2D</a></code></td>
<td class="colLast"><span class="typeNameLabel">Barcode2D.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/Barcode2D.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取二维条码操作实例<br>
 * Acquire 2D barcode operation Instance.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443A4CPU.html" title="class in com.rscja.deviceapi">RFIDWithISO14443A4CPU</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithISO14443A4CPU.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443A4CPU.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO14443A CPU卡协议操作实例<br>
 Acquire ISO14443A CPU card protocol operation example<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></code></td>
<td class="colLast"><span class="typeNameLabel">Printer.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/Printer.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取打印机模块操作实例<br>
 Acquire printer module operation Instance<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUART.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取UHF操作实例<br>
 Acquire UHF operation Instance<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a> in <a href="../../../../../com/rscja/team/mtk/package-summary.html">com.rscja.team.mtk</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/mtk/package-summary.html">com.rscja.team.mtk</a> that throw <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builder1DConfiguration--">builder1DConfiguration</a></span>()</code>
<div class="block">创建一维条码配置信息<br>
 Create 1D barcode config infor.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builder2DConfiguration--">builder2DConfiguration</a></span>()</code>
<div class="block">创建二维条码配置信息<br>
 Create 2D barcode config infor.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderA8ExtendedUartConfiguration--">builderA8ExtendedUartConfiguration</a></span>()</code>
<div class="block">创建A8扩展串口配置信息<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderBDConfiguration--">builderBDConfiguration</a></span>()</code>
<div class="block">创建北斗配置信息<br>
 Create Beidou config infor.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderDefaultConfiguration--">builderDefaultConfiguration</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderFingerprintConfiguration--">builderFingerprintConfiguration</a></span>()</code>
<div class="block">创建Fingerprint配置信息<br>
 Create Fingerprint config infor.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderInfraredConfiguration--">builderInfraredConfiguration</a></span>()</code>
<div class="block">创建红外配置信息<br>
 Create infrared config infor.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderLFConfiguration--">builderLFConfiguration</a></span>()</code>
<div class="block">创建低频配置信息<br>
 Create LF config infor.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderPrinterConfiguration--">builderPrinterConfiguration</a></span>()</code>
<div class="block">创建打印机配置信息<br>
 Create printer config infor.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderRFIDConfiguration--">builderRFIDConfiguration</a></span>()</code>
<div class="block">创建RFID配置信息<br>
 Create RFID config infor.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderUHFConfiguration--">builderUHFConfiguration</a></span>()</code>
<div class="block">创建UHF配置信息<br>
 Create UHF config infor.<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a> in <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a> that throw <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUART_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取UHF操作实例<br>
 Acquire UHF operation Instance<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithISO15693_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO15693协议操作实例<br>
 Acquire ISO15693 protocol operation Instance<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443B_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithISO14443B_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO14443B协议操作实例<br>
 Acquire ISO14443B protocol operation Instance<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443A_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithISO14443A_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO14443A协议操作实例<br>
 Acquire ISO14443A protocol operation Instance<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A4CPU_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443A4CPU_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithISO14443A4CPU_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A4CPU_mtk.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO14443A CPU卡协议操作实例<br>
 Acquire ISO14443A CPU card protocol operation example<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Printer_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">Printer_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/Printer_mtk.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取打印机模块操作实例<br>
 Acquire printer module operation Instance<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/Infrared_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Infrared_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">Infrared_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/Infrared_mtk.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithTLK1NC_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithMorpho_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithMorpho_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取指纹模块操作实例<br>
 acquire fingerprint module operation eample<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Barcode2D_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">Barcode2D_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取二维条码操作实例<br>
 * Acquire 2D barcode operation Instance.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/Barcode1D_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Barcode1D_mtk</a></code></td>
<td class="colLast"><span class="typeNameLabel">Barcode1D_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/Barcode1D_mtk.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取一维条码操作实例<br>
 Acquire 1D barcode operation Instance.<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a> in <a href="../../../../../com/rscja/team/qcom/package-summary.html">com.rscja.team.qcom</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/package-summary.html">com.rscja.team.qcom</a> that throw <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builder1DConfiguration--">builder1DConfiguration</a></span>()</code>
<div class="block">创建一维条码配置信息<br>
 Create 1D barcode config infor.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builder2DConfiguration--">builder2DConfiguration</a></span>()</code>
<div class="block">创建二维条码配置信息<br>
 Create 2D barcode config infor.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderA8ExtendedUartConfiguration--">builderA8ExtendedUartConfiguration</a></span>()</code>
<div class="block">创建A8扩展串口配置信息<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderBDConfiguration--">builderBDConfiguration</a></span>()</code>
<div class="block">创建北斗配置信息<br>
 Create Beidou config infor.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderDefaultConfiguration--">builderDefaultConfiguration</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderFingerprintConfiguration--">builderFingerprintConfiguration</a></span>()</code>
<div class="block">创建Fingerprint配置信息<br>
 Create Fingerprint config infor.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderInfraredConfiguration--">builderInfraredConfiguration</a></span>()</code>
<div class="block">创建红外配置信息<br>
 Create infrared config infor.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderLFConfiguration--">builderLFConfiguration</a></span>()</code>
<div class="block">创建低频配置信息<br>
 Create LF config infor.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderPrinterConfiguration--">builderPrinterConfiguration</a></span>()</code>
<div class="block">创建打印机配置信息<br>
 Create printer config infor.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderRFIDConfiguration--">builderRFIDConfiguration</a></span>()</code>
<div class="block">创建RFID配置信息<br>
 Create RFID config infor.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom">DeviceConfiguration_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">DeviceConfiguration_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/DeviceConfiguration_qcom.html#builderUHFConfiguration--">builderUHFConfiguration</a></span>()</code>
<div class="block">创建UHF配置信息<br>
 Create UHF config infor.<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.custom">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a> in <a href="../../../../../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a> that throw <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/custom/RFIDWithUHFUARTUAE_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFUARTUAE_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUARTUAE_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/custom/RFIDWithUHFUARTUAE_qcom.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/custom/RFIDWithUHFShuangYingDianZi_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFShuangYingDianZi_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFShuangYingDianZi_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/custom/RFIDWithUHFShuangYingDianZi_qcom.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a> in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> that throw <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUART_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取UHF操作实例<br>
 Acquire UHF operation Instance<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA4_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithLF_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithLF_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取RFID低频操作实例<br>
 Acquire RFID LF operation Instance<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithISO15693_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO15693协议操作实例<br>
 Acquire ISO15693 protocol operation Instance<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443B_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443B_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithISO14443B_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443B_qcom.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO14443B协议操作实例<br>
 Acquire ISO14443B protocol operation Instance<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443A_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithISO14443A_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO14443A协议操作实例<br>
 Acquire ISO14443A protocol operation Instance<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A4CPU_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443A4CPU_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithISO14443A4CPU_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A4CPU_qcom.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO14443A CPU卡协议操作实例<br>
 Acquire ISO14443A CPU card protocol operation example<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithZAZ_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取指纹模块操作实例<br>
 acquire fingerprint module operation eample<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">FingerprintWithTLK1NC_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html" title="class in com.rscja.team.qcom.deviceapi">CardWithBYL_qcom</a></code></td>
<td class="colLast"><span class="typeNameLabel">CardWithBYL_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取RFID低频操作实例</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/deviceapi/exception/class-use/ConfigurationException.html" target="_top">Frames</a></li>
<li><a href="ConfigurationException.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
