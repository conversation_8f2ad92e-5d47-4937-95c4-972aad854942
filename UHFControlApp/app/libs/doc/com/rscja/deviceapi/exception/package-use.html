<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Package com.rscja.deviceapi.exception</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package com.rscja.deviceapi.exception";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/exception/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package com.rscja.deviceapi.exception" class="title">Uses of Package<br>com.rscja.deviceapi.exception</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.custom">com.rscja.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.interfaces">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.mtk">com.rscja.team.mtk</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.deviceapi">com.rscja.team.mtk.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom">com.rscja.team.qcom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.custom">com.rscja.team.qcom.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.r1.hf">com.rscja.team.qcom.r1.hf</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a> used by <a href="../../../../com/rscja/custom/package-summary.html">com.rscja.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/ConfigurationException.html#com.rscja.custom">ConfigurationException</a>
<div class="block">设备配置异常类<br>
 Device configuration exception type<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a> used by <a href="../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/ConfigurationException.html#com.rscja.deviceapi">ConfigurationException</a>
<div class="block">设备配置异常类<br>
 Device configuration exception type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/LowBatteryException.html#com.rscja.deviceapi">LowBatteryException</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/PrinterBarcodeInvalidException.html#com.rscja.deviceapi">PrinterBarcodeInvalidException</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDNotFoundException.html#com.rscja.deviceapi">RFIDNotFoundException</a>
<div class="block">寻卡失败异常<br>
 card searching failed err<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDReadFailureException.html#com.rscja.deviceapi">RFIDReadFailureException</a>
<div class="block">读卡失败异常<br>
 card reading failure err<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDVerificationException.html#com.rscja.deviceapi">RFIDVerificationException</a>
<div class="block">验证失败异常<br>
 verify failure err<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.interfaces">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a> used by <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/LowBatteryException.html#com.rscja.deviceapi.interfaces">LowBatteryException</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/PrinterBarcodeInvalidException.html#com.rscja.deviceapi.interfaces">PrinterBarcodeInvalidException</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDArgumentException.html#com.rscja.deviceapi.interfaces">RFIDArgumentException</a>
<div class="block">参数错误异常<br>
 parameter err<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDNotFoundException.html#com.rscja.deviceapi.interfaces">RFIDNotFoundException</a>
<div class="block">寻卡失败异常<br>
 card searching failed err<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDReadFailureException.html#com.rscja.deviceapi.interfaces">RFIDReadFailureException</a>
<div class="block">读卡失败异常<br>
 card reading failure err<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDVerificationException.html#com.rscja.deviceapi.interfaces">RFIDVerificationException</a>
<div class="block">验证失败异常<br>
 verify failure err<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a> used by <a href="../../../../com/rscja/team/mtk/package-summary.html">com.rscja.team.mtk</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/ConfigurationException.html#com.rscja.team.mtk">ConfigurationException</a>
<div class="block">设备配置异常类<br>
 Device configuration exception type<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a> used by <a href="../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/ConfigurationException.html#com.rscja.team.mtk.deviceapi">ConfigurationException</a>
<div class="block">设备配置异常类<br>
 Device configuration exception type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/LowBatteryException.html#com.rscja.team.mtk.deviceapi">LowBatteryException</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/PrinterBarcodeInvalidException.html#com.rscja.team.mtk.deviceapi">PrinterBarcodeInvalidException</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDNotFoundException.html#com.rscja.team.mtk.deviceapi">RFIDNotFoundException</a>
<div class="block">寻卡失败异常<br>
 card searching failed err<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDReadFailureException.html#com.rscja.team.mtk.deviceapi">RFIDReadFailureException</a>
<div class="block">读卡失败异常<br>
 card reading failure err<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDVerificationException.html#com.rscja.team.mtk.deviceapi">RFIDVerificationException</a>
<div class="block">验证失败异常<br>
 verify failure err<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a> used by <a href="../../../../com/rscja/team/qcom/package-summary.html">com.rscja.team.qcom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/ConfigurationException.html#com.rscja.team.qcom">ConfigurationException</a>
<div class="block">设备配置异常类<br>
 Device configuration exception type<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a> used by <a href="../../../../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/ConfigurationException.html#com.rscja.team.qcom.custom">ConfigurationException</a>
<div class="block">设备配置异常类<br>
 Device configuration exception type<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a> used by <a href="../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/ConfigurationException.html#com.rscja.team.qcom.deviceapi">ConfigurationException</a>
<div class="block">设备配置异常类<br>
 Device configuration exception type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDNotFoundException.html#com.rscja.team.qcom.deviceapi">RFIDNotFoundException</a>
<div class="block">寻卡失败异常<br>
 card searching failed err<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDReadFailureException.html#com.rscja.team.qcom.deviceapi">RFIDReadFailureException</a>
<div class="block">读卡失败异常<br>
 card reading failure err<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDVerificationException.html#com.rscja.team.qcom.deviceapi">RFIDVerificationException</a>
<div class="block">验证失败异常<br>
 verify failure err<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.r1.hf">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/exception/package-summary.html">com.rscja.deviceapi.exception</a> used by <a href="../../../../com/rscja/team/qcom/r1/hf/package-summary.html">com.rscja.team.qcom.r1.hf</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/exception/class-use/RFIDArgumentException.html#com.rscja.team.qcom.r1.hf">RFIDArgumentException</a>
<div class="block">参数错误异常<br>
 parameter err<br></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/exception/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
