<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>com.rscja.deviceapi.exception</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.rscja.deviceapi.exception";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/enums/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/exception/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;com.rscja.deviceapi.exception</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></td>
<td class="colLast">
<div class="block">设备配置异常类<br>
 Device configuration exception type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/exception/DeviceNotConnectException.html" title="class in com.rscja.deviceapi.exception">DeviceNotConnectException</a></td>
<td class="colLast">
<div class="block">模块未打开或未连接异常类<br>
 module deactivated or disconnected exception type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/exception/FingerprintAlreadyEnrolledException.html" title="class in com.rscja.deviceapi.exception">FingerprintAlreadyEnrolledException</a></td>
<td class="colLast">
<div class="block">指纹已经存在异常类<br>
 fingerprint has exception type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/exception/FingerprintInvalidIDException.html" title="class in com.rscja.deviceapi.exception">FingerprintInvalidIDException</a></td>
<td class="colLast">
<div class="block">ID已被占用异常类<br>
 ID has been occupied exception type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/exception/LowBatteryException.html" title="class in com.rscja.deviceapi.exception">LowBatteryException</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/exception/PrinterBarcodeInvalidException.html" title="class in com.rscja.deviceapi.exception">PrinterBarcodeInvalidException</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/exception/PrinterLowPager.html" title="class in com.rscja.deviceapi.exception">PrinterLowPager</a></td>
<td class="colLast">
<div class="block">打印机缺纸异常类<br>
 printer lack paper exceotion type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/exception/PSAMException.html" title="class in com.rscja.deviceapi.exception">PSAMException</a></td>
<td class="colLast">
<div class="block">PSAM异常<br>
 PSAM exceprion<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/exception/RFIDArgumentException.html" title="class in com.rscja.deviceapi.exception">RFIDArgumentException</a></td>
<td class="colLast">
<div class="block">参数错误异常<br>
 parameter err<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></td>
<td class="colLast">
<div class="block">寻卡失败异常<br>
 card searching failed err<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></td>
<td class="colLast">
<div class="block">读卡失败异常<br>
 card reading failure err<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/exception/RFIDVerificationException.html" title="class in com.rscja.deviceapi.exception">RFIDVerificationException</a></td>
<td class="colLast">
<div class="block">验证失败异常<br>
 verify failure err<br></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/enums/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/exception/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
