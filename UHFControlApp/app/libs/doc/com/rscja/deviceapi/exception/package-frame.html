<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>com.rscja.deviceapi.exception</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../com/rscja/deviceapi/exception/package-summary.html" target="classFrame">com.rscja.deviceapi.exception</a></h1>
<div class="indexContainer">
<h2 title="Exceptions">Exceptions</h2>
<ul title="Exceptions">
<li><a href="ConfigurationException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">ConfigurationException</a></li>
<li><a href="DeviceNotConnectException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">DeviceNotConnectException</a></li>
<li><a href="FingerprintAlreadyEnrolledException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">FingerprintAlreadyEnrolledException</a></li>
<li><a href="FingerprintInvalidIDException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">FingerprintInvalidIDException</a></li>
<li><a href="LowBatteryException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">LowBatteryException</a></li>
<li><a href="PrinterBarcodeInvalidException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">PrinterBarcodeInvalidException</a></li>
<li><a href="PrinterLowPager.html" title="class in com.rscja.deviceapi.exception" target="classFrame">PrinterLowPager</a></li>
<li><a href="PSAMException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">PSAMException</a></li>
<li><a href="RFIDArgumentException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">RFIDArgumentException</a></li>
<li><a href="RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">RFIDNotFoundException</a></li>
<li><a href="RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">RFIDReadFailureException</a></li>
<li><a href="RFIDVerificationException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">RFIDVerificationException</a></li>
</ul>
</div>
</body>
</html>
