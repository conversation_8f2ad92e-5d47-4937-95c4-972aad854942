<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>UhfBase</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UhfBase";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":6,"i3":10,"i4":6,"i5":6,"i6":10,"i7":10,"i8":10,"i9":10,"i10":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UhfBase.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/ScanerLedLight.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/UhfBase.html" target="_top">Frames</a></li>
<li><a href="UhfBase.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Class UhfBase" class="title">Class UhfBase</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.UhfBase</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi">BleDevice</a>, <a href="../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8NetWork_qcom</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a>, <a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a>, <a href="../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a>, <a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a>, <a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a>, <a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a>, <a href="../../../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">UhfBase</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.html#UhfBase--">UhfBase</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.html#factoryReset--">factoryReset</a></span>()</code>
<div class="block">恢复出厂设置(Reset uhf parameters)</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.html#free--">free</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>abstract <a href="../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.html#getFastInventoryMode--">getFastInventoryMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.html#init-android.content.Context-">init</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.html#isInventorying--">isInventorying</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.html#readTcpServiceState--">readTcpServiceState</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a></span>(<a href="../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;entity)</code>
<div class="block">设置循环盘点同时读取 EPC、TID、USER 模式  <br>
 Setup auto scan to acquire EPC, TID, User mode</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.html#setFastInventoryMode-boolean-">setFastInventoryMode</a></span>(boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.html#setOnLowBatteryListener-android.content.Context-com.rscja.deviceapi.entity.LowBatteryEntity-com.rscja.deviceapi.interfaces.OnLowBatteryListener-">setOnLowBatteryListener</a></span>(android.content.Context&nbsp;context,
                       <a href="../../../com/rscja/deviceapi/entity/LowBatteryEntity.html" title="class in com.rscja.deviceapi.entity">LowBatteryEntity</a>&nbsp;entity,
                       <a href="../../../com/rscja/deviceapi/interfaces/OnLowBatteryListener.html" title="interface in com.rscja.deviceapi.interfaces">OnLowBatteryListener</a>&nbsp;onLowBatteryListener)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.html#stopInventory--">stopInventory</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="UhfBase--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UhfBase</h4>
<pre>public&nbsp;UhfBase()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="init-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public abstract&nbsp;boolean&nbsp;init(android.content.Context&nbsp;context)</pre>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;boolean&nbsp;free()</pre>
</li>
</ul>
<a name="getConnectStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConnectStatus</h4>
<pre>public abstract&nbsp;<a href="../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;getConnectStatus()</pre>
</li>
</ul>
<a name="isInventorying--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isInventorying</h4>
<pre>public abstract&nbsp;boolean&nbsp;isInventorying()</pre>
</li>
</ul>
<a name="stopInventory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopInventory</h4>
<pre>public abstract&nbsp;boolean&nbsp;stopInventory()</pre>
</li>
</ul>
<a name="setFastInventoryMode-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFastInventoryMode</h4>
<pre>public&nbsp;boolean&nbsp;setFastInventoryMode(boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="getFastInventoryMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFastInventoryMode</h4>
<pre>public&nbsp;int&nbsp;getFastInventoryMode()</pre>
</li>
</ul>
<a name="setOnLowBatteryListener-android.content.Context-com.rscja.deviceapi.entity.LowBatteryEntity-com.rscja.deviceapi.interfaces.OnLowBatteryListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnLowBatteryListener</h4>
<pre>public&nbsp;void&nbsp;setOnLowBatteryListener(android.content.Context&nbsp;context,
                                    <a href="../../../com/rscja/deviceapi/entity/LowBatteryEntity.html" title="class in com.rscja.deviceapi.entity">LowBatteryEntity</a>&nbsp;entity,
                                    <a href="../../../com/rscja/deviceapi/interfaces/OnLowBatteryListener.html" title="interface in com.rscja.deviceapi.interfaces">OnLowBatteryListener</a>&nbsp;onLowBatteryListener)
                             throws <a href="../../../com/rscja/deviceapi/exception/LowBatteryException.html" title="class in com.rscja.deviceapi.exception">LowBatteryException</a></pre>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/exception/LowBatteryException.html" title="class in com.rscja.deviceapi.exception">LowBatteryException</a></code></dd>
</dl>
</li>
</ul>
<a name="readTcpServiceState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTcpServiceState</h4>
<pre>public&nbsp;java.lang.String&nbsp;readTcpServiceState()</pre>
</li>
</ul>
<a name="factoryReset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>factoryReset</h4>
<pre>public&nbsp;boolean&nbsp;factoryReset()</pre>
<div class="block">恢复出厂设置(Reset uhf parameters)</div>
</li>
</ul>
<a name="setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setEPCAndTIDUserMode</h4>
<pre>public&nbsp;boolean&nbsp;setEPCAndTIDUserMode(<a href="../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;entity)</pre>
<div class="block">设置循环盘点同时读取 EPC、TID、USER 模式  <br>
 Setup auto scan to acquire EPC, TID, User mode</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>entity</code> - inventory mode， 0：EPC,1:EPC+TID, 2：EPC+TID+USER, 10:EPC+RESERVED</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UhfBase.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/ScanerLedLight.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/UhfBase.html" target="_top">Frames</a></li>
<li><a href="UhfBase.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
