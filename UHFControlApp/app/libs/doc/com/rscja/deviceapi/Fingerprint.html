<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Fingerprint</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Fingerprint";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":9,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Fingerprint.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/CardWithBYL.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/Fingerprint.html" target="_top">Frames</a></li>
<li><a href="Fingerprint.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Class Fingerprint" class="title">Class Fingerprint</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.Fingerprint</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Fingerprint</span>
extends java.lang.Object
implements <a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></pre>
<div class="block">指纹识别模块操作类,<br>
 Fingerprint identify module operation type<br>
 <p>
 注意： <br>
 Note: <br>
 1、使用前请确认您的机器已安装此模块。<br>
 1. Please make sure this module is installed in your device before using.<br>
 2、要正常使用模块需要在\libs\armeabi\目录放置libDeviceAPI.so文件 <br>
 2. Put libDeviceAPI.so file in directory \libs\armeabi\ then module can be used normally.<br>
 3、在操作设备前需要调用 <b><a href="../../../com/rscja/deviceapi/Fingerprint.html#init--"><code>init()</code></a></b> 打开设备，使用完后调用 <b><a href="../../../com/rscja/deviceapi/Fingerprint.html#free--"><code>free()</code></a></b> 关闭设备<br>
 3. Before operating the device, call <b><a href="../../../com/rscja/deviceapi/Fingerprint.html#init--"><code>init()</code></a></b> to switch on the device, then call <b><a href="../../../com/rscja/deviceapi/Fingerprint.html#free--"><code>free()</code></a></b> to switch off the device.<br></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>liuruifeng</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a></span></code>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#autoEnroll-int-int-">autoEnroll</a></span>(int&nbsp;count,
          int&nbsp;userID)</code>
<div class="block">刷指定次数指纹，模块自动完成注册功能<br>
 Scan fingerprint in specified number, module complete registered function automatically<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#autoMatch-int-int-int-">autoMatch</a></span>(int&nbsp;count,
         int&nbsp;startPage,
         int&nbsp;pageNum)</code>
<div class="block">刷指定次数指纹，自动完成比对功能<br>
 Scan fingerprint in specified number, complete comparison function automatically<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#deletChar-int-int-">deletChar</a></span>(int&nbsp;pageID,
         int&nbsp;num)</code>
<div class="block">删除库中特征值<br>
 delete feature value in database.<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#downChar-com.rscja.deviceapi.Fingerprint.BufferEnum-java.lang.String-">downChar</a></span>(<a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a>&nbsp;buffer,
        java.lang.String&nbsp;hexStr)</code>
<div class="block">下载特征数据到指定缓存区<br>
 download feature data to specified buffer zone<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#empty--">empty</a></span>()</code>
<div class="block">清空模块中保存的指纹数据<br>
 empty out saved fingerprint data in module.<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#free--">free</a></span>()</code>
<div class="block">释放指纹模块<br>
 Release fingerprint module.<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#genChar-com.rscja.deviceapi.Fingerprint.BufferEnum-">genChar</a></span>(<a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a>&nbsp;buffer)</code>
<div class="block">生成特征值（存于指定缓存区）<br>
 Generate feature value( save in specified buffer zone)<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#getImage--">getImage</a></span>()</code>
<div class="block">获取指纹图像（存于模块图像缓存区）<br>
 Acquire fingerprint image( save in buffer zone of module image)<br></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取指纹模块操作实例<br>
 Acquire fingerprint module operation Instance.<br></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#getRandomData--">getRandomData</a></span>()</code>
<div class="block">获取随机数，检测模块是否正常<br>
 Acquire random number, detect module is normal or not.<br></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#getVersion--">getVersion</a></span>()</code>
<div class="block">获取模块版本<br>
 Acquire module version<br></div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#init--">init</a></span>()</code>
<div class="block">初始化指纹模块，默认波特率为57600<br>
 Initialize fingerprint module, default baud rate is 57600.<br></div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#init-int-">init</a></span>(int&nbsp;baudrate)</code>
<div class="block">初始化指纹模块<br>
 Initialize fingerprint module.<br></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#isPowerOn--">isPowerOn</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#loadChar-com.rscja.deviceapi.Fingerprint.BufferEnum-int-">loadChar</a></span>(<a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a>&nbsp;buffer,
        int&nbsp;pageID)</code>
<div class="block">加载指定ID页到特征值缓存区<br>
 Load specified ID page to feature value buffer zone<br></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#match--">match</a></span>()</code>
<div class="block">对比模板缓冲区1与模板缓冲区2的指纹模板文件<br>
 fingerprint template file of comparison template buffer zone 1 and template buffer zone 2.<br></div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#readChipSN--">readChipSN</a></span>()</code>
<div class="block">获取芯片序列号<br>
 acquire chip serial number<br></div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#readSysPara--">readSysPara</a></span>()</code>
<div class="block">获取系统参数，仅返回版本号<br>
 Acquire system parameter, return version number only<br></div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#readSysParaMore--">readSysParaMore</a></span>()</code>
<div class="block">获取系统参数<br>
 Acquire system parameter<br></div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#regModel--">regModel</a></span>()</code>
<div class="block">合并特征文件。将模板缓冲区1与模板缓冲区2中的模板文件合并生成模板，结果存于模板缓冲区1。<br>
 Combine feature file.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#search-com.rscja.deviceapi.Fingerprint.BufferEnum-int-int-">search</a></span>(<a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a>&nbsp;buffer,
      int&nbsp;startPage,
      int&nbsp;pageNum)</code>
<div class="block">检索指纹库<br>
 Searching fingerprint database<br></div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#setDeviceName-java.lang.String-">setDeviceName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">设置设备名称<br>
 Setup device name<br></div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#setManuFacture-java.lang.String-">setManuFacture</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">设置厂商名称<br>
 Setup manufacture name<br></div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#setPWD-java.lang.String-">setPWD</a></span>(java.lang.String&nbsp;passWord)</code>
<div class="block">设置密码<br>
 Setup password<br></div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#setReg-int-int-">setReg</a></span>(int&nbsp;regID,
      int&nbsp;value)</code>
<div class="block">设置寄存器值，修改模块参数<br>
 Setup the value in RAM, modify the parameter in module.<br></div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#storChar-com.rscja.deviceapi.Fingerprint.BufferEnum-int-">storChar</a></span>(<a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a>&nbsp;buffer,
        int&nbsp;pageID)</code>
<div class="block">存储模板文件。将模板缓冲区buffer中的模板文件存到 PageID号所对应的指纹库位置<br>
 save templatefile.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#upChar-com.rscja.deviceapi.Fingerprint.BufferEnum-">upChar</a></span>(<a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a>&nbsp;buffer)</code>
<div class="block">上传指定缓冲区中的指纹特征数据<br>
 Upload fingerprint feature data of specified buffer zone<br></div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#upImage-int-java.lang.String-">upImage</a></span>(int&nbsp;mode,
       java.lang.String&nbsp;fileName)</code>
<div class="block">上传指纹图像文件<br>
 Upload finerprint image file<br></div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#upImageISO-int-java.lang.String-">upImageISO</a></span>(int&nbsp;mode,
          java.lang.String&nbsp;fileName)</code>
<div class="block">上传指纹ISO图像文件<br>
 Upload fingerprint ISO image file<br></div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#validPWD-java.lang.String-">validPWD</a></span>(java.lang.String&nbsp;passWord)</code>
<div class="block">验证密码<br>
 Verify password<br></div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Fingerprint.html#validTempleteNum--">validTempleteNum</a></span>()</code>
<div class="block">获取模块中已保存的指纹特征数据个数<br>
 Acquire number of saved fingerprint feature data in module<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi">Fingerprint</a>&nbsp;getInstance()
                               throws <a href="../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">获取指纹模块操作实例<br>
 Acquire fingerprint module operation Instance.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>指纹模块操作实例<br>
 fingerprint module operation example.<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code> - 配置错误异常<br>
                                configuration error<br></dd>
</dl>
</li>
</ul>
<a name="init--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;boolean&nbsp;init()</pre>
<div class="block">初始化指纹模块，默认波特率为57600<br>
 Initialize fingerprint module, default baud rate is 57600.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#init--">init</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="init-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;boolean&nbsp;init(int&nbsp;baudrate)</pre>
<div class="block">初始化指纹模块<br>
 Initialize fingerprint module.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#init-int-">init</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baudrate</code> - 波特率(57600或115200)<br>
                 Baud rate(57600 or 115200)<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;boolean&nbsp;free()</pre>
<div class="block">释放指纹模块<br>
 Release fingerprint module.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#free--">free</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="getRandomData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRandomData</h4>
<pre>public&nbsp;java.lang.String&nbsp;getRandomData()</pre>
<div class="block">获取随机数，检测模块是否正常<br>
 Acquire random number, detect module is normal or not.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#getRandomData--">getRandomData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>random number content in hexdecimal format, null means failure.<br></dd>
</dl>
</li>
</ul>
<a name="getImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public&nbsp;boolean&nbsp;getImage()</pre>
<div class="block">获取指纹图像（存于模块图像缓存区）<br>
 Acquire fingerprint image( save in buffer zone of module image)<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#getImage--">getImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="genChar-com.rscja.deviceapi.Fingerprint.BufferEnum-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>genChar</h4>
<pre>public&nbsp;boolean&nbsp;genChar(<a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a>&nbsp;buffer)</pre>
<div class="block">生成特征值（存于指定缓存区）<br>
 Generate feature value( save in specified buffer zone)<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#genChar-com.rscja.deviceapi.Fingerprint.BufferEnum-">genChar</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buffer</code> - 缓冲区编号枚举
               Buffer zone code example</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="search-com.rscja.deviceapi.Fingerprint.BufferEnum-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>search</h4>
<pre>public&nbsp;int[]&nbsp;search(<a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a>&nbsp;buffer,
                    int&nbsp;startPage,
                    int&nbsp;pageNum)</pre>
<div class="block">检索指纹库<br>
 Searching fingerprint database<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#search-com.rscja.deviceapi.Fingerprint.BufferEnum-int-int-">search</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buffer</code> - 缓存区<br>
                  Buffer zone<br></dd>
<dd><code>startPage</code> - 页开始ID<br>
                  Page start ID<br></dd>
<dd><code>pageNum</code> - 从startPage开始的num个<br>
                  Page number from start Page<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>number 0 is page ID, 1st element is comparison result, feedback null if it is failure.<br></dd>
</dl>
</li>
</ul>
<a name="match--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>match</h4>
<pre>public&nbsp;int&nbsp;match()</pre>
<div class="block">对比模板缓冲区1与模板缓冲区2的指纹模板文件<br>
 fingerprint template file of comparison template buffer zone 1 and template buffer zone 2.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#match--">match</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>comparison score, if comparison failed, feedback is -1.<br></dd>
</dl>
</li>
</ul>
<a name="regModel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>regModel</h4>
<pre>public&nbsp;boolean&nbsp;regModel()</pre>
<div class="block">合并特征文件。将模板缓冲区1与模板缓冲区2中的模板文件合并生成模板，结果存于模板缓冲区1。<br>
 Combine feature file. combine the template file in template buffer zone 1 and template buffer zone 2 then generate template, save the result in template buffer zone 1.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#regModel--">regModel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="storChar-com.rscja.deviceapi.Fingerprint.BufferEnum-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>storChar</h4>
<pre>public&nbsp;boolean&nbsp;storChar(<a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a>&nbsp;buffer,
                        int&nbsp;pageID)</pre>
<div class="block">存储模板文件。将模板缓冲区buffer中的模板文件存到 PageID号所对应的指纹库位置<br>
 save templatefile. save the template file of template buffer zone in according position of PageID.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#storChar-com.rscja.deviceapi.Fingerprint.BufferEnum-int-">storChar</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buffer</code> - 缓冲区<br>
               Buffer zone<br></dd>
<dd><code>pageID</code> - 页ID，值为0~254<br>
               PageID, value range 0~254<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="loadChar-com.rscja.deviceapi.Fingerprint.BufferEnum-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>loadChar</h4>
<pre>public&nbsp;boolean&nbsp;loadChar(<a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a>&nbsp;buffer,
                        int&nbsp;pageID)</pre>
<div class="block">加载指定ID页到特征值缓存区<br>
 Load specified ID page to feature value buffer zone<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#loadChar-com.rscja.deviceapi.Fingerprint.BufferEnum-int-">loadChar</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buffer</code> - 缓存区<br>
               Buffer zone<br></dd>
<dd><code>pageID</code> - 页ID<br>
               Page ID<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="upChar-com.rscja.deviceapi.Fingerprint.BufferEnum-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>upChar</h4>
<pre>public&nbsp;java.lang.String&nbsp;upChar(<a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a>&nbsp;buffer)</pre>
<div class="block">上传指定缓冲区中的指纹特征数据<br>
 Upload fingerprint feature data of specified buffer zone<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#upChar-com.rscja.deviceapi.Fingerprint.BufferEnum-">upChar</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buffer</code> - 缓存区<br>
               buffer zone<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hexdecimal feature value, null means failure.<br></dd>
</dl>
</li>
</ul>
<a name="downChar-com.rscja.deviceapi.Fingerprint.BufferEnum-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>downChar</h4>
<pre>public&nbsp;boolean&nbsp;downChar(<a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi">Fingerprint.BufferEnum</a>&nbsp;buffer,
                        java.lang.String&nbsp;hexStr)</pre>
<div class="block">下载特征数据到指定缓存区<br>
 download feature data to specified buffer zone<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#downChar-com.rscja.deviceapi.Fingerprint.BufferEnum-java.lang.String-">downChar</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buffer</code> - 缓存区<br>
               Buffer zone<br></dd>
<dd><code>hexStr</code> - 十六进制特征数据<br>
               hexdecimal feature data<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="deletChar-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deletChar</h4>
<pre>public&nbsp;boolean&nbsp;deletChar(int&nbsp;pageID,
                         int&nbsp;num)</pre>
<div class="block">删除库中特征值<br>
 delete feature value in database.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#deletChar-int-int-">deletChar</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>pageID</code> - 页ID<br>
               PageID<br></dd>
<dd><code>num</code> - 从PageID开始的num个<br>
               Page number from PageID.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="empty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>empty</h4>
<pre>public&nbsp;boolean&nbsp;empty()</pre>
<div class="block">清空模块中保存的指纹数据<br>
 empty out saved fingerprint data in module.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#empty--">empty</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="setReg-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReg</h4>
<pre>public&nbsp;boolean&nbsp;setReg(int&nbsp;regID,
                      int&nbsp;value)</pre>
<div class="block">设置寄存器值，修改模块参数<br>
 Setup the value in RAM, modify the parameter in module.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#setReg-int-int-">setReg</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>regID</code> - 寄存器ID<br>
              RAM ID<br></dd>
<dd><code>value</code> - 需要修改的值<br>
              Values need to be modified<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="autoEnroll-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>autoEnroll</h4>
<pre>public&nbsp;int&nbsp;autoEnroll(int&nbsp;count,
                      int&nbsp;userID)</pre>
<div class="block">刷指定次数指纹，模块自动完成注册功能<br>
 Scan fingerprint in specified number, module complete registered function automatically<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#autoEnroll-int-int-">autoEnroll</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>count</code> - 次数<br>
               frequency<br></dd>
<dd><code>userID</code> - 注册ID<br>
               Registered ID<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>register success return ID, failure return -1<br></dd>
</dl>
</li>
</ul>
<a name="autoMatch-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>autoMatch</h4>
<pre>public&nbsp;int[]&nbsp;autoMatch(int&nbsp;count,
                       int&nbsp;startPage,
                       int&nbsp;pageNum)</pre>
<div class="block">刷指定次数指纹，自动完成比对功能<br>
 Scan fingerprint in specified number, complete comparison function automatically<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#autoMatch-int-int-int-">autoMatch</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>count</code> - 次数<br>
                  frequency<br></dd>
<dd><code>startPage</code> - 起始页<br>
                  Start page<br></dd>
<dd><code>pageNum</code> - 页数<br>
                  Page number<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>1st element is successful matchin ID, 2nd element is comparison score, null is failure.<br></dd>
</dl>
</li>
</ul>
<a name="validTempleteNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>validTempleteNum</h4>
<pre>public&nbsp;int&nbsp;validTempleteNum()</pre>
<div class="block">获取模块中已保存的指纹特征数据个数<br>
 Acquire number of saved fingerprint feature data in module<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#validTempleteNum--">validTempleteNum</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>successful return number of fingerprint feature data, failed return -1<br></dd>
</dl>
</li>
</ul>
<a name="readChipSN--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readChipSN</h4>
<pre>public&nbsp;java.lang.String&nbsp;readChipSN()</pre>
<div class="block">获取芯片序列号<br>
 acquire chip serial number<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#readChipSN--">readChipSN</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hexdecimal chip serial number, null is failure<br></dd>
</dl>
</li>
</ul>
<a name="setManuFacture-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setManuFacture</h4>
<pre>public&nbsp;boolean&nbsp;setManuFacture(java.lang.String&nbsp;name)</pre>
<div class="block">设置厂商名称<br>
 Setup manufacture name<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#setManuFacture-java.lang.String-">setManuFacture</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - 名称,缓存 8字节<br>
             Name, cache 8 bytes<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="setDeviceName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeviceName</h4>
<pre>public&nbsp;boolean&nbsp;setDeviceName(java.lang.String&nbsp;name)</pre>
<div class="block">设置设备名称<br>
 Setup device name<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#setDeviceName-java.lang.String-">setDeviceName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - 设备名称,缓存 8字节<br>
             Device name, cache 8 byte<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="readSysPara--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readSysPara</h4>
<pre>public&nbsp;java.lang.String&nbsp;readSysPara()</pre>
<div class="block">获取系统参数，仅返回版本号<br>
 Acquire system parameter, return version number only<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#readSysPara--">readSysPara</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hexdecimal parameter data, null is failure<br></dd>
</dl>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;java.lang.String&nbsp;getVersion()</pre>
<div class="block">获取模块版本<br>
 Acquire module version<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#getVersion--">getVersion</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>null is failure<br></dd>
</dl>
</li>
</ul>
<a name="upImage-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>upImage</h4>
<pre>public&nbsp;int&nbsp;upImage(int&nbsp;mode,
                   java.lang.String&nbsp;fileName)</pre>
<div class="block">上传指纹图像文件<br>
 Upload finerprint image file<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#upImage-int-java.lang.String-">upImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mode</code> - 是否带bmp格式头文件 0表示不带 大于0表示带。默认为1<br>
                 be with bmp header file or not, 0 means without, greater than 0 means with. Default 1<br></dd>
<dd><code>fileName</code> - 文件路径，包括文件名，函数根据该参数生成图像文件<br>
                 File directory, including file name, the formula will generate image file according to this parameter<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success return image size value, failure return -1<br></dd>
</dl>
</li>
</ul>
<a name="upImageISO-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>upImageISO</h4>
<pre>public&nbsp;int&nbsp;upImageISO(int&nbsp;mode,
                      java.lang.String&nbsp;fileName)</pre>
<div class="block">上传指纹ISO图像文件<br>
 Upload fingerprint ISO image file<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#upImageISO-int-java.lang.String-">upImageISO</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mode</code> - 是否带bmp格式头文件 0表示不带 大于0表示带。默认为1<br>
                 be with bmp header file or not, 0 means without, greater than 0 means with. Default 1<br></dd>
<dd><code>fileName</code> - 文件路径，包括文件名，函数根据该参数生成图像文件<br>
                 file directory, including file name, formula will generate image file according to this parameter<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success return image size value, failure return -1<br></dd>
</dl>
</li>
</ul>
<a name="setPWD-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPWD</h4>
<pre>public&nbsp;boolean&nbsp;setPWD(java.lang.String&nbsp;passWord)</pre>
<div class="block">设置密码<br>
 Setup password<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#setPWD-java.lang.String-">setPWD</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>passWord</code> - 密码<br>
                 password<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="validPWD-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>validPWD</h4>
<pre>public&nbsp;boolean&nbsp;validPWD(java.lang.String&nbsp;passWord)</pre>
<div class="block">验证密码<br>
 Verify password<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#validPWD-java.lang.String-">validPWD</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>passWord</code> - 密码<br>
                 Password<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="readSysParaMore--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readSysParaMore</h4>
<pre>public&nbsp;java.lang.String[]&nbsp;readSysParaMore()</pre>
<div class="block">获取系统参数<br>
 Acquire system parameter<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#readSysParaMore--">readSysParaMore</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>[0]safety level [1]distribute size [2]baud rate [3]product serial code [4]version number [5]manufacture code [6]sensor name, null means failure<br></dd>
</dl>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>public&nbsp;boolean&nbsp;isPowerOn()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html#isPowerOn--">isPowerOn</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Fingerprint.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/CardWithBYL.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/Fingerprint.html" target="_top">Frames</a></li>
<li><a href="Fingerprint.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
