<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>BarcodeEntity</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BarcodeEntity";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BarcodeEntity.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/BarcodeEntity.html" target="_top">Frames</a></li>
<li><a href="BarcodeEntity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.entity</div>
<h2 title="Class BarcodeEntity" class="title">Class BarcodeEntity</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.entity.BarcodeEntity</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">BarcodeEntity</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#BarcodeEntity--">BarcodeEntity</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#BarcodeEntity-int-int-">BarcodeEntity</a></span>(int&nbsp;resultCode,
             int&nbsp;decodeTime)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#getAimId--">getAimId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#getBarcodeBytesData--">getBarcodeBytesData</a></span>()</code>
<div class="block">条码原始数据</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#getBarcodeData--">getBarcodeData</a></span>()</code>
<div class="block">条码数据</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#getBarcodeName--">getBarcodeName</a></span>()</code>
<div class="block">条码类型</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#getBarcodeSymbology--">getBarcodeSymbology</a></span>()</code>
<div class="block">symbologyCode 条码类型code</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#getDecodeTime--">getDecodeTime</a></span>()</code>
<div class="block">解码时间</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#getErrCode--">getErrCode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#getPrefix--">getPrefix</a></span>()</code>
<div class="block">前缀</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#getResultCode--">getResultCode</a></span>()</code>
<div class="block">结果码
   <code>BarcodeDecoder.DECODE_SUCCESS</code><br>
   <code>BarcodeDecoder.DECODE_FAILURE</code><br>
   <code>BarcodeDecoder.DECODE_TIMEOUT</code><br>
   <code>BarcodeDecoder.DECODE_CANCEL</code><br></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#setAimId-java.lang.String-">setAimId</a></span>(java.lang.String&nbsp;aimId)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#setBarcodeBytesData-byte:A-">setBarcodeBytesData</a></span>(byte[]&nbsp;barcodeByteData)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#setBarcodeData-java.lang.String-">setBarcodeData</a></span>(java.lang.String&nbsp;barcodeData)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#setBarcodeName-java.lang.String-">setBarcodeName</a></span>(java.lang.String&nbsp;barcodeName)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#setBarcodeSymbology-int-">setBarcodeSymbology</a></span>(int&nbsp;barcodeSymbology)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#setDecodeTime-int-">setDecodeTime</a></span>(int&nbsp;decodeTime)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#setErrCode-int-">setErrCode</a></span>(int&nbsp;errCode)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#setPrefix-byte:A-">setPrefix</a></span>(byte[]&nbsp;prefix)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html#setResultCode-int-">setResultCode</a></span>(int&nbsp;resultCode)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BarcodeEntity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BarcodeEntity</h4>
<pre>public&nbsp;BarcodeEntity()</pre>
</li>
</ul>
<a name="BarcodeEntity-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BarcodeEntity</h4>
<pre>public&nbsp;BarcodeEntity(int&nbsp;resultCode,
                     int&nbsp;decodeTime)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getResultCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResultCode</h4>
<pre>public&nbsp;int&nbsp;getResultCode()</pre>
<div class="block">结果码
   <code>BarcodeDecoder.DECODE_SUCCESS</code><br>
   <code>BarcodeDecoder.DECODE_FAILURE</code><br>
   <code>BarcodeDecoder.DECODE_TIMEOUT</code><br>
   <code>BarcodeDecoder.DECODE_CANCEL</code><br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setResultCode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResultCode</h4>
<pre>public&nbsp;void&nbsp;setResultCode(int&nbsp;resultCode)</pre>
</li>
</ul>
<a name="getDecodeTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDecodeTime</h4>
<pre>public&nbsp;int&nbsp;getDecodeTime()</pre>
<div class="block">解码时间</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setDecodeTime-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDecodeTime</h4>
<pre>public&nbsp;void&nbsp;setDecodeTime(int&nbsp;decodeTime)</pre>
</li>
</ul>
<a name="getBarcodeSymbology--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarcodeSymbology</h4>
<pre>public&nbsp;int&nbsp;getBarcodeSymbology()</pre>
<div class="block">symbologyCode 条码类型code</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setBarcodeSymbology-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeSymbology</h4>
<pre>public&nbsp;void&nbsp;setBarcodeSymbology(int&nbsp;barcodeSymbology)</pre>
</li>
</ul>
<a name="getBarcodeName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarcodeName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBarcodeName()</pre>
<div class="block">条码类型</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setBarcodeName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeName</h4>
<pre>public&nbsp;void&nbsp;setBarcodeName(java.lang.String&nbsp;barcodeName)</pre>
</li>
</ul>
<a name="getBarcodeBytesData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarcodeBytesData</h4>
<pre>public&nbsp;byte[]&nbsp;getBarcodeBytesData()</pre>
<div class="block">条码原始数据</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setBarcodeBytesData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeBytesData</h4>
<pre>public&nbsp;void&nbsp;setBarcodeBytesData(byte[]&nbsp;barcodeByteData)</pre>
</li>
</ul>
<a name="getBarcodeData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarcodeData</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBarcodeData()</pre>
<div class="block">条码数据</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setBarcodeData-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeData</h4>
<pre>public&nbsp;void&nbsp;setBarcodeData(java.lang.String&nbsp;barcodeData)</pre>
</li>
</ul>
<a name="getPrefix--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrefix</h4>
<pre>public&nbsp;byte[]&nbsp;getPrefix()</pre>
<div class="block">前缀</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setPrefix-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrefix</h4>
<pre>public&nbsp;void&nbsp;setPrefix(byte[]&nbsp;prefix)</pre>
</li>
</ul>
<a name="getAimId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAimId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAimId()</pre>
</li>
</ul>
<a name="setAimId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAimId</h4>
<pre>public&nbsp;void&nbsp;setAimId(java.lang.String&nbsp;aimId)</pre>
</li>
</ul>
<a name="getErrCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getErrCode</h4>
<pre>public&nbsp;int&nbsp;getErrCode()</pre>
</li>
</ul>
<a name="setErrCode-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setErrCode</h4>
<pre>public&nbsp;void&nbsp;setErrCode(int&nbsp;errCode)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BarcodeEntity.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/BarcodeEntity.html" target="_top">Frames</a></li>
<li><a href="BarcodeEntity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
