<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>UHFTAGInfo</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UHFTAGInfo";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFTAGInfo.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/TagLocationInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/UHFTAGInfo.html" target="_top">Frames</a></li>
<li><a href="UHFTAGInfo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.entity</div>
<h2 title="Class UHFTAGInfo" class="title">Class UHFTAGInfo</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.entity.UHFTAGInfo</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">UHFTAGInfo</span>
extends java.lang.Object
implements java.lang.Cloneable</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo.ChipInfo</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#EXTRADATA_EPCAREA">EXTRADATA_EPCAREA</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#UHFTAGInfo--">UHFTAGInfo</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#clone--">clone</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getAnt--">getAnt</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo.ChipInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getChipInfo--">getChipInfo</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getCount--">getCount</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getEPC--">getEPC</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getEpcBytes--">getEpcBytes</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getExtraData-java.lang.String-">getExtraData</a></span>(java.lang.String&nbsp;key)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getFrequencyPoint--">getFrequencyPoint</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getIndex--">getIndex</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getPc--">getPc</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getPhase--">getPhase</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getRemain--">getRemain</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getReserved--">getReserved</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getRssi--">getRssi</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getTid--">getTid</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getTidBytes--">getTidBytes</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getTimestamp--">getTimestamp</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getUser--">getUser</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#getUserBytes--">getUserBytes</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setAnt-java.lang.String-">setAnt</a></span>(java.lang.String&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setChipInfo-com.rscja.deviceapi.entity.UHFTAGInfo.ChipInfo-">setChipInfo</a></span>(<a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo.ChipInfo</a>&nbsp;chipInfo)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setCount-int-">setCount</a></span>(int&nbsp;count)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setEPC-java.lang.String-">setEPC</a></span>(java.lang.String&nbsp;epc)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setEpcBytes-byte:A-">setEpcBytes</a></span>(byte[]&nbsp;epcBytes)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setExtraData-java.lang.String-java.lang.String-">setExtraData</a></span>(java.lang.String&nbsp;Key,
            java.lang.String&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setFrequencyPoint-float-">setFrequencyPoint</a></span>(float&nbsp;frequencyPoint)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setIndex-int-">setIndex</a></span>(int&nbsp;index)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setPc-java.lang.String-">setPc</a></span>(java.lang.String&nbsp;pc)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setPhase-int-">setPhase</a></span>(int&nbsp;phase)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setRemain-int-">setRemain</a></span>(int&nbsp;remain)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setReserved-java.lang.String-">setReserved</a></span>(java.lang.String&nbsp;reserved)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setRssi-java.lang.String-">setRssi</a></span>(java.lang.String&nbsp;rssi)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setTid-java.lang.String-">setTid</a></span>(java.lang.String&nbsp;tid)</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setTidBytes-byte:A-">setTidBytes</a></span>(byte[]&nbsp;tidBytes)</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setTimestamp-long-">setTimestamp</a></span>(long&nbsp;timestamp)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setUser-java.lang.String-">setUser</a></span>(java.lang.String&nbsp;user)</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html#setUserBytes-byte:A-">setUserBytes</a></span>(byte[]&nbsp;userBytes)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="EXTRADATA_EPCAREA">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>EXTRADATA_EPCAREA</h4>
<pre>public final&nbsp;java.lang.String EXTRADATA_EPCAREA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.UHFTAGInfo.EXTRADATA_EPCAREA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="UHFTAGInfo--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UHFTAGInfo</h4>
<pre>public&nbsp;UHFTAGInfo()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getFrequencyPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrequencyPoint</h4>
<pre>public&nbsp;float&nbsp;getFrequencyPoint()</pre>
</li>
</ul>
<a name="setFrequencyPoint-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFrequencyPoint</h4>
<pre>public&nbsp;void&nbsp;setFrequencyPoint(float&nbsp;frequencyPoint)</pre>
</li>
</ul>
<a name="setEPC-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPC</h4>
<pre>public&nbsp;void&nbsp;setEPC(java.lang.String&nbsp;epc)</pre>
</li>
</ul>
<a name="getEPC--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEPC</h4>
<pre>public&nbsp;java.lang.String&nbsp;getEPC()</pre>
</li>
</ul>
<a name="getTid--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTid</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTid()</pre>
</li>
</ul>
<a name="setTid-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTid</h4>
<pre>public&nbsp;void&nbsp;setTid(java.lang.String&nbsp;tid)</pre>
</li>
</ul>
<a name="getUser--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUser</h4>
<pre>public&nbsp;java.lang.String&nbsp;getUser()</pre>
</li>
</ul>
<a name="setUser-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUser</h4>
<pre>public&nbsp;void&nbsp;setUser(java.lang.String&nbsp;user)</pre>
</li>
</ul>
<a name="getPc--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPc</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPc()</pre>
</li>
</ul>
<a name="setPc-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPc</h4>
<pre>public&nbsp;void&nbsp;setPc(java.lang.String&nbsp;pc)</pre>
</li>
</ul>
<a name="getRssi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRssi</h4>
<pre>public&nbsp;java.lang.String&nbsp;getRssi()</pre>
</li>
</ul>
<a name="setRssi-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRssi</h4>
<pre>public&nbsp;void&nbsp;setRssi(java.lang.String&nbsp;rssi)</pre>
</li>
</ul>
<a name="getAnt--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnt</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAnt()</pre>
</li>
</ul>
<a name="setAnt-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnt</h4>
<pre>public&nbsp;void&nbsp;setAnt(java.lang.String&nbsp;ant)</pre>
</li>
</ul>
<a name="getRemain--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemain</h4>
<pre>public&nbsp;int&nbsp;getRemain()</pre>
</li>
</ul>
<a name="setRemain-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemain</h4>
<pre>public&nbsp;void&nbsp;setRemain(int&nbsp;remain)</pre>
</li>
</ul>
<a name="getIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIndex</h4>
<pre>public&nbsp;int&nbsp;getIndex()</pre>
</li>
</ul>
<a name="setIndex-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIndex</h4>
<pre>public&nbsp;void&nbsp;setIndex(int&nbsp;index)</pre>
</li>
</ul>
<a name="getEpcBytes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEpcBytes</h4>
<pre>public&nbsp;byte[]&nbsp;getEpcBytes()</pre>
</li>
</ul>
<a name="setEpcBytes-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEpcBytes</h4>
<pre>public&nbsp;void&nbsp;setEpcBytes(byte[]&nbsp;epcBytes)</pre>
</li>
</ul>
<a name="getReserved--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReserved</h4>
<pre>public&nbsp;java.lang.String&nbsp;getReserved()</pre>
</li>
</ul>
<a name="setReserved-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReserved</h4>
<pre>public&nbsp;void&nbsp;setReserved(java.lang.String&nbsp;reserved)</pre>
</li>
</ul>
<a name="getExtraData-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtraData</h4>
<pre>public&nbsp;java.lang.String&nbsp;getExtraData(java.lang.String&nbsp;key)</pre>
</li>
</ul>
<a name="setExtraData-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExtraData</h4>
<pre>public&nbsp;void&nbsp;setExtraData(java.lang.String&nbsp;Key,
                         java.lang.String&nbsp;value)</pre>
</li>
</ul>
<a name="getCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCount</h4>
<pre>public&nbsp;int&nbsp;getCount()</pre>
</li>
</ul>
<a name="setCount-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCount</h4>
<pre>public&nbsp;void&nbsp;setCount(int&nbsp;count)</pre>
</li>
</ul>
<a name="getChipInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChipInfo</h4>
<pre>public&nbsp;<a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo.ChipInfo</a>&nbsp;getChipInfo()</pre>
</li>
</ul>
<a name="setChipInfo-com.rscja.deviceapi.entity.UHFTAGInfo.ChipInfo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setChipInfo</h4>
<pre>public&nbsp;void&nbsp;setChipInfo(<a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo.ChipInfo</a>&nbsp;chipInfo)</pre>
</li>
</ul>
<a name="getTimestamp--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimestamp</h4>
<pre>public&nbsp;long&nbsp;getTimestamp()</pre>
</li>
</ul>
<a name="setTimestamp-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimestamp</h4>
<pre>public&nbsp;void&nbsp;setTimestamp(long&nbsp;timestamp)</pre>
</li>
</ul>
<a name="getTidBytes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTidBytes</h4>
<pre>public&nbsp;byte[]&nbsp;getTidBytes()</pre>
</li>
</ul>
<a name="setTidBytes-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTidBytes</h4>
<pre>public&nbsp;void&nbsp;setTidBytes(byte[]&nbsp;tidBytes)</pre>
</li>
</ul>
<a name="getUserBytes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUserBytes</h4>
<pre>public&nbsp;byte[]&nbsp;getUserBytes()</pre>
</li>
</ul>
<a name="setUserBytes-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUserBytes</h4>
<pre>public&nbsp;void&nbsp;setUserBytes(byte[]&nbsp;userBytes)</pre>
</li>
</ul>
<a name="getPhase--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhase</h4>
<pre>public&nbsp;int&nbsp;getPhase()</pre>
</li>
</ul>
<a name="setPhase-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPhase</h4>
<pre>public&nbsp;void&nbsp;setPhase(int&nbsp;phase)</pre>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;clone()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>clone</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFTAGInfo.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/TagLocationInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/UHFTAGInfo.html" target="_top">Frames</a></li>
<li><a href="UHFTAGInfo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
