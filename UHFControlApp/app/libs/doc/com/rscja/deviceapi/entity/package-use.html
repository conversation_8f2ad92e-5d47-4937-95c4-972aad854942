<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Package com.rscja.deviceapi.entity</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package com.rscja.deviceapi.entity";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package com.rscja.deviceapi.entity" class="title">Uses of Package<br>com.rscja.deviceapi.entity</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.barcode">com.rscja.barcode</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.custom">com.rscja.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.custom.interfaces">com.rscja.custom.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.entity">com.rscja.deviceapi.entity</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.interfaces">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.scanner">com.rscja.scanner</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.scanner.utility">com.rscja.scanner.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.barcode">com.rscja.team.mtk.barcode</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.deviceapi">com.rscja.team.mtk.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.scanner.utility">com.rscja.team.mtk.scanner.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.ble">com.rscja.team.qcom.ble</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.custom">com.rscja.team.qcom.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.r1.hf">com.rscja.team.qcom.r1.hf</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.rs232utils">com.rscja.team.qcom.rs232utils</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.scanner.utility">com.rscja.team.qcom.scanner.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.urax">com.rscja.team.qcom.urax</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.utility">com.rscja.team.qcom.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.utility">com.rscja.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.barcode">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/barcode/package-summary.html">com.rscja.barcode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/BarcodeEntity.html#com.rscja.barcode">BarcodeEntity</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/custom/package-summary.html">com.rscja.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryModeEntity.html#com.rscja.custom">InventoryModeEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.html#com.rscja.custom">UHFTAGInfo</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.custom.interfaces">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/custom/interfaces/package-summary.html">com.rscja.custom.interfaces</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.html#com.rscja.custom.interfaces">UHFTAGInfo</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/AnimalEntity.html#com.rscja.deviceapi">AnimalEntity</a>
<div class="block">动物标签数据实体类<br>
 animal tag data entity<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/AntennaConnectState.html#com.rscja.deviceapi">AntennaConnectState</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/AntennaPowerEntity.html#com.rscja.deviceapi">AntennaPowerEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/AntennaState.html#com.rscja.deviceapi">AntennaState</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/BarcodeResult.html#com.rscja.deviceapi">BarcodeResult</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/DESFireFile.html#com.rscja.deviceapi">DESFireFile</a>
<div class="block">DESFire文件简单数据实体<br>
 DESFire file simple data entity<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/Gen2Entity.html#com.rscja.deviceapi">Gen2Entity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/GPIStateEntity.html#com.rscja.deviceapi">GPIStateEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/GPOEntity.html#com.rscja.deviceapi">GPOEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryModeEntity.html#com.rscja.deviceapi">InventoryModeEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryParameter.html#com.rscja.deviceapi">InventoryParameter</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ISO15693Entity.html#com.rscja.deviceapi">ISO15693Entity</a>
<div class="block">ISO15693协议数据实体<br>
 ISO15639 protocol data entity<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/LowBatteryEntity.html#com.rscja.deviceapi">LowBatteryEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ReaderIPEntity.html#com.rscja.deviceapi">ReaderIPEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/SimpleRFIDEntity.html#com.rscja.deviceapi">SimpleRFIDEntity</a>
<div class="block">RFID简单数据实体<br>
 RFID simple data entity<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/TagInfoRule.html#com.rscja.deviceapi">TagInfoRule</a>
<div class="block">盘点规则</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.html#com.rscja.deviceapi">UHFTAGInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/WifiConfig.html#com.rscja.deviceapi">WifiConfig</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.entity">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/BarcodeResult.html#com.rscja.deviceapi.entity">BarcodeResult</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/HF14443RequestEntity.html#com.rscja.deviceapi.entity">HF14443RequestEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/HF15693RequestEntity.html#com.rscja.deviceapi.entity">HF15693RequestEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/HF15693RequestEntity.TagType.html#com.rscja.deviceapi.entity">HF15693RequestEntity.TagType</a>
<div class="block">标签类型<br>
 Tag type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryModeEntity.html#com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryModeEntity.Builder.html#com.rscja.deviceapi.entity">InventoryModeEntity.Builder</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryParameter.html#com.rscja.deviceapi.entity">InventoryParameter</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryParameter.ResultData.html#com.rscja.deviceapi.entity">InventoryParameter.ResultData</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/RadarLocationEntity.html#com.rscja.deviceapi.entity">RadarLocationEntity</a>
<div class="block">回调数据实体类型 (Callback data entity type)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/RadarLocationEntity.Builder.html#com.rscja.deviceapi.entity">RadarLocationEntity.Builder</a>
<div class="block">标签实体构造器 (Constructor for label entities)</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ReaderIPEntity.html#com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ScannerParameterEntity.html#com.rscja.deviceapi.entity">ScannerParameterEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ScannerParameterEntity.Builder.html#com.rscja.deviceapi.entity">ScannerParameterEntity.Builder</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/SimpleRFIDEntity.html#com.rscja.deviceapi.entity">SimpleRFIDEntity</a>
<div class="block">RFID简单数据实体<br>
 RFID simple data entity<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/TagInfoRule.html#com.rscja.deviceapi.entity">TagInfoRule</a>
<div class="block">盘点规则</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/TagLocationInfo.html#com.rscja.deviceapi.entity">TagLocationInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.html#com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.ChipInfo.html#com.rscja.deviceapi.entity">UHFTAGInfo.ChipInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.ChipInfo.Builder.html#com.rscja.deviceapi.entity">UHFTAGInfo.ChipInfo.Builder</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.interfaces">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/AnimalEntity.html#com.rscja.deviceapi.interfaces">AnimalEntity</a>
<div class="block">动物标签数据实体类<br>
 animal tag data entity<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/AntennaConnectState.html#com.rscja.deviceapi.interfaces">AntennaConnectState</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/AntennaPowerEntity.html#com.rscja.deviceapi.interfaces">AntennaPowerEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/AntennaState.html#com.rscja.deviceapi.interfaces">AntennaState</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/BarcodeResult.html#com.rscja.deviceapi.interfaces">BarcodeResult</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/BatteryEntity.html#com.rscja.deviceapi.interfaces">BatteryEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/DESFireFile.html#com.rscja.deviceapi.interfaces">DESFireFile</a>
<div class="block">DESFire文件简单数据实体<br>
 DESFire file simple data entity<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/Gen2Entity.html#com.rscja.deviceapi.interfaces">Gen2Entity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/GPIStateEntity.html#com.rscja.deviceapi.interfaces">GPIStateEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/GPOEntity.html#com.rscja.deviceapi.interfaces">GPOEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/HF14443RequestEntity.html#com.rscja.deviceapi.interfaces">HF14443RequestEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/HF15693RequestEntity.html#com.rscja.deviceapi.interfaces">HF15693RequestEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryModeEntity.html#com.rscja.deviceapi.interfaces">InventoryModeEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryParameter.html#com.rscja.deviceapi.interfaces">InventoryParameter</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ISO15693Entity.html#com.rscja.deviceapi.interfaces">ISO15693Entity</a>
<div class="block">ISO15693协议数据实体<br>
 ISO15639 protocol data entity<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/LowBatteryEntity.html#com.rscja.deviceapi.interfaces">LowBatteryEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/RadarLocationEntity.html#com.rscja.deviceapi.interfaces">RadarLocationEntity</a>
<div class="block">回调数据实体类型 (Callback data entity type)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ReaderIPEntity.html#com.rscja.deviceapi.interfaces">ReaderIPEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/SimpleRFIDEntity.html#com.rscja.deviceapi.interfaces">SimpleRFIDEntity</a>
<div class="block">RFID简单数据实体<br>
 RFID simple data entity<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/TagLocationEntity.html#com.rscja.deviceapi.interfaces">TagLocationEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.html#com.rscja.deviceapi.interfaces">UHFTAGInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/WifiConfig.html#com.rscja.deviceapi.interfaces">WifiConfig</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.scanner">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/scanner/package-summary.html">com.rscja.scanner</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ScannerParameterEntity.html#com.rscja.scanner">ScannerParameterEntity</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.scanner.utility">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/scanner/utility/package-summary.html">com.rscja.scanner.utility</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ScannerParameterEntity.html#com.rscja.scanner.utility">ScannerParameterEntity</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.barcode">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/team/mtk/barcode/package-summary.html">com.rscja.team.mtk.barcode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/BarcodeEntity.html#com.rscja.team.mtk.barcode">BarcodeEntity</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/DESFireFile.html#com.rscja.team.mtk.deviceapi">DESFireFile</a>
<div class="block">DESFire文件简单数据实体<br>
 DESFire file simple data entity<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/Gen2Entity.html#com.rscja.team.mtk.deviceapi">Gen2Entity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryModeEntity.html#com.rscja.team.mtk.deviceapi">InventoryModeEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryParameter.html#com.rscja.team.mtk.deviceapi">InventoryParameter</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ISO15693Entity.html#com.rscja.team.mtk.deviceapi">ISO15693Entity</a>
<div class="block">ISO15693协议数据实体<br>
 ISO15639 protocol data entity<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/LowBatteryEntity.html#com.rscja.team.mtk.deviceapi">LowBatteryEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/SimpleRFIDEntity.html#com.rscja.team.mtk.deviceapi">SimpleRFIDEntity</a>
<div class="block">RFID简单数据实体<br>
 RFID simple data entity<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.html#com.rscja.team.mtk.deviceapi">UHFTAGInfo</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.scanner.utility">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/team/mtk/scanner/utility/package-summary.html">com.rscja.team.mtk.scanner.utility</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ScannerParameterEntity.html#com.rscja.team.mtk.scanner.utility">ScannerParameterEntity</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.ble">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/team/qcom/ble/package-summary.html">com.rscja.team.qcom.ble</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/BarcodeResult.html#com.rscja.team.qcom.ble">BarcodeResult</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/Gen2Entity.html#com.rscja.team.qcom.ble">Gen2Entity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryModeEntity.html#com.rscja.team.qcom.ble">InventoryModeEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryParameter.html#com.rscja.team.qcom.ble">InventoryParameter</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.html#com.rscja.team.qcom.ble">UHFTAGInfo</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.html#com.rscja.team.qcom.custom">UHFTAGInfo</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/AnimalEntity.html#com.rscja.team.qcom.deviceapi">AnimalEntity</a>
<div class="block">动物标签数据实体类<br>
 animal tag data entity<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/AntennaConnectState.html#com.rscja.team.qcom.deviceapi">AntennaConnectState</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/AntennaPowerEntity.html#com.rscja.team.qcom.deviceapi">AntennaPowerEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/AntennaState.html#com.rscja.team.qcom.deviceapi">AntennaState</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/BarcodeResult.html#com.rscja.team.qcom.deviceapi">BarcodeResult</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/DESFireFile.html#com.rscja.team.qcom.deviceapi">DESFireFile</a>
<div class="block">DESFire文件简单数据实体<br>
 DESFire file simple data entity<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/Gen2Entity.html#com.rscja.team.qcom.deviceapi">Gen2Entity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/GPIStateEntity.html#com.rscja.team.qcom.deviceapi">GPIStateEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/GPOEntity.html#com.rscja.team.qcom.deviceapi">GPOEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryModeEntity.html#com.rscja.team.qcom.deviceapi">InventoryModeEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/InventoryParameter.html#com.rscja.team.qcom.deviceapi">InventoryParameter</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ISO15693Entity.html#com.rscja.team.qcom.deviceapi">ISO15693Entity</a>
<div class="block">ISO15693协议数据实体<br>
 ISO15639 protocol data entity<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ReaderIPEntity.html#com.rscja.team.qcom.deviceapi">ReaderIPEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/SimpleRFIDEntity.html#com.rscja.team.qcom.deviceapi">SimpleRFIDEntity</a>
<div class="block">RFID简单数据实体<br>
 RFID simple data entity<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/TagInfoRule.html#com.rscja.team.qcom.deviceapi">TagInfoRule</a>
<div class="block">盘点规则</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/TagLocationEntity.html#com.rscja.team.qcom.deviceapi">TagLocationEntity</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.html#com.rscja.team.qcom.deviceapi">UHFTAGInfo</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.r1.hf">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/team/qcom/r1/hf/package-summary.html">com.rscja.team.qcom.r1.hf</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/HF14443RequestEntity.html#com.rscja.team.qcom.r1.hf">HF14443RequestEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/HF15693RequestEntity.html#com.rscja.team.qcom.r1.hf">HF15693RequestEntity</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.rs232utils">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/team/qcom/rs232utils/package-summary.html">com.rscja.team.qcom.rs232utils</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ReaderIPEntity.html#com.rscja.team.qcom.rs232utils">ReaderIPEntity</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/WifiConfig.html#com.rscja.team.qcom.rs232utils">WifiConfig</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.scanner.utility">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/team/qcom/scanner/utility/package-summary.html">com.rscja.team.qcom.scanner.utility</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/ScannerParameterEntity.html#com.rscja.team.qcom.scanner.utility">ScannerParameterEntity</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.urax">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/team/qcom/urax/package-summary.html">com.rscja.team.qcom.urax</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/GPIStateEntity.html#com.rscja.team.qcom.urax">GPIStateEntity</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.utility">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/team/qcom/utility/package-summary.html">com.rscja.team.qcom.utility</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.html#com.rscja.team.qcom.utility">UHFTAGInfo</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.utility">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> used by <a href="../../../../com/rscja/utility/package-summary.html">com.rscja.utility</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.html#com.rscja.utility">UHFTAGInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/entity/class-use/UHFTAGInfo.ChipInfo.html#com.rscja.utility">UHFTAGInfo.ChipInfo</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
