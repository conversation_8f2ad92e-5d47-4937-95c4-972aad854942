<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Gen2Entity</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Gen2Entity";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Gen2Entity.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/GPIOInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/Gen2Entity.html" target="_top">Frames</a></li>
<li><a href="Gen2Entity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.entity</div>
<h2 title="Class Gen2Entity" class="title">Class Gen2Entity</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.entity.Gen2Entity</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Gen2Entity</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#Gen2Entity--">Gen2Entity</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#checkParameter--">checkParameter</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getLinkFrequency--">getLinkFrequency</a></span>()</code>
<div class="block">Link Frequency 设置(Link Frequency setting)</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getMaxQ--">getMaxQ</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getMinQ--">getMinQ</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getQ--">getQ</a></span>()</code>
<div class="block">获取Q值(Q value)</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getQueryDR--">getQueryDR</a></span>()</code>
<div class="block">获取query命令的DR参数(DR parameter of the query command)</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getQueryM--">getQueryM</a></span>()</code>
<div class="block">获取query命令的M参数(M parameter of the query command )</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getQuerySel--">getQuerySel</a></span>()</code>
<div class="block">获取query命令的sel参数(sel parameter of the query command)</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getQuerySession--">getQuerySession</a></span>()</code>
<div class="block">获取query命令的session参数(session parameter of the query command)</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getQueryTarget--">getQueryTarget</a></span>()</code>
<div class="block">获取query命令的Target参数(Target parameter of the query command)</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getQueryTRext--">getQueryTRext</a></span>()</code>
<div class="block">获取query命令的TRext参数(TRext parameter of the query command )</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getSelectAction--">getSelectAction</a></span>()</code>
<div class="block">获取select 命令的Action参数 (Action parameter of the select command)</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getSelectTarget--">getSelectTarget</a></span>()</code>
<div class="block">获取select命令的Target参数 (Target parameter of the select command)</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getSelectTruncate--">getSelectTruncate</a></span>()</code>
<div class="block">获取select命令的Truncate参数(Truncate parameter of the select command)</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#getStartQ--">getStartQ</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setLinkFrequency-int-">setLinkFrequency</a></span>(int&nbsp;linkFrequency)</code>
<div class="block">Link Frequency 设置(Link Frequency setting)</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setMaxQ-int-">setMaxQ</a></span>(int&nbsp;maxQ)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setMinQ-int-">setMinQ</a></span>(int&nbsp;minQ)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setQ-int-">setQ</a></span>(int&nbsp;q)</code>
<div class="block">设置Q值(Q value)</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setQueryDR-int-">setQueryDR</a></span>(int&nbsp;queryDR)</code>
<div class="block">设置query命令的DR参数(DR parameter of the query command)</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setQueryM-int-">setQueryM</a></span>(int&nbsp;queryM)</code>
<div class="block">设置query命令的M参数(M parameter of the query command )</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setQuerySel-int-">setQuerySel</a></span>(int&nbsp;querySel)</code>
<div class="block">设置query命令的sel参数(sel parameter of the query command)</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setQuerySession-int-">setQuerySession</a></span>(int&nbsp;querySession)</code>
<div class="block">设置query命令的session参数(session parameter of the query command)</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setQueryTarget-int-">setQueryTarget</a></span>(int&nbsp;queryTarget)</code>
<div class="block">设置 query命令的Target参数(Target parameter of the query command)</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setQueryTRext-int-">setQueryTRext</a></span>(int&nbsp;queryTRext)</code>
<div class="block">设置query命令的TRext参数(TRext parameter of the query command )</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setSelectAction-int-">setSelectAction</a></span>(int&nbsp;selectAction)</code>
<div class="block">设置select 命令的Action参数 (Action parameter of the select command)</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setSelectTarget-int-">setSelectTarget</a></span>(int&nbsp;selectTarget)</code>
<div class="block">设置select命令的Target参数 (Target parameter of the select command)</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setSelectTruncate-int-">setSelectTruncate</a></span>(int&nbsp;selectTruncate)</code>
<div class="block">设置select命令的Truncate参数(Truncate parameter of the select command)</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#setStartQ-int-">setStartQ</a></span>(int&nbsp;startQ)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Gen2Entity--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Gen2Entity</h4>
<pre>public&nbsp;Gen2Entity()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="checkParameter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkParameter</h4>
<pre>public&nbsp;boolean&nbsp;checkParameter()</pre>
</li>
</ul>
<a name="getSelectTarget--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectTarget</h4>
<pre>public&nbsp;int&nbsp;getSelectTarget()</pre>
<div class="block">获取select命令的Target参数 (Target parameter of the select command)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0:s0  1:s1  2:s2  3:s3  4:SL</dd>
</dl>
</li>
</ul>
<a name="setSelectTarget-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSelectTarget</h4>
<pre>public&nbsp;void&nbsp;setSelectTarget(int&nbsp;selectTarget)</pre>
<div class="block">设置select命令的Target参数 (Target parameter of the select command)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>selectTarget</code> - 0:s0  1:s1  2:s2  3:s3  4:SL</dd>
</dl>
</li>
</ul>
<a name="getSelectAction--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectAction</h4>
<pre>public&nbsp;int&nbsp;getSelectAction()</pre>
<div class="block">获取select 命令的Action参数 (Action parameter of the select command)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0( Matching: assert SL or inventoried->A    ;  Non-Matching: de-assert SL or inventoried->B)<br>
       1( Matching: assert SL or inventoried->A    ;  Non-Matching: do nothing)<br>
       2( Matching: do nothing                     ;  Non-Matching: de-assert SL or inventoried->B)<br>
       3( Matching: negate SL or (A -> B, B -> A)  ;  Non-Matching: do nothing                     <br>
       4( Matching: de-assert SL or inventoried->B ;  Non-Matching: de-assert SL or inventoried->A<br>
       5( Matching: de-assert SL or inventoried->B ;  Non-Matching: do nothing<br>
       6( Matching: do nothing                     ;  Non-Matching: de-assert SL or inventoried->A<br>
       7( Matching: do nothing                     ;  Non-Matching: negate SL or (A->B, B->A))<br></dd>
</dl>
</li>
</ul>
<a name="setSelectAction-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSelectAction</h4>
<pre>public&nbsp;void&nbsp;setSelectAction(int&nbsp;selectAction)</pre>
<div class="block">设置select 命令的Action参数 (Action parameter of the select command)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>selectAction</code> - 0( Matching: assert SL or inventoried->A    ;  Non-Matching: de-assert SL or inventoried->B)<br>
       1( Matching: assert SL or inventoried->A    ;  Non-Matching: do nothing)<br>
       2( Matching: do nothing                     ;  Non-Matching: de-assert SL or inventoried->B)<br>
       3( Matching: negate SL or (A -> B, B -> A)  ;  Non-Matching: do nothing                     <br>
       4( Matching: de-assert SL or inventoried->B ;  Non-Matching: de-assert SL or inventoried->A<br>
       5( Matching: de-assert SL or inventoried->B ;  Non-Matching: do nothing<br>
       6( Matching: do nothing                     ;  Non-Matching: de-assert SL or inventoried->A<br>
       7( Matching: do nothing                     ;  Non-Matching: negate SL or (A->B, B->A))<br></dd>
</dl>
</li>
</ul>
<a name="getSelectTruncate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectTruncate</h4>
<pre>public&nbsp;int&nbsp;getSelectTruncate()</pre>
<div class="block">获取select命令的Truncate参数(Truncate parameter of the select command)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0:Disable truncation , 1:Enable truncation</dd>
</dl>
</li>
</ul>
<a name="setSelectTruncate-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSelectTruncate</h4>
<pre>public&nbsp;void&nbsp;setSelectTruncate(int&nbsp;selectTruncate)</pre>
<div class="block">设置select命令的Truncate参数(Truncate parameter of the select command)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>selectTruncate</code> - 0:Disable truncation , 1:Enable truncation</dd>
</dl>
</li>
</ul>
<a name="getQ--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQ</h4>
<pre>public&nbsp;int&nbsp;getQ()</pre>
<div class="block">获取Q值(Q value)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0:固定Q算法,1:动态Q算法  (0:fixed Q algorithm, 1:dynamic Q algorithm)<br>
  注意：在固定Q算法下，Q固定为StartQ，忽略MinQ 和 MaxQ (note: in fixed algorithm, Q will be fixed as StartQ, neglect MinQ and MaxQ)<br></dd>
</dl>
</li>
</ul>
<a name="setQ-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQ</h4>
<pre>public&nbsp;void&nbsp;setQ(int&nbsp;q)</pre>
<div class="block">设置Q值(Q value)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>q</code> - 0:固定Q算法,  1:动态Q算法  (0:fixed Q algorithm, 1:dynamic Q algorithm)<br>
  注意：在固定Q算法下，Q固定为StartQ，忽略MinQ 和 MaxQ (note: in fixed algorithm, Q will be fixed as StartQ, neglect MinQ and MaxQ)<br></dd>
</dl>
</li>
</ul>
<a name="getStartQ--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartQ</h4>
<pre>public&nbsp;int&nbsp;getStartQ()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0,1,2,3.....15</dd>
</dl>
</li>
</ul>
<a name="setStartQ-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartQ</h4>
<pre>public&nbsp;void&nbsp;setStartQ(int&nbsp;startQ)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>startQ</code> - 0,1,2,3.....15</dd>
</dl>
</li>
</ul>
<a name="getMinQ--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinQ</h4>
<pre>public&nbsp;int&nbsp;getMinQ()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0,1,2,3.....15</dd>
</dl>
</li>
</ul>
<a name="setMinQ-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinQ</h4>
<pre>public&nbsp;void&nbsp;setMinQ(int&nbsp;minQ)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minQ</code> - 0,1,2,3.....15</dd>
</dl>
</li>
</ul>
<a name="getMaxQ--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxQ</h4>
<pre>public&nbsp;int&nbsp;getMaxQ()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0,1,2,3.....15</dd>
</dl>
</li>
</ul>
<a name="setMaxQ-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxQ</h4>
<pre>public&nbsp;void&nbsp;setMaxQ(int&nbsp;maxQ)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>maxQ</code> - 0,1,2,3.....15</dd>
</dl>
</li>
</ul>
<a name="getQueryDR--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQueryDR</h4>
<pre>public&nbsp;int&nbsp;getQueryDR()</pre>
<div class="block">获取query命令的DR参数(DR parameter of the query command)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0:8 , 1:64/3</dd>
</dl>
</li>
</ul>
<a name="setQueryDR-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQueryDR</h4>
<pre>public&nbsp;void&nbsp;setQueryDR(int&nbsp;queryDR)</pre>
<div class="block">设置query命令的DR参数(DR parameter of the query command)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryDR</code> - 0:8 , 1:64/3</dd>
</dl>
</li>
</ul>
<a name="getQueryM--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQueryM</h4>
<pre>public&nbsp;int&nbsp;getQueryM()</pre>
<div class="block">获取query命令的M参数(M parameter of the query command )</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0:FM0, 1:Miller2, 2:Miller4, 3:Miller8</dd>
</dl>
</li>
</ul>
<a name="setQueryM-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQueryM</h4>
<pre>public&nbsp;void&nbsp;setQueryM(int&nbsp;queryM)</pre>
<div class="block">设置query命令的M参数(M parameter of the query command )</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryM</code> - 0:FM0, 1:Miller2, 2:Miller4, 3:Miller8</dd>
</dl>
</li>
</ul>
<a name="getQueryTRext--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQueryTRext</h4>
<pre>public&nbsp;int&nbsp;getQueryTRext()</pre>
<div class="block">获取query命令的TRext参数(TRext parameter of the query command )</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0:No pilot,  1:Use pilot</dd>
</dl>
</li>
</ul>
<a name="setQueryTRext-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQueryTRext</h4>
<pre>public&nbsp;void&nbsp;setQueryTRext(int&nbsp;queryTRext)</pre>
<div class="block">设置query命令的TRext参数(TRext parameter of the query command )</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryTRext</code> - 0:No pilot,  1:Use pilot</dd>
</dl>
</li>
</ul>
<a name="getQuerySel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQuerySel</h4>
<pre>public&nbsp;int&nbsp;getQuerySel()</pre>
<div class="block">获取query命令的sel参数(sel parameter of the query command)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0:All,  1:All,  2:~SL  ,3:SL</dd>
</dl>
</li>
</ul>
<a name="setQuerySel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQuerySel</h4>
<pre>public&nbsp;void&nbsp;setQuerySel(int&nbsp;querySel)</pre>
<div class="block">设置query命令的sel参数(sel parameter of the query command)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>querySel</code> - 0:All,  1:All,  2:~SL  ,3:SL</dd>
</dl>
</li>
</ul>
<a name="getQuerySession--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQuerySession</h4>
<pre>public&nbsp;int&nbsp;getQuerySession()</pre>
<div class="block">获取query命令的session参数(session parameter of the query command)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0:S0, 1:S1, 2:S2, 3:S3</dd>
</dl>
</li>
</ul>
<a name="setQuerySession-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQuerySession</h4>
<pre>public&nbsp;void&nbsp;setQuerySession(int&nbsp;querySession)</pre>
<div class="block">设置query命令的session参数(session parameter of the query command)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>querySession</code> - 0:S0, 1:S1, 2:S2, 3:S3</dd>
</dl>
</li>
</ul>
<a name="getQueryTarget--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQueryTarget</h4>
<pre>public&nbsp;int&nbsp;getQueryTarget()</pre>
<div class="block">获取query命令的Target参数(Target parameter of the query command)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0:A, 1:B</dd>
</dl>
</li>
</ul>
<a name="setQueryTarget-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQueryTarget</h4>
<pre>public&nbsp;void&nbsp;setQueryTarget(int&nbsp;queryTarget)</pre>
<div class="block">设置 query命令的Target参数(Target parameter of the query command)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>queryTarget</code> - 0:A, 1:B</dd>
</dl>
</li>
</ul>
<a name="getLinkFrequency--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLinkFrequency</h4>
<pre>public&nbsp;int&nbsp;getLinkFrequency()</pre>
<div class="block">Link Frequency 设置(Link Frequency setting)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0:40KHz, 1:160KHz, 2:200KHz, 3:250KHz, 4:300KHz, 5:320KHz, 6:400KHz, 7:640KHz</dd>
</dl>
</li>
</ul>
<a name="setLinkFrequency-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLinkFrequency</h4>
<pre>public&nbsp;void&nbsp;setLinkFrequency(int&nbsp;linkFrequency)</pre>
<div class="block">Link Frequency 设置(Link Frequency setting)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>linkFrequency</code> - 0:40KHz, 1:160KHz, 2:200KHz, 3:250KHz, 4:300KHz, 5:320KHz, 6:400KHz, 7:640KHz</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>toString</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Gen2Entity.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/GPIOInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/Gen2Entity.html" target="_top">Frames</a></li>
<li><a href="Gen2Entity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
