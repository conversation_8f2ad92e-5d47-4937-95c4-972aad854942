<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>BarcodeResult</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BarcodeResult";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":9,"i5":9,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BarcodeResult.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/BatteryEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/BarcodeResult.html" target="_top">Frames</a></li>
<li><a href="BarcodeResult.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.entity</div>
<h2 title="Class BarcodeResult" class="title">Class BarcodeResult</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.entity.BarcodeResult</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">BarcodeResult</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#A">A</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#B">B</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#C">C</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#D">D</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#E">E</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#F">F</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#G">G</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#H">H</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#J">J</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#K">K</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#L">L</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#M">M</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#N">N</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#P00">P00</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#P01">P01</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#P02">P02</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#P03">P03</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#P04">P04</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#P05">P05</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#P06">P06</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#P08">P08</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#P09">P09</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#P0A">P0A</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#P0B">P0B</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#P0H">P0H</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#P0X">P0X</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#R">R</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#S">S</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#T">T</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#U">U</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#V">V</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#X">X</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#Z">Z</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#build-byte:A-boolean-boolean-">build</a></span>(byte[]&nbsp;data,
     boolean&nbsp;isContainCodeId,
     boolean&nbsp;isContainSSIID)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#getBarcodeBytesData--">getBarcodeBytesData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#getBarcodeCodeID--">getBarcodeCodeID</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#getBarcodeSSIID--">getBarcodeSSIID</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#getBarcodeTypeByCodeId-java.lang.String-">getBarcodeTypeByCodeId</a></span>(java.lang.String&nbsp;CodeId)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#getBarcodeTypeBySSIID-int-">getBarcodeTypeBySSIID</a></span>(int&nbsp;ssiId)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#setBarcodeBytesData-byte:A-">setBarcodeBytesData</a></span>(byte[]&nbsp;barcodeBytesData)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#setBarcodeCodeID-java.lang.String-">setBarcodeCodeID</a></span>(java.lang.String&nbsp;barcodeCodeID)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html#setBarcodeSSIID-int-">setBarcodeSSIID</a></span>(int&nbsp;barcodeSSIID)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="A">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A</h4>
<pre>public static final&nbsp;java.lang.String A</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.A">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="B">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>B</h4>
<pre>public static final&nbsp;java.lang.String B</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.B">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C</h4>
<pre>public static final&nbsp;java.lang.String C</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.C">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="D">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>D</h4>
<pre>public static final&nbsp;java.lang.String D</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.D">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="E">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>E</h4>
<pre>public static final&nbsp;java.lang.String E</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.E">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="F">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>F</h4>
<pre>public static final&nbsp;java.lang.String F</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.F">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="G">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>G</h4>
<pre>public static final&nbsp;java.lang.String G</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.G">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="H">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>H</h4>
<pre>public static final&nbsp;java.lang.String H</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.H">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="J">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>J</h4>
<pre>public static final&nbsp;java.lang.String J</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.J">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="K">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>K</h4>
<pre>public static final&nbsp;java.lang.String K</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.K">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="L">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>L</h4>
<pre>public static final&nbsp;java.lang.String L</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.L">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="M">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>M</h4>
<pre>public static final&nbsp;java.lang.String M</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.M">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="N">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>N</h4>
<pre>public static final&nbsp;java.lang.String N</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.N">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="R">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>R</h4>
<pre>public static final&nbsp;java.lang.String R</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.R">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="S">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>S</h4>
<pre>public static final&nbsp;java.lang.String S</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.S">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="T">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>T</h4>
<pre>public static final&nbsp;java.lang.String T</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.T">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="U">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>U</h4>
<pre>public static final&nbsp;java.lang.String U</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.U">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="V">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>V</h4>
<pre>public static final&nbsp;java.lang.String V</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.V">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="X">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>X</h4>
<pre>public static final&nbsp;java.lang.String X</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.X">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Z">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Z</h4>
<pre>public static final&nbsp;java.lang.String Z</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.Z">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P00">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P00</h4>
<pre>public static final&nbsp;java.lang.String P00</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.P00">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P01">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P01</h4>
<pre>public static final&nbsp;java.lang.String P01</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.P01">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P02">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P02</h4>
<pre>public static final&nbsp;java.lang.String P02</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.P02">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P03">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P03</h4>
<pre>public static final&nbsp;java.lang.String P03</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.P03">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P04">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P04</h4>
<pre>public static final&nbsp;java.lang.String P04</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.P04">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P05">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P05</h4>
<pre>public static final&nbsp;java.lang.String P05</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.P05">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P06">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P06</h4>
<pre>public static final&nbsp;java.lang.String P06</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.P06">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P08">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P08</h4>
<pre>public static final&nbsp;java.lang.String P08</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.P08">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P09">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P09</h4>
<pre>public static final&nbsp;java.lang.String P09</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.P09">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P0A">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P0A</h4>
<pre>public static final&nbsp;java.lang.String P0A</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.P0A">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P0B">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P0B</h4>
<pre>public static final&nbsp;java.lang.String P0B</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.P0B">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P0H">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P0H</h4>
<pre>public static final&nbsp;java.lang.String P0H</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.P0H">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P0X">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>P0X</h4>
<pre>public static final&nbsp;java.lang.String P0X</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.entity.BarcodeResult.P0X">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getBarcodeCodeID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarcodeCodeID</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBarcodeCodeID()</pre>
</li>
</ul>
<a name="setBarcodeCodeID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeCodeID</h4>
<pre>public&nbsp;void&nbsp;setBarcodeCodeID(java.lang.String&nbsp;barcodeCodeID)</pre>
</li>
</ul>
<a name="getBarcodeBytesData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarcodeBytesData</h4>
<pre>public&nbsp;byte[]&nbsp;getBarcodeBytesData()</pre>
</li>
</ul>
<a name="setBarcodeBytesData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeBytesData</h4>
<pre>public&nbsp;void&nbsp;setBarcodeBytesData(byte[]&nbsp;barcodeBytesData)</pre>
</li>
</ul>
<a name="getBarcodeSSIID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarcodeSSIID</h4>
<pre>public&nbsp;int&nbsp;getBarcodeSSIID()</pre>
</li>
</ul>
<a name="setBarcodeSSIID-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeSSIID</h4>
<pre>public&nbsp;void&nbsp;setBarcodeSSIID(int&nbsp;barcodeSSIID)</pre>
</li>
</ul>
<a name="build-byte:A-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>build</h4>
<pre>public static&nbsp;<a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity">BarcodeResult</a>&nbsp;build(byte[]&nbsp;data,
                                  boolean&nbsp;isContainCodeId,
                                  boolean&nbsp;isContainSSIID)</pre>
</li>
</ul>
<a name="getBarcodeTypeByCodeId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarcodeTypeByCodeId</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getBarcodeTypeByCodeId(java.lang.String&nbsp;CodeId)</pre>
</li>
</ul>
<a name="getBarcodeTypeBySSIID-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getBarcodeTypeBySSIID</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getBarcodeTypeBySSIID(int&nbsp;ssiId)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BarcodeResult.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/BatteryEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/BarcodeResult.html" target="_top">Frames</a></li>
<li><a href="BarcodeResult.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
