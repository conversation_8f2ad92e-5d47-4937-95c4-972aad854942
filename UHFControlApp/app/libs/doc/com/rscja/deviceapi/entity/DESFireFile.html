<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>DESFireFile</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DESFireFile";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DESFireFile.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/BatteryEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/DESFireFile.html" target="_top">Frames</a></li>
<li><a href="DESFireFile.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.entity</div>
<h2 title="Class DESFireFile" class="title">Class DESFireFile</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.entity.DESFireFile</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DESFireFile</span>
extends java.lang.Object</pre>
<div class="block">DESFire文件简单数据实体<br>
 DESFire file simple data entity<br></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#DESFireFile-int-com.rscja.deviceapi.RFIDWithISO14443A.DESFireFileTypekEnum-com.rscja.deviceapi.RFIDWithISO14443A.DESFireEncryptionTypekEnum-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">DESFireFile</a></span>(int&nbsp;fileNo,
           <a href="../../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireFileTypekEnum</a>&nbsp;fileType,
           <a href="../../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a>&nbsp;encryptionType,
           java.lang.String&nbsp;readPermissions,
           java.lang.String&nbsp;writePermissions,
           java.lang.String&nbsp;readWritePermissions,
           java.lang.String&nbsp;updatePermissions)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#getData--">getData</a></span>()</code>
<div class="block">获取数据，仅对数据文件类型有效<br>
 acquire data, it is only valid for data file type only<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#getEncryptionType--">getEncryptionType</a></span>()</code>
<div class="block">获取加密方式<br>
 acquire encryption method<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#getFileNo--">getFileNo</a></span>()</code>
<div class="block">获取文件号<br>
 acquire file number<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#getFileSize--">getFileSize</a></span>()</code>
<div class="block">获取文件大小，仅对数据文件类型有效<br>
 acquire file size, it is valid for data file type only<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireFileTypekEnum</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#getFileType--">getFileType</a></span>()</code>
<div class="block">获取文件类型<br>
 acquire file type<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#getMaxValue--">getMaxValue</a></span>()</code>
<div class="block">获取最大值，仅对值文件类型有效<br>
 acquire maximum value, it is valid for value file type only<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#getMinValue--">getMinValue</a></span>()</code>
<div class="block">获取最小值，仅对值文件类型有效<br>
 acquire minimum value, it is valid for value file type only<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#getReadPermissions--">getReadPermissions</a></span>()</code>
<div class="block">获取读取权限<br>
 acquire read authorization<br></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#getReadWritePermissions--">getReadWritePermissions</a></span>()</code>
<div class="block">获取读写权限<br>
 acquire read-write authorization<br></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#getUpdatePermissions--">getUpdatePermissions</a></span>()</code>
<div class="block">获取修改权限<br>
 acquire modify authorization<br></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#getWritePermissions--">getWritePermissions</a></span>()</code>
<div class="block">获取写权限<br>
 acquire write authorization<br></div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#setData-java.lang.String-">setData</a></span>(java.lang.String&nbsp;hexData)</code>
<div class="block">设置数据，仅对数据文件类型有效<br>
 setup data, it is valid for value file type only<br></div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#setFileSize-int-">setFileSize</a></span>(int&nbsp;fileSize)</code>
<div class="block">设置文件大小，仅对数据文件类型有效<br>
 setup file size, it is valid for data file type only<br></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#setMaxValue-int-">setMaxValue</a></span>(int&nbsp;maxValue)</code>
<div class="block">设置最小值，仅对值文件类型有效<br>
 setup minimum value, it is valid for value file type only<br></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html#setMinValue-int-">setMinValue</a></span>(int&nbsp;minValue)</code>
<div class="block">设置最小值，仅对值文件类型有效<br>
 setup minimum value, it is valid for value file type only<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DESFireFile-int-com.rscja.deviceapi.RFIDWithISO14443A.DESFireFileTypekEnum-com.rscja.deviceapi.RFIDWithISO14443A.DESFireEncryptionTypekEnum-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DESFireFile</h4>
<pre>public&nbsp;DESFireFile(int&nbsp;fileNo,
                   <a href="../../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireFileTypekEnum</a>&nbsp;fileType,
                   <a href="../../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a>&nbsp;encryptionType,
                   java.lang.String&nbsp;readPermissions,
                   java.lang.String&nbsp;writePermissions,
                   java.lang.String&nbsp;readWritePermissions,
                   java.lang.String&nbsp;updatePermissions)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getFileNo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileNo</h4>
<pre>public&nbsp;int&nbsp;getFileNo()</pre>
<div class="block">获取文件号<br>
 acquire file number<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getFileType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileType</h4>
<pre>public&nbsp;<a href="../../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireFileTypekEnum</a>&nbsp;getFileType()</pre>
<div class="block">获取文件类型<br>
 acquire file type<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getFileSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileSize</h4>
<pre>public&nbsp;int&nbsp;getFileSize()</pre>
<div class="block">获取文件大小，仅对数据文件类型有效<br>
 acquire file size, it is valid for data file type only<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setFileSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFileSize</h4>
<pre>public&nbsp;void&nbsp;setFileSize(int&nbsp;fileSize)</pre>
<div class="block">设置文件大小，仅对数据文件类型有效<br>
 setup file size, it is valid for data file type only<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileSize</code> - </dd>
</dl>
</li>
</ul>
<a name="getEncryptionType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEncryptionType</h4>
<pre>public&nbsp;<a href="../../../../com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a>&nbsp;getEncryptionType()</pre>
<div class="block">获取加密方式<br>
 acquire encryption method<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getReadPermissions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReadPermissions</h4>
<pre>public&nbsp;java.lang.String&nbsp;getReadPermissions()</pre>
<div class="block">获取读取权限<br>
 acquire read authorization<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getWritePermissions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWritePermissions</h4>
<pre>public&nbsp;java.lang.String&nbsp;getWritePermissions()</pre>
<div class="block">获取写权限<br>
 acquire write authorization<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getReadWritePermissions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReadWritePermissions</h4>
<pre>public&nbsp;java.lang.String&nbsp;getReadWritePermissions()</pre>
<div class="block">获取读写权限<br>
 acquire read-write authorization<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getUpdatePermissions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUpdatePermissions</h4>
<pre>public&nbsp;java.lang.String&nbsp;getUpdatePermissions()</pre>
<div class="block">获取修改权限<br>
 acquire modify authorization<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setMinValue-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinValue</h4>
<pre>public&nbsp;void&nbsp;setMinValue(int&nbsp;minValue)</pre>
<div class="block">设置最小值，仅对值文件类型有效<br>
 setup minimum value, it is valid for value file type only<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minValue</code> - </dd>
</dl>
</li>
</ul>
<a name="setMaxValue-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxValue</h4>
<pre>public&nbsp;void&nbsp;setMaxValue(int&nbsp;maxValue)</pre>
<div class="block">设置最小值，仅对值文件类型有效<br>
 setup minimum value, it is valid for value file type only<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>maxValue</code> - </dd>
</dl>
</li>
</ul>
<a name="getMinValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinValue</h4>
<pre>public&nbsp;int&nbsp;getMinValue()</pre>
<div class="block">获取最小值，仅对值文件类型有效<br>
 acquire minimum value, it is valid for value file type only<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getMaxValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxValue</h4>
<pre>public&nbsp;int&nbsp;getMaxValue()</pre>
<div class="block">获取最大值，仅对值文件类型有效<br>
 acquire maximum value, it is valid for value file type only<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getData</h4>
<pre>public&nbsp;java.lang.String&nbsp;getData()</pre>
<div class="block">获取数据，仅对数据文件类型有效<br>
 acquire data, it is only valid for data file type only<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setData-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setData</h4>
<pre>public&nbsp;void&nbsp;setData(java.lang.String&nbsp;hexData)</pre>
<div class="block">设置数据，仅对数据文件类型有效<br>
 setup data, it is valid for value file type only<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>hexData</code> - </dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DESFireFile.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/BatteryEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/DESFireFile.html" target="_top">Frames</a></li>
<li><a href="DESFireFile.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
