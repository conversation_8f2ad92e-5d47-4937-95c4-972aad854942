<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>ScannerParameterEntity</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ScannerParameterEntity";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ScannerParameterEntity.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/ScannerParameterEntity.html" target="_top">Frames</a></li>
<li><a href="ScannerParameterEntity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.entity</div>
<h2 title="Class ScannerParameterEntity" class="title">Class ScannerParameterEntity</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.entity.ScannerParameterEntity</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ScannerParameterEntity</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.Builder.html" title="class in com.rscja.deviceapi.entity">ScannerParameterEntity.Builder</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#ScannerParameterEntity--">ScannerParameterEntity</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getBarcodeContinuousScanIntervalTime--">getBarcodeContinuousScanIntervalTime</a></span>()</code>
<div class="block">连续扫描间隔时间<br>
continuous scanning intervals</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getBarcodeContinuousScanTimeOut--">getBarcodeContinuousScanTimeOut</a></span>()</code>
<div class="block">连续扫描超时时间<br>
 continuous scanning time-out</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getBarcodeFormat--">getBarcodeFormat</a></span>()</code>
<div class="block">条码编码格式<br>
 barcode decoding format</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getFilterChars--">getFilterChars</a></span>()</code>
<div class="block">过滤字符串<br>
 Filter string</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getInterceptTrimLeft--">getInterceptTrimLeft</a></span>()</code>
<div class="block">截取左边字符串<br>
 Capture string on left</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getInterceptTrimRight--">getInterceptTrimRight</a></span>()</code>
<div class="block">截取右边字符串<br>
 capture string on right</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getOutputMode--">getOutputMode</a></span>()</code>
<div class="block">输出模式<br>
 Output mode</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getPrefix--">getPrefix</a></span>()</code>
<div class="block">Prefix</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getScanResultBroadcastAction--">getScanResultBroadcastAction</a></span>()</code>
<div class="block">Broadcast Action</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getScanResultBroadcastKey--">getScanResultBroadcastKey</a></span>()</code>
<div class="block">Broadcast Key</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#getSuffix--">getSuffix</a></span>()</code>
<div class="block">suffix</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#isBarcodeContinuousScan--">isBarcodeContinuousScan</a></span>()</code>
<div class="block">扫描头连续扫描模式
 Scanning head Continuous scanning mode</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#isEnableBarcode2D--">isEnableBarcode2D</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#isEnableScanner--">isEnableScanner</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#isEnter--">isEnter</a></span>()</code>
<div class="block">是否启用回车<br>
 Enter ON/OFF</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#isFailureSound--">isFailureSound</a></span>()</code>
<div class="block">扫描失败是否播放提示音<br>
 scan failure sound ON/OFF</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#isReleaseScanKeyOnStartScan--">isReleaseScanKeyOnStartScan</a></span>()</code>
<div class="block">ScanOnRelease</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#isReleaseScanKeyOnStopScan--">isReleaseScanKeyOnStopScan</a></span>()</code>
<div class="block">松开扫描按键停止扫描<br>
 Stop scan after release scan button</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#isScanFailureBroadcast--">isScanFailureBroadcast</a></span>()</code>
<div class="block">扫描失败是否发送广播,接收广播的action和扫描成功的action是同一个<br>
 Send broadcast when scan failure</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#isSuccessSound--">isSuccessSound</a></span>()</code>
<div class="block">扫描成功是否播放提示音<br>
 Scan success sound ON/OFF</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#isTab--">isTab</a></span>()</code>
<div class="block">是否启用TAB<br>
 TAB ON/OFF</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#isVibrate--">isVibrate</a></span>()</code>
<div class="block">扫描成功是否震动提示<br>
 scan success vibrate ON/OFF</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ScannerParameterEntity--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ScannerParameterEntity</h4>
<pre>public&nbsp;ScannerParameterEntity()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isReleaseScanKeyOnStopScan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isReleaseScanKeyOnStopScan</h4>
<pre>public&nbsp;boolean&nbsp;isReleaseScanKeyOnStopScan()</pre>
<div class="block">松开扫描按键停止扫描<br>
 Stop scan after release scan button</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="isVibrate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isVibrate</h4>
<pre>public&nbsp;boolean&nbsp;isVibrate()</pre>
<div class="block">扫描成功是否震动提示<br>
 scan success vibrate ON/OFF</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="isSuccessSound--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSuccessSound</h4>
<pre>public&nbsp;boolean&nbsp;isSuccessSound()</pre>
<div class="block">扫描成功是否播放提示音<br>
 Scan success sound ON/OFF</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="isFailureSound--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFailureSound</h4>
<pre>public&nbsp;boolean&nbsp;isFailureSound()</pre>
<div class="block">扫描失败是否播放提示音<br>
 scan failure sound ON/OFF</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="isEnter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnter</h4>
<pre>public&nbsp;boolean&nbsp;isEnter()</pre>
<div class="block">是否启用回车<br>
 Enter ON/OFF</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="isTab--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTab</h4>
<pre>public&nbsp;boolean&nbsp;isTab()</pre>
<div class="block">是否启用TAB<br>
 TAB ON/OFF</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getOutputMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutputMode</h4>
<pre>public&nbsp;int&nbsp;getOutputMode()</pre>
<div class="block">输出模式<br>
 Output mode</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>outputMode 0:扫描到光标位置(scan content to cursor)    1:剪切板(clipboard)   2:广播(broadcast)    3:模拟键盘(analog keyboard)</dd>
</dl>
</li>
</ul>
<a name="getScanResultBroadcastAction--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScanResultBroadcastAction</h4>
<pre>public&nbsp;java.lang.String&nbsp;getScanResultBroadcastAction()</pre>
<div class="block">Broadcast Action</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getScanResultBroadcastKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScanResultBroadcastKey</h4>
<pre>public&nbsp;java.lang.String&nbsp;getScanResultBroadcastKey()</pre>
<div class="block">Broadcast Key</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getSuffix--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSuffix</h4>
<pre>public&nbsp;java.lang.String&nbsp;getSuffix()</pre>
<div class="block">suffix</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getPrefix--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrefix</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPrefix()</pre>
<div class="block">Prefix</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getInterceptTrimLeft--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInterceptTrimLeft</h4>
<pre>public&nbsp;int&nbsp;getInterceptTrimLeft()</pre>
<div class="block">截取左边字符串<br>
 Capture string on left</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getInterceptTrimRight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInterceptTrimRight</h4>
<pre>public&nbsp;int&nbsp;getInterceptTrimRight()</pre>
<div class="block">截取右边字符串<br>
 capture string on right</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getFilterChars--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFilterChars</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFilterChars()</pre>
<div class="block">过滤字符串<br>
 Filter string</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getBarcodeFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarcodeFormat</h4>
<pre>public&nbsp;int&nbsp;getBarcodeFormat()</pre>
<div class="block">条码编码格式<br>
 barcode decoding format</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0: Default   1: ASCII    2:GB2312   3:UTF8   4:Unicode    10:GBK,    11:GB18030 <br></dd>
</dl>
</li>
</ul>
<a name="isBarcodeContinuousScan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBarcodeContinuousScan</h4>
<pre>public&nbsp;boolean&nbsp;isBarcodeContinuousScan()</pre>
<div class="block">扫描头连续扫描模式
 Scanning head Continuous scanning mode</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getBarcodeContinuousScanIntervalTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarcodeContinuousScanIntervalTime</h4>
<pre>public&nbsp;int&nbsp;getBarcodeContinuousScanIntervalTime()</pre>
<div class="block">连续扫描间隔时间<br>
continuous scanning intervals</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getBarcodeContinuousScanTimeOut--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarcodeContinuousScanTimeOut</h4>
<pre>public&nbsp;int&nbsp;getBarcodeContinuousScanTimeOut()</pre>
<div class="block">连续扫描超时时间<br>
 continuous scanning time-out</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="isScanFailureBroadcast--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isScanFailureBroadcast</h4>
<pre>public&nbsp;boolean&nbsp;isScanFailureBroadcast()</pre>
<div class="block">扫描失败是否发送广播,接收广播的action和扫描成功的action是同一个<br>
 Send broadcast when scan failure</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="isReleaseScanKeyOnStartScan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isReleaseScanKeyOnStartScan</h4>
<pre>public&nbsp;boolean&nbsp;isReleaseScanKeyOnStartScan()</pre>
<div class="block">ScanOnRelease</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="isEnableBarcode2D--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableBarcode2D</h4>
<pre>public&nbsp;boolean&nbsp;isEnableBarcode2D()</pre>
</li>
</ul>
<a name="isEnableScanner--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableScanner</h4>
<pre>public&nbsp;boolean&nbsp;isEnableScanner()</pre>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>toString</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ScannerParameterEntity.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/ScannerParameterEntity.html" target="_top">Frames</a></li>
<li><a href="ScannerParameterEntity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
