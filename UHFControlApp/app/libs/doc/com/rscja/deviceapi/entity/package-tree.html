<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>com.rscja.deviceapi.entity Class Hierarchy</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.rscja.deviceapi.entity Class Hierarchy";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/package-tree.html">Prev</a></li>
<li><a href="../../../../com/rscja/deviceapi/enums/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package com.rscja.deviceapi.entity</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">AnimalEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/AntennaConnectState.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">AntennaConnectState</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">AntennaPowerEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">AntennaState</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">BarcodeEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">BarcodeResult</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/BatteryEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">BatteryEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">DESFireFile</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Gen2Entity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/GPIOInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">GPIOInfo</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">GPIStateEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">GPOEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/HF14443RequestEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">HF14443RequestEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">HF15693RequestEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/HF15693RequestEntity.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">HF15693RequestEntity.Builder</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">InventoryModeEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/InventoryModeEntity.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">InventoryModeEntity.Builder</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/InventoryParameter.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">InventoryParameter</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/InventoryParameter.ResultData.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">InventoryParameter.ResultData</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/LowBatteryEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">LowBatteryEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/RadarLocationEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">RadarLocationEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/RadarLocationEntity.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">RadarLocationEntity.Builder</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">ReaderIPEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">ScannerParameterEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">ScannerParameterEntity.Builder</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">SimpleRFIDEntity</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">ISO15693Entity</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">TagInfoRule</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/TagLocationEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">TagLocationEntity</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/TagLocationInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">TagLocationInfo</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">UHFTAGInfo</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">UHFTAGInfo.ChipInfo</span></a> (implements java.lang.Cloneable)</li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">UHFTAGInfo.ChipInfo.Builder</span></a></li>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">WifiConfig</span></a></li>
</ul>
</li>
</ul>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li type="circle">com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/HF15693RequestEntity.TagType.html" title="enum in com.rscja.deviceapi.entity"><span class="typeNameLink">HF15693RequestEntity.TagType</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/package-tree.html">Prev</a></li>
<li><a href="../../../../com/rscja/deviceapi/enums/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
