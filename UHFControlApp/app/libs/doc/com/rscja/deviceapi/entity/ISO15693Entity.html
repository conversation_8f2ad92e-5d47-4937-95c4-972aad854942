<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>ISO15693Entity</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ISO15693Entity";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ISO15693Entity.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/InventoryParameter.ResultData.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/LowBatteryEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/ISO15693Entity.html" target="_top">Frames</a></li>
<li><a href="ISO15693Entity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.entity</div>
<h2 title="Class ISO15693Entity" class="title">Class ISO15693Entity</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">com.rscja.deviceapi.entity.SimpleRFIDEntity</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.entity.ISO15693Entity</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ISO15693Entity</span>
extends <a href="../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a></pre>
<div class="block">ISO15693协议数据实体<br>
 ISO15639 protocol data entity<br></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>liuruifeng</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ISO15693Entity.html#ISO15693Entity-java.lang.String-java.lang.String-">ISO15693Entity</a></span>(java.lang.String&nbsp;id,
              java.lang.String&nbsp;type)</code>
<div class="block">构造函数<br>
 Constructor<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ISO15693Entity.html#ISO15693Entity-java.lang.String-java.lang.String-char:A-">ISO15693Entity</a></span>(java.lang.String&nbsp;id,
              java.lang.String&nbsp;type,
              char[]&nbsp;originalUID)</code>
<div class="block">构造函数<br>
 Constructor<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ISO15693Entity.html#ISO15693Entity-java.lang.String-java.lang.String-char:A-byte:A-">ISO15693Entity</a></span>(java.lang.String&nbsp;id,
              java.lang.String&nbsp;type,
              char[]&nbsp;originalUID,
              byte[]&nbsp;id_byte)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ISO15693Entity.html#ISO15693Entity-java.lang.String-java.lang.String-char:A-java.lang.String-java.lang.String-">ISO15693Entity</a></span>(java.lang.String&nbsp;id,
              java.lang.String&nbsp;type,
              char[]&nbsp;originalUID,
              java.lang.String&nbsp;afi,
              java.lang.String&nbsp;desfid)</code>
<div class="block">构造函数<br>
 Constructor<br></div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ISO15693Entity.html#getAFI--">getAFI</a></span>()</code>
<div class="block">获取AFI值<br>
 acquire AFI value<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ISO15693Entity.html#getDESFID--">getDESFID</a></span>()</code>
<div class="block">获取DESFID值<br>
 acquire DESFID value<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ISO15693Entity.html#getOriginalUID--">getOriginalUID</a></span>()</code>
<div class="block">获取标签原始的UID<br>
 acquire original UID of tag<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ISO15693Entity.html#setAFI-java.lang.String-">setAFI</a></span>(java.lang.String&nbsp;aFI)</code>
<div class="block">设置AFI值<br>
 setup AFI value<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ISO15693Entity.html#setDESFID-java.lang.String-">setDESFID</a></span>(java.lang.String&nbsp;dESFID)</code>
<div class="block">设置DESFID值<br>
 setup DESFID value<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ISO15693Entity.html#setOriginalUID-char:A-">setOriginalUID</a></span>(char[]&nbsp;originalUID)</code>
<div class="block">设置标签原始的UID<br>
 setup original UID of tag<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.entity.SimpleRFIDEntity">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.deviceapi.entity.<a href="../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a></h3>
<code><a href="../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html#getData--">getData</a>, <a href="../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html#getId--">getId</a>, <a href="../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html#getIdBytes--">getIdBytes</a>, <a href="../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html#getType--">getType</a>, <a href="../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html#setData-java.lang.String-">setData</a>, <a href="../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html#toString--">toString</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ISO15693Entity-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693Entity</h4>
<pre>public&nbsp;ISO15693Entity(java.lang.String&nbsp;id,
                      java.lang.String&nbsp;type)</pre>
<div class="block">构造函数<br>
 Constructor<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - 标签ID<br>
             tag ID<br></dd>
<dd><code>type</code> - 标签类型<br>
             tag type<br></dd>
</dl>
</li>
</ul>
<a name="ISO15693Entity-java.lang.String-java.lang.String-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693Entity</h4>
<pre>public&nbsp;ISO15693Entity(java.lang.String&nbsp;id,
                      java.lang.String&nbsp;type,
                      char[]&nbsp;originalUID)</pre>
<div class="block">构造函数<br>
 Constructor<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - 标签ID<br>
                    tag ID<br></dd>
<dd><code>type</code> - 标签类型<br>
                    tag type<br></dd>
<dd><code>originalUID</code> - 标签原始的UID<br>
                    tag original UID<br></dd>
</dl>
</li>
</ul>
<a name="ISO15693Entity-java.lang.String-java.lang.String-char:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693Entity</h4>
<pre>public&nbsp;ISO15693Entity(java.lang.String&nbsp;id,
                      java.lang.String&nbsp;type,
                      char[]&nbsp;originalUID,
                      byte[]&nbsp;id_byte)</pre>
</li>
</ul>
<a name="ISO15693Entity-java.lang.String-java.lang.String-char:A-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ISO15693Entity</h4>
<pre>public&nbsp;ISO15693Entity(java.lang.String&nbsp;id,
                      java.lang.String&nbsp;type,
                      char[]&nbsp;originalUID,
                      java.lang.String&nbsp;afi,
                      java.lang.String&nbsp;desfid)</pre>
<div class="block">构造函数<br>
 Constructor<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - 标签ID<br>
                    tag ID<br></dd>
<dd><code>type</code> - 标签类型<br>
                    tag type<br></dd>
<dd><code>originalUID</code> - 标签原始的UID<br>
                    tag original UID<br></dd>
<dd><code>afi</code> - afi值<br>
                    afi value<br></dd>
<dd><code>desfid</code> - desfid值<br>
                    desfid value<br></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getOriginalUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOriginalUID</h4>
<pre>public&nbsp;char[]&nbsp;getOriginalUID()</pre>
<div class="block">获取标签原始的UID<br>
 acquire original UID of tag<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>original UID of tag<br></dd>
</dl>
</li>
</ul>
<a name="setOriginalUID-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOriginalUID</h4>
<pre>public&nbsp;void&nbsp;setOriginalUID(char[]&nbsp;originalUID)</pre>
<div class="block">设置标签原始的UID<br>
 setup original UID of tag<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>originalUID</code> - 标签原始的UID<br>
                    tag original UID<br></dd>
</dl>
</li>
</ul>
<a name="getAFI--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAFI</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAFI()</pre>
<div class="block">获取AFI值<br>
 acquire AFI value<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>AFI value<br></dd>
</dl>
</li>
</ul>
<a name="setAFI-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAFI</h4>
<pre>public&nbsp;void&nbsp;setAFI(java.lang.String&nbsp;aFI)</pre>
<div class="block">设置AFI值<br>
 setup AFI value<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>aFI</code> - AFI值<br>
            AFI value<br></dd>
</dl>
</li>
</ul>
<a name="getDESFID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDESFID</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDESFID()</pre>
<div class="block">获取DESFID值<br>
 acquire DESFID value<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>DESFID value<br></dd>
</dl>
</li>
</ul>
<a name="setDESFID-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setDESFID</h4>
<pre>public&nbsp;void&nbsp;setDESFID(java.lang.String&nbsp;dESFID)</pre>
<div class="block">设置DESFID值<br>
 setup DESFID value<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dESFID</code> - DESFID值<br>
               DESFID value<br></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ISO15693Entity.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/InventoryParameter.ResultData.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/LowBatteryEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/ISO15693Entity.html" target="_top">Frames</a></li>
<li><a href="ISO15693Entity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
