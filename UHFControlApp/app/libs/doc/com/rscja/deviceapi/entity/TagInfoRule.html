<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>TagInfoRule</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TagInfoRule";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TagInfoRule.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/TagLocationEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/TagInfoRule.html" target="_top">Frames</a></li>
<li><a href="TagInfoRule.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.entity</div>
<h2 title="Class TagInfoRule" class="title">Class TagInfoRule</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.entity.TagInfoRule</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">TagInfoRule</span>
extends java.lang.Object</pre>
<div class="block">盘点规则</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html#TagInfoRule--">TagInfoRule</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html#getInventoryModeEntity--">getInventoryModeEntity</a></span>()</code>
<div class="block">获取盘点模式</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/InventoryParameter.html" title="class in com.rscja.deviceapi.entity">InventoryParameter</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html#getInventoryParameter--">getInventoryParameter</a></span>()</code>
<div class="block">获取盘点参数</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html#isContainAnt--">isContainAnt</a></span>()</code>
<div class="block">是否包含天线</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html#setContainAnt-boolean-">setContainAnt</a></span>(boolean&nbsp;containAnt)</code>
<div class="block">设置标签是否包含天线信息</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html#setInventoryModeEntity-com.rscja.deviceapi.entity.InventoryModeEntity-">setInventoryModeEntity</a></span>(<a href="../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;inventoryModeEntity)</code>
<div class="block">设置盘点模式</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html#setInventoryParameter-com.rscja.deviceapi.entity.InventoryParameter-">setInventoryParameter</a></span>(<a href="../../../../com/rscja/deviceapi/entity/InventoryParameter.html" title="class in com.rscja.deviceapi.entity">InventoryParameter</a>&nbsp;inventoryParameter)</code>
<div class="block">设置盘点标签的参数信息</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TagInfoRule--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TagInfoRule</h4>
<pre>public&nbsp;TagInfoRule()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isContainAnt--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isContainAnt</h4>
<pre>public&nbsp;boolean&nbsp;isContainAnt()</pre>
<div class="block">是否包含天线</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:包含： false:不包含</dd>
</dl>
</li>
</ul>
<a name="setContainAnt-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContainAnt</h4>
<pre>public&nbsp;<a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a>&nbsp;setContainAnt(boolean&nbsp;containAnt)</pre>
<div class="block">设置标签是否包含天线信息</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>containAnt</code> - true:包含： false:不包含</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>盘点规则对象</dd>
</dl>
</li>
</ul>
<a name="getInventoryParameter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInventoryParameter</h4>
<pre>public&nbsp;<a href="../../../../com/rscja/deviceapi/entity/InventoryParameter.html" title="class in com.rscja.deviceapi.entity">InventoryParameter</a>&nbsp;getInventoryParameter()</pre>
<div class="block">获取盘点参数</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>盘点规则对象</dd>
</dl>
</li>
</ul>
<a name="setInventoryParameter-com.rscja.deviceapi.entity.InventoryParameter-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInventoryParameter</h4>
<pre>public&nbsp;<a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a>&nbsp;setInventoryParameter(<a href="../../../../com/rscja/deviceapi/entity/InventoryParameter.html" title="class in com.rscja.deviceapi.entity">InventoryParameter</a>&nbsp;inventoryParameter)</pre>
<div class="block">设置盘点标签的参数信息</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inventoryParameter</code> - 标签的参数信息</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>盘点规则对象</dd>
</dl>
</li>
</ul>
<a name="getInventoryModeEntity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInventoryModeEntity</h4>
<pre>public&nbsp;<a href="../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;getInventoryModeEntity()</pre>
<div class="block">获取盘点模式</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>InventoryModeEntity</dd>
</dl>
</li>
</ul>
<a name="setInventoryModeEntity-com.rscja.deviceapi.entity.InventoryModeEntity-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setInventoryModeEntity</h4>
<pre>public&nbsp;<a href="../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a>&nbsp;setInventoryModeEntity(<a href="../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;inventoryModeEntity)</pre>
<div class="block">设置盘点模式</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inventoryModeEntity</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>盘点规则对象</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TagInfoRule.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/TagLocationEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/TagInfoRule.html" target="_top">Frames</a></li>
<li><a href="TagInfoRule.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
