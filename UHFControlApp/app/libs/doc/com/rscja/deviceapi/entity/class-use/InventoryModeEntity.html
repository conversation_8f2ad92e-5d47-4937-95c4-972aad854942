<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Class com.rscja.deviceapi.entity.InventoryModeEntity</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.rscja.deviceapi.entity.InventoryModeEntity";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/deviceapi/entity/class-use/InventoryModeEntity.html" target="_top">Frames</a></li>
<li><a href="InventoryModeEntity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.rscja.deviceapi.entity.InventoryModeEntity" class="title">Uses of Class<br>com.rscja.deviceapi.entity.InventoryModeEntity</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.custom">com.rscja.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.entity">com.rscja.deviceapi.entity</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.interfaces">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.deviceapi">com.rscja.team.mtk.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.ble">com.rscja.team.qcom.ble</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.rscja.custom">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a> in <a href="../../../../../com/rscja/custom/package-summary.html">com.rscja.custom</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/custom/package-summary.html">com.rscja.custom</a> that return <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">UHFXSAPI.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFXSAPI.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a> in <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> that return <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA8NetWork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>
<div class="block">获取当前设置的盘点模式 (Get the currently set inventory mode)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUSB.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUart.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>
<div class="block">获取当前循环盘点的模式(EPC或者EPC+TID或者EPC+TID+USER)<br>
 Acquire current scan mode(EPC or EPC+TID or EPC+TID+USER)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUsbToUart.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">BleDevice.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/BleDevice.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>
<div class="block">获取当前循环盘点的模式(EPC或者EPC+TID或者EPC+TID+USER)<br>
 Acquire current scan mode(EPC or EPC+TID or EPC+TID+USER)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxNetwork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA4NetWork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>
<div class="block">获取当前设置的盘点模式 (Get the currently set inventory mode)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFBLE.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFBLE.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>
<div class="block">设置R6工作模式<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUART.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA4RS232.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>
<div class="block">获取当前设置的盘点模式 (Get the currently set inventory mode)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA8RS232.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>
<div class="block">获取当前设置的盘点模式 (Get the currently set inventory mode)</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA8NetWork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;entity)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA4NetWork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;entity)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">UhfBase.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/UhfBase.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;entity)</code>
<div class="block">设置循环盘点同时读取 EPC、TID、USER 模式  <br>
 Setup auto scan to acquire EPC, TID, User mode</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFBLE.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFBLE.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;entity)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUART.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;entity)</code>
<div class="block">设置循环盘点同时读取 EPC、TID、USER 模式  <br>
 Setup auto scan to acquire EPC, TID, User mode</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA4RS232.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;entity)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA8RS232.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;entity)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.entity">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a> in <a href="../../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> that return <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">InventoryModeEntity.Builder.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.Builder.html#build--">build</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">TagInfoRule.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/entity/TagInfoRule.html#getInventoryModeEntity--">getInventoryModeEntity</a></span>()</code>
<div class="block">获取盘点模式</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a></code></td>
<td class="colLast"><span class="typeNameLabel">TagInfoRule.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/entity/TagInfoRule.html#setInventoryModeEntity-com.rscja.deviceapi.entity.InventoryModeEntity-">setInventoryModeEntity</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;inventoryModeEntity)</code>
<div class="block">设置盘点模式</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.interfaces">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a> in <a href="../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> that return <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">IUHF.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>
<div class="block">获取当前设置的盘点模式 (Get the currently set inventory mode)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">IBleDevice.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IBleDevice.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">IUHF.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;entity)</code>
<div class="block">设置循环盘点同时读取 EPC、TID、USER 模式  <br>
 Setup auto scan to acquire EPC, TID, User mode</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a> in <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a> that return <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUART_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUART_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;entity)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.ble">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a> in <a href="../../../../../com/rscja/team/qcom/ble/package-summary.html">com.rscja.team.qcom.ble</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/ble/package-summary.html">com.rscja.team.qcom.ble</a> that return <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">EmptyUhfBle.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/ble/package-summary.html">com.rscja.team.qcom.ble</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFBLEN52_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html#setInventoryModeEntity-com.rscja.deviceapi.entity.InventoryModeEntity-">setInventoryModeEntity</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;inventoryModeEntity)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a> in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> that return <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUSB_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUsbToUart_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUart_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>
<div class="block">获取当前循环盘点的模式(EPC或者EPC+TID或者EPC+TID+USER)<br>
 Acquire current scan mode(EPC or EPC+TID or EPC+TID+USER)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUart2_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxNetwork_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUART_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFBLE_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>
<div class="block">设置R6工作模式<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><span class="typeNameLabel">BleDevice_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUART_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;entity)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFBLE_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;entity)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/deviceapi/entity/class-use/InventoryModeEntity.html" target="_top">Frames</a></li>
<li><a href="InventoryModeEntity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
