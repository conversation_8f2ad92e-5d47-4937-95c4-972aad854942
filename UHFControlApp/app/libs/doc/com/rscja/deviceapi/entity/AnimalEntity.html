<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>AnimalEntity</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AnimalEntity";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/AnimalEntity.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../com/rscja/deviceapi/entity/AntennaConnectState.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/AnimalEntity.html" target="_top">Frames</a></li>
<li><a href="AnimalEntity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.entity</div>
<h2 title="Class AnimalEntity" class="title">Class AnimalEntity</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.entity.AnimalEntity</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">AnimalEntity</span>
extends java.lang.Object</pre>
<div class="block">动物标签数据实体类<br>
 animal tag data entity<br></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>liuruifeng</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#AnimalEntity--">AnimalEntity</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#AnimalEntity-long-long-long-long-long-">AnimalEntity</a></span>(long&nbsp;nationalID,
            long&nbsp;countryID,
            long&nbsp;reserved,
            long&nbsp;dataBlock,
            long&nbsp;animalFlag)</code>
<div class="block">构造函数<br>
 Constructor<br></div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#getAnimalFlag--">getAnimalFlag</a></span>()</code>
<div class="block">获取动物标签的AnimalFlag<br>
 acquire AnimalFlag of animal tag<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#getCountryID--">getCountryID</a></span>()</code>
<div class="block">获取动物标签的国家代码<br>
 acquire national code of animal tag<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#getDataBlock--">getDataBlock</a></span>()</code>
<div class="block">获取动物标签的DataBlock<br>
 acquire DataBlock of animal tag<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#getIdData--">getIdData</a></span>()</code>
<div class="block">获取64bit的识别号数据，十六进制表示<br>
 acquire 64bit identification number, hexdecimal<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#getNationalID--">getNationalID</a></span>()</code>
<div class="block">获取动物标签的NationalID信息<br>
 acquire national infor of animal tag<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#getRawData--">getRawData</a></span>()</code>
<div class="block">获取原始数据，十六进制表示<br>
 acquire original data, hexdecimal<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#getReserved--">getReserved</a></span>()</code>
<div class="block">获取动物标签的Reserved信息<br>
 acquire Reserved infor of animal tag<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#setAnimalFlag-long-">setAnimalFlag</a></span>(long&nbsp;animalFlag)</code>
<div class="block">设置动物标签的AnimalFlag<br>
 setup AnimalFlag of animal tag<br></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#setCountryID-long-">setCountryID</a></span>(long&nbsp;countryID)</code>
<div class="block">设置动物标签的国家代码<br>
 setup national code of animal tag<br></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#setDataBlock-long-">setDataBlock</a></span>(long&nbsp;dataBlock)</code>
<div class="block">设置动物标签的DataBlock<br>
 setup DataBlock of animal tag<br></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#setIdData-java.lang.String-">setIdData</a></span>(java.lang.String&nbsp;idData)</code>
<div class="block">设置64bit的识别号数据，十六进制表示<br>
 setup 64bit identification number, hexdecimal<br></div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#setNationalID-long-">setNationalID</a></span>(long&nbsp;nationalID)</code>
<div class="block">设置动物标签的NationalID信息<br>
 setup NationalID infor of animal tag<br></div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#setRawData-java.lang.String-">setRawData</a></span>(java.lang.String&nbsp;rawData)</code>
<div class="block">设置原始数据<br>
 setup original data<br></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html#setReserved-long-">setReserved</a></span>(long&nbsp;reserved)</code>
<div class="block">设置动物标签的Reserved信息<br>
 setup Reserved infor of animal tag<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AnimalEntity-long-long-long-long-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AnimalEntity</h4>
<pre>public&nbsp;AnimalEntity(long&nbsp;nationalID,
                    long&nbsp;countryID,
                    long&nbsp;reserved,
                    long&nbsp;dataBlock,
                    long&nbsp;animalFlag)</pre>
<div class="block">构造函数<br>
 Constructor<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nationalID</code> - 动物标签的NationalID信息<br>
                   NationalID infor of animal tag<br></dd>
<dd><code>countryID</code> - 动物标签的国家代码<br>
                   national code of animal tag<br></dd>
<dd><code>reserved</code> - 动物标签的Reserved信息<br>
                   Reserved infor of animal tag<br></dd>
<dd><code>dataBlock</code> - 动物标签的DataBlock<br>
                   DataBlock of animal tag<br></dd>
<dd><code>animalFlag</code> - 动物标签的AnimalFlag<br>
                   AnimalFlag of animal tag<br></dd>
</dl>
</li>
</ul>
<a name="AnimalEntity--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AnimalEntity</h4>
<pre>public&nbsp;AnimalEntity()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getNationalID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNationalID</h4>
<pre>public&nbsp;long&nbsp;getNationalID()</pre>
<div class="block">获取动物标签的NationalID信息<br>
 acquire national infor of animal tag<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setNationalID-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNationalID</h4>
<pre>public&nbsp;void&nbsp;setNationalID(long&nbsp;nationalID)</pre>
<div class="block">设置动物标签的NationalID信息<br>
 setup NationalID infor of animal tag<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nationalID</code> - </dd>
</dl>
</li>
</ul>
<a name="getCountryID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCountryID</h4>
<pre>public&nbsp;long&nbsp;getCountryID()</pre>
<div class="block">获取动物标签的国家代码<br>
 acquire national code of animal tag<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setCountryID-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCountryID</h4>
<pre>public&nbsp;void&nbsp;setCountryID(long&nbsp;countryID)</pre>
<div class="block">设置动物标签的国家代码<br>
 setup national code of animal tag<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>countryID</code> - </dd>
</dl>
</li>
</ul>
<a name="getReserved--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReserved</h4>
<pre>public&nbsp;long&nbsp;getReserved()</pre>
<div class="block">获取动物标签的Reserved信息<br>
 acquire Reserved infor of animal tag<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setReserved-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReserved</h4>
<pre>public&nbsp;void&nbsp;setReserved(long&nbsp;reserved)</pre>
<div class="block">设置动物标签的Reserved信息<br>
 setup Reserved infor of animal tag<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>reserved</code> - </dd>
</dl>
</li>
</ul>
<a name="getDataBlock--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDataBlock</h4>
<pre>public&nbsp;long&nbsp;getDataBlock()</pre>
<div class="block">获取动物标签的DataBlock<br>
 acquire DataBlock of animal tag<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setDataBlock-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDataBlock</h4>
<pre>public&nbsp;void&nbsp;setDataBlock(long&nbsp;dataBlock)</pre>
<div class="block">设置动物标签的DataBlock<br>
 setup DataBlock of animal tag<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nationalID</code> - </dd>
</dl>
</li>
</ul>
<a name="getAnimalFlag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnimalFlag</h4>
<pre>public&nbsp;long&nbsp;getAnimalFlag()</pre>
<div class="block">获取动物标签的AnimalFlag<br>
 acquire AnimalFlag of animal tag<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setAnimalFlag-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnimalFlag</h4>
<pre>public&nbsp;void&nbsp;setAnimalFlag(long&nbsp;animalFlag)</pre>
<div class="block">设置动物标签的AnimalFlag<br>
 setup AnimalFlag of animal tag<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nationalID</code> - </dd>
</dl>
</li>
</ul>
<a name="getRawData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRawData</h4>
<pre>public&nbsp;java.lang.String&nbsp;getRawData()</pre>
<div class="block">获取原始数据，十六进制表示<br>
 acquire original data, hexdecimal<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setRawData-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRawData</h4>
<pre>public&nbsp;void&nbsp;setRawData(java.lang.String&nbsp;rawData)</pre>
<div class="block">设置原始数据<br>
 setup original data<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rawData</code> - </dd>
</dl>
</li>
</ul>
<a name="getIdData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIdData</h4>
<pre>public&nbsp;java.lang.String&nbsp;getIdData()</pre>
<div class="block">获取64bit的识别号数据，十六进制表示<br>
 acquire 64bit identification number, hexdecimal<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setIdData-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setIdData</h4>
<pre>public&nbsp;void&nbsp;setIdData(java.lang.String&nbsp;idData)</pre>
<div class="block">设置64bit的识别号数据，十六进制表示<br>
 setup 64bit identification number, hexdecimal<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>idData</code> - </dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/AnimalEntity.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../com/rscja/deviceapi/entity/AntennaConnectState.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/AnimalEntity.html" target="_top">Frames</a></li>
<li><a href="AnimalEntity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
