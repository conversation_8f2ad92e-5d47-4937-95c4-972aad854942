<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>com.rscja.deviceapi.entity</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../com/rscja/deviceapi/entity/package-summary.html" target="classFrame">com.rscja.deviceapi.entity</a></h1>
<div class="indexContainer">
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="AnimalEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">AnimalEntity</a></li>
<li><a href="AntennaConnectState.html" title="class in com.rscja.deviceapi.entity" target="classFrame">AntennaConnectState</a></li>
<li><a href="AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">AntennaPowerEntity</a></li>
<li><a href="AntennaState.html" title="class in com.rscja.deviceapi.entity" target="classFrame">AntennaState</a></li>
<li><a href="BarcodeEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">BarcodeEntity</a></li>
<li><a href="BarcodeResult.html" title="class in com.rscja.deviceapi.entity" target="classFrame">BarcodeResult</a></li>
<li><a href="BatteryEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">BatteryEntity</a></li>
<li><a href="DESFireFile.html" title="class in com.rscja.deviceapi.entity" target="classFrame">DESFireFile</a></li>
<li><a href="Gen2Entity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">Gen2Entity</a></li>
<li><a href="GPIOInfo.html" title="class in com.rscja.deviceapi.entity" target="classFrame">GPIOInfo</a></li>
<li><a href="GPIStateEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">GPIStateEntity</a></li>
<li><a href="GPOEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">GPOEntity</a></li>
<li><a href="HF14443RequestEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">HF14443RequestEntity</a></li>
<li><a href="HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">HF15693RequestEntity</a></li>
<li><a href="HF15693RequestEntity.Builder.html" title="class in com.rscja.deviceapi.entity" target="classFrame">HF15693RequestEntity.Builder</a></li>
<li><a href="InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">InventoryModeEntity</a></li>
<li><a href="InventoryModeEntity.Builder.html" title="class in com.rscja.deviceapi.entity" target="classFrame">InventoryModeEntity.Builder</a></li>
<li><a href="InventoryParameter.html" title="class in com.rscja.deviceapi.entity" target="classFrame">InventoryParameter</a></li>
<li><a href="InventoryParameter.ResultData.html" title="class in com.rscja.deviceapi.entity" target="classFrame">InventoryParameter.ResultData</a></li>
<li><a href="ISO15693Entity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">ISO15693Entity</a></li>
<li><a href="LowBatteryEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">LowBatteryEntity</a></li>
<li><a href="RadarLocationEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">RadarLocationEntity</a></li>
<li><a href="RadarLocationEntity.Builder.html" title="class in com.rscja.deviceapi.entity" target="classFrame">RadarLocationEntity.Builder</a></li>
<li><a href="ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">ReaderIPEntity</a></li>
<li><a href="ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">ScannerParameterEntity</a></li>
<li><a href="ScannerParameterEntity.Builder.html" title="class in com.rscja.deviceapi.entity" target="classFrame">ScannerParameterEntity.Builder</a></li>
<li><a href="SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">SimpleRFIDEntity</a></li>
<li><a href="TagInfoRule.html" title="class in com.rscja.deviceapi.entity" target="classFrame">TagInfoRule</a></li>
<li><a href="TagLocationEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">TagLocationEntity</a></li>
<li><a href="TagLocationInfo.html" title="class in com.rscja.deviceapi.entity" target="classFrame">TagLocationInfo</a></li>
<li><a href="UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity" target="classFrame">UHFTAGInfo</a></li>
<li><a href="UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity" target="classFrame">UHFTAGInfo.ChipInfo</a></li>
<li><a href="UHFTAGInfo.ChipInfo.Builder.html" title="class in com.rscja.deviceapi.entity" target="classFrame">UHFTAGInfo.ChipInfo.Builder</a></li>
<li><a href="WifiConfig.html" title="class in com.rscja.deviceapi.entity" target="classFrame">WifiConfig</a></li>
</ul>
<h2 title="Enums">Enums</h2>
<ul title="Enums">
<li><a href="HF15693RequestEntity.TagType.html" title="enum in com.rscja.deviceapi.entity" target="classFrame">HF15693RequestEntity.TagType</a></li>
</ul>
</div>
</body>
</html>
