<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>ReaderIPEntity</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ReaderIPEntity";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ReaderIPEntity.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/RadarLocationEntity.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/ReaderIPEntity.html" target="_top">Frames</a></li>
<li><a href="ReaderIPEntity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.entity</div>
<h2 title="Class ReaderIPEntity" class="title">Class ReaderIPEntity</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.entity.ReaderIPEntity</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ReaderIPEntity</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#ReaderIPEntity--">ReaderIPEntity</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#ReaderIPEntity-java.lang.String-int-">ReaderIPEntity</a></span>(java.lang.String&nbsp;ip,
              int&nbsp;port)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#getDns1--">getDns1</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#getDns2--">getDns2</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#getGateway--">getGateway</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#getIp--">getIp</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#getIpv6Dns1--">getIpv6Dns1</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#getIpv6Dns2--">getIpv6Dns2</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#getIpv6Ip1--">getIpv6Ip1</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#getIpv6Ip2--">getIpv6Ip2</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#getIpv6Ip3--">getIpv6Ip3</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#getNetworkType--">getNetworkType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#getPort--">getPort</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#getSsid--">getSsid</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#getSubnetMask--">getSubnetMask</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#setDns1-java.lang.String-">setDns1</a></span>(java.lang.String&nbsp;dns1)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#setDns2-java.lang.String-">setDns2</a></span>(java.lang.String&nbsp;dns2)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#setGateway-java.lang.String-">setGateway</a></span>(java.lang.String&nbsp;gateway)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#setIp-java.lang.String-">setIp</a></span>(java.lang.String&nbsp;ip)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#setIpv6Dns1-java.lang.String-">setIpv6Dns1</a></span>(java.lang.String&nbsp;ipv6Dns1)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#setIpv6Dns2-java.lang.String-">setIpv6Dns2</a></span>(java.lang.String&nbsp;ipv6Dns2)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#setIpv6Ip1-java.lang.String-">setIpv6Ip1</a></span>(java.lang.String&nbsp;ipv6Ip1)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#setIpv6Ip2-java.lang.String-">setIpv6Ip2</a></span>(java.lang.String&nbsp;ipv6Ip2)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#setIpv6Ip3-java.lang.String-">setIpv6Ip3</a></span>(java.lang.String&nbsp;ipv6Ip3)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#setNetworkType-int-">setNetworkType</a></span>(int&nbsp;networkType)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#setPort-int-">setPort</a></span>(int&nbsp;port)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#setSsid-java.lang.String-">setSsid</a></span>(java.lang.String&nbsp;ssid)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html#setSubnetMask-java.lang.String-">setSubnetMask</a></span>(java.lang.String&nbsp;subnetMask)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ReaderIPEntity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ReaderIPEntity</h4>
<pre>public&nbsp;ReaderIPEntity()</pre>
</li>
</ul>
<a name="ReaderIPEntity-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ReaderIPEntity</h4>
<pre>public&nbsp;ReaderIPEntity(java.lang.String&nbsp;ip,
                      int&nbsp;port)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPort--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPort</h4>
<pre>public&nbsp;int&nbsp;getPort()</pre>
</li>
</ul>
<a name="setPort-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPort</h4>
<pre>public&nbsp;void&nbsp;setPort(int&nbsp;port)</pre>
</li>
</ul>
<a name="getIp--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIp</h4>
<pre>public&nbsp;java.lang.String&nbsp;getIp()</pre>
</li>
</ul>
<a name="setIp-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIp</h4>
<pre>public&nbsp;void&nbsp;setIp(java.lang.String&nbsp;ip)</pre>
</li>
</ul>
<a name="getGateway--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGateway</h4>
<pre>public&nbsp;java.lang.String&nbsp;getGateway()</pre>
</li>
</ul>
<a name="setGateway-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGateway</h4>
<pre>public&nbsp;void&nbsp;setGateway(java.lang.String&nbsp;gateway)</pre>
</li>
</ul>
<a name="getSubnetMask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubnetMask</h4>
<pre>public&nbsp;java.lang.String&nbsp;getSubnetMask()</pre>
</li>
</ul>
<a name="setSubnetMask-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubnetMask</h4>
<pre>public&nbsp;void&nbsp;setSubnetMask(java.lang.String&nbsp;subnetMask)</pre>
</li>
</ul>
<a name="getDns1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDns1</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDns1()</pre>
</li>
</ul>
<a name="setDns1-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDns1</h4>
<pre>public&nbsp;void&nbsp;setDns1(java.lang.String&nbsp;dns1)</pre>
</li>
</ul>
<a name="getDns2--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDns2</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDns2()</pre>
</li>
</ul>
<a name="setDns2-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDns2</h4>
<pre>public&nbsp;void&nbsp;setDns2(java.lang.String&nbsp;dns2)</pre>
</li>
</ul>
<a name="getIpv6Ip1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIpv6Ip1</h4>
<pre>public&nbsp;java.lang.String&nbsp;getIpv6Ip1()</pre>
</li>
</ul>
<a name="setIpv6Ip1-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIpv6Ip1</h4>
<pre>public&nbsp;void&nbsp;setIpv6Ip1(java.lang.String&nbsp;ipv6Ip1)</pre>
</li>
</ul>
<a name="getIpv6Ip2--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIpv6Ip2</h4>
<pre>public&nbsp;java.lang.String&nbsp;getIpv6Ip2()</pre>
</li>
</ul>
<a name="setIpv6Ip2-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIpv6Ip2</h4>
<pre>public&nbsp;void&nbsp;setIpv6Ip2(java.lang.String&nbsp;ipv6Ip2)</pre>
</li>
</ul>
<a name="getIpv6Ip3--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIpv6Ip3</h4>
<pre>public&nbsp;java.lang.String&nbsp;getIpv6Ip3()</pre>
</li>
</ul>
<a name="setIpv6Ip3-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIpv6Ip3</h4>
<pre>public&nbsp;void&nbsp;setIpv6Ip3(java.lang.String&nbsp;ipv6Ip3)</pre>
</li>
</ul>
<a name="getIpv6Dns1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIpv6Dns1</h4>
<pre>public&nbsp;java.lang.String&nbsp;getIpv6Dns1()</pre>
</li>
</ul>
<a name="setIpv6Dns1-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIpv6Dns1</h4>
<pre>public&nbsp;void&nbsp;setIpv6Dns1(java.lang.String&nbsp;ipv6Dns1)</pre>
</li>
</ul>
<a name="getIpv6Dns2--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIpv6Dns2</h4>
<pre>public&nbsp;java.lang.String&nbsp;getIpv6Dns2()</pre>
</li>
</ul>
<a name="setIpv6Dns2-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIpv6Dns2</h4>
<pre>public&nbsp;void&nbsp;setIpv6Dns2(java.lang.String&nbsp;ipv6Dns2)</pre>
</li>
</ul>
<a name="getNetworkType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNetworkType</h4>
<pre>public&nbsp;int&nbsp;getNetworkType()</pre>
</li>
</ul>
<a name="setNetworkType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNetworkType</h4>
<pre>public&nbsp;void&nbsp;setNetworkType(int&nbsp;networkType)</pre>
</li>
</ul>
<a name="getSsid--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSsid</h4>
<pre>public&nbsp;java.lang.String&nbsp;getSsid()</pre>
</li>
</ul>
<a name="setSsid-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setSsid</h4>
<pre>public&nbsp;void&nbsp;setSsid(java.lang.String&nbsp;ssid)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ReaderIPEntity.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/entity/RadarLocationEntity.Builder.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/entity/ReaderIPEntity.html" target="_top">Frames</a></li>
<li><a href="ReaderIPEntity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
