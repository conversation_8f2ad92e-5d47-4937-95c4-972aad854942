<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Printer</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Printer";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":9,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Printer.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/Module.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/Printer.BarcodeType.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/Printer.html" target="_top">Frames</a></li>
<li><a href="Printer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Class Printer" class="title">Class Printer</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.Printer</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Printer</span>
extends java.lang.Object
implements <a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></pre>
<div class="block">打印机操作类<br>
 Printer operation type<br></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Administrator</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.BarcodeType.html" title="enum in com.rscja.deviceapi">Printer.BarcodeType</a></span></code>
<div class="block">1D条码类型</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.PrinterStatus.html" title="enum in com.rscja.deviceapi">Printer.PrinterStatus</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.PrinterStatusCallBack.html" title="interface in com.rscja.deviceapi">Printer.PrinterStatusCallBack</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#clearCache--">clearCache</a></span>()</code>
<div class="block">立即清空打印缓存,清空打印机接收缓冲区和打印缓冲区<br>
 Clear up printing cache, clear printer receive zone and buffer zone<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#eraseFlash--">eraseFlash</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#eraseFW-long-">eraseFW</a></span>(long&nbsp;size)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#free--">free</a></span>()</code>
<div class="block">关闭打印机模块<br>
 Switch off printer module<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取打印机模块操作实例<br>
 Acquire printer module operation Instance<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#getPrintCodePage--">getPrintCodePage</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#getPrinterType--">getPrinterType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#getVersion--">getVersion</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#init-int-">init</a></span>(int&nbsp;isUpgrade)</code>
<div class="block">打开打印机模块<br>
 Switch on printer module<br></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#initFW--">initFW</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#initPrinterGpio-boolean-">initPrinterGpio</a></span>(boolean&nbsp;isUpgrade)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#isPowerOn--">isPowerOn</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#openPrinterSerialPort-boolean-">openPrinterSerialPort</a></span>(boolean&nbsp;isUpgrade)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#print-android.graphics.Bitmap-">print</a></span>(android.graphics.Bitmap&nbsp;bitmap)</code>
<div class="block">打印图片<br>
 print picture<br></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#print-android.graphics.Bitmap-int-">print</a></span>(android.graphics.Bitmap&nbsp;bitmap,
     int&nbsp;interval)</code>
<div class="block">打印图片<br>
 print picture<br></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#print-android.graphics.Bitmap-int-int-">print</a></span>(android.graphics.Bitmap&nbsp;bitmap,
     int&nbsp;mode,
     int&nbsp;interval)</code>
<div class="block">打印图片<br>
 print pciture<br></div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#print-byte:A-">print</a></span>(byte[]&nbsp;content)</code>
<div class="block">打印字符<br>
 print character<br></div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#print-java.lang.String-">print</a></span>(java.lang.String&nbsp;content)</code>
<div class="block">打印字符<br>
 printe character<br></div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#print-java.lang.String-com.rscja.deviceapi.Printer.BarcodeType-">print</a></span>(java.lang.String&nbsp;barcodeData,
     <a href="../../../com/rscja/deviceapi/Printer.BarcodeType.html" title="enum in com.rscja.deviceapi">Printer.BarcodeType</a>&nbsp;barcodeType)</code>
<div class="block">打印条码<br>
print barcode<br></div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#print-java.lang.String-java.lang.String-">print</a></span>(java.lang.String&nbsp;content,
     java.lang.String&nbsp;charsetName)</code>
<div class="block">打印字符<br>
 print character<br></div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#receiveData-byte:A-">receiveData</a></span>(byte[]&nbsp;outData)</code>
<div class="block">接收串口数据<br>
 receive serial port data<br></div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#releasePrinterGpio--">releasePrinterGpio</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#restoreDefault--">restoreDefault</a></span>()</code>
<div class="block">参数还原默认值<br>
 parameter reset to default<br></div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#sendAndReceiveData-byte:A-byte:A-">sendAndReceiveData</a></span>(byte[]&nbsp;sendData,
                  byte[]&nbsp;outData)</code>
<div class="block">收发数据<br>
 sent/received data<br></div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#sendData-byte:A-">sendData</a></span>(byte[]&nbsp;sendData)</code>
<div class="block">发送数据到串口<br>
 send data to serial port<br></div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setBarcodeHeight-int-">setBarcodeHeight</a></span>(int&nbsp;height)</code>
<div class="block">设置一维条码的高度<br>
 Setup 1D barcode height<br></div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setBarcodeHRI-int-">setBarcodeHRI</a></span>(int&nbsp;position)</code>
<div class="block">设置一维条码可读字符（HRI）打印位置<br>
setup 1D barcode readable character (HRI) print position<br></div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setBarcodeWidth-int-">setBarcodeWidth</a></span>(int&nbsp;width)</code>
<div class="block">设置一维条码的宽度<br>
 Setup 1D barcode width<br></div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setFeedRow-int-">setFeedRow</a></span>(int&nbsp;n)</code>
<div class="block">打印并进纸 n行<br>
 print and paper infeed n<br></div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setPrintCharacterStyle-boolean-boolean-boolean-boolean-boolean-boolean-boolean-">setPrintCharacterStyle</a></span>(boolean&nbsp;italic,
                      boolean&nbsp;frame,
                      boolean&nbsp;bold,
                      boolean&nbsp;doubleWidth,
                      boolean&nbsp;doubleHigh,
                      boolean&nbsp;white,
                      boolean&nbsp;underline)</code>
<div class="block">设置字体样式<br>
 set font style<br></div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setPrintCodePage-int-">setPrintCodePage</a></span>(int&nbsp;page)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setPrinterStatusCallBack-com.rscja.deviceapi.Printer.PrinterStatusCallBack-">setPrinterStatusCallBack</a></span>(<a href="../../../com/rscja/deviceapi/Printer.PrinterStatusCallBack.html" title="interface in com.rscja.deviceapi">Printer.PrinterStatusCallBack</a>&nbsp;callBack)</code>
<div class="block">设置接收打印机状态的回调<br>
 set call back of printer received status <br></div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setPrinterStatusCallBackEnable-boolean-">setPrinterStatusCallBackEnable</a></span>(boolean&nbsp;b)</code>
<div class="block">设置是否接收打印机状态回调消息<br>
 Setup receive printer status return message or not<br></div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setPrinterType-int-">setPrinterType</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setPrintGrayLevel-int-">setPrintGrayLevel</a></span>(int&nbsp;gray)</code>
<div class="block">设置打印 灰度<br>
 setup print grey level<br></div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setPrintLeftMargin-int-">setPrintLeftMargin</a></span>(int&nbsp;margin)</code>
<div class="block">设置左边距<br>
 Setup left margin<br></div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setPrintRightMargin-int-">setPrintRightMargin</a></span>(int&nbsp;margin)</code>
<div class="block">设置左边距<br>
 Setup right margin<br></div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setPrintRowSpacing-int-">setPrintRowSpacing</a></span>(int&nbsp;spacing)</code>
<div class="block">设置行间距(若设定的行间距小于一行中的最大字符高度，那么该行行间距等于最大字符高度)<br>
 Setup line spacing (if the set value is less than max.charater height, then the line spacing equals to max.character height)<br></div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#setPrintSpeed-int-">setPrintSpeed</a></span>(int&nbsp;speed)</code>
<div class="block">设置打印速度<br>
 setup print velocity<br></div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#upgradeFont-int-int-int-byte:A-">upgradeFont</a></span>(int&nbsp;packageCount,
           int&nbsp;index,
           int&nbsp;currSize,
           byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#upgradeFW-int-int-int-byte:A-">upgradeFW</a></span>(int&nbsp;packageCount,
         int&nbsp;index,
         int&nbsp;currSize,
         byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Printer.html#verifyFW-int-">verifyFW</a></span>(int&nbsp;upgradeCRC)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi">Printer</a>&nbsp;getInstance()
                           throws <a href="../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">获取打印机模块操作实例<br>
 Acquire printer module operation Instance<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>打印机模块操作实例<br>
   printer module operation Instance<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code></dd>
</dl>
</li>
</ul>
<a name="setPrinterStatusCallBackEnable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrinterStatusCallBackEnable</h4>
<pre>public&nbsp;void&nbsp;setPrinterStatusCallBackEnable(boolean&nbsp;b)</pre>
<div class="block">设置是否接收打印机状态回调消息<br>
 Setup receive printer status return message or not<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setPrinterStatusCallBackEnable-boolean-">setPrinterStatusCallBackEnable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>b</code> - true:表示接收打印机回调消息，false:表示不接收回调消息,默认是true<br>
 ture: means receive return infor of printer, false: means donot receive return infor,default is ture.<br></dd>
</dl>
</li>
</ul>
<a name="receiveData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>receiveData</h4>
<pre>public&nbsp;int&nbsp;receiveData(byte[]&nbsp;outData)</pre>
<div class="block">接收串口数据<br>
 receive serial port data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#receiveData-byte:A-">receiveData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outData</code> - 接收到的数据<br>
 outData received data<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回接收数据的长度<br>
 length of returned data<br></dd>
</dl>
</li>
</ul>
<a name="sendData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sendData</h4>
<pre>public&nbsp;int&nbsp;sendData(byte[]&nbsp;sendData)</pre>
<div class="block">发送数据到串口<br>
 send data to serial port<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#sendData-byte:A-">sendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sendData</code> - 发送的数据<br>
 send data<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回发送数据的长度<br>
 length of return sent data<br></dd>
</dl>
</li>
</ul>
<a name="sendAndReceiveData-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sendAndReceiveData</h4>
<pre>public&nbsp;int&nbsp;sendAndReceiveData(byte[]&nbsp;sendData,
                              byte[]&nbsp;outData)</pre>
<div class="block">收发数据<br>
 sent/received data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#sendAndReceiveData-byte:A-byte:A-">sendAndReceiveData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sendData</code> - 发送的数据<br>
 sent data<br></dd>
<dd><code>outData</code> - 接收的数据<br>
 received data<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回接收的数据长度(- 1表示发送失败)<br>
 returned data length(-1 means send failed)<br></dd>
</dl>
</li>
</ul>
<a name="setPrinterStatusCallBack-com.rscja.deviceapi.Printer.PrinterStatusCallBack-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrinterStatusCallBack</h4>
<pre>public&nbsp;void&nbsp;setPrinterStatusCallBack(<a href="../../../com/rscja/deviceapi/Printer.PrinterStatusCallBack.html" title="interface in com.rscja.deviceapi">Printer.PrinterStatusCallBack</a>&nbsp;callBack)</pre>
<div class="block">设置接收打印机状态的回调<br>
 set call back of printer received status <br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setPrinterStatusCallBack-com.rscja.deviceapi.Printer.PrinterStatusCallBack-">setPrinterStatusCallBack</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>callBack</code> - </dd>
</dl>
</li>
</ul>
<a name="init-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;boolean&nbsp;init(int&nbsp;isUpgrade)</pre>
<div class="block">打开打印机模块<br>
 Switch on printer module<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#init-int-">init</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功true，失败false</dd>
</dl>
</li>
</ul>
<a name="initPrinterGpio-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initPrinterGpio</h4>
<pre>public&nbsp;boolean&nbsp;initPrinterGpio(boolean&nbsp;isUpgrade)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#initPrinterGpio-boolean-">initPrinterGpio</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="releasePrinterGpio--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>releasePrinterGpio</h4>
<pre>public&nbsp;boolean&nbsp;releasePrinterGpio()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#releasePrinterGpio--">releasePrinterGpio</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="openPrinterSerialPort-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openPrinterSerialPort</h4>
<pre>public&nbsp;boolean&nbsp;openPrinterSerialPort(boolean&nbsp;isUpgrade)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#openPrinterSerialPort-boolean-">openPrinterSerialPort</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;boolean&nbsp;free()</pre>
<div class="block">关闭打印机模块<br>
 Switch off printer module<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#free--">free</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功true，失败false<br>
 true means success, false means failed<br></dd>
</dl>
</li>
</ul>
<a name="print-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>print</h4>
<pre>public&nbsp;void&nbsp;print(byte[]&nbsp;content)</pre>
<div class="block">打印字符<br>
 print character<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#print-byte:A-">print</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>content</code> - 打印的内容<br></dd>
</dl>
</li>
</ul>
<a name="print-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>print</h4>
<pre>public&nbsp;void&nbsp;print(java.lang.String&nbsp;content)</pre>
<div class="block">打印字符<br>
 printe character<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#print-java.lang.String-">print</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>content</code> - 打印的内容<br>
 print content<br></dd>
</dl>
</li>
</ul>
<a name="print-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>print</h4>
<pre>public&nbsp;void&nbsp;print(java.lang.String&nbsp;content,
                  java.lang.String&nbsp;charsetName)</pre>
<div class="block">打印字符<br>
 print character<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#print-java.lang.String-java.lang.String-">print</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>content</code> - 打印的内容<br>
 print content<br></dd>
<dd><code>charsetName</code> - 字符编码格式<br>
 character coding format<br></dd>
</dl>
</li>
</ul>
<a name="print-android.graphics.Bitmap-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>print</h4>
<pre>public&nbsp;void&nbsp;print(android.graphics.Bitmap&nbsp;bitmap,
                  int&nbsp;mode,
                  int&nbsp;interval)</pre>
<div class="block">打印图片<br>
 print pciture<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#print-android.graphics.Bitmap-int-int-">print</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bitmap</code> - 要打印的图片<br>
 picture for printing<br></dd>
<dd><code>mode(0,1,32,33)</code> - 点图格式:<br>
 picture format:<br>
   模式                      水平比例      垂直比例<br>
   format                       horizontal scale        vertical scale<br>
    0: 8 点单密度        ×2      ×3   <br>
    0: 8 point single density ×2 ×3   <br>
    1: 8 点双密度        ×1      ×3   <br>
    1: 8 point dual density   ×1 ×3   <br>
    32:24 点单密度      ×2      ×1   <br>
    32:24 point single density ×2      ×1   <br>
    33:24 点双密度      ×1      ×1   <br>
    33:24 point dual density ×1      ×1   <br></dd>
</dl>
</li>
</ul>
<a name="print-android.graphics.Bitmap-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>print</h4>
<pre>public&nbsp;void&nbsp;print(android.graphics.Bitmap&nbsp;bitmap,
                  int&nbsp;interval)</pre>
<div class="block">打印图片<br>
 print picture<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#print-android.graphics.Bitmap-int-">print</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bitmap</code> - 图片数据<br>
 bitmap picture data<br></dd>
<dd><code>interval</code> - 发送每行数据的时间间隔<br>
 Time interval for sending per row of data</dd>
</dl>
</li>
</ul>
<a name="print-android.graphics.Bitmap-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>print</h4>
<pre>public&nbsp;void&nbsp;print(android.graphics.Bitmap&nbsp;bitmap)</pre>
<div class="block">打印图片<br>
 print picture<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#print-android.graphics.Bitmap-">print</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bitmap</code> - 图片数据<br>
 bitmap picture data<br></dd>
</dl>
</li>
</ul>
<a name="print-java.lang.String-com.rscja.deviceapi.Printer.BarcodeType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>print</h4>
<pre>public&nbsp;void&nbsp;print(java.lang.String&nbsp;barcodeData,
                  <a href="../../../com/rscja/deviceapi/Printer.BarcodeType.html" title="enum in com.rscja.deviceapi">Printer.BarcodeType</a>&nbsp;barcodeType)
           throws <a href="../../../com/rscja/deviceapi/exception/PrinterBarcodeInvalidException.html" title="class in com.rscja.deviceapi.exception">PrinterBarcodeInvalidException</a></pre>
<div class="block">打印条码<br>
print barcode<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#print-java.lang.String-com.rscja.deviceapi.Printer.BarcodeType-">print</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>barcodeData</code> - 条码数据<br>
 barcodeData <br></dd>
<dd><code>barcodeType</code> - 条码类型<br>
 barcodeType <br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/exception/PrinterBarcodeInvalidException.html" title="class in com.rscja.deviceapi.exception">PrinterBarcodeInvalidException</a></code></dd>
</dl>
</li>
</ul>
<a name="setFeedRow-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFeedRow</h4>
<pre>public&nbsp;void&nbsp;setFeedRow(int&nbsp;n)</pre>
<div class="block">打印并进纸 n行<br>
 print and paper infeed n<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setFeedRow-int-">setFeedRow</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>n(0-255)</code> - </dd>
</dl>
</li>
</ul>
<a name="setPrintRowSpacing-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrintRowSpacing</h4>
<pre>public&nbsp;void&nbsp;setPrintRowSpacing(int&nbsp;spacing)</pre>
<div class="block">设置行间距(若设定的行间距小于一行中的最大字符高度，那么该行行间距等于最大字符高度)<br>
 Setup line spacing (if the set value is less than max.charater height, then the line spacing equals to max.character height)<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setPrintRowSpacing-int-">setPrintRowSpacing</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spacing</code> - (0 ≤ spacing ≤ 255), 默认值33<br>
 spacing (0≤ spacing ≤ 255), default 33<br></dd>
</dl>
</li>
</ul>
<a name="setPrintLeftMargin-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrintLeftMargin</h4>
<pre>public&nbsp;void&nbsp;setPrintLeftMargin(int&nbsp;margin)</pre>
<div class="block">设置左边距<br>
 Setup left margin<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setPrintLeftMargin-int-">setPrintLeftMargin</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>margin</code> - (0 ≤ left margin ≤ 47，且 0 ≤ （左边距 + 右边距） ≤ 47), 默认值0<br>
 margin (0 ≤ left margin ≤ 47,0 ≤ （left margin + right margin,  ≤ 47),default 0.<br></dd>
</dl>
</li>
</ul>
<a name="setPrintRightMargin-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrintRightMargin</h4>
<pre>public&nbsp;void&nbsp;setPrintRightMargin(int&nbsp;margin)</pre>
<div class="block">设置左边距<br>
 Setup right margin<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setPrintRightMargin-int-">setPrintRightMargin</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>margin</code> - (0 ≤ left margin ≤ 47，且 0 ≤ （左边距 + 右边距） ≤ 47), 默认值0<br>
 margin (0 ≤ left margin ≤ 47,0 ≤ （left margin + right margin,  ≤ 47),default 0.<br></dd>
</dl>
</li>
</ul>
<a name="setPrintCharacterStyle-boolean-boolean-boolean-boolean-boolean-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrintCharacterStyle</h4>
<pre>public&nbsp;void&nbsp;setPrintCharacterStyle(boolean&nbsp;italic,
                                   boolean&nbsp;frame,
                                   boolean&nbsp;bold,
                                   boolean&nbsp;doubleWidth,
                                   boolean&nbsp;doubleHigh,
                                   boolean&nbsp;white,
                                   boolean&nbsp;underline)</pre>
<div class="block">设置字体样式<br>
 set font style<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setPrintCharacterStyle-boolean-boolean-boolean-boolean-boolean-boolean-boolean-">setPrintCharacterStyle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>italic</code> - 斜体<br>
 italic<br></dd>
<dd><code>frame</code> - 边框<br>
 frame <br></dd>
<dd><code>bold</code> - 加粗<br>
 bold <br></dd>
<dd><code>doubleWidth</code> - 倍宽<br>
 doubleWidth<br></dd>
<dd><code>doubleHigh</code> - 倍高<br>
 douleHigh<br></dd>
<dd><code>white</code> - 反白<br>
 white<br></dd>
<dd><code>underline</code> - 下划线<br>
 underline<br></dd>
</dl>
</li>
</ul>
<a name="setPrintSpeed-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrintSpeed</h4>
<pre>public&nbsp;void&nbsp;setPrintSpeed(int&nbsp;speed)</pre>
<div class="block">设置打印速度<br>
 setup print velocity<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setPrintSpeed-int-">setPrintSpeed</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>speed</code> - 0:低速,1:中速,2:高速<br>
 speed 0:low,1:medium,2:high<br>
 备注：默认是1<br>
 comment: default is 1<br></dd>
</dl>
</li>
</ul>
<a name="setPrintGrayLevel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrintGrayLevel</h4>
<pre>public&nbsp;void&nbsp;setPrintGrayLevel(int&nbsp;gray)</pre>
<div class="block">设置打印 灰度<br>
 setup print grey level<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setPrintGrayLevel-int-">setPrintGrayLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gray</code> - 1-8<br>
 gray 1-8<br>
 备注：默认是4<br>
 comment: default is 4<br></dd>
</dl>
</li>
</ul>
<a name="restoreDefault--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>restoreDefault</h4>
<pre>public&nbsp;void&nbsp;restoreDefault()</pre>
<div class="block">参数还原默认值<br>
 parameter reset to default<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#restoreDefault--">restoreDefault</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="clearCache--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clearCache</h4>
<pre>public&nbsp;void&nbsp;clearCache()</pre>
<div class="block">立即清空打印缓存,清空打印机接收缓冲区和打印缓冲区<br>
 Clear up printing cache, clear printer receive zone and buffer zone<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#clearCache--">clearCache</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="setBarcodeHeight-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeHeight</h4>
<pre>public&nbsp;void&nbsp;setBarcodeHeight(int&nbsp;height)</pre>
<div class="block">设置一维条码的高度<br>
 Setup 1D barcode height<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setBarcodeHeight-int-">setBarcodeHeight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>height</code> - 高度(1-255),默认值:64<br>
 height(1-255), default:64<br></dd>
</dl>
</li>
</ul>
<a name="setBarcodeWidth-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeWidth</h4>
<pre>public&nbsp;void&nbsp;setBarcodeWidth(int&nbsp;width)</pre>
<div class="block">设置一维条码的宽度<br>
 Setup 1D barcode width<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setBarcodeWidth-int-">setBarcodeWidth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>width</code> - 宽度(1-6),默认值:2<br>
 width(1-6), default:2<br></dd>
</dl>
</li>
</ul>
<a name="setBarcodeHRI-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeHRI</h4>
<pre>public&nbsp;void&nbsp;setBarcodeHRI(int&nbsp;position)</pre>
<div class="block">设置一维条码可读字符（HRI）打印位置<br>
setup 1D barcode readable character (HRI) print position<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setBarcodeHRI-int-">setBarcodeHRI</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>position(0-3)</code> - 默认是0; [0:不打印,   1: 条码的上方 ,  2:条码的下方,   3:条码的上方和下方]<br>
 position(0-3) default is 0; [0:not print, 1:barcode up side 2: barcode down side, 3: barcode up and down]<br></dd>
</dl>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>public&nbsp;boolean&nbsp;isPowerOn()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#isPowerOn--">isPowerOn</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;java.lang.String&nbsp;getVersion()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#getVersion--">getVersion</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="setPrinterType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrinterType</h4>
<pre>public&nbsp;void&nbsp;setPrinterType(int&nbsp;type)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setPrinterType-int-">setPrinterType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="getPrinterType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrinterType</h4>
<pre>public&nbsp;int&nbsp;getPrinterType()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#getPrinterType--">getPrinterType</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="setPrintCodePage-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrintCodePage</h4>
<pre>public&nbsp;void&nbsp;setPrintCodePage(int&nbsp;page)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#setPrintCodePage-int-">setPrintCodePage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="getPrintCodePage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrintCodePage</h4>
<pre>public&nbsp;int&nbsp;getPrintCodePage()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#getPrintCodePage--">getPrintCodePage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="initFW--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initFW</h4>
<pre>public&nbsp;boolean&nbsp;initFW()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#initFW--">initFW</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="eraseFW-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eraseFW</h4>
<pre>public&nbsp;boolean&nbsp;eraseFW(long&nbsp;size)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#eraseFW-long-">eraseFW</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="upgradeFW-int-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>upgradeFW</h4>
<pre>public&nbsp;boolean&nbsp;upgradeFW(int&nbsp;packageCount,
                         int&nbsp;index,
                         int&nbsp;currSize,
                         byte[]&nbsp;data)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#upgradeFW-int-int-int-byte:A-">upgradeFW</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="verifyFW-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>verifyFW</h4>
<pre>public&nbsp;boolean&nbsp;verifyFW(int&nbsp;upgradeCRC)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#verifyFW-int-">verifyFW</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="eraseFlash--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eraseFlash</h4>
<pre>public&nbsp;boolean&nbsp;eraseFlash()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#eraseFlash--">eraseFlash</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
<a name="upgradeFont-int-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>upgradeFont</h4>
<pre>public&nbsp;boolean&nbsp;upgradeFont(int&nbsp;packageCount,
                           int&nbsp;index,
                           int&nbsp;currSize,
                           byte[]&nbsp;data)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html#upgradeFont-int-int-int-byte:A-">upgradeFont</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Printer.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/Module.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/Printer.BarcodeType.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/Printer.html" target="_top">Frames</a></li>
<li><a href="Printer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
