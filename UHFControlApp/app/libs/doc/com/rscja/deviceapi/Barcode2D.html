<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Barcode2D</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Barcode2D";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Barcode2D.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/Barcode1D.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/Barcode2D.html" target="_top">Frames</a></li>
<li><a href="Barcode2D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Class Barcode2D" class="title">Class Barcode2D</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.Barcode2D</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Barcode2D</span>
extends java.lang.Object
implements <a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a></pre>
<div class="block">二维条码操作类 <br>
 2D barcode operation class. <br>
 <p>
 注意： <br/>
 Attention: <br>
 1、使用前请确认您的机器已安装此模块。 <br/>
 1. Make sure the module is installed before using. <br>
 2、要正常使用模块需要在\libs\armeabi\目录放置libDeviceAPI.so文件。 <br/>
 2. Put the file libDeviceAPI.so in directory \libs\armeabi\ then module can be used normally.<br>
 3、在操作设备前需要调用 <b><a href="../../../com/rscja/deviceapi/Barcode2D.html#open--"><code>open()</code></a></b> 打开设备，使用完后调用 <b><a href="../../../com/rscja/deviceapi/Barcode2D.html#close--"><code>close()</code></a>
 3. Before operating, call <b><a href="../../../com/rscja/deviceapi/Barcode2D.html#open--"><code>open()</code></a></b>to switch on the device, after using call <b><a href="../../../com/rscja/deviceapi/Barcode1D.html#close--"><code>Barcode1D.close()</code></a></b> to<br>
 </b> 关闭设备
 </b> switch off the device.<br></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>liuruifeng</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Barcode2D.html#close--">close</a></span>()</code>
<div class="block">关闭二维扫描设备<br>
 Switch off 2D barcode scanning device.<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/deviceapi/Barcode2D.html" title="class in com.rscja.deviceapi">Barcode2D</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Barcode2D.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取二维条码操作实例<br>
 * Acquire 2D barcode operation Instance.<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Barcode2D.html#isPowerOn--">isPowerOn</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Barcode2D.html#open--">open</a></span>()</code>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Barcode2D.html#open-android.content.Context-">open</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Barcode2D.html#scan--">scan</a></span>()</code>
<div class="block">触发一次条码扫描<br>
 Enable barcode scanning for 1 time.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Barcode2D.html#scanBarcode--">scanBarcode</a></span>()</code>
<div class="block">触发一次条码扫描<br>
 Enable barcode scanning for 1 time.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Barcode2D.html#setTimeOut-int-">setTimeOut</a></span>(int&nbsp;time)</code>
<div class="block">设置超时时间<br>
 set time out.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Barcode2D.html#stopScan--">stopScan</a></span>()</code>
<div class="block">终止扫描<br>
 Scanning terminated<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/deviceapi/Barcode2D.html" title="class in com.rscja.deviceapi">Barcode2D</a>&nbsp;getInstance()
                             throws <a href="../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">获取二维条码操作实例<br>
 * Acquire 2D barcode operation Instance.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>2D barcode operation example<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code> - 配置错误异常<br>
                                Configuration Error<br></dd>
</dl>
</li>
</ul>
<a name="open--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open()</pre>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#open--">open</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功,false:失败<br>
 true:success,false:failuer</dd>
</dl>
</li>
</ul>
<a name="open-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(android.content.Context&nbsp;context)</pre>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#open-android.content.Context-">open</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功,false:失败<br>
 true:success,false:failuer</dd>
</dl>
</li>
</ul>
<a name="scan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scan</h4>
<pre>public&nbsp;java.lang.String&nbsp;scan()</pre>
<div class="block">触发一次条码扫描<br>
 Enable barcode scanning for 1 time.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#scan--">scan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>扫描得到的条码信息<br>
 scanning barcode infor that acquired.</dd>
</dl>
</li>
</ul>
<a name="scanBarcode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scanBarcode</h4>
<pre>public&nbsp;byte[]&nbsp;scanBarcode()</pre>
<div class="block">触发一次条码扫描<br>
 Enable barcode scanning for 1 time.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#scanBarcode--">scanBarcode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>扫描得到的条码信息<br>
 scanning barcode infor that acquired.</dd>
</dl>
</li>
</ul>
<a name="stopScan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopScan</h4>
<pre>public&nbsp;boolean&nbsp;stopScan()</pre>
<div class="block">终止扫描<br>
 Scanning terminated<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#stopScan--">stopScan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功，false:失败<br>
 true:success, false:failuer<br></dd>
</dl>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;boolean&nbsp;close()</pre>
<div class="block">关闭二维扫描设备<br>
 Switch off 2D barcode scanning device.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#close--">close</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功，false:失败<br>
 true:success, false:failuer</dd>
</dl>
</li>
</ul>
<a name="setTimeOut-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimeOut</h4>
<pre>public&nbsp;void&nbsp;setTimeOut(int&nbsp;time)</pre>
<div class="block">设置超时时间<br>
 set time out.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#setTimeOut-int-">setTimeOut</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>time(500-10000</code> - ms) 超时时间<br>
                       TimeOut</dd>
</dl>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>public&nbsp;boolean&nbsp;isPowerOn()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#isPowerOn--">isPowerOn</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Barcode2D.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/Barcode1D.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/Barcode2D.html" target="_top">Frames</a></li>
<li><a href="Barcode2D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
