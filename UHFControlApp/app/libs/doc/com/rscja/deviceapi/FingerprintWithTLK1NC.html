<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FingerprintWithTLK1NC</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FingerprintWithTLK1NC";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":9,"i20":9,"i21":10,"i22":10,"i23":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FingerprintWithTLK1NC.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/FingerprintWithMorpho.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/FingerprintWithTLK1NC.html" target="_top">Frames</a></li>
<li><a href="FingerprintWithTLK1NC.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Class FingerprintWithTLK1NC" class="title">Class FingerprintWithTLK1NC</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.FingerprintWithTLK1NC</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">FingerprintWithTLK1NC</span>
extends java.lang.Object
implements <a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></pre>
<div class="block">迪安杰</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Administrator</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a></span></code>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.IUPImageCallback.html" title="interface in com.rscja.deviceapi">FingerprintWithTLK1NC.IUPImageCallback</a></span></code>
<div class="block">指纹图像回调接口<br>
 fingerprint image call-back contact<br></div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#STATUS_FAILURE">STATUS_FAILURE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#STATUS_SUCCESS">STATUS_SUCCESS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#STATUS_TIMEOUT">STATUS_TIMEOUT</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerCLEARTemplate--">fingerCLEARTemplate</a></span>()</code>
<div class="block">清空FLASH中指纹模板</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerCLEARTemplateBuffer--">fingerCLEARTemplateBuffer</a></span>()</code>
<div class="block">清空指纹特征缓冲区</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerDELTemplateBufferID-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerDELTemplateBufferID</a></span>(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID)</code>
<div class="block">删除内存中指定的模板</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerDELTemplatePageID-int-">fingerDELTemplatePageID</a></span>(int&nbsp;PageID)</code>
<div class="block">删除FLASH中指定的模板</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerDOWNTemplateToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-byte:A-">fingerDOWNTemplateToBuffer</a></span>(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID,
                          byte[]&nbsp;templateData)</code>
<div class="block">下载指纹特征到指定的特征缓冲区中</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerDOWNTemplateToFlashPage-int-byte:A-">fingerDOWNTemplateToFlashPage</a></span>(int&nbsp;PageID,
                             byte[]&nbsp;templateData)</code>
<div class="block">下载指纹特征到指定的Flash Page中</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerGETImage--">fingerGETImage</a></span>()</code>
<div class="block">从采集器采集指纹图像并保存于 ImageBuffer 中</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerGETTemplateCount--">fingerGETTemplateCount</a></span>()</code>
<div class="block">获取可存储的指纹模板数</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerPKTemplate-int-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerPKTemplate</a></span>(int&nbsp;PageID,
                <a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID)</code>
<div class="block">精确比对指定特征缓冲区和指定的Flash Page中特征</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerPKTemplate2-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-">fingerPKTemplate2</a></span>(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID,
                 int&nbsp;PageID)</code>
<div class="block">精确比对指定特征缓冲区和指定的Flash Page中特征</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerPKTemplateBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerPKTemplateBuffer</a></span>(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID1,
                      <a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID2)</code>
<div class="block">精确比对指定特征缓冲区中特征</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerPKTemplatePage-int-int-">fingerPKTemplatePage</a></span>(int&nbsp;PageID1,
                    int&nbsp;PageID2)</code>
<div class="block">精确比对指定Flash Page中特征</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerSearchTemplate-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-int-">fingerSearchTemplate</a></span>(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID,
                    int&nbsp;templateIdStart,
                    int&nbsp;templateIdEnd)</code>
<div class="block">下载一个特征到指定特征缓冲区，然后用此特征搜索指纹库中的模板</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerStoreCharToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerStoreCharToBuffer</a></span>(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID)</code>
<div class="block">生成指纹特征，存储在指定特征缓冲区</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerStoreCharToFlashPage-int-">fingerStoreCharToFlashPage</a></span>(int&nbsp;PageID)</code>
<div class="block">生成指纹特征，存储在Flash Page</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerUPTemplateFromBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerUPTemplateFromBuffer</a></span>(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID)</code>
<div class="block">上传指纹特征值，将指定缓冲区中的特征值上传到上位机</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#fingerUPTemplateFromFlashPage-int-">fingerUPTemplateFromFlashPage</a></span>(int&nbsp;PageID)</code>
<div class="block">上传指纹特征值，将指定Flash Page中的特征值上传到上位机</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#free--">free</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#generateImg-byte:A-java.lang.String-">generateImg</a></span>(byte[]&nbsp;data,
           java.lang.String&nbsp;filePath)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#getStatusMsg-int-">getStatusMsg</a></span>(int&nbsp;status)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#getVersion--">getVersion</a></span>()</code>
<div class="block">获取模块的设备信息</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#init--">init</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html#startUPImage-com.rscja.deviceapi.FingerprintWithTLK1NC.IUPImageCallback-">startUPImage</a></span>(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.IUPImageCallback.html" title="interface in com.rscja.deviceapi">FingerprintWithTLK1NC.IUPImageCallback</a>&nbsp;callBack)</code>
<div class="block">//  * @param  timeOut timeOut  (unit:millisecond)</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="STATUS_SUCCESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STATUS_SUCCESS</h4>
<pre>public&nbsp;int STATUS_SUCCESS</pre>
</li>
</ul>
<a name="STATUS_FAILURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STATUS_FAILURE</h4>
<pre>public&nbsp;int STATUS_FAILURE</pre>
</li>
</ul>
<a name="STATUS_TIMEOUT">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>STATUS_TIMEOUT</h4>
<pre>public&nbsp;int STATUS_TIMEOUT</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi">FingerprintWithTLK1NC</a>&nbsp;getInstance()
                                         throws <a href="../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code></dd>
</dl>
</li>
</ul>
<a name="init--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;boolean&nbsp;init()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#init--">init</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;boolean&nbsp;free()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#free--">free</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
</dl>
</li>
</ul>
<a name="fingerGETImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerGETImage</h4>
<pre>public&nbsp;int&nbsp;fingerGETImage()</pre>
<div class="block">从采集器采集指纹图像并保存于 ImageBuffer 中</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerGETImage--">fingerGETImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回-1，错误 ，返回非负数： 指纹图像质量评分, 范围：0~100</dd>
</dl>
</li>
</ul>
<a name="fingerStoreCharToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerStoreCharToBuffer</h4>
<pre>public&nbsp;boolean&nbsp;fingerStoreCharToBuffer(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID)</pre>
<div class="block">生成指纹特征，存储在指定特征缓冲区</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerStoreCharToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerStoreCharToBuffer</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>BufferID</code> - 缓冲区ID</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功  false:失败</dd>
</dl>
</li>
</ul>
<a name="fingerStoreCharToFlashPage-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerStoreCharToFlashPage</h4>
<pre>public&nbsp;boolean&nbsp;fingerStoreCharToFlashPage(int&nbsp;PageID)</pre>
<div class="block">生成指纹特征，存储在Flash Page</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerStoreCharToFlashPage-int-">fingerStoreCharToFlashPage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>PageID</code> - Flash Page ID (0~127)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功  false:失败</dd>
</dl>
</li>
</ul>
<a name="fingerUPTemplateFromBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerUPTemplateFromBuffer</h4>
<pre>public&nbsp;byte[]&nbsp;fingerUPTemplateFromBuffer(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID)</pre>
<div class="block">上传指纹特征值，将指定缓冲区中的特征值上传到上位机</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerUPTemplateFromBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerUPTemplateFromBuffer</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>BufferID</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回指纹模板数据</dd>
</dl>
</li>
</ul>
<a name="fingerUPTemplateFromFlashPage-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerUPTemplateFromFlashPage</h4>
<pre>public&nbsp;byte[]&nbsp;fingerUPTemplateFromFlashPage(int&nbsp;PageID)</pre>
<div class="block">上传指纹特征值，将指定Flash Page中的特征值上传到上位机</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerUPTemplateFromFlashPage-int-">fingerUPTemplateFromFlashPage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>PageID</code> - PageID(0~127)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回指纹模板数据</dd>
</dl>
</li>
</ul>
<a name="fingerDOWNTemplateToFlashPage-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerDOWNTemplateToFlashPage</h4>
<pre>public&nbsp;boolean&nbsp;fingerDOWNTemplateToFlashPage(int&nbsp;PageID,
                                             byte[]&nbsp;templateData)</pre>
<div class="block">下载指纹特征到指定的Flash Page中</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerDOWNTemplateToFlashPage-int-byte:A-">fingerDOWNTemplateToFlashPage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>PageID</code> - PageID(0~127)</dd>
<dd><code>templateData</code> - 模板数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false:失败</dd>
</dl>
</li>
</ul>
<a name="fingerDOWNTemplateToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerDOWNTemplateToBuffer</h4>
<pre>public&nbsp;boolean&nbsp;fingerDOWNTemplateToBuffer(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID,
                                          byte[]&nbsp;templateData)</pre>
<div class="block">下载指纹特征到指定的特征缓冲区中</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerDOWNTemplateToBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-byte:A-">fingerDOWNTemplateToBuffer</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>BufferID</code> - </dd>
<dd><code>templateData</code> - 模板数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false:失败</dd>
</dl>
</li>
</ul>
<a name="fingerPKTemplateBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerPKTemplateBuffer</h4>
<pre>public&nbsp;boolean&nbsp;fingerPKTemplateBuffer(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID1,
                                      <a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID2)</pre>
<div class="block">精确比对指定特征缓冲区中特征</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerPKTemplateBuffer-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerPKTemplateBuffer</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>BufferID1</code> - </dd>
<dd><code>BufferID2</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功  false:失败</dd>
</dl>
</li>
</ul>
<a name="fingerPKTemplatePage-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerPKTemplatePage</h4>
<pre>public&nbsp;boolean&nbsp;fingerPKTemplatePage(int&nbsp;PageID1,
                                    int&nbsp;PageID2)</pre>
<div class="block">精确比对指定Flash Page中特征</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerPKTemplatePage-int-int-">fingerPKTemplatePage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>PageID1</code> - </dd>
<dd><code>PageID2</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功  false:失败</dd>
</dl>
</li>
</ul>
<a name="fingerPKTemplate-int-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerPKTemplate</h4>
<pre>public&nbsp;boolean&nbsp;fingerPKTemplate(int&nbsp;PageID,
                                <a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID)</pre>
<div class="block">精确比对指定特征缓冲区和指定的Flash Page中特征</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerPKTemplate-int-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerPKTemplate</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>PageID</code> - </dd>
<dd><code>BufferID</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功  false:失败</dd>
</dl>
</li>
</ul>
<a name="fingerPKTemplate2-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerPKTemplate2</h4>
<pre>public&nbsp;boolean&nbsp;fingerPKTemplate2(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID,
                                 int&nbsp;PageID)</pre>
<div class="block">精确比对指定特征缓冲区和指定的Flash Page中特征</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerPKTemplate2-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-">fingerPKTemplate2</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>PageID</code> - </dd>
<dd><code>BufferID</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功  false:失败</dd>
</dl>
</li>
</ul>
<a name="fingerSearchTemplate-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerSearchTemplate</h4>
<pre>public&nbsp;int&nbsp;fingerSearchTemplate(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID,
                                int&nbsp;templateIdStart,
                                int&nbsp;templateIdEnd)</pre>
<div class="block">下载一个特征到指定特征缓冲区，然后用此特征搜索指纹库中的模板</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerSearchTemplate-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-int-int-">fingerSearchTemplate</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>BufferID</code> - Buffer id</dd>
<dd><code>templateIdStart</code> - 待搜索的起始 Template编号</dd>
<dd><code>templateIdEnd</code> - 待搜索的结束 Template编号</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回模板编号</dd>
</dl>
</li>
</ul>
<a name="fingerDELTemplateBufferID-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerDELTemplateBufferID</h4>
<pre>public&nbsp;boolean&nbsp;fingerDELTemplateBufferID(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>&nbsp;BufferID)</pre>
<div class="block">删除内存中指定的模板</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerDELTemplateBufferID-com.rscja.deviceapi.FingerprintWithTLK1NC.BufferEnum-">fingerDELTemplateBufferID</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>BufferID</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回指纹模板数据</dd>
</dl>
</li>
</ul>
<a name="fingerDELTemplatePageID-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerDELTemplatePageID</h4>
<pre>public&nbsp;boolean&nbsp;fingerDELTemplatePageID(int&nbsp;PageID)</pre>
<div class="block">删除FLASH中指定的模板</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerDELTemplatePageID-int-">fingerDELTemplatePageID</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>PageID</code> - PageID(0~127)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回指纹模板数据</dd>
</dl>
</li>
</ul>
<a name="fingerCLEARTemplate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerCLEARTemplate</h4>
<pre>public&nbsp;boolean&nbsp;fingerCLEARTemplate()</pre>
<div class="block">清空FLASH中指纹模板</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerCLEARTemplate--">fingerCLEARTemplate</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="fingerCLEARTemplateBuffer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerCLEARTemplateBuffer</h4>
<pre>public&nbsp;boolean&nbsp;fingerCLEARTemplateBuffer()</pre>
<div class="block">清空指纹特征缓冲区</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerCLEARTemplateBuffer--">fingerCLEARTemplateBuffer</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="fingerGETTemplateCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fingerGETTemplateCount</h4>
<pre>public&nbsp;int&nbsp;fingerGETTemplateCount()</pre>
<div class="block">获取可存储的指纹模板数</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#fingerGETTemplateCount--">fingerGETTemplateCount</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="generateImg-byte:A-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateImg</h4>
<pre>public&nbsp;boolean&nbsp;generateImg(byte[]&nbsp;data,
                           java.lang.String&nbsp;filePath)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#generateImg-byte:A-java.lang.String-">generateImg</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
</dl>
</li>
</ul>
<a name="startUPImage-com.rscja.deviceapi.FingerprintWithTLK1NC.IUPImageCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startUPImage</h4>
<pre>public&nbsp;void&nbsp;startUPImage(<a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.IUPImageCallback.html" title="interface in com.rscja.deviceapi">FingerprintWithTLK1NC.IUPImageCallback</a>&nbsp;callBack)</pre>
<div class="block">//  * @param  timeOut timeOut  (unit:millisecond)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#startUPImage-com.rscja.deviceapi.FingerprintWithTLK1NC.IUPImageCallback-">startUPImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
</dl>
</li>
</ul>
<a name="getStatusMsg-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatusMsg</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getStatusMsg(int&nbsp;status)</pre>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;java.lang.String&nbsp;getVersion()</pre>
<div class="block">获取模块的设备信息</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html#getVersion--">getVersion</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回模块的设备信息</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FingerprintWithTLK1NC.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/FingerprintWithMorpho.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/FingerprintWithTLK1NC.html" target="_top">Frames</a></li>
<li><a href="FingerprintWithTLK1NC.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
