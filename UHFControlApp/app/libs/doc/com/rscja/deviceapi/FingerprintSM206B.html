<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FingerprintSM206B</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FingerprintSM206B";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":9,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FingerprintSM206B.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/FingerprintSM206B.html" target="_top">Frames</a></li>
<li><a href="FingerprintSM206B.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Class FingerprintSM206B" class="title">Class FingerprintSM206B</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.FingerprintSM206B</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">FingerprintSM206B</span>
extends java.lang.Object
implements <a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></pre>
<div class="block">SM206B 指纹模块操作接口<br/>

  第一步:调用init(Context context) 函数初始化指纹模块<br/>
  第二步:调用指纹相关接口,比如：search()，getDeviceVersion()...<br/>
  第三步:调用free()释放指纹模块相关资源<br/>
  示例代码:<br/>
 public void Test() {<br/>
 <br>&emsp;     Context context;
 <br>&emsp;FingerprintSM206B fingerprintSM206B= FingerprintSM206B.getInstance();
 <br>&emsp;boolean result= fingerprintSM206B.init(context);
 <br>&emsp;if(!result){
 <br>&emsp;&emsp;//init fail
 <br>&emsp;&emsp;return;
 <br>&emsp;}
 <br>&emsp; fingerprintSM206B.getImage();
 <br>&emsp; fingerprintSM206B.getFingerTemplate();
 <br>&emsp; fingerprintSM206B.getDeviceVersion();
 <br>&emsp; //...........
 <br>&emsp;fingerprintSM206B.free();
  }</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintSM206B.html#enroll--">enroll</a></span>()</code>
<div class="block">录入指纹，用户录入 3~5 次指纹，生成 256 字节指纹特征值</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintSM206B.html#free--">free</a></span>()</code>
<div class="block">释放指纹模块</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintSM206B.html#getDeviceVersion--">getDeviceVersion</a></span>()</code>
<div class="block">获取指纹模块固件版本</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintSM206B.html#getFingerTemplate--">getFingerTemplate</a></span>()</code>
<div class="block">获取指纹模板数据，用户录入 1 次指纹，生成 256 字节指纹特征值</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintSM206B.html#getImage--">getImage</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/deviceapi/FingerprintSM206B.html" title="class in com.rscja.deviceapi">FingerprintSM206B</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintSM206B.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取指纹模块操作实例<br>
 Acquire fingerprint module operation Instance<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>android.graphics.Bitmap</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintSM206B.html#imgageRawToBimap-byte:A-">imgageRawToBimap</a></span>(byte[]&nbsp;imgBuf)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/FingerprintSM206B.html#init-android.content.Context-">init</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">初始化指纹模块</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/deviceapi/FingerprintSM206B.html" title="class in com.rscja.deviceapi">FingerprintSM206B</a>&nbsp;getInstance()</pre>
<div class="block">获取指纹模块操作实例<br>
 Acquire fingerprint module operation Instance<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>指纹模块操作实例<br>
 return fingerprint operation Instance<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code> - 配置错误异常<br>
                                Configuration error<br></dd>
</dl>
</li>
</ul>
<a name="init-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;boolean&nbsp;init(android.content.Context&nbsp;context)</pre>
<div class="block">初始化指纹模块</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html#init-android.content.Context-">init</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - Context</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success  false:fail</dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;boolean&nbsp;free()</pre>
<div class="block">释放指纹模块</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html#free--">free</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success  false:fail</dd>
</dl>
</li>
</ul>
<a name="getDeviceVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeviceVersion</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDeviceVersion()</pre>
<div class="block">获取指纹模块固件版本</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html#getDeviceVersion--">getDeviceVersion</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回指纹模块固件版本</dd>
</dl>
</li>
</ul>
<a name="enroll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enroll</h4>
<pre>public&nbsp;byte[]&nbsp;enroll()</pre>
<div class="block">录入指纹，用户录入 3~5 次指纹，生成 256 字节指纹特征值</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html#enroll--">enroll</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回 256 字节指纹模板数据</dd>
</dl>
</li>
</ul>
<a name="getFingerTemplate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFingerTemplate</h4>
<pre>public&nbsp;byte[]&nbsp;getFingerTemplate()</pre>
<div class="block">获取指纹模板数据，用户录入 1 次指纹，生成 256 字节指纹特征值</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html#getFingerTemplate--">getFingerTemplate</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回 256 字节指纹模板数据</dd>
</dl>
</li>
</ul>
<a name="getImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public&nbsp;byte[]&nbsp;getImage()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html#getImage--">getImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></code></dd>
</dl>
</li>
</ul>
<a name="imgageRawToBimap-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>imgageRawToBimap</h4>
<pre>public&nbsp;android.graphics.Bitmap&nbsp;imgageRawToBimap(byte[]&nbsp;imgBuf)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FingerprintSM206B.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/FingerprintSM206B.html" target="_top">Frames</a></li>
<li><a href="FingerprintSM206B.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
