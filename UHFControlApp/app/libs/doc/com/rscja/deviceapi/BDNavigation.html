<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Fri Sep 27 10:50:21 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>BDNavigation</title>
<meta name="date" content="2024-09-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BDNavigation";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":9,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BDNavigation.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/Barcode1D.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/BDNavigation.BDLocationListener.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/BDNavigation.html" target="_top">Frames</a></li>
<li><a href="BDNavigation.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Class BDNavigation" class="title">Class BDNavigation</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.BDNavigation</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">BDNavigation</span>
extends java.lang.Object</pre>
<div class="block">北斗导航模块操作类
 <p>
 注意： <br/>
 1、使用前请确认您的机器已安装此模块。 <br/>
 2、在操作设备前需要调用 <b><a href="../../../com/rscja/deviceapi/BDNavigation.html#open--"><code>open()</code></a></b> 打开设备，使用完后调用 <b><a href="../../../com/rscja/deviceapi/BDNavigation.html#close--"><code>close()</code></a></b> 关闭设备
 <br/>
 3、要实时跟踪位置的变化需要调用 <a href="../../../com/rscja/deviceapi/BDNavigation.html#addBDLocationListener-com.rscja.deviceapi.BDNavigation.BDProviderEnum-com.rscja.deviceapi.BDNavigation.BDLocationListener-"><code>addBDLocationListener(BDProviderEnum, BDLocationListener)</code></a>方法注册监听； <b>
 要实时跟踪定位状态需要调用<a href="../../../com/rscja/deviceapi/BDNavigation.html#addBDStatusListener-com.rscja.deviceapi.BDNavigation.BDStatusListener-"><code>addBDStatusListener(BDStatusListener)</code></a>方法注册监听<b>。
 要实时跟踪定位状态需要调用 <a href="../../../com/rscja/deviceapi/BDNavigation.html#addBDStatusListener-com.rscja.deviceapi.BDNavigation.BDStatusListener-"><code>addBDStatusListener(BDStatusListener)</code></a> 方法注册监听<b>。
 实时获取定位原始数据需要调用 <a href="../../../com/rscja/deviceapi/BDNavigation.html#addTestBDRawDataListener-com.rscja.deviceapi.BDNavigation.TestResultRawData-"><code>addTestBDRawDataListener(TestResultRawData testResultData)</code></a> 方法注册监听<b>。</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>liuruifeng</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.BDLocationListener.html" title="interface in com.rscja.deviceapi">BDNavigation.BDLocationListener</a></span></code>
<div class="block">北斗位置服务的监听接口</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.BDProviderEnum.html" title="enum in com.rscja.deviceapi">BDNavigation.BDProviderEnum</a></span></code>
<div class="block">定位类型枚举</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.BDStartModeEnum.html" title="enum in com.rscja.deviceapi">BDNavigation.BDStartModeEnum</a></span></code>
<div class="block">模块启动类型枚举</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.BDStatusListener.html" title="interface in com.rscja.deviceapi">BDNavigation.BDStatusListener</a></span></code>
<div class="block">北斗模块状态监听接口</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.TestResultRawData.html" title="interface in com.rscja.deviceapi">BDNavigation.TestResultRawData</a></span></code>
<div class="block">获取返回的所有原始数据，</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#addBDLocationListener-com.rscja.deviceapi.BDNavigation.BDProviderEnum-com.rscja.deviceapi.BDNavigation.BDLocationListener-">addBDLocationListener</a></span>(<a href="../../../com/rscja/deviceapi/BDNavigation.BDProviderEnum.html" title="enum in com.rscja.deviceapi">BDNavigation.BDProviderEnum</a>&nbsp;provider,
                     <a href="../../../com/rscja/deviceapi/BDNavigation.BDLocationListener.html" title="interface in com.rscja.deviceapi">BDNavigation.BDLocationListener</a>&nbsp;listener)</code>
<div class="block">跟踪位置的变化</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#addBDStatusListener-com.rscja.deviceapi.BDNavigation.BDStatusListener-">addBDStatusListener</a></span>(<a href="../../../com/rscja/deviceapi/BDNavigation.BDStatusListener.html" title="interface in com.rscja.deviceapi">BDNavigation.BDStatusListener</a>&nbsp;listener)</code>
<div class="block">添加定位状态监听</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#addTestBDRawDataListener-com.rscja.deviceapi.BDNavigation.TestResultRawData-">addTestBDRawDataListener</a></span>(<a href="../../../com/rscja/deviceapi/BDNavigation.TestResultRawData.html" title="interface in com.rscja.deviceapi">BDNavigation.TestResultRawData</a>&nbsp;testResultData)</code>
<div class="block">添加获取原始数据监听</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#changeBDProvider-com.rscja.deviceapi.BDNavigation.BDProviderEnum-">changeBDProvider</a></span>(<a href="../../../com/rscja/deviceapi/BDNavigation.BDProviderEnum.html" title="enum in com.rscja.deviceapi">BDNavigation.BDProviderEnum</a>&nbsp;provider)</code>
<div class="block">改变参与定位的卫星系统</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#changeBDStartMode-com.rscja.deviceapi.BDNavigation.BDStartModeEnum-">changeBDStartMode</a></span>(<a href="../../../com/rscja/deviceapi/BDNavigation.BDStartModeEnum.html" title="enum in com.rscja.deviceapi">BDNavigation.BDStartModeEnum</a>&nbsp;mode)</code>
<div class="block">模块改变启动模式</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#close--">close</a></span>()</code>
<div class="block">关闭北斗导航模块</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#closeEx--">closeEx</a></span>()</code>
<div class="block">北斗模块只关闭电源，不关闭串口</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#free--">free</a></span>()</code>
<div class="block">关闭北斗导航模块，关闭串口+模块下</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#getBDUTC--">getBDUTC</a></span>()</code>
<div class="block">UTC 时间</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/deviceapi/BDNavigation.html" title="class in com.rscja.deviceapi">BDNavigation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取北斗导航模块操作实例</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../com/rscja/deviceapi/entity/BDLocation.html" title="class in com.rscja.deviceapi.entity">BDLocation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#getLastLocation--">getLastLocation</a></span>()</code>
<div class="block">获取最后一次定位数据</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#getLastSatelliteUCount--">getLastSatelliteUCount</a></span>()</code>
<div class="block">获取最新已用卫星数</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#getLastsatelliteVCount--">getLastsatelliteVCount</a></span>()</code>
<div class="block">获取最新可视卫星数</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#getLastUTCDateTime--">getLastUTCDateTime</a></span>()</code>
<div class="block">获取最新UTC时间</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#getTimeToFirstFix--">getTimeToFirstFix</a></span>()</code>
<div class="block">获取第一次定位成功所用时间</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#isPowerOn--">isPowerOn</a></span>()</code>
<div class="block">判断设备是否上电 <br/>
 Detect whether device is powered on or not.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#open--">open</a></span>()</code>
<div class="block">打开北斗导航模块，打开串口+模块上电</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#openEx--">openEx</a></span>()</code>
<div class="block">北斗模块只上电不打开串口</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#setClosePort--">setClosePort</a></span>()</code>
<div class="block">关闭串口  不关电源</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#setOpenPort--">setOpenPort</a></span>()</code>
<div class="block">打开串口  不关电源</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/BDNavigation.html#writeData-byte:A-">writeData</a></span>(byte[]&nbsp;data)</code>
<div class="block">写入GPS命令</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setOpenPort--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOpenPort</h4>
<pre>public&nbsp;boolean&nbsp;setOpenPort()</pre>
<div class="block">打开串口  不关电源</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setClosePort--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setClosePort</h4>
<pre>public&nbsp;void&nbsp;setClosePort()</pre>
<div class="block">关闭串口  不关电源</div>
</li>
</ul>
<a name="writeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeData</h4>
<pre>public&nbsp;int&nbsp;writeData(byte[]&nbsp;data)</pre>
<div class="block">写入GPS命令</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getLastUTCDateTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastUTCDateTime</h4>
<pre>public&nbsp;java.util.Date&nbsp;getLastUTCDateTime()</pre>
<div class="block">获取最新UTC时间</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getLastSatelliteUCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastSatelliteUCount</h4>
<pre>public&nbsp;int&nbsp;getLastSatelliteUCount()</pre>
<div class="block">获取最新已用卫星数</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>已用卫星数</dd>
</dl>
</li>
</ul>
<a name="getLastsatelliteVCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastsatelliteVCount</h4>
<pre>public&nbsp;int&nbsp;getLastsatelliteVCount()</pre>
<div class="block">获取最新可视卫星数</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>可视卫星数</dd>
</dl>
</li>
</ul>
<a name="getTimeToFirstFix--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimeToFirstFix</h4>
<pre>public&nbsp;int&nbsp;getTimeToFirstFix()</pre>
<div class="block">获取第一次定位成功所用时间</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="addBDStatusListener-com.rscja.deviceapi.BDNavigation.BDStatusListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addBDStatusListener</h4>
<pre>public&nbsp;void&nbsp;addBDStatusListener(<a href="../../../com/rscja/deviceapi/BDNavigation.BDStatusListener.html" title="interface in com.rscja.deviceapi">BDNavigation.BDStatusListener</a>&nbsp;listener)</pre>
<div class="block">添加定位状态监听</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - </dd>
</dl>
</li>
</ul>
<a name="addTestBDRawDataListener-com.rscja.deviceapi.BDNavigation.TestResultRawData-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addTestBDRawDataListener</h4>
<pre>public&nbsp;void&nbsp;addTestBDRawDataListener(<a href="../../../com/rscja/deviceapi/BDNavigation.TestResultRawData.html" title="interface in com.rscja.deviceapi">BDNavigation.TestResultRawData</a>&nbsp;testResultData)</pre>
<div class="block">添加获取原始数据监听</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>testResultData</code> - </dd>
</dl>
</li>
</ul>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/deviceapi/BDNavigation.html" title="class in com.rscja.deviceapi">BDNavigation</a>&nbsp;getInstance()
                                throws ConfigurationException</pre>
<div class="block">获取北斗导航模块操作实例</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>北斗导航模块操作实例</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>ConfigurationException</code></dd>
</dl>
</li>
</ul>
<a name="getLastLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastLocation</h4>
<pre>public&nbsp;<a href="../../../com/rscja/deviceapi/entity/BDLocation.html" title="class in com.rscja.deviceapi.entity">BDLocation</a>&nbsp;getLastLocation()</pre>
<div class="block">获取最后一次定位数据</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="openEx--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openEx</h4>
<pre>public&nbsp;boolean&nbsp;openEx()</pre>
<div class="block">北斗模块只上电不打开串口</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功true，失败false</dd>
</dl>
</li>
</ul>
<a name="closeEx--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeEx</h4>
<pre>public&nbsp;boolean&nbsp;closeEx()</pre>
<div class="block">北斗模块只关闭电源，不关闭串口</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="open--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open()
             throws java.lang.SecurityException,
                    java.io.IOException</pre>
<div class="block">打开北斗导航模块，打开串口+模块上电</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功true，失败false</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dd><code>java.lang.SecurityException</code></dd>
</dl>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;boolean&nbsp;close()</pre>
<div class="block">关闭北斗导航模块</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功true，失败false</dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;boolean&nbsp;free()</pre>
<div class="block">关闭北斗导航模块，关闭串口+模块下</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功true，失败false</dd>
</dl>
</li>
</ul>
<a name="addBDLocationListener-com.rscja.deviceapi.BDNavigation.BDProviderEnum-com.rscja.deviceapi.BDNavigation.BDLocationListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addBDLocationListener</h4>
<pre>public&nbsp;void&nbsp;addBDLocationListener(<a href="../../../com/rscja/deviceapi/BDNavigation.BDProviderEnum.html" title="enum in com.rscja.deviceapi">BDNavigation.BDProviderEnum</a>&nbsp;provider,
                                  <a href="../../../com/rscja/deviceapi/BDNavigation.BDLocationListener.html" title="interface in com.rscja.deviceapi">BDNavigation.BDLocationListener</a>&nbsp;listener)</pre>
<div class="block">跟踪位置的变化</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - 监听对象</dd>
</dl>
</li>
</ul>
<a name="getBDUTC--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBDUTC</h4>
<pre>public&nbsp;void&nbsp;getBDUTC()</pre>
<div class="block">UTC 时间</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>getBDUTC</code> - </dd>
</dl>
</li>
</ul>
<a name="changeBDProvider-com.rscja.deviceapi.BDNavigation.BDProviderEnum-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeBDProvider</h4>
<pre>public&nbsp;void&nbsp;changeBDProvider(<a href="../../../com/rscja/deviceapi/BDNavigation.BDProviderEnum.html" title="enum in com.rscja.deviceapi">BDNavigation.BDProviderEnum</a>&nbsp;provider)</pre>
<div class="block">改变参与定位的卫星系统</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>provider</code> - </dd>
</dl>
</li>
</ul>
<a name="changeBDStartMode-com.rscja.deviceapi.BDNavigation.BDStartModeEnum-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>changeBDStartMode</h4>
<pre>public&nbsp;void&nbsp;changeBDStartMode(<a href="../../../com/rscja/deviceapi/BDNavigation.BDStartModeEnum.html" title="enum in com.rscja.deviceapi">BDNavigation.BDStartModeEnum</a>&nbsp;mode)</pre>
<div class="block">模块改变启动模式</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mode</code> - </dd>
</dl>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>public&nbsp;boolean&nbsp;isPowerOn()</pre>
<div class="block">判断设备是否上电 <br/>
 Detect whether device is powered on or not.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>已经上电返回true，反之返回false <br/>
 If powered on,return true, if not, return false.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BDNavigation.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/Barcode1D.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/BDNavigation.BDLocationListener.html" title="interface in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/BDNavigation.html" target="_top">Frames</a></li>
<li><a href="BDNavigation.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
