<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Package com.rscja.deviceapi</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package com.rscja.deviceapi";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package com.rscja.deviceapi" class="title">Uses of Package<br>com.rscja.deviceapi</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.custom">com.rscja.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.entity">com.rscja.deviceapi.entity</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.interfaces">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.custom">com.rscja.team.mtk.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.deviceapi">com.rscja.team.mtk.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.ble">com.rscja.team.qcom.ble</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.custom">com.rscja.team.qcom.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.uhfhandler">com.rscja.team.qcom.uhfhandler</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> used by <a href="../../../com/rscja/custom/package-summary.html">com.rscja.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithUHFUART.html#com.rscja.custom">RFIDWithUHFUART</a>
<div class="block">UHF模块手持机，串口通信操作类<br>
 UHF module handheld, serial communication operation interface <br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点标签先调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>RFIDWithUHFUART.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>设置标签回调接口，有标签数据就会回调这个接口。
        然后在调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#startInventoryTag--"><code>RFIDWithUHFUART.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#stopInventory--"><code>RFIDWithUHFUART.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/UhfBase.html#com.rscja.custom">UhfBase</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> used by <a href="../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Barcode1D.html#com.rscja.deviceapi">Barcode1D</a>
<div class="block">一维条码操作类<br>1D barcode operation class<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Barcode2D.html#com.rscja.deviceapi">Barcode2D</a>
<div class="block">二维条码操作类 <br>
 2D barcode operation class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/BleDevice.html#com.rscja.deviceapi">BleDevice</a>
<div class="block">BLE蓝牙的操作对象<br>

 1.通过<code>#connect(ConnectionStatusCallback<Object> bleStatusCallback)</code> 来连接蓝牙，bleStatusCallback是蓝牙状态的连接回调<br>
 2.通过<a href="../../../com/rscja/deviceapi/BleDevice.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>BleDevice.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a> 标签数据回调接口。<br>
 3.通过<a href="../../../com/rscja/deviceapi/BleDevice.html#startInventoryTag--"><code>BleDevice.startInventoryTag()</code></a> 开始盘点，在盘点过程中，ble设备只会响应<a href="../../../com/rscja/deviceapi/BleDevice.html#stopInventory--"><code>BleDevice.stopInventory()</code></a> ()}函数，不能设置和获取ble设备的相关其他参数。<br>
 4.通过<a href="../../../com/rscja/deviceapi/BleDevice.html#stopInventory--"><code>BleDevice.stopInventory()</code></a> ()} 停止盘点<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/BleDevice.BleDeviceInfo.html#com.rscja.deviceapi">BleDevice.BleDeviceInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/BluetoothReader.html#com.rscja.deviceapi">BluetoothReader</a>
<div class="block">蓝牙读写器操作类：<br>
 Bluetoot reader operation
 第一步：调用<a href="../../../com/rscja/deviceapi/BluetoothReader.html#init-android.content.Context-"><code>BluetoothReader.init(Context context)</code></a> 函数初始化蓝牙相关服务，退出应用程序需要调用<a href="../../../com/rscja/deviceapi/BluetoothReader.html#free--"><code>BluetoothReader.free()</code></a>是否蓝牙相关资源<br>
 First step: Call <a href="../../../com/rscja/deviceapi/BluetoothReader.html#init-android.content.Context-"><code>BluetoothReader.init(Context context)</code></a>to initiate BT service, call <a href="../../../com/rscja/deviceapi/BluetoothReader.html#free--"><code>BluetoothReader.free()</code></a> to exit application.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/BluetoothReader.OnDataChangeListener.html#com.rscja.deviceapi">BluetoothReader.OnDataChangeListener</a>
<div class="block">接收蓝牙原始数据的接口 <br>
 Interface of receiving initial data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/CardWithBYL.html#com.rscja.deviceapi">CardWithBYL</a>
<div class="block">白玉兰公交卡操作类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Fingerprint.html#com.rscja.deviceapi">Fingerprint</a>
<div class="block">指纹识别模块操作类,<br>
 Fingerprint identify module operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Fingerprint.BufferEnum.html#com.rscja.deviceapi">Fingerprint.BufferEnum</a>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintSM206B.html#com.rscja.deviceapi">FingerprintSM206B</a>
<div class="block">SM206B 指纹模块操作接口<br/>

  第一步:调用init(Context context) 函数初始化指纹模块<br/>
  第二步:调用指纹相关接口,比如：search()，getDeviceVersion()...<br/>
  第三步:调用free()释放指纹模块相关资源<br/>
  示例代码:<br/>
 public void Test() {<br/>
 <br>&emsp;     Context context;
 <br>&emsp;FingerprintSM206B fingerprintSM206B= FingerprintSM206B.getInstance();
 <br>&emsp;boolean result= fingerprintSM206B.init(context);
 <br>&emsp;if(!result){
 <br>&emsp;&emsp;//init fail
 <br>&emsp;&emsp;return;
 <br>&emsp;}
 <br>&emsp; fingerprintSM206B.getImage();
 <br>&emsp; fingerprintSM206B.getFingerTemplate();
 <br>&emsp; fingerprintSM206B.getDeviceVersion();
 <br>&emsp; //...........</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.html#com.rscja.deviceapi">FingerprintWithFIPS</a>
<div class="block">FIPS指纹识别模块操作类,<br>
 FIPS fingerprint indentify module operation type,<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.DataFormat.html#com.rscja.deviceapi">FingerprintWithFIPS.DataFormat</a>
<div class="block">指纹数据格式<br>
 Fingerprint data format<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.EnrollCallBack.html#com.rscja.deviceapi">FingerprintWithFIPS.EnrollCallBack</a>
<div class="block">采集指纹回调接口<br>
 call-back contact for acquiring fingerprint<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.FingerprintInfo.html#com.rscja.deviceapi">FingerprintWithFIPS.FingerprintInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.GRABCallBack.html#com.rscja.deviceapi">FingerprintWithFIPS.GRABCallBack</a>
<div class="block">指纹图像回调接口<br>
 fingerprint image call-back contact<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.IdentificationCallBack.html#com.rscja.deviceapi">FingerprintWithFIPS.IdentificationCallBack</a>
<div class="block">验证指纹的回调接口<br>
 call-back contact of verify fingerprint<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.PtCaptureCallBack.html#com.rscja.deviceapi">FingerprintWithFIPS.PtCaptureCallBack</a>
<div class="block">获取指纹模版数据回调接口<br>
 Acquire call-back contact fingerprint template data<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.TemplateVerifyCallBack.html#com.rscja.deviceapi">FingerprintWithFIPS.TemplateVerifyCallBack</a>
<div class="block">指纹模版比对<br>
 fingerprint template comparison<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.html#com.rscja.deviceapi">FingerprintWithMorpho</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.EnrollCallBack.html#com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a>
<div class="block">采集指纹回调接口<br>
 acquire fingerprint call-back contact<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.GrabCallBack.html#com.rscja.deviceapi">FingerprintWithMorpho.GrabCallBack</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.IdentificationCallBack.html#com.rscja.deviceapi">FingerprintWithMorpho.IdentificationCallBack</a>
<div class="block">验证指纹的回调接口<br>
 call-back contact for fingerprint verification<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.PtCaptureCallBack.html#com.rscja.deviceapi">FingerprintWithMorpho.PtCaptureCallBack</a>
<div class="block">设置获取指纹模版回调接口<br>
 setup fingerprint template acquire call-back contact<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.TemplateVerifyCallBack.html#com.rscja.deviceapi">FingerprintWithMorpho.TemplateVerifyCallBack</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithTLK1NC.html#com.rscja.deviceapi">FingerprintWithTLK1NC</a>
<div class="block">迪安杰</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithTLK1NC.BufferEnum.html#com.rscja.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithTLK1NC.IUPImageCallback.html#com.rscja.deviceapi">FingerprintWithTLK1NC.IUPImageCallback</a>
<div class="block">指纹图像回调接口<br>
 fingerprint image call-back contact<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithZAZ.html#com.rscja.deviceapi">FingerprintWithZAZ</a>
<div class="block">指昂</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithZAZ.BufferEnum.html#com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Infrared.html#com.rscja.deviceapi">Infrared</a>
<div class="block">红外模块操作类
 Infared module operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/LedLight.html#com.rscja.deviceapi">LedLight</a>
<div class="block">手柄LED灯控制类<br>
 Handdeld LED control type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Module.html#com.rscja.deviceapi">Module</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Printer.html#com.rscja.deviceapi">Printer</a>
<div class="block">打印机操作类<br>
 Printer operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Printer.BarcodeType.html#com.rscja.deviceapi">Printer.BarcodeType</a>
<div class="block">1D条码类型</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Printer.PrinterStatus.html#com.rscja.deviceapi">Printer.PrinterStatus</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Printer.PrinterStatusCallBack.html#com.rscja.deviceapi">Printer.PrinterStatusCallBack</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/PSAM.html#com.rscja.deviceapi">PSAM</a>
<div class="block">PSAM操作类<br>
 PSAM operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/R1HFAndPsamManage.html#com.rscja.deviceapi">R1HFAndPsamManage</a>
<div class="block">R1设备高频和PSAM操作类<br>
 R1 device HF and PSAM operation interfaces<br><br>

 第一步:通过<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#connect-android.content.Context-"><code>R1HFAndPsamManage.connect(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#connect-android.content.Context-"><code>R1HFAndPsamManage.connect(Context context)</code></a><br><br>

 第二步:获取相关接口对象<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443A--"><code>R1HFAndPsamManage.getHF14443A()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443B--"><code>R1HFAndPsamManage.getHF14443B()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF15693--"><code>R1HFAndPsamManage.getHF15693()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getPSAM--"><code>R1HFAndPsamManage.getPSAM()</code></a><br>
 Step 2:Gets the associated interface object <a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443A--"><code>R1HFAndPsamManage.getHF14443A()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF14443B--"><code>R1HFAndPsamManage.getHF14443B()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getHF15693--"><code>R1HFAndPsamManage.getHF15693()</code></a>、<a href="../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getPSAM--"><code>R1HFAndPsamManage.getPSAM()</code></a>  <br><br>
 <br>
 <br>
 Example Usage:<br>

   public void HFDemo() {
 &emsp;       R1HFAndPsamManage rfid = R1HFAndPsamManage.getInstance();
 &emsp;   if (!rfid.connect(context)) {
 &emsp;&emsp;        //fail
 &emsp;&emsp;         return;
 &emsp;    }
 &emsp;    IHF14443A ihf14443A = rfid.getHF14443A();
 &emsp;   IHF14443B ihf14443B = rfid.getHF14443B();
 &emsp;   IHF15693 ihf15693 = rfid.getHF15693();
 &emsp;  IPSAM ipsam = rfid.getPSAM();

 &emsp;  HF14443RequestEntity entity = ihf14443A.requestTypeA();
 &emsp;   if (entity == null) {
 &emsp;&emsp;        //"Card not found!"
 &emsp;&emsp;        return;
 &emsp;   }
 &emsp;   byte cMode =  0x60:A  ;  0x61:B
 &emsp;   byte cBlock;
 &emsp;    byte[] txtKey;//6bytes
 &emsp;  boolean reuslt = ihf14443A.authentication(cMode, cBlock, txtKey);
 &emsp;  if (!reuslt) {
 &emsp;&emsp;       //The key validation fail
 &emsp;&emsp;       return;
 &emsp;    }
 &emsp;  //................</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDBase.html#com.rscja.deviceapi">RFIDBase</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.html#com.rscja.deviceapi">RFIDWithISO14443A</a>
<div class="block">RFID模块ISO14443A协议操作类<br>
 RFID module ISO 14443A protocol operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html#com.rscja.deviceapi">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a>
<div class="block">DESFire卡加密类型<br>
 DESFire card encyption type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.DESFireFileTypekEnum.html#com.rscja.deviceapi">RFIDWithISO14443A.DESFireFileTypekEnum</a>
<div class="block">DESFire卡文件类型<br>
 DESFire card file typr<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.KeyType.html#com.rscja.deviceapi">RFIDWithISO14443A.KeyType</a>
<div class="block">密钥类型，适用于S50和S70标签。<br>
 key type, used for S50 and S70 tags.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.TagType.html#com.rscja.deviceapi">RFIDWithISO14443A.TagType</a>
<div class="block">M1标签类型定义<br>
 M1 tag type definition<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A4CPU.html#com.rscja.deviceapi">RFIDWithISO14443A4CPU</a>
<div class="block">RFID模块ISO14443A CPU卡协议操作类<br>
 RFID module ISO14443A CPU card protocol operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443B.html#com.rscja.deviceapi">RFIDWithISO14443B</a>
<div class="block">RFID模块ISO14443B协议操作类<br>
 RFID module ISO14443B protocol operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO15693.html#com.rscja.deviceapi">RFIDWithISO15693</a>
<div class="block">RFID模块ISO15693协议操作类,<br>
 RFID module ISO15639 protocol operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO15693.TagType.html#com.rscja.deviceapi">RFIDWithISO15693.TagType</a>
<div class="block">标签类型<br>
 Tag type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithLF.html#com.rscja.deviceapi">RFIDWithLF</a>
<div class="block">RFID低频（125K）操作类<br>
 RFID LF (125K) operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithUHFA4.html#com.rscja.deviceapi">RFIDWithUHFA4</a>
<div class="block">UHF模块 A4操作类<br>
 UHF module operation type<br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4.html#init-android.content.Context-"><code>RFIDWithUHFA4.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4.html#init-android.content.Context-"><code>RFIDWithUHFA4.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点标签先调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>RFIDWithUHFA4.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>设置标签回调接口，有标签数据就会回调这个接口。
        然后在调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4.html#startInventoryTag--"><code>RFIDWithUHFA4.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4.html#stopInventory--"><code>RFIDWithUHFA4.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithUHFA4RS232.html#com.rscja.deviceapi">RFIDWithUHFA4RS232</a>
<div class="block">操作URA4设备以及UHF模块相关接口。(通过其他android设备控制A4)<br>
 Operate URA4 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA4RS232.setUart(String path)</code></a>设置要连接的IP地址, 再通过 <a href="../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#init-android.content.Context-"><code>RFIDWithUHFA4RS232.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA4RS232.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA4RS232.setUart(String path)</code></a>-> <a href="../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#init-android.content.Context-"><code>RFIDWithUHFA4RS232.init(Context context)</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithUHFA8.html#com.rscja.deviceapi">RFIDWithUHFA8</a>
<div class="block">UHF模块 A8操作类<br>
 UHF module operation type<br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8.html#init-android.content.Context-"><code>RFIDWithUHFA8.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8.html#init-android.content.Context-"><code>RFIDWithUHFA8.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点标签先调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>RFIDWithUHFA8.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>设置标签回调接口，有标签数据就会回调这个接口。
        然后在调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8.html#startInventoryTag--"><code>RFIDWithUHFA8.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8.html#stopInventory--"><code>RFIDWithUHFA8.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithUHFA8RS232.html#com.rscja.deviceapi">RFIDWithUHFA8RS232</a>
<div class="block">操作URA8设备以及UHF模块相关接口。(通过其他android设备控制A8)<br>
 Operate URA8 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA8RS232.setUart(String path)</code></a>设置要连接的IP地址, 再通过 <a href="../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#init-android.content.Context-"><code>RFIDWithUHFA8RS232.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA8RS232.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA8RS232.setUart(String path)</code></a>-> <a href="../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#init-android.content.Context-"><code>RFIDWithUHFA8RS232.init(Context context)</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithUHFBLE.html#com.rscja.deviceapi">RFIDWithUHFBLE</a>
<div class="block">UHF模块低功耗蓝牙操作类<br>
 UHF module operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithUHFBLEManage.html#com.rscja.deviceapi">RFIDWithUHFBLEManage</a>
<div class="block">BLE设备读写器管理类，支持一对多连接<br>
 具体操作步骤:<br>
 1.通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#getInstance--"><code>RFIDWithUHFBLEManage.getInstance()</code></a> 函数获取 RFIDWithUHFBLEManage对象<br>
 2.调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#addBleDevice-java.lang.String-android.content.Context-"><code>RFIDWithUHFBLEManage.addBleDevice(String address, Context context)</code></a>添加BLE读写器设备，每个蓝牙地址作为设备的唯一标识<br>
 3.通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFBLEManage.html#getBleDeviceByMac-java.lang.String-"><code>RFIDWithUHFBLEManage.getBleDeviceByMac(String address)</code></a> 函数获取每一台蓝牙设备的操作对象 <code>com.rscja.deviceapi.BleDevice</code>。<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithUHFRLM.html#com.rscja.deviceapi">RFIDWithUHFRLM</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithUHFUART.html#com.rscja.deviceapi">RFIDWithUHFUART</a>
<div class="block">UHF模块手持机，串口通信操作类<br>
 UHF module handheld, serial communication operation interface <br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点标签先调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>RFIDWithUHFUART.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>设置标签回调接口，有标签数据就会回调这个接口。
        然后在调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#startInventoryTag--"><code>RFIDWithUHFUART.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#stopInventory--"><code>RFIDWithUHFUART.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithUHFUrxUart.html#com.rscja.deviceapi">RFIDWithUHFUrxUart</a>
<div class="block">URx 模块，串口通信操作类<br>
 URx module , serial communication operation interface <br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#init-android.content.Context-"><code>RFIDWithUHFUrxUart.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#init-android.content.Context-"><code>RFIDWithUHFUrxUart.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#startInventoryTag--"><code>RFIDWithUHFUrxUart.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#stopInventory--"><code>RFIDWithUHFUrxUart.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithUHFUrxUsbToUart.html#com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a>
<div class="block">URx 模块，串口通信操作类<br>
 URx module , serial communication operation interface <br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#init-android.content.Context-"><code>RFIDWithUHFUrxUsbToUart.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#init-android.content.Context-"><code>RFIDWithUHFUrxUsbToUart.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#startInventoryTag--"><code>RFIDWithUHFUrxUsbToUart.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#stopInventory--"><code>RFIDWithUHFUrxUsbToUart.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithUHFUSB.html#com.rscja.deviceapi">RFIDWithUHFUSB</a>
<div class="block">UHF模块手持机，USB通信操作类<br>
 UHF module handheld, USB operation interface <br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#init-android.content.Context-"><code>RFIDWithUHFUSB.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#init-android.content.Context-"><code>RFIDWithUHFUSB.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点调用<a href="../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#startInventoryTag--"><code>RFIDWithUHFUSB.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#stopInventory--"><code>RFIDWithUHFUSB.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/ScanerLedLight.html#com.rscja.deviceapi">ScanerLedLight</a>
<div class="block">扫描LED灯控制类（仅C6000有效）<br>
 Scanning LED light control type ( valid for C6000 only)<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/UhfBase.html#com.rscja.deviceapi">UhfBase</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/UsbFingerprint.html#com.rscja.deviceapi">UsbFingerprint</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.entity">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> used by <a href="../../../com/rscja/deviceapi/entity/package-summary.html">com.rscja.deviceapi.entity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html#com.rscja.deviceapi.entity">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a>
<div class="block">DESFire卡加密类型<br>
 DESFire card encyption type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.DESFireFileTypekEnum.html#com.rscja.deviceapi.entity">RFIDWithISO14443A.DESFireFileTypekEnum</a>
<div class="block">DESFire卡文件类型<br>
 DESFire card file typr<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.interfaces">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> used by <a href="../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/BleDevice.BleDeviceInfo.html#com.rscja.deviceapi.interfaces">BleDevice.BleDeviceInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/BluetoothReader.OnDataChangeListener.html#com.rscja.deviceapi.interfaces">BluetoothReader.OnDataChangeListener</a>
<div class="block">接收蓝牙原始数据的接口 <br>
 Interface of receiving initial data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Fingerprint.BufferEnum.html#com.rscja.deviceapi.interfaces">Fingerprint.BufferEnum</a>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.DataFormat.html#com.rscja.deviceapi.interfaces">FingerprintWithFIPS.DataFormat</a>
<div class="block">指纹数据格式<br>
 Fingerprint data format<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.EnrollCallBack.html#com.rscja.deviceapi.interfaces">FingerprintWithFIPS.EnrollCallBack</a>
<div class="block">采集指纹回调接口<br>
 call-back contact for acquiring fingerprint<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.FingerprintInfo.html#com.rscja.deviceapi.interfaces">FingerprintWithFIPS.FingerprintInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.GRABCallBack.html#com.rscja.deviceapi.interfaces">FingerprintWithFIPS.GRABCallBack</a>
<div class="block">指纹图像回调接口<br>
 fingerprint image call-back contact<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.IdentificationCallBack.html#com.rscja.deviceapi.interfaces">FingerprintWithFIPS.IdentificationCallBack</a>
<div class="block">验证指纹的回调接口<br>
 call-back contact of verify fingerprint<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.PtCaptureCallBack.html#com.rscja.deviceapi.interfaces">FingerprintWithFIPS.PtCaptureCallBack</a>
<div class="block">获取指纹模版数据回调接口<br>
 Acquire call-back contact fingerprint template data<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.TemplateVerifyCallBack.html#com.rscja.deviceapi.interfaces">FingerprintWithFIPS.TemplateVerifyCallBack</a>
<div class="block">指纹模版比对<br>
 fingerprint template comparison<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.EnrollCallBack.html#com.rscja.deviceapi.interfaces">FingerprintWithMorpho.EnrollCallBack</a>
<div class="block">采集指纹回调接口<br>
 acquire fingerprint call-back contact<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.GrabCallBack.html#com.rscja.deviceapi.interfaces">FingerprintWithMorpho.GrabCallBack</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.IdentificationCallBack.html#com.rscja.deviceapi.interfaces">FingerprintWithMorpho.IdentificationCallBack</a>
<div class="block">验证指纹的回调接口<br>
 call-back contact for fingerprint verification<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.PtCaptureCallBack.html#com.rscja.deviceapi.interfaces">FingerprintWithMorpho.PtCaptureCallBack</a>
<div class="block">设置获取指纹模版回调接口<br>
 setup fingerprint template acquire call-back contact<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.TemplateVerifyCallBack.html#com.rscja.deviceapi.interfaces">FingerprintWithMorpho.TemplateVerifyCallBack</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithTLK1NC.BufferEnum.html#com.rscja.deviceapi.interfaces">FingerprintWithTLK1NC.BufferEnum</a>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithTLK1NC.IUPImageCallback.html#com.rscja.deviceapi.interfaces">FingerprintWithTLK1NC.IUPImageCallback</a>
<div class="block">指纹图像回调接口<br>
 fingerprint image call-back contact<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithZAZ.BufferEnum.html#com.rscja.deviceapi.interfaces">FingerprintWithZAZ.BufferEnum</a>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Printer.BarcodeType.html#com.rscja.deviceapi.interfaces">Printer.BarcodeType</a>
<div class="block">1D条码类型</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Printer.PrinterStatusCallBack.html#com.rscja.deviceapi.interfaces">Printer.PrinterStatusCallBack</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.KeyType.html#com.rscja.deviceapi.interfaces">RFIDWithISO14443A.KeyType</a>
<div class="block">密钥类型，适用于S50和S70标签。<br>
 key type, used for S50 and S70 tags.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.TagType.html#com.rscja.deviceapi.interfaces">RFIDWithISO14443A.TagType</a>
<div class="block">M1标签类型定义<br>
 M1 tag type definition<br></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> used by <a href="../../../com/rscja/team/mtk/custom/package-summary.html">com.rscja.team.mtk.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/UhfBase.html#com.rscja.team.mtk.custom">UhfBase</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> used by <a href="../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.DataFormat.html#com.rscja.team.mtk.deviceapi">FingerprintWithFIPS.DataFormat</a>
<div class="block">指纹数据格式<br>
 Fingerprint data format<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.EnrollCallBack.html#com.rscja.team.mtk.deviceapi">FingerprintWithFIPS.EnrollCallBack</a>
<div class="block">采集指纹回调接口<br>
 call-back contact for acquiring fingerprint<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.FingerprintInfo.html#com.rscja.team.mtk.deviceapi">FingerprintWithFIPS.FingerprintInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.GRABCallBack.html#com.rscja.team.mtk.deviceapi">FingerprintWithFIPS.GRABCallBack</a>
<div class="block">指纹图像回调接口<br>
 fingerprint image call-back contact<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.IdentificationCallBack.html#com.rscja.team.mtk.deviceapi">FingerprintWithFIPS.IdentificationCallBack</a>
<div class="block">验证指纹的回调接口<br>
 call-back contact of verify fingerprint<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.PtCaptureCallBack.html#com.rscja.team.mtk.deviceapi">FingerprintWithFIPS.PtCaptureCallBack</a>
<div class="block">获取指纹模版数据回调接口<br>
 Acquire call-back contact fingerprint template data<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.TemplateVerifyCallBack.html#com.rscja.team.mtk.deviceapi">FingerprintWithFIPS.TemplateVerifyCallBack</a>
<div class="block">指纹模版比对<br>
 fingerprint template comparison<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.EnrollCallBack.html#com.rscja.team.mtk.deviceapi">FingerprintWithMorpho.EnrollCallBack</a>
<div class="block">采集指纹回调接口<br>
 acquire fingerprint call-back contact<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.GrabCallBack.html#com.rscja.team.mtk.deviceapi">FingerprintWithMorpho.GrabCallBack</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.IdentificationCallBack.html#com.rscja.team.mtk.deviceapi">FingerprintWithMorpho.IdentificationCallBack</a>
<div class="block">验证指纹的回调接口<br>
 call-back contact for fingerprint verification<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.PtCaptureCallBack.html#com.rscja.team.mtk.deviceapi">FingerprintWithMorpho.PtCaptureCallBack</a>
<div class="block">设置获取指纹模版回调接口<br>
 setup fingerprint template acquire call-back contact<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.TemplateVerifyCallBack.html#com.rscja.team.mtk.deviceapi">FingerprintWithMorpho.TemplateVerifyCallBack</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithTLK1NC.BufferEnum.html#com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithTLK1NC.IUPImageCallback.html#com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC.IUPImageCallback</a>
<div class="block">指纹图像回调接口<br>
 fingerprint image call-back contact<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Printer.BarcodeType.html#com.rscja.team.mtk.deviceapi">Printer.BarcodeType</a>
<div class="block">1D条码类型</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Printer.PrinterStatusCallBack.html#com.rscja.team.mtk.deviceapi">Printer.PrinterStatusCallBack</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.KeyType.html#com.rscja.team.mtk.deviceapi">RFIDWithISO14443A.KeyType</a>
<div class="block">密钥类型，适用于S50和S70标签。<br>
 key type, used for S50 and S70 tags.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.TagType.html#com.rscja.team.mtk.deviceapi">RFIDWithISO14443A.TagType</a>
<div class="block">M1标签类型定义<br>
 M1 tag type definition<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/UhfBase.html#com.rscja.team.mtk.deviceapi">UhfBase</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.ble">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> used by <a href="../../../com/rscja/team/qcom/ble/package-summary.html">com.rscja.team.qcom.ble</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/BluetoothReader.DecodeCallback.html#com.rscja.team.qcom.ble">BluetoothReader.DecodeCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/BluetoothReader.OnDataChangeListener.html#com.rscja.team.qcom.ble">BluetoothReader.OnDataChangeListener</a>
<div class="block">接收蓝牙原始数据的接口 <br>
 Interface of receiving initial data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/UhfBase.html#com.rscja.team.qcom.ble">UhfBase</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> used by <a href="../../../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/UhfBase.html#com.rscja.team.qcom.custom">UhfBase</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> used by <a href="../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/BleDevice.BleDeviceInfo.html#com.rscja.team.qcom.deviceapi">BleDevice.BleDeviceInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/BluetoothReader.DecodeCallback.html#com.rscja.team.qcom.deviceapi">BluetoothReader.DecodeCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/BluetoothReader.OnDataChangeListener.html#com.rscja.team.qcom.deviceapi">BluetoothReader.OnDataChangeListener</a>
<div class="block">接收蓝牙原始数据的接口 <br>
 Interface of receiving initial data.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/Fingerprint.BufferEnum.html#com.rscja.team.qcom.deviceapi">Fingerprint.BufferEnum</a>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.DataFormat.html#com.rscja.team.qcom.deviceapi">FingerprintWithFIPS.DataFormat</a>
<div class="block">指纹数据格式<br>
 Fingerprint data format<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.EnrollCallBack.html#com.rscja.team.qcom.deviceapi">FingerprintWithFIPS.EnrollCallBack</a>
<div class="block">采集指纹回调接口<br>
 call-back contact for acquiring fingerprint<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.FingerprintInfo.html#com.rscja.team.qcom.deviceapi">FingerprintWithFIPS.FingerprintInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.GRABCallBack.html#com.rscja.team.qcom.deviceapi">FingerprintWithFIPS.GRABCallBack</a>
<div class="block">指纹图像回调接口<br>
 fingerprint image call-back contact<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.IdentificationCallBack.html#com.rscja.team.qcom.deviceapi">FingerprintWithFIPS.IdentificationCallBack</a>
<div class="block">验证指纹的回调接口<br>
 call-back contact of verify fingerprint<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.PtCaptureCallBack.html#com.rscja.team.qcom.deviceapi">FingerprintWithFIPS.PtCaptureCallBack</a>
<div class="block">获取指纹模版数据回调接口<br>
 Acquire call-back contact fingerprint template data<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithFIPS.TemplateVerifyCallBack.html#com.rscja.team.qcom.deviceapi">FingerprintWithFIPS.TemplateVerifyCallBack</a>
<div class="block">指纹模版比对<br>
 fingerprint template comparison<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.EnrollCallBack.html#com.rscja.team.qcom.deviceapi">FingerprintWithMorpho.EnrollCallBack</a>
<div class="block">采集指纹回调接口<br>
 acquire fingerprint call-back contact<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.GrabCallBack.html#com.rscja.team.qcom.deviceapi">FingerprintWithMorpho.GrabCallBack</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.IdentificationCallBack.html#com.rscja.team.qcom.deviceapi">FingerprintWithMorpho.IdentificationCallBack</a>
<div class="block">验证指纹的回调接口<br>
 call-back contact for fingerprint verification<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.PtCaptureCallBack.html#com.rscja.team.qcom.deviceapi">FingerprintWithMorpho.PtCaptureCallBack</a>
<div class="block">设置获取指纹模版回调接口<br>
 setup fingerprint template acquire call-back contact<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithMorpho.TemplateVerifyCallBack.html#com.rscja.team.qcom.deviceapi">FingerprintWithMorpho.TemplateVerifyCallBack</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithTLK1NC.BufferEnum.html#com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC.BufferEnum</a>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithTLK1NC.IUPImageCallback.html#com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC.IUPImageCallback</a>
<div class="block">指纹图像回调接口<br>
 fingerprint image call-back contact<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/FingerprintWithZAZ.BufferEnum.html#com.rscja.team.qcom.deviceapi">FingerprintWithZAZ.BufferEnum</a>
<div class="block">模块缓冲区枚举<br>
 Module buffer zone example.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.html#com.rscja.team.qcom.deviceapi">RFIDWithISO14443A</a>
<div class="block">RFID模块ISO14443A协议操作类<br>
 RFID module ISO 14443A protocol operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.KeyType.html#com.rscja.team.qcom.deviceapi">RFIDWithISO14443A.KeyType</a>
<div class="block">密钥类型，适用于S50和S70标签。<br>
 key type, used for S50 and S70 tags.<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/RFIDWithISO14443A.TagType.html#com.rscja.team.qcom.deviceapi">RFIDWithISO14443A.TagType</a>
<div class="block">M1标签类型定义<br>
 M1 tag type definition<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/UhfBase.html#com.rscja.team.qcom.deviceapi">UhfBase</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.uhfhandler">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> used by <a href="../../../com/rscja/team/qcom/uhfhandler/package-summary.html">com.rscja.team.qcom.uhfhandler</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/deviceapi/class-use/BluetoothReader.DecodeCallback.html#com.rscja.team.qcom.uhfhandler">BluetoothReader.DecodeCallback</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
