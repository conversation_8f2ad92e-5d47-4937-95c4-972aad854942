<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>UhfBase.ErrorCode</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UhfBase.ErrorCode";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UhfBase.ErrorCode.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/UHFProtocolParseBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/UhfBase.ErrorCode.html" target="_top">Frames</a></li>
<li><a href="UhfBase.ErrorCode.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Class UhfBase.ErrorCode" class="title">Class UhfBase.ErrorCode</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.UhfBase.ErrorCode</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">UhfBase.ErrorCode</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERRCODE_FAILURE">ERRCODE_FAILURE</a></span></code>
<div class="block">失败(Failure)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERRCODE_SUCCESS">ERRCODE_SUCCESS</a></span></code>
<div class="block">成功(Success)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_INSUFFICIENT_PRIVILEGES">ERROR_INSUFFICIENT_PRIVILEGES</a></span></code>
<div class="block">没有权限访问(The Interrogator did not authenticate itself with sufficient privileges for the Tag to perform the operation)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_MEMORY_LOCK">ERROR_MEMORY_LOCK</a></span></code>
<div class="block">数据区被锁定(Memory lock)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_MEMORY_OVERRUN">ERROR_MEMORY_OVERRUN</a></span></code>
<div class="block">数据区超限(Memory Overflow)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_NO_ENOUGH_POWER_ON_TAG">ERROR_NO_ENOUGH_POWER_ON_TAG</a></span></code>
<div class="block">标签能量不足(no enough power on tag)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_NO_TAG">ERROR_NO_TAG</a></span></code>
<div class="block">找不到标签(No tags found.)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_OPERATION_FAILED">ERROR_OPERATION_FAILED</a></span></code>
<div class="block">操作失败(0peration failure)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_PASSWORD_IS_INCORRECT">ERROR_PASSWORD_IS_INCORRECT</a></span></code>
<div class="block">密码不正确(Incorrect Password)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_RECV_FAIL">ERROR_RECV_FAIL</a></span></code>
<div class="block">接收失败(Receive failure)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_RESPONSE_BUFFER_OVERFLOW">ERROR_RESPONSE_BUFFER_OVERFLOW</a></span></code>
<div class="block">缓冲区溢出(The operation failed because the ResponseBuffer overflowed)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_SEND_FAIL">ERROR_SEND_FAIL</a></span></code>
<div class="block">发送失败(Send failure)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ERROR_TAG_NO_REPLY">ERROR_TAG_NO_REPLY</a></span></code>
<div class="block">标签没有应答(Tag not responding)</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html#ErrorCode--">ErrorCode</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ERROR_NO_TAG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_NO_TAG</h4>
<pre>public static final&nbsp;int ERROR_NO_TAG</pre>
<div class="block">找不到标签(No tags found.)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_NO_TAG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_INSUFFICIENT_PRIVILEGES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_INSUFFICIENT_PRIVILEGES</h4>
<pre>public static final&nbsp;int ERROR_INSUFFICIENT_PRIVILEGES</pre>
<div class="block">没有权限访问(The Interrogator did not authenticate itself with sufficient privileges for the Tag to perform the operation)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_INSUFFICIENT_PRIVILEGES">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_MEMORY_OVERRUN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_MEMORY_OVERRUN</h4>
<pre>public static final&nbsp;int ERROR_MEMORY_OVERRUN</pre>
<div class="block">数据区超限(Memory Overflow)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_MEMORY_OVERRUN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_MEMORY_LOCK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_MEMORY_LOCK</h4>
<pre>public static final&nbsp;int ERROR_MEMORY_LOCK</pre>
<div class="block">数据区被锁定(Memory lock)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_MEMORY_LOCK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_TAG_NO_REPLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_TAG_NO_REPLY</h4>
<pre>public static final&nbsp;int ERROR_TAG_NO_REPLY</pre>
<div class="block">标签没有应答(Tag not responding)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_TAG_NO_REPLY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_PASSWORD_IS_INCORRECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_PASSWORD_IS_INCORRECT</h4>
<pre>public static final&nbsp;int ERROR_PASSWORD_IS_INCORRECT</pre>
<div class="block">密码不正确(Incorrect Password)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_PASSWORD_IS_INCORRECT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_RESPONSE_BUFFER_OVERFLOW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_RESPONSE_BUFFER_OVERFLOW</h4>
<pre>public static final&nbsp;int ERROR_RESPONSE_BUFFER_OVERFLOW</pre>
<div class="block">缓冲区溢出(The operation failed because the ResponseBuffer overflowed)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_RESPONSE_BUFFER_OVERFLOW">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_NO_ENOUGH_POWER_ON_TAG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_NO_ENOUGH_POWER_ON_TAG</h4>
<pre>public static final&nbsp;int ERROR_NO_ENOUGH_POWER_ON_TAG</pre>
<div class="block">标签能量不足(no enough power on tag)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_NO_ENOUGH_POWER_ON_TAG">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_OPERATION_FAILED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_OPERATION_FAILED</h4>
<pre>public static final&nbsp;int ERROR_OPERATION_FAILED</pre>
<div class="block">操作失败(0peration failure)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_OPERATION_FAILED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_SEND_FAIL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_SEND_FAIL</h4>
<pre>public static final&nbsp;int ERROR_SEND_FAIL</pre>
<div class="block">发送失败(Send failure)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_SEND_FAIL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ERROR_RECV_FAIL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_RECV_FAIL</h4>
<pre>public static final&nbsp;int ERROR_RECV_FAIL</pre>
<div class="block">接收失败(Receive failure)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.UhfBase.ErrorCode.ERROR_RECV_FAIL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ERRCODE_SUCCESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERRCODE_SUCCESS</h4>
<pre>public static final&nbsp;int ERRCODE_SUCCESS</pre>
<div class="block">成功(Success)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.UhfBase.ErrorCode.ERRCODE_SUCCESS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ERRCODE_FAILURE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ERRCODE_FAILURE</h4>
<pre>public static final&nbsp;int ERRCODE_FAILURE</pre>
<div class="block">失败(Failure)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.UhfBase.ErrorCode.ERRCODE_FAILURE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ErrorCode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ErrorCode</h4>
<pre>public&nbsp;ErrorCode()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UhfBase.ErrorCode.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/UHFProtocolParseBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/UhfBase.ErrorCode.html" target="_top">Frames</a></li>
<li><a href="UhfBase.ErrorCode.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
