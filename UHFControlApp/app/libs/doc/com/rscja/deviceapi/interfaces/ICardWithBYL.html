<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>ICardWithBYL</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ICardWithBYL";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ICardWithBYL.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IConnectionStatusChangedListener.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/ICardWithBYL.html" target="_top">Frames</a></li>
<li><a href="ICardWithBYL.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface ICardWithBYL" class="title">Interface ICardWithBYL</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/deviceapi/CardWithBYL.html" title="class in com.rscja.deviceapi">CardWithBYL</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html" title="class in com.rscja.team.qcom.deviceapi">CardWithBYL_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ICardWithBYL</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/ICardWithBYL.html#cardConsumeConfirm-java.lang.String-java.lang.String-java.lang.String-">cardConsumeConfirm</a></span>(java.lang.String&nbsp;time,
                  java.lang.String&nbsp;dealnum,
                  java.lang.String&nbsp;cardnum)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/ICardWithBYL.html#cleanFaultCard--">cleanFaultCard</a></span>()</code>
<div class="block">清除故障卡</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/ICardWithBYL.html#Consume-float-">Consume</a></span>(float&nbsp;money)</code>
<div class="block">扣费</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/ICardWithBYL.html#free--">free</a></span>()</code>
<div class="block">释放模块</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/ICardWithBYL.html#getBalance--">getBalance</a></span>()</code>
<div class="block">获取余额和有效期</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/ICardWithBYL.html#getMessage-int-">getMessage</a></span>(int&nbsp;code)</code>
<div class="block">将错误代码转换为语义消息</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/ICardWithBYL.html#getPsamCardID--">getPsamCardID</a></span>()</code>
<div class="block">获取psam卡id</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/ICardWithBYL.html#init--">init</a></span>()</code>
<div class="block">初始化RFID和PSAM模块</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/ICardWithBYL.html#isPowerOn--">isPowerOn</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="init--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>boolean&nbsp;init()</pre>
<div class="block">初始化RFID和PSAM模块</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>boolean&nbsp;free()</pre>
<div class="block">释放模块</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="getBalance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBalance</h4>
<pre>java.lang.String[]&nbsp;getBalance()</pre>
<div class="block">获取余额和有效期</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>失败返回null，索引0为余额，索引1为开始日期，索引2为结束日期</dd>
</dl>
</li>
</ul>
<a name="Consume-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Consume</h4>
<pre>java.lang.String[]&nbsp;Consume(float&nbsp;money)</pre>
<div class="block">扣费</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>money</code> - 金额</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回数组长度为1表示失败，索引0为失败消息； 数组长度为10表示成功，
 索引
 0：交易时间 索引
 1：pos机号 索引
 2：PSAM交易号 索引
 3：卡号 索引
 4：CPU卡交易号 索引
 5：城市代码 索引
 6：扣费前余额 索引
 7：扣费金额 索引
 8：交易类型 索引
 9：TAC
 10:行业代码
 11:卡类型
 12:有效时间
 13:芯片标志
 14:清算交易类型
 15:卡版本
 16：卡面号</dd>
</dl>
</li>
</ul>
<a name="getPsamCardID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPsamCardID</h4>
<pre>java.lang.String&nbsp;getPsamCardID()</pre>
<div class="block">获取psam卡id</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功返回psam卡id，失败返回null</dd>
</dl>
</li>
</ul>
<a name="cleanFaultCard--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cleanFaultCard</h4>
<pre>void&nbsp;cleanFaultCard()</pre>
<div class="block">清除故障卡</div>
</li>
</ul>
<a name="getMessage-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessage</h4>
<pre>java.lang.String&nbsp;getMessage(int&nbsp;code)</pre>
<div class="block">将错误代码转换为语义消息</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>code</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="cardConsumeConfirm-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cardConsumeConfirm</h4>
<pre>java.lang.String&nbsp;cardConsumeConfirm(java.lang.String&nbsp;time,
                                    java.lang.String&nbsp;dealnum,
                                    java.lang.String&nbsp;cardnum)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>time</code> - 时间   yyyyMMddHHmmss</dd>
<dd><code>dealnum</code> - 两个字节交易号</dd>
<dd><code>cardnum</code> - 四个字节卡号</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回数据：获取数据失败返回(错误码)，获取成功返回TAC的数据</dd>
</dl>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>boolean&nbsp;isPowerOn()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ICardWithBYL.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IConnectionStatusChangedListener.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/ICardWithBYL.html" target="_top">Frames</a></li>
<li><a href="ICardWithBYL.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
