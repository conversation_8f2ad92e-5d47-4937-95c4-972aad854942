<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IFingerprintWithZAZ</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IFingerprintWithZAZ";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IFingerprintWithZAZ.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IGPIStateCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" target="_top">Frames</a></li>
<li><a href="IFingerprintWithZAZ.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IFingerprintWithZAZ" class="title">Interface IFingerprintWithZAZ</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi">FingerprintWithZAZ</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IFingerprintWithZAZ</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#delete-int-int-">delete</a></span>(int&nbsp;templateIdStart,
      int&nbsp;templateIdEnd)</code>
<div class="block">删除指定编号范围(起始Template编号 ~ 结束Template编号)内全部已注册的 Template</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#downChar-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-byte:A-int:A-">downChar</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
        byte[]&nbsp;templateData,
        int[]&nbsp;outErrCode)</code>
<div class="block">下载指纹模板数据到模块指定的RamBuffer</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#downImageData-int-byte:A-int:A-">downImageData</a></span>(int&nbsp;imageBlockNumber,
             byte[]&nbsp;imageData,
             int[]&nbsp;outErrCode)</code>
<div class="block">下载指纹图像到模块,每次下载512个数据</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#exists-int-">exists</a></span>(int&nbsp;templateId)</code>
<div class="block">检查指定的编号是否已被注册</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#free--">free</a></span>()</code>
<div class="block">释放指纹模块<br>
 free fingerprint module<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#generate-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">generate</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
        int[]&nbsp;outErrCode)</code>
<div class="block">生成模版</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#getBrokenId-int-int-int:A-">getBrokenId</a></span>(int&nbsp;templateIdStart,
           int&nbsp;templateIdEnd,
           int[]&nbsp;outBrokenId)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#getDeviceInfo--">getDeviceInfo</a></span>()</code>
<div class="block">获取模块的设备信息</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#getEmptyId-int-int-">getEmptyId</a></span>(int&nbsp;templateIdStart,
          int&nbsp;templateIdEnd)</code>
<div class="block">获取指定编号范围内可注册的首个编号</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#getEnrollCount-int-int-int:A-">getEnrollCount</a></span>(int&nbsp;templateIdStart,
              int&nbsp;templateIdEnd,
              int[]&nbsp;outEnrollCount)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#getImage-int:A-">getImage</a></span>(int[]&nbsp;outErrCode)</code>
<div class="block">从采集器采集指纹图像并保存于 ImageBuffer 中</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#getParam-int-">getParam</a></span>(int&nbsp;type)</code>
<div class="block">根据指定的参数类型，读取设备参数</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#init--">init</a></span>()</code>
<div class="block">初始化指纹模块 <br>
 Initialize fingerprint module<br></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#isConnection--">isConnection</a></span>()</code>
<div class="block">检查 Target 和 Host 的连接状态<br></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#isPowerOn--">isPowerOn</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#loadChar-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">loadChar</a></span>(int&nbsp;templateIdStart,
        <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
        int[]&nbsp;outErrCode)</code>
<div class="block">读取模块中的指纹并暂存在RamBuffer中</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#match-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">match</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId1,
     <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId2,
     int[]&nbsp;outErrCode)</code>
<div class="block">指定的两个 Ram Buffer 中的 Template 之间进行比对</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#merge-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int-int:A-">merge</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
     int&nbsp;number,
     int[]&nbsp;outErrCode)</code>
<div class="block">合并模版</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#search-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int-int-int:A-int:A-int:A-">search</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
      int&nbsp;templateIdStart,
      int&nbsp;templateIdEnd,
      int[]&nbsp;outTemplateID,
      int[]&nbsp;outUpStatus,
      int[]&nbsp;outErrCode)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#setParam-int-int-int:A-">setParam</a></span>(int&nbsp;type,
        int&nbsp;value,
        int[]&nbsp;outErrCode)</code>
<div class="block">根据指定的参数类型，设置设备参数</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#sledCtrl-boolean-">sledCtrl</a></span>(boolean&nbsp;on)</code>
<div class="block">控制采集器背光灯的开/关</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#storeChar-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">storeChar</a></span>(int&nbsp;templateId,
         <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
         int[]&nbsp;outErrCode)</code>
<div class="block">将保存于指定 Ram Buffer 中的模板保存于指定编号的模块指纹库中</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#upChar-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">upChar</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
      int[]&nbsp;outErrCode)</code>
<div class="block">将暂存在RamBuffer中的指纹模板上传到主机</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#upImageData-int-java.lang.String-int:A-">upImageData</a></span>(int&nbsp;imageMode,
           java.lang.String&nbsp;lpFileName,
           int[]&nbsp;outErrCode)</code>
<div class="block">上传指纹图像到外部文件</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html#verify-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-int:A-int:A-">verify</a></span>(int&nbsp;templateId,
      <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;buffer,
      int[]&nbsp;outTemplateID,
      int[]&nbsp;outUpStatus,
      int[]&nbsp;outErrCode)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="init--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>boolean&nbsp;init()</pre>
<div class="block">初始化指纹模块 <br>
 Initialize fingerprint module<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true means success, false means failed<br></dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>boolean&nbsp;free()</pre>
<div class="block">释放指纹模块<br>
 free fingerprint module<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true means success,false means failed<br></dd>
</dl>
</li>
</ul>
<a name="isConnection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isConnection</h4>
<pre>boolean&nbsp;isConnection()</pre>
<div class="block">检查 Target 和 Host 的连接状态<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true means success,false means failed<br></dd>
</dl>
</li>
</ul>
<a name="getImage-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>int&nbsp;getImage(int[]&nbsp;outErrCode)</pre>
<div class="block">从采集器采集指纹图像并保存于 ImageBuffer 中</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>outErrCode</code> - 返回错误代码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>指纹图片原始数据</dd>
</dl>
</li>
</ul>
<a name="verify-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-int:A-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>verify</h4>
<pre>boolean&nbsp;verify(int&nbsp;templateId,
               <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;buffer,
               int[]&nbsp;outTemplateID,
               int[]&nbsp;outUpStatus,
               int[]&nbsp;outErrCode)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>templateId</code> - 待比对的 Template 编号</dd>
<dd><code>buffer</code> - Ram Buffer编号</dd>
<dd><code>outTemplateID</code> - Template 编号</dd>
<dd><code>outUpStatus</code> - 智能更新结果 1 :已进行智能更新，0：没有更新</dd>
<dd><code>outErrCode</code> - 返回错误代码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="search-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int-int-int:A-int:A-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>search</h4>
<pre>boolean&nbsp;search(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
               int&nbsp;templateIdStart,
               int&nbsp;templateIdEnd,
               int[]&nbsp;outTemplateID,
               int[]&nbsp;outUpStatus,
               int[]&nbsp;outErrCode)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ramBufferId</code> - 待比对的 Template 编号</dd>
<dd><code>templateIdStart</code> - 待搜索的起始 Template编号</dd>
<dd><code>templateIdEnd</code> - 待搜索的结束 Template编号</dd>
<dd><code>outUpStatus</code> - 智能更新结果 1 :已进行智能更新，0：没有更新</dd>
<dd><code>outErrCode</code> - 返回错误代码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="match-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>match</h4>
<pre>boolean&nbsp;match(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId1,
              <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId2,
              int[]&nbsp;outErrCode)</pre>
<div class="block">指定的两个 Ram Buffer 中的 Template 之间进行比对</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ramBufferId1</code> - </dd>
<dd><code>ramBufferId2</code> - </dd>
<dd><code>outErrCode</code> - 返回错误代码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="merge-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>merge</h4>
<pre>boolean&nbsp;merge(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
              int&nbsp;number,
              int[]&nbsp;outErrCode)</pre>
<div class="block">合并模版</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ramBufferId</code> - 模版id</dd>
<dd><code>number</code> - 合成个数(2或3)</dd>
<dd><code>outErrCode</code> - 返回错误代码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="generate-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generate</h4>
<pre>boolean&nbsp;generate(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
                 int[]&nbsp;outErrCode)</pre>
<div class="block">生成模版</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ramBufferId</code> - 模版id</dd>
<dd><code>outErrCode</code> - 返回错误代码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getEnrollCount-int-int-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnrollCount</h4>
<pre>boolean&nbsp;getEnrollCount(int&nbsp;templateIdStart,
                       int&nbsp;templateIdEnd,
                       int[]&nbsp;outEnrollCount)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>templateIdStart</code> - 起始Template编号</dd>
<dd><code>templateIdEnd</code> - 结束Template编号</dd>
<dd><code>outEnrollCount</code> - 输出已注册的 Template 个数</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getBrokenId-int-int-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBrokenId</h4>
<pre>boolean&nbsp;getBrokenId(int&nbsp;templateIdStart,
                    int&nbsp;templateIdEnd,
                    int[]&nbsp;outBrokenId)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>templateIdStart</code> - 起始Template编号</dd>
<dd><code>templateIdEnd</code> - 结束Template编号</dd>
<dd><code>outBrokenId</code> - 破损的Template, outBrokenId[0]:破损Template的个数,outBrokenId[1]:第一个破损Template编号</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="exists-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exists</h4>
<pre>boolean&nbsp;exists(int&nbsp;templateId)</pre>
<div class="block">检查指定的编号是否已被注册</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>templateId</code> - 模版编号</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:已经注册  false:未注册</dd>
</dl>
</li>
</ul>
<a name="getEmptyId-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEmptyId</h4>
<pre>int&nbsp;getEmptyId(int&nbsp;templateIdStart,
               int&nbsp;templateIdEnd)</pre>
<div class="block">获取指定编号范围内可注册的首个编号</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>templateIdStart</code> - 起始模版编号</dd>
<dd><code>templateIdEnd</code> - 结束模版编号</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回可注册的首个编号</dd>
</dl>
</li>
</ul>
<a name="delete-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>delete</h4>
<pre>boolean&nbsp;delete(int&nbsp;templateIdStart,
               int&nbsp;templateIdEnd)</pre>
<div class="block">删除指定编号范围(起始Template编号 ~ 结束Template编号)内全部已注册的 Template</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>templateIdStart</code> - 起始Template编号</dd>
<dd><code>templateIdEnd</code> - 结束Template编号</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="loadChar-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>loadChar</h4>
<pre>boolean&nbsp;loadChar(int&nbsp;templateIdStart,
                 <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
                 int[]&nbsp;outErrCode)</pre>
<div class="block">读取模块中的指纹并暂存在RamBuffer中</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>templateIdStart</code> - Template编号</dd>
<dd><code>ramBufferId</code> - Ram Buffer编号</dd>
<dd><code>outErrCode</code> - 返回错误代码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="storeChar-int-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>storeChar</h4>
<pre>boolean&nbsp;storeChar(int&nbsp;templateId,
                  <a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
                  int[]&nbsp;outErrCode)</pre>
<div class="block">将保存于指定 Ram Buffer 中的模板保存于指定编号的模块指纹库中</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>templateId</code> - Template编号</dd>
<dd><code>ramBufferId</code> - Ram Buffer编号</dd>
<dd><code>outErrCode</code> - 返回错误代码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setParam-int-int-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParam</h4>
<pre>boolean&nbsp;setParam(int&nbsp;type,
                 int&nbsp;value,
                 int[]&nbsp;outErrCode)</pre>
<div class="block">根据指定的参数类型，设置设备参数</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - 0-8
                   0:deviceID 设备编号 <br>
                   1:DupFPCheck 指纹复制检测<br>
                   2:Baudrate波特率<br>
                   3:AutoLome 自动学习 <br>
                   4:DupIDCheck 检测ID复制<br>
                   5:GenScore  合并模版的等级要求<br>
                   6:MatchScore  匹配指纹的等级要求<br>
                   7:FPCheckScore 获取图片的等级要求<br>
                   8:StdMode 指纹模版格式 <br></dd>
<dd><code>value</code> - deviceID   1-255<br>
                   DupFPCheck 0:不检查指纹复制;1:检测指纹复制。 默认值1<br>
                   Baudrate   1-8 (9600,19200,38400,57600,115200,230400,460800,921600)<br>
                   AutoLome   1:更新指纹模版数据块,0:不更新;<br>
                   DupIDCheck 1:检测ID复制,0:不检查<br>
                   GenScore   0-100,默认值30<br>
                   MatchScore 0-100,默认值45<br>
                   FPCheckScore 0-100,默认值60<br>
                   StdMode    0:保密,1:iso,2:ansi<br></dd>
<dd><code>outErrCode</code> - 返回错误代码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getParam-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParam</h4>
<pre>int&nbsp;getParam(int&nbsp;type)</pre>
<div class="block">根据指定的参数类型，读取设备参数</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - 0-8
             0:deviceID 设备编号 <br>
             1:DupFPCheck 指纹复制检测<br>
             2:Baudrate波特率<br>
             3:AutoLome 自动学习 <br>
             4:DupIDCheck 检测ID复制<br>
             5:GenScore  合并模版的等级要求<br>
             6:MatchScore  匹配指纹的等级要求<br>
             7:FPCheckScore 获取图片的等级要求<br>
             8:StdMode 指纹模版格式 <br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getDeviceInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeviceInfo</h4>
<pre>java.lang.String&nbsp;getDeviceInfo()</pre>
<div class="block">获取模块的设备信息</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回模块的设备信息</dd>
</dl>
</li>
</ul>
<a name="upImageData-int-java.lang.String-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>upImageData</h4>
<pre>int&nbsp;upImageData(int&nbsp;imageMode,
                java.lang.String&nbsp;lpFileName,
                int[]&nbsp;outErrCode)</pre>
<div class="block">上传指纹图像到外部文件</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>imageMode</code> - 0(完全模式):则发送完整图像 ,1(四分之一模式):发送1/4图像</dd>
<dd><code>lpFileName</code> - 文件位置，包括文件名，函数根据该参数生成图像文件</dd>
<dd><code>outErrCode</code> - 返回错误代码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回生成的文件大小</dd>
</dl>
</li>
</ul>
<a name="downImageData-int-byte:A-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>downImageData</h4>
<pre>boolean&nbsp;downImageData(int&nbsp;imageBlockNumber,
                      byte[]&nbsp;imageData,
                      int[]&nbsp;outErrCode)</pre>
<div class="block">下载指纹图像到模块,每次下载512个数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>imageBlockNumber</code> - 图像数据块号 (0-116)</dd>
<dd><code>imageData</code> - 图像数据   固定一次发512字节</dd>
<dd><code>outErrCode</code> - 返回错误代码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="sledCtrl-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sledCtrl</h4>
<pre>boolean&nbsp;sledCtrl(boolean&nbsp;on)</pre>
<div class="block">控制采集器背光灯的开/关</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>on:</code> - true:开  , false:关</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="upChar-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>upChar</h4>
<pre>byte[]&nbsp;upChar(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
              int[]&nbsp;outErrCode)</pre>
<div class="block">将暂存在RamBuffer中的指纹模板上传到主机</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ramBufferId</code> - Ram Buffer编号</dd>
<dd><code>outErrCode</code> - 返回错误代码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>模板数据</dd>
</dl>
</li>
</ul>
<a name="downChar-com.rscja.deviceapi.FingerprintWithZAZ.BufferEnum-byte:A-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>downChar</h4>
<pre>boolean&nbsp;downChar(<a href="../../../../com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi">FingerprintWithZAZ.BufferEnum</a>&nbsp;ramBufferId,
                 byte[]&nbsp;templateData,
                 int[]&nbsp;outErrCode)</pre>
<div class="block">下载指纹模板数据到模块指定的RamBuffer</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ramBufferId</code> - Ram Buffer编号</dd>
<dd><code>templateData</code> - 模版数据，固定1024长度</dd>
<dd><code>outErrCode</code> - 返回错误代码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>boolean&nbsp;isPowerOn()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IFingerprintWithZAZ.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IGPIStateCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" target="_top">Frames</a></li>
<li><a href="IFingerprintWithZAZ.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
