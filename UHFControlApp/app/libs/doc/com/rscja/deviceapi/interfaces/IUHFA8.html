<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IUHFA8</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IUHFA8";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFA8.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IUHFA8.html" target="_top">Frames</a></li>
<li><a href="IUHFA8.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IUHFA8" class="title">Interface IUHFA8</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8NetWork</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8RS232</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8NetWork_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IUHFA8</span>
extends <a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC">Bank_EPC</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED">Bank_RESERVED</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID">Bank_TID</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER">Bank_USER</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_ACCESS">LockBank_ACCESS</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_EPC">LockBank_EPC</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_KILL">LockBank_KILL</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_TID">LockBank_TID</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_USER">LockBank_USER</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_LOCK">LockMode_LOCK</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_OPEN">LockMode_OPEN</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_PLOCK">LockMode_PLOCK</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_POPEN">LockMode_POPEN</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#UPDATE_UHF">UPDATE_UHF</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IRFIDWithUHFA8">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a></h3>
<code><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#inputStatus--">inputStatus</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#output3Off--">output3Off</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#output3On--">output3On</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#output4Off--">output4Off</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#output4On--">output4On</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IMultipleAntenna">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a></h3>
<code><a href="../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getANT--">getANT</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getAntennaPower--">getAntennaPower</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#setANT-java.util.List-">setANT</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUHFURAxExtend">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></h3>
<code><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#closeWifi--">closeWifi</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#disableBeep--">disableBeep</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#enableBeep--">enableBeep</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getAndroidDeviceHardwareVersion--">getAndroidDeviceHardwareVersion</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getEthernetIpConfig--">getEthernetIpConfig</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getReaderCurrentIp--">getReaderCurrentIp</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getTcpServiceVersion--">getTcpServiceVersion</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getWifiInfo--">getWifiInfo</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getWifiIpConfig--">getWifiIpConfig</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#isEnableBeep--">isEnableBeep</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#openWifi--">openWifi</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#outputOnAndOff-java.util.List-">outputOnAndOff</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#readyUpgradeTcpService--">readyUpgradeTcpService</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#rebootDevice--">rebootDevice</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setBuzzerOff--">setBuzzerOff</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setBuzzerOn-int-">setBuzzerOn</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setEthernetConfigInfo-com.rscja.deviceapi.entity.ReaderIPEntity-">setEthernetConfigInfo</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setEthernetIpDynamicAssign--">setEthernetIpDynamicAssign</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setGPIStateCallback-com.rscja.deviceapi.interfaces.IGPIStateCallback-">setGPIStateCallback</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setGPIStateReverse-boolean-">setGPIStateReverse</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setTcpServicePort-int-">setTcpServicePort</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setUpgradeProgress-com.rscja.deviceapi.interfaces.IUpgradeProgress-">setUpgradeProgress</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setUpgradeTcpServiceData-byte:A-">setUpgradeTcpServiceData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setWifiConfigInfo-com.rscja.deviceapi.entity.WifiConfig-">setWifiConfigInfo</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#startUpgradeTcpService--">startUpgradeTcpService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#factoryReset--">factoryReset</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#free--">free</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getConnectStatus--">getConnectStatus</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getCW--">getCW</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getFastInventoryMode--">getFastInventoryMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getFrequencyMode--">getFrequencyMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getGen2--">getGen2</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getProtocol--">getProtocol</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getRFLink--">getRFLink</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getTemperature--">getTemperature</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getVersion--">getVersion</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#init-android.content.Context-">init</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#inventorySingleTag--">inventorySingleTag</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#isInventorying--">isInventorying</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-">killTag</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-">readData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">readData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#readTagFromBuffer--">readTagFromBuffer</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setCW-int-">setCW</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDMode--">setEPCAndTIDMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-int-int-">setEPCAndTIDUserMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCMode--">setEPCMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFastID-boolean-">setFastID</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFastInventoryMode-boolean-">setFastInventoryMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFilter-int-int-int-java.lang.String-">setFilter</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFreHop-float-">setFreHop</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFrequencyMode-int-">setFrequencyMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">setGen2</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setProtocol-int-">setProtocol</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setRFLink-int-">setRFLink</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setTagFocus-boolean-">setTagFocus</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--">startInventoryTag</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--">stopInventory</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot--">uhfJump2Boot</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot-int-">uhfJump2Boot</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStartUpdate--">uhfStartUpdate</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStopUpdate--">uhfStopUpdate</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfUpdating-byte:A-">uhfUpdating</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFA8.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IUHFA8.html" target="_top">Frames</a></li>
<li><a href="IUHFA8.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
