<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IHF15693</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IHF15693";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IHF15693.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IHF15693.html" target="_top">Frames</a></li>
<li><a href="IHF15693.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IHF15693" class="title">Interface IHF15693</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf">HF15693</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IHF15693</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF15693.html#lockAFI--">lockAFI</a></span>()</code>
<div class="block">锁定AFI(lock AFI)</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF15693.html#lockDsfid--">lockDsfid</a></span>()</code>
<div class="block">锁定Dsfid(lock Dsfid)</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF15693.html#read-com.rscja.deviceapi.entity.HF15693RequestEntity-int-">read</a></span>(<a href="../../../../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a>&nbsp;entity,
    int&nbsp;block)</code>
<div class="block">读卡(Read card)</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF15693.html#request15693--">request15693</a></span>()</code>
<div class="block">寻卡(Search card)<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF15693.html#write-com.rscja.deviceapi.entity.HF15693RequestEntity-int-byte:A-">write</a></span>(<a href="../../../../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a>&nbsp;entity,
     int&nbsp;block,
     byte[]&nbsp;pszData)</code>
<div class="block">写卡(Write card)</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF15693.html#writeAFI-com.rscja.deviceapi.entity.HF15693RequestEntity-byte-">writeAFI</a></span>(<a href="../../../../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a>&nbsp;entity,
        byte&nbsp;afi)</code>
<div class="block">写入AFI(Write AFi)</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF15693.html#writeDsfid-com.rscja.deviceapi.entity.HF15693RequestEntity-byte-">writeDsfid</a></span>(<a href="../../../../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a>&nbsp;entity,
          byte&nbsp;dsfid)</code>
<div class="block">写入Dsfid(Write Dsfid)</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="request15693--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>request15693</h4>
<pre><a href="../../../../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a>&nbsp;request15693()</pre>
<div class="block">寻卡(Search card)<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success will return HF15693RequestEntity data, failure will return null.<br></dd>
</dl>
</li>
</ul>
<a name="read-com.rscja.deviceapi.entity.HF15693RequestEntity-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>byte[]&nbsp;read(<a href="../../../../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a>&nbsp;entity,
            int&nbsp;block)</pre>
<div class="block">读卡(Read card)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>entity</code> - HF15693RequestEntity</dd>
<dd><code>block</code> - 数据块0~27 (the cBlock of card, value 0~27 )</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success will return data, failure will return null.<br></dd>
</dl>
</li>
</ul>
<a name="write-com.rscja.deviceapi.entity.HF15693RequestEntity-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>boolean&nbsp;write(<a href="../../../../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a>&nbsp;entity,
              int&nbsp;block,
              byte[]&nbsp;pszData)</pre>
<div class="block">写卡(Write card)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>entity</code> - HF15693RequestEntity</dd>
<dd><code>block</code> - 数据块0~27 (the cBlock of card, value 0~27 )</dd>
<dd><code>pszData</code> - 四个字节的数据(4 bytes Block data)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success false:fail</dd>
</dl>
</li>
</ul>
<a name="writeAFI-com.rscja.deviceapi.entity.HF15693RequestEntity-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeAFI</h4>
<pre>boolean&nbsp;writeAFI(<a href="../../../../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a>&nbsp;entity,
                 byte&nbsp;afi)</pre>
<div class="block">写入AFI(Write AFi)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>entity</code> - HF15693RequestEntity</dd>
<dd><code>afi</code> - AFI值(AFI value)</dd>
</dl>
</li>
</ul>
<a name="lockAFI--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lockAFI</h4>
<pre>boolean&nbsp;lockAFI()</pre>
<div class="block">锁定AFI(lock AFI)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success false:fail</dd>
</dl>
</li>
</ul>
<a name="writeDsfid-com.rscja.deviceapi.entity.HF15693RequestEntity-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeDsfid</h4>
<pre>boolean&nbsp;writeDsfid(<a href="../../../../com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF15693RequestEntity</a>&nbsp;entity,
                   byte&nbsp;dsfid)</pre>
<div class="block">写入Dsfid(Write Dsfid)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>entity</code> - HF15693RequestEntity</dd>
<dd><code>dsfid</code> - Dsfid值(Dsfid value)</dd>
</dl>
</li>
</ul>
<a name="lockDsfid--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>lockDsfid</h4>
<pre>boolean&nbsp;lockDsfid()</pre>
<div class="block">锁定Dsfid(lock Dsfid)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success false:fail</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IHF15693.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IHF15693.html" target="_top">Frames</a></li>
<li><a href="IHF15693.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
