<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IBarcodeUtility</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IBarcodeUtility";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IBarcodeUtility.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeVideoCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IBarcodeUtility.html" target="_top">Frames</a></li>
<li><a href="IBarcodeUtility.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IBarcodeUtility" class="title">Interface IBarcodeUtility</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a>, <a href="../../../../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeUtility_mtk</a>, <a href="../../../../com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode">BarcodeUtility_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IBarcodeUtility</span></pre>
<div class="block">条码操作类
 barcode operate class



 注意：
 Attention:
 1、使用前请确认您的机器已安装键盘助手v1.9.0 之后的版本。
 1. Confirm keyboardeumulator v1.9.0 has been installed before using.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>zhoupin</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#close-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">close</a></span>(android.content.Context&nbsp;context,
     <a href="../../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</code>
<div class="block">关闭指定功能</br>
 Switch off designated function</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#closeKeyboardHelper-android.content.Context-">closeKeyboardHelper</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">关闭键盘助手功能总开关</br>
 Switch off main function switch of keyboardemulator</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enableContinuousScan-android.content.Context-boolean-">enableContinuousScan</a></span>(android.content.Context&nbsp;context,
                    boolean&nbsp;isContinuous)</code>
<div class="block">启用连续扫描</br>
 Enable continuous scan</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enableEnter-android.content.Context-boolean-">enableEnter</a></span>(android.content.Context&nbsp;context,
           boolean&nbsp;isEnter)</code>
<div class="block">是否启用回车</br>
 Whether enable Enter function</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enablePlayFailureSound-android.content.Context-boolean-">enablePlayFailureSound</a></span>(android.content.Context&nbsp;context,
                      boolean&nbsp;isFailureSound)</code>
<div class="block">扫描失败是否播放提示音</br>
 whether playing notification when scanning failure.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enablePlaySuccessSound-android.content.Context-boolean-">enablePlaySuccessSound</a></span>(android.content.Context&nbsp;context,
                      boolean&nbsp;isSuccessSound)</code>
<div class="block">是否播放成功的提示音</br>
 whether playing notification when scanning success.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enableTAB-android.content.Context-boolean-">enableTAB</a></span>(android.content.Context&nbsp;context,
         boolean&nbsp;isTab)</code>
<div class="block">是否启用TAB</br>
 Whether enable TAB function</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enableVibrate-android.content.Context-boolean-">enableVibrate</a></span>(android.content.Context&nbsp;context,
             boolean&nbsp;isVibrate)</code>
<div class="block">扫描成功是否震动提示</br>
 whether vibrating when scanning success</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#filterCharacter-android.content.Context-java.lang.String-">filterCharacter</a></span>(android.content.Context&nbsp;context,
               java.lang.String&nbsp;chars)</code>
<div class="block">过滤字符串</br>
 Filter charactor string</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#interceptTrimLeft-android.content.Context-int-">interceptTrimLeft</a></span>(android.content.Context&nbsp;context,
                 int&nbsp;num)</code>
<div class="block">截取左边字符串数量</br>
 Capture left side charactor string amount</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#interceptTrimRight-android.content.Context-int-">interceptTrimRight</a></span>(android.content.Context&nbsp;context,
                  int&nbsp;num)</code>
<div class="block">截取右边字符串数量</br>
 Capture right side charactor string amount</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#open-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">open</a></span>(android.content.Context&nbsp;context,
    <a href="../../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</code>
<div class="block">打开指定功能</br>
 Switch on designated function</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#openKeyboardHelper-android.content.Context-">openKeyboardHelper</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">打开键盘助手功能总开关</br>
 Switch on main function switch of keyboardemulator</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setBarcodeEncodingFormat-android.content.Context-int-">setBarcodeEncodingFormat</a></span>(android.content.Context&nbsp;context,
                        int&nbsp;format)</code>
<div class="block">设置条码编码格式</br>
 Setup barcode coding format</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setContinuousScanIntervalTime-android.content.Context-int-">setContinuousScanIntervalTime</a></span>(android.content.Context&nbsp;context,
                             int&nbsp;intervalTime)</code>
<div class="block">设置连续扫描间隔时间</br>
 Setup continuous scanning duration</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setContinuousScanTimeOut-android.content.Context-int-">setContinuousScanTimeOut</a></span>(android.content.Context&nbsp;context,
                        int&nbsp;timeOut)</code>
<div class="block">设置连续扫描超时时间</br>
 Setup continuous scanning overtime duration</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setOutputMode-android.content.Context-int-">setOutputMode</a></span>(android.content.Context&nbsp;context,
             int&nbsp;mode)</code>
<div class="block">设置输出模式</br>
 Setup ouput mode</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setParam_zebra-android.content.Context-int-int-">setParam_zebra</a></span>(android.content.Context&nbsp;context,
              int&nbsp;paramId,
              int&nbsp;paramValue)</code>
<div class="block">设置斑马扫描头参数,扫描头上电之后设置一次即可,扫描头断电之后失效。(备注：键盘助手v2.2.0.3 之后的版本才支持)</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setPrefix-android.content.Context-java.lang.String-">setPrefix</a></span>(android.content.Context&nbsp;context,
         java.lang.String&nbsp;prefix)</code>
<div class="block">设置前缀</br>
 Setup prefix</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setReleaseScan-android.content.Context-boolean-">setReleaseScan</a></span>(android.content.Context&nbsp;context,
              boolean&nbsp;enable)</code>
<div class="block">松开扫描按键是否停止扫描</br>
 loose scanning button to stop scanning or not</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setScanFailureBroadcast-android.content.Context-boolean-">setScanFailureBroadcast</a></span>(android.content.Context&nbsp;context,
                       boolean&nbsp;enable)</code>
<div class="block">扫描失败是否发送广播</br>
 Send broadcast when scanning failure.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setScanOutTime-android.content.Context-int-">setScanOutTime</a></span>(android.content.Context&nbsp;context,
              int&nbsp;time)</code>
<div class="block">设置超时时间</br>
 Setup overtime duration</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setScanResultBroadcast-android.content.Context-java.lang.String-java.lang.String-">setScanResultBroadcast</a></span>(android.content.Context&nbsp;context,
                      java.lang.String&nbsp;broadcastAction,
                      java.lang.String&nbsp;extraName)</code>
<div class="block">设置接收扫描数据的广播</br>
 Setup broad cast of received scanning data</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setSuffix-android.content.Context-java.lang.String-">setSuffix</a></span>(android.content.Context&nbsp;context,
         java.lang.String&nbsp;suffix)</code>
<div class="block">设置后缀</br>
 Setup suffix</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#startScan-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">startScan</a></span>(android.content.Context&nbsp;context,
         <a href="../../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</code>
<div class="block">开始扫描 </br>
 Start scanning</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#stopScan-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">stopScan</a></span>(android.content.Context&nbsp;context,
        <a href="../../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</code>
<div class="block">停止扫描</br>
 Stop scanning</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="open-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>void&nbsp;open(android.content.Context&nbsp;context,
          <a href="../../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</pre>
<div class="block">打开指定功能</br>
 Switch on designated function</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>modul</code> - 功能模块</dd>
</dl>
</li>
</ul>
<a name="close-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>void&nbsp;close(android.content.Context&nbsp;context,
           <a href="../../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</pre>
<div class="block">关闭指定功能</br>
 Switch off designated function</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>modul</code> - 功能模块</dd>
</dl>
</li>
</ul>
<a name="startScan-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startScan</h4>
<pre>void&nbsp;startScan(android.content.Context&nbsp;context,
               <a href="../../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</pre>
<div class="block">开始扫描 </br>
 Start scanning</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>modul</code> - 功能模块</dd>
</dl>
</li>
</ul>
<a name="stopScan-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopScan</h4>
<pre>void&nbsp;stopScan(android.content.Context&nbsp;context,
              <a href="../../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</pre>
<div class="block">停止扫描</br>
 Stop scanning</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>modul</code> - 功能模块</dd>
</dl>
</li>
</ul>
<a name="setOutputMode-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutputMode</h4>
<pre>void&nbsp;setOutputMode(android.content.Context&nbsp;context,
                   int&nbsp;mode)</pre>
<div class="block">设置输出模式</br>
 Setup ouput mode</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>mode</code> - 0:扫描到光标位置(scan content to cursor)    1:剪切板(clipboard)   2:广播(broadcast)    3:模拟键盘(analog keyboard)</dd>
</dl>
</li>
</ul>
<a name="setScanOutTime-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScanOutTime</h4>
<pre>void&nbsp;setScanOutTime(android.content.Context&nbsp;context,
                    int&nbsp;time)</pre>
<div class="block">设置超时时间</br>
 Setup overtime duration</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>time</code> - 超时时间，单位秒</br>
                time overtime time, unit is second</dd>
</dl>
</li>
</ul>
<a name="setScanResultBroadcast-android.content.Context-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScanResultBroadcast</h4>
<pre>void&nbsp;setScanResultBroadcast(android.content.Context&nbsp;context,
                            java.lang.String&nbsp;broadcastAction,
                            java.lang.String&nbsp;extraName)</pre>
<div class="block">设置接收扫描数据的广播</br>
 Setup broad cast of received scanning data</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>broadcastAction</code> - 接收扫描数据的action</dd>
<dd><code>extraName</code> - Intent返回的扩展数据项目名称</dd>
</dl>
</li>
</ul>
<a name="openKeyboardHelper-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openKeyboardHelper</h4>
<pre>void&nbsp;openKeyboardHelper(android.content.Context&nbsp;context)</pre>
<div class="block">打开键盘助手功能总开关</br>
 Switch on main function switch of keyboardemulator</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
</dl>
</li>
</ul>
<a name="closeKeyboardHelper-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeKeyboardHelper</h4>
<pre>void&nbsp;closeKeyboardHelper(android.content.Context&nbsp;context)</pre>
<div class="block">关闭键盘助手功能总开关</br>
 Switch off main function switch of keyboardemulator</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
</dl>
</li>
</ul>
<a name="enablePlaySuccessSound-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enablePlaySuccessSound</h4>
<pre>void&nbsp;enablePlaySuccessSound(android.content.Context&nbsp;context,
                            boolean&nbsp;isSuccessSound)</pre>
<div class="block">是否播放成功的提示音</br>
 whether playing notification when scanning success.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isSuccessSound</code> - true:播放成功提示音,false:不播放成功提示音</br>
                       true: enable false:disable</dd>
</dl>
</li>
</ul>
<a name="enablePlayFailureSound-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enablePlayFailureSound</h4>
<pre>void&nbsp;enablePlayFailureSound(android.content.Context&nbsp;context,
                            boolean&nbsp;isFailureSound)</pre>
<div class="block">扫描失败是否播放提示音</br>
 whether playing notification when scanning failure.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isFailureSound</code> - true:播放失败提示音,false:不播放失败提示音</br>
                       true: enable false:disable</dd>
</dl>
</li>
</ul>
<a name="enableVibrate-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableVibrate</h4>
<pre>void&nbsp;enableVibrate(android.content.Context&nbsp;context,
                   boolean&nbsp;isVibrate)</pre>
<div class="block">扫描成功是否震动提示</br>
 whether vibrating when scanning success</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isVibrate</code> - true:震动,false:不震动 </br>
                  true: enable false:disable</dd>
</dl>
</li>
</ul>
<a name="enableEnter-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableEnter</h4>
<pre>void&nbsp;enableEnter(android.content.Context&nbsp;context,
                 boolean&nbsp;isEnter)</pre>
<div class="block">是否启用回车</br>
 Whether enable Enter function</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isEnter</code> - true: 启用回车   false:不启用回车</br>
                true: enable false:disable</dd>
</dl>
</li>
</ul>
<a name="enableTAB-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableTAB</h4>
<pre>void&nbsp;enableTAB(android.content.Context&nbsp;context,
               boolean&nbsp;isTab)</pre>
<div class="block">是否启用TAB</br>
 Whether enable TAB function</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isTab</code> - true: 启用回车   false:不启用回车</br>
                true: enable false:disable</dd>
</dl>
</li>
</ul>
<a name="setSuffix-android.content.Context-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSuffix</h4>
<pre>void&nbsp;setSuffix(android.content.Context&nbsp;context,
               java.lang.String&nbsp;suffix)</pre>
<div class="block">设置后缀</br>
 Setup suffix</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>suffix</code> - 后缀字符</dd>
</dl>
</li>
</ul>
<a name="setPrefix-android.content.Context-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrefix</h4>
<pre>void&nbsp;setPrefix(android.content.Context&nbsp;context,
               java.lang.String&nbsp;prefix)</pre>
<div class="block">设置前缀</br>
 Setup prefix</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>prefix</code> - 前缀字符</dd>
</dl>
</li>
</ul>
<a name="interceptTrimLeft-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>interceptTrimLeft</h4>
<pre>void&nbsp;interceptTrimLeft(android.content.Context&nbsp;context,
                       int&nbsp;num)</pre>
<div class="block">截取左边字符串数量</br>
 Capture left side charactor string amount</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>num</code> - 左边截取的字符数量</dd>
</dl>
</li>
</ul>
<a name="interceptTrimRight-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>interceptTrimRight</h4>
<pre>void&nbsp;interceptTrimRight(android.content.Context&nbsp;context,
                        int&nbsp;num)</pre>
<div class="block">截取右边字符串数量</br>
 Capture right side charactor string amount</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>num</code> - 右边截取的字符数量</dd>
</dl>
</li>
</ul>
<a name="filterCharacter-android.content.Context-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filterCharacter</h4>
<pre>void&nbsp;filterCharacter(android.content.Context&nbsp;context,
                     java.lang.String&nbsp;chars)</pre>
<div class="block">过滤字符串</br>
 Filter charactor string</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>chars</code> - 过滤的字符</dd>
</dl>
</li>
</ul>
<a name="setBarcodeEncodingFormat-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeEncodingFormat</h4>
<pre>void&nbsp;setBarcodeEncodingFormat(android.content.Context&nbsp;context,
                              int&nbsp;format)</pre>
<div class="block">设置条码编码格式</br>
 Setup barcode coding format</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>format</code> - 0: Default   1: ASCII    2:GB2312   3:UTF8   4:Unicode    10:GBK,    11:GB18030</dd>
</dl>
</li>
</ul>
<a name="enableContinuousScan-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableContinuousScan</h4>
<pre>void&nbsp;enableContinuousScan(android.content.Context&nbsp;context,
                          boolean&nbsp;isContinuous)</pre>
<div class="block">启用连续扫描</br>
 Enable continuous scan</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isContinuous</code> - true: 连续扫描    false：单次扫描</br>
                     true: enable  false：disable</dd>
</dl>
</li>
</ul>
<a name="setContinuousScanIntervalTime-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContinuousScanIntervalTime</h4>
<pre>void&nbsp;setContinuousScanIntervalTime(android.content.Context&nbsp;context,
                                   int&nbsp;intervalTime)</pre>
<div class="block">设置连续扫描间隔时间</br>
 Setup continuous scanning duration</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>intervalTime</code> - 间隔时间，单位毫秒</br>
                     interval Time, (unit: millisecond)</dd>
</dl>
</li>
</ul>
<a name="setContinuousScanTimeOut-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContinuousScanTimeOut</h4>
<pre>void&nbsp;setContinuousScanTimeOut(android.content.Context&nbsp;context,
                              int&nbsp;timeOut)</pre>
<div class="block">设置连续扫描超时时间</br>
 Setup continuous scanning overtime duration</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>timeOut</code> - 超时时间，单位秒</br>
                time Out(unit:second)</dd>
</dl>
</li>
</ul>
<a name="setScanFailureBroadcast-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScanFailureBroadcast</h4>
<pre>void&nbsp;setScanFailureBroadcast(android.content.Context&nbsp;context,
                             boolean&nbsp;enable)</pre>
<div class="block">扫描失败是否发送广播</br>
 Send broadcast when scanning failure.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>enable</code> - true:发送    false：不发送</dd>
</dl>
</li>
</ul>
<a name="setReleaseScan-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReleaseScan</h4>
<pre>void&nbsp;setReleaseScan(android.content.Context&nbsp;context,
                    boolean&nbsp;enable)</pre>
<div class="block">松开扫描按键是否停止扫描</br>
 loose scanning button to stop scanning or not</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>enable</code> - true:停止扫描    false：不停止扫描</dd>
</dl>
</li>
</ul>
<a name="setParam_zebra-android.content.Context-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setParam_zebra</h4>
<pre>void&nbsp;setParam_zebra(android.content.Context&nbsp;context,
                    int&nbsp;paramId,
                    int&nbsp;paramValue)</pre>
<div class="block">设置斑马扫描头参数,扫描头上电之后设置一次即可,扫描头断电之后失效。(备注：键盘助手v2.2.0.3 之后的版本才支持)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>paramId</code> - id</dd>
<dd><code>paramValue</code> - value</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IBarcodeUtility.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeVideoCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IBarcodeUtility.html" target="_top">Frames</a></li>
<li><a href="IBarcodeUtility.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
