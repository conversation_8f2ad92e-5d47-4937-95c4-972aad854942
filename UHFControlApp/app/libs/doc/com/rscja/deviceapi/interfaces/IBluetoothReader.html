<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IBluetoothReader</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IBluetoothReader";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IBluetoothReader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothData.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/ICardWithBYL.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IBluetoothReader.html" target="_top">Frames</a></li>
<li><a href="IBluetoothReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IBluetoothReader" class="title">Interface IBluetoothReader</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi">BluetoothReader</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a>, <a href="../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a>, <a href="../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html" title="class in com.rscja.team.qcom.ble">RFIDWithUHFBLEN51_qcom</a>, <a href="../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html" title="class in com.rscja.team.qcom.ble">RFIDWithUHFBLEN52_qcom</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTag.html" title="class in com.rscja.custom">UHFTemperatureTag</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN51.html" title="class in com.rscja.custom">UHFTemperatureTag.RFIDWithUHFBLEN51</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN52.html" title="class in com.rscja.custom">UHFTemperatureTag.RFIDWithUHFBLEN52</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFTemperatureTagsBLEAPI.html" title="class in com.rscja.team.qcom.custom">UHFTemperatureTagsBLEAPI</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IBluetoothReader</span>
extends <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></pre>
<div class="block">蓝牙读写器的接口<br>
 Interface of Bluetooth reader.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_FIRMWARE">VERSION_BT_FIRMWARE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_HARDWARE">VERSION_BT_HARDWARE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_SOFTWARE">VERSION_BT_SOFTWARE</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IReader">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></h3>
<code><a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#UPDATE_STM32">UPDATE_STM32</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#connect-java.lang.String-">connect</a></span>(java.lang.String&nbsp;address)</code>
<div class="block">连接蓝牙(Connect Bluetooth)</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#connect-java.lang.String-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">connect</a></span>(java.lang.String&nbsp;address,
       <a href="../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;java.lang.Object&gt;&nbsp;btStatusCallback)</code>
<div class="block">连接蓝牙(Connect Bluetooth)</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#disconnect--">disconnect</a></span>()</code>
<div class="block">断开蓝牙(Disconnect Bluetooth)</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#free--">free</a></span>()</code>
<div class="block">释放蓝牙相关的资源(free Bluetooth resources)</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBeep--">getBeep</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBleHardwareVersion--">getBleHardwareVersion</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.HashMap&lt;java.lang.String,java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBluetoothVersion--">getBluetoothVersion</a></span>()</code>
<div class="block">获取蓝牙版本号(acquire Bluetooth version)</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getConnectStatus--">getConnectStatus</a></span>()</code>
<div class="block">获取蓝牙连接状态(Acquire Bluetooth connection status)</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#init-android.content.Context-">init</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">初始化蓝牙相关的服务(Intialize Bluetooth services)</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#sendData-byte:A-">sendData</a></span>(byte[]&nbsp;sendData)</code>
<div class="block">发送指令(send data)</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a></span>(<a href="../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;java.lang.Object&gt;&nbsp;btStatusCallback)</code>
<div class="block">设置蓝牙连接状态的回调(Setup Bluetooth connection status call back)</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#setOnDataChangeListener-com.rscja.deviceapi.BluetoothReader.OnDataChangeListener-">setOnDataChangeListener</a></span>(<a href="../../../../com/rscja/deviceapi/BluetoothReader.OnDataChangeListener.html" title="interface in com.rscja.deviceapi">BluetoothReader.OnDataChangeListener</a>&nbsp;onDataChangeListener)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#setRemoteBluetoothName-java.lang.String-">setRemoteBluetoothName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">修改蓝牙名称(Change Bluetooth device Name)</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-">startScanBTDevices</a></span>(<a href="../../../../com/rscja/deviceapi/interfaces/ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces">ScanBTCallback</a>&nbsp;scanBTCallback)</code>
<div class="block">扫描蓝牙设备(scanning Bluetooth devices)</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#stopScanBarcode--">stopScanBarcode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#stopScanBTDevices--">stopScanBTDevices</a></span>()</code>
<div class="block">停止扫描蓝牙设备(Stop scanning Bluetooth devices)</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IReader">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></h3>
<code><a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#blinkOfLed-int-int-int-">blinkOfLed</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#closeLed--">closeLed</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#getBattery--">getBattery</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#getParameter-byte:A-">getParameter</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#getSTM32Version--">getSTM32Version</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#openLed--">openLed</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#scanBarcode--">scanBarcode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#scanBarcodeToBytes--">scanBarcodeToBytes</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#setBarcodeTypeInSSIID-boolean-">setBarcodeTypeInSSIID</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#setBeep-boolean-">setBeep</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#setKeyEventCallback-com.rscja.deviceapi.interfaces.KeyEventCallback-">setKeyEventCallback</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#setParameter-byte:A-byte:A-">setParameter</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#startScanBarcode--">startScanBarcode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#triggerBeep-int-">triggerBeep</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="VERSION_BT_FIRMWARE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VERSION_BT_FIRMWARE</h4>
<pre>static final&nbsp;java.lang.String VERSION_BT_FIRMWARE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.interfaces.IBluetoothReader.VERSION_BT_FIRMWARE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VERSION_BT_HARDWARE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VERSION_BT_HARDWARE</h4>
<pre>static final&nbsp;java.lang.String VERSION_BT_HARDWARE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.interfaces.IBluetoothReader.VERSION_BT_HARDWARE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VERSION_BT_SOFTWARE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>VERSION_BT_SOFTWARE</h4>
<pre>static final&nbsp;java.lang.String VERSION_BT_SOFTWARE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.deviceapi.interfaces.IBluetoothReader.VERSION_BT_SOFTWARE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="init-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>boolean&nbsp;init(android.content.Context&nbsp;context)</pre>
<div class="block">初始化蓝牙相关的服务(Intialize Bluetooth services)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - context</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>boolean&nbsp;free()</pre>
<div class="block">释放蓝牙相关的资源(free Bluetooth resources)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startScanBTDevices</h4>
<pre>void&nbsp;startScanBTDevices(<a href="../../../../com/rscja/deviceapi/interfaces/ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces">ScanBTCallback</a>&nbsp;scanBTCallback)</pre>
<div class="block">扫描蓝牙设备(scanning Bluetooth devices)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scanBTCallback</code> - 扫描结果回调(scanning result call back)</dd>
</dl>
</li>
</ul>
<a name="stopScanBTDevices--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopScanBTDevices</h4>
<pre>void&nbsp;stopScanBTDevices()</pre>
<div class="block">停止扫描蓝牙设备(Stop scanning Bluetooth devices)</div>
</li>
</ul>
<a name="setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConnectionStatusCallback</h4>
<pre>void&nbsp;setConnectionStatusCallback(<a href="../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;java.lang.Object&gt;&nbsp;btStatusCallback)</pre>
<div class="block">设置蓝牙连接状态的回调(Setup Bluetooth connection status call back)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>btStatusCallback</code> - 蓝牙连接状态回调接口(Bluetooth connection status call back)</dd>
</dl>
</li>
</ul>
<a name="connect-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>connect</h4>
<pre>void&nbsp;connect(java.lang.String&nbsp;address)</pre>
<div class="block">连接蓝牙(Connect Bluetooth)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>address</code> - 蓝牙地址(Bluetooth address)</dd>
</dl>
</li>
</ul>
<a name="connect-java.lang.String-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>connect</h4>
<pre>void&nbsp;connect(java.lang.String&nbsp;address,
             <a href="../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;java.lang.Object&gt;&nbsp;btStatusCallback)</pre>
<div class="block">连接蓝牙(Connect Bluetooth)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>address</code> - 蓝牙地址(Bluetooth address )</dd>
<dd><code>btStatusCallback</code> - 蓝牙连接状态回调接口(Bluetooth connection status call back)</dd>
</dl>
</li>
</ul>
<a name="disconnect--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disconnect</h4>
<pre>void&nbsp;disconnect()</pre>
<div class="block">断开蓝牙(Disconnect Bluetooth)</div>
</li>
</ul>
<a name="getConnectStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConnectStatus</h4>
<pre><a href="../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;getConnectStatus()</pre>
<div class="block">获取蓝牙连接状态(Acquire Bluetooth connection status)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回蓝牙连接状态(Return Bluetooth connection status)</dd>
</dl>
</li>
</ul>
<a name="sendData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sendData</h4>
<pre>boolean&nbsp;sendData(byte[]&nbsp;sendData)</pre>
<div class="block">发送指令(send data)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="setRemoteBluetoothName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemoteBluetoothName</h4>
<pre>boolean&nbsp;setRemoteBluetoothName(java.lang.String&nbsp;name)</pre>
<div class="block">修改蓝牙名称(Change Bluetooth device Name)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - 新的蓝牙名称(New Bluetooth name)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="getBluetoothVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBluetoothVersion</h4>
<pre>java.util.HashMap&lt;java.lang.String,java.lang.String&gt;&nbsp;getBluetoothVersion()</pre>
<div class="block">获取蓝牙版本号(acquire Bluetooth version)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>key:<a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_FIRMWARE"><code>VERSION_BT_FIRMWARE</code></a>  表示固件版本(Firmware Version), key:<a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_HARDWARE"><code>VERSION_BT_HARDWARE</code></a>
 表示硬件版本(Hardware version) ， key:<a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_SOFTWARE"><code>VERSION_BT_SOFTWARE</code></a>  表示软件版本(software version)</dd>
</dl>
</li>
</ul>
<a name="getBleHardwareVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBleHardwareVersion</h4>
<pre>java.lang.String&nbsp;getBleHardwareVersion()</pre>
</li>
</ul>
<a name="setOnDataChangeListener-com.rscja.deviceapi.BluetoothReader.OnDataChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnDataChangeListener</h4>
<pre>void&nbsp;setOnDataChangeListener(<a href="../../../../com/rscja/deviceapi/BluetoothReader.OnDataChangeListener.html" title="interface in com.rscja.deviceapi">BluetoothReader.OnDataChangeListener</a>&nbsp;onDataChangeListener)</pre>
</li>
</ul>
<a name="stopScanBarcode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopScanBarcode</h4>
<pre>boolean&nbsp;stopScanBarcode()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getBeep--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getBeep</h4>
<pre>int&nbsp;getBeep()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IBluetoothReader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothData.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/ICardWithBYL.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IBluetoothReader.html" target="_top">Frames</a></li>
<li><a href="IBluetoothReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
