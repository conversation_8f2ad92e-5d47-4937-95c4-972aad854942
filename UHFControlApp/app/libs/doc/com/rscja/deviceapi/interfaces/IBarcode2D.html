<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IBarcode2D</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IBarcode2D";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IBarcode2D.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IBarcode1D.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodePhoto.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IBarcode2D.html" target="_top">Frames</a></li>
<li><a href="IBarcode2D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IBarcode2D" class="title">Interface IBarcode2D</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/deviceapi/Barcode2D.html" title="class in com.rscja.deviceapi">Barcode2D</a>, <a href="../../../../com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Barcode2D_mtk</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/Barcode2D_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Barcode2D_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IBarcode2D</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#close--">close</a></span>()</code>
<div class="block">关闭二维扫描设备<br>
 Switch off 2D barcode scanning device.<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#isPowerOn--">isPowerOn</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#open--">open</a></span>()</code>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#open-android.content.Context-">open</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#scan--">scan</a></span>()</code>
<div class="block">触发一次条码扫描<br>
 Enable barcode scanning for 1 time.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#scanBarcode--">scanBarcode</a></span>()</code>
<div class="block">触发一次条码扫描<br>
 Enable barcode scanning for 1 time.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#setTimeOut-int-">setTimeOut</a></span>(int&nbsp;time)</code>
<div class="block">设置超时时间<br>
 set time out.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcode2D.html#stopScan--">stopScan</a></span>()</code>
<div class="block">终止扫描<br>
 Scanning terminated<br></div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="open--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>boolean&nbsp;open()</pre>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功,false:失败<br>
 true:success,false:failuer</dd>
</dl>
</li>
</ul>
<a name="open-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>boolean&nbsp;open(android.content.Context&nbsp;context)</pre>
<div class="block">打开二维扫描设备<br>
 Switch on 1D scanning device.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功,false:失败<br>
 true:success,false:failuer</dd>
</dl>
</li>
</ul>
<a name="scan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scan</h4>
<pre>java.lang.String&nbsp;scan()</pre>
<div class="block">触发一次条码扫描<br>
 Enable barcode scanning for 1 time.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>扫描得到的条码信息<br>
 scanning barcode infor that acquired.</dd>
</dl>
</li>
</ul>
<a name="scanBarcode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scanBarcode</h4>
<pre>byte[]&nbsp;scanBarcode()</pre>
<div class="block">触发一次条码扫描<br>
 Enable barcode scanning for 1 time.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>扫描得到的条码信息<br>
 scanning barcode infor that acquired.</dd>
</dl>
</li>
</ul>
<a name="stopScan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopScan</h4>
<pre>boolean&nbsp;stopScan()</pre>
<div class="block">终止扫描<br>
 Scanning terminated<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功，false:失败<br>
 true:success, false:failuer<br></dd>
</dl>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>boolean&nbsp;close()</pre>
<div class="block">关闭二维扫描设备<br>
 Switch off 2D barcode scanning device.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功，false:失败<br>
 true:success, false:failuer</dd>
</dl>
</li>
</ul>
<a name="setTimeOut-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimeOut</h4>
<pre>void&nbsp;setTimeOut(int&nbsp;time)</pre>
<div class="block">设置超时时间<br>
 set time out.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>time(500-10000</code> - ms) 超时时间<br>
                       TimeOut</dd>
</dl>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>boolean&nbsp;isPowerOn()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IBarcode2D.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IBarcode1D.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodePhoto.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IBarcode2D.html" target="_top">Frames</a></li>
<li><a href="IBarcode2D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
