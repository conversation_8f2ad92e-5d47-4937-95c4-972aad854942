<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IHF14443A</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IHF14443A";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IHF14443A.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IHF14443A.html" target="_top">Frames</a></li>
<li><a href="IHF14443A.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IHF14443A" class="title">Interface IHF14443A</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/team/qcom/r1/hf/HF14443A.html" title="class in com.rscja.team.qcom.r1.hf">HF14443A</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IHF14443A</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443A.html#authentication-byte-byte-byte:A-">authentication</a></span>(byte&nbsp;cMode,
              byte&nbsp;cBlock,
              byte[]&nbsp;pcKey)</code>
<div class="block">验证14443A卡(Authentication 14443A card)</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443A.html#cpuCommand-byte:A-">cpuCommand</a></span>(byte[]&nbsp;cmd)</code>
<div class="block">CPU卡发送命令(execute cpu card command)</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443A.html#ratsTypeA--">ratsTypeA</a></span>()</code>
<div class="block">CPU卡RATS操作指令(CPU card RATS operation command)</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443A.html#readBlock-byte-">readBlock</a></span>(byte&nbsp;cBlock)</code>
<div class="block">读卡(Read card)</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/HF14443RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF14443RequestEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443A.html#requestTypeA--">requestTypeA</a></span>()</code>
<div class="block">寻卡(Search card)<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/HF14443RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF14443RequestEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443A.html#requestTypeA-int-">requestTypeA</a></span>(int&nbsp;mode)</code>
<div class="block">寻卡(Search card)<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443A.html#writeBlock-byte-byte:A-">writeBlock</a></span>(byte&nbsp;cBlock,
          byte[]&nbsp;pcBlockData)</code>
<div class="block">写卡(Write card)</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="requestTypeA--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestTypeA</h4>
<pre><a href="../../../../com/rscja/deviceapi/entity/HF14443RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF14443RequestEntity</a>&nbsp;requestTypeA()</pre>
<div class="block">寻卡(Search card)<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success will return HF14443RequestEntity data, failure will return null.<br></dd>
</dl>
</li>
</ul>
<a name="requestTypeA-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestTypeA</h4>
<pre><a href="../../../../com/rscja/deviceapi/entity/HF14443RequestEntity.html" title="class in com.rscja.deviceapi.entity">HF14443RequestEntity</a>&nbsp;requestTypeA(int&nbsp;mode)</pre>
<div class="block">寻卡(Search card)<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mode</code> - 0x26:request idle;  0x52:request all</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success will return HF14443RequestEntity data, failure will return null.<br></dd>
</dl>
</li>
</ul>
<a name="authentication-byte-byte-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>authentication</h4>
<pre>boolean&nbsp;authentication(byte&nbsp;cMode,
                       byte&nbsp;cBlock,
                       byte[]&nbsp;pcKey)
                throws <a href="../../../../com/rscja/deviceapi/exception/RFIDArgumentException.html" title="class in com.rscja.deviceapi.exception">RFIDArgumentException</a></pre>
<div class="block">验证14443A卡(Authentication 14443A card)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cMode</code> - 模式0x60:类型A,0x61类型B  (0x60 A type key, 0x61 B type key)</dd>
<dd><code>cBlock</code> - 要写入的数据块S50 0-63,S70 0-255(the cBlock of card,such as S50 card value 0~63,S70 0~255)</dd>
<dd><code>pcKey</code> - 6个字节的密码(6 bytes key value)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success false:fail
RFIDArgumentException</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/exception/RFIDArgumentException.html" title="class in com.rscja.deviceapi.exception">RFIDArgumentException</a></code></dd>
</dl>
</li>
</ul>
<a name="readBlock-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readBlock</h4>
<pre>byte[]&nbsp;readBlock(byte&nbsp;cBlock)</pre>
<div class="block">读卡(Read card)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cBlock</code> - 数据块0~63  (the cBlock of card,such as M1 card value 0~63 )</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success will return data, failure will return null.<br></dd>
</dl>
</li>
</ul>
<a name="writeBlock-byte-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeBlock</h4>
<pre>boolean&nbsp;writeBlock(byte&nbsp;cBlock,
                   byte[]&nbsp;pcBlockData)
            throws <a href="../../../../com/rscja/deviceapi/exception/RFIDArgumentException.html" title="class in com.rscja.deviceapi.exception">RFIDArgumentException</a></pre>
<div class="block">写卡(Write card)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cBlock</code> - 要写入的数据块S50 0-63,S70 0-255(the cBlock of card,such as S50 card value 0~63,S70 0~255)</dd>
<dd><code>pcBlockData</code> - 十六个字节的数据(16 bytes Block data)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success false:fail</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/exception/RFIDArgumentException.html" title="class in com.rscja.deviceapi.exception">RFIDArgumentException</a></code></dd>
</dl>
</li>
</ul>
<a name="ratsTypeA--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ratsTypeA</h4>
<pre>byte[]&nbsp;ratsTypeA()</pre>
<div class="block">CPU卡RATS操作指令(CPU card RATS operation command)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success will return data, failure will return null.<br></dd>
</dl>
</li>
</ul>
<a name="cpuCommand-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>cpuCommand</h4>
<pre>byte[]&nbsp;cpuCommand(byte[]&nbsp;cmd)</pre>
<div class="block">CPU卡发送命令(execute cpu card command)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cmd</code> - 命令数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success will return data, failure will return null.<br></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IHF14443A.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IHF14443A.html" target="_top">Frames</a></li>
<li><a href="IHF14443A.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
