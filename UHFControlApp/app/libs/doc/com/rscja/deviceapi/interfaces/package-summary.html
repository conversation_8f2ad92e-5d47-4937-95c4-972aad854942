<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>com.rscja.deviceapi.interfaces</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.rscja.deviceapi.interfaces";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/exception/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../com/rscja/scanner/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;com.rscja.deviceapi.interfaces</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;T&gt;</td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcode1D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode1D</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodePhoto.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodePhoto</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodePictureCallback.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodePictureCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeSymbolUtility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></td>
<td class="colLast">
<div class="block">条码操作类
 barcode operate class



 注意：
 Attention:
 1、使用前请确认您的机器已安装键盘助手v1.9.0 之后的版本。
 1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeVideoCallback.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeVideoCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces">IBleDevice</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothData.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothData</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></td>
<td class="colLast">
<div class="block">蓝牙读写器的接口<br>
 Interface of Bluetooth reader.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/ICardWithBYL.html" title="interface in com.rscja.deviceapi.interfaces">ICardWithBYL</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IConnectionStatusChangedListener.html" title="interface in com.rscja.deviceapi.interfaces">IConnectionStatusChangedListener</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprint</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintSM206B</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithZAZ</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IGPIStateCallback.html" title="interface in com.rscja.deviceapi.interfaces">IGPIStateCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443A.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443A</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443B</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces">IHF15693</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces">IInfrared</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/ILedLight.html" title="interface in com.rscja.deviceapi.interfaces">ILedLight</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a></td>
<td class="colLast">
<div class="block">打印机操作类<br>
 Printer operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></td>
<td class="colLast">
<div class="block">R2、R5、R6  蓝牙和USB读写器
 有些客户只需要我们R6的背夹，不需要uhf模块，所以将R6背夹接口独立出来</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDBase</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A4CPU.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A4CPU</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443B</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithLF</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4NetWork</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4RS232</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4Uart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4Uart</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8NetWork</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8RS232</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8Uart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8Uart</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxNetwork</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxUart</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUsbToUart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxUsbToUart</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUSB</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces">IScanerLedLight</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/ITagLocate.html" title="interface in com.rscja.deviceapi.interfaces">ITagLocate</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/ITagLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">ITagLocationCallback</a>&lt;T&gt;</td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IUHFA4</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IUHFA8</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFInventoryCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFInventoryCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFLocationCallback</a></td>
<td class="colLast">
<div class="block">UHF 定位回调接口 （UHF location callback interface）</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFRadarLocationCallback</a></td>
<td class="colLast">
<div class="block">回调定位过程产生的数据 (Callback of data generated by the positioning process)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a></td>
<td class="colLast">
<div class="block">R2、R5、R6  蓝牙和USB读写器包含的uhf特有内容</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></td>
<td class="colLast">
<div class="block">UHF UR4特有接口</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFUrxAutoInventoryTag.html" title="interface in com.rscja.deviceapi.interfaces">IUHFUrxAutoInventoryTag</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUpgradeProgress.html" title="interface in com.rscja.deviceapi.interfaces">IUpgradeProgress</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/IUsbFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IUsbFingerprint</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/KeyEventCallback.html" title="interface in com.rscja.deviceapi.interfaces">KeyEventCallback</a></td>
<td class="colLast">
<div class="block">接收扫描按键的回调
 Receive call back of SCAN Key</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/OnLowBatteryListener.html" title="interface in com.rscja.deviceapi.interfaces">OnLowBatteryListener</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces">ScanBTCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/exception/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../com/rscja/scanner/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
