<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IUHFURAxExtend</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IUHFURAxExtend";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFURAxExtend.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" target="_top">Frames</a></li>
<li><a href="IUHFURAxExtend.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IUHFURAxExtend" class="title">Interface IUHFURAxExtend</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4NetWork</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4RS232</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8NetWork</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8RS232</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IUHFA4</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IUHFA8</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8NetWork_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a>, <a href="../../../../com/rscja/custom/UHFCSYX_A4NetWork.html" title="class in com.rscja.custom">UHFCSYX_A4NetWork</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IUHFURAxExtend</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#closeWifi--">closeWifi</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#disableBeep--">disableBeep</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#enableBeep--">enableBeep</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getAndroidDeviceHardwareVersion--">getAndroidDeviceHardwareVersion</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getEthernetIpConfig--">getEthernetIpConfig</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getReaderCurrentIp--">getReaderCurrentIp</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getTcpServiceVersion--">getTcpServiceVersion</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getWifiInfo--">getWifiInfo</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getWifiIpConfig--">getWifiIpConfig</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#isEnableBeep--">isEnableBeep</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#openWifi--">openWifi</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#outputOnAndOff-java.util.List-">outputOnAndOff</a></span>(java.util.List&lt;<a href="../../../../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a>&gt;&nbsp;list)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#readyUpgradeTcpService--">readyUpgradeTcpService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#rebootDevice--">rebootDevice</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setBuzzerOff--">setBuzzerOff</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setBuzzerOn-int-">setBuzzerOn</a></span>(int&nbsp;time)</code>
<div class="block">单位:毫秒,time:100-65535</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setEthernetConfigInfo-com.rscja.deviceapi.entity.ReaderIPEntity-">setEthernetConfigInfo</a></span>(<a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;ipconfig)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setEthernetIpDynamicAssign--">setEthernetIpDynamicAssign</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setGPIStateCallback-com.rscja.deviceapi.interfaces.IGPIStateCallback-">setGPIStateCallback</a></span>(<a href="../../../../com/rscja/deviceapi/interfaces/IGPIStateCallback.html" title="interface in com.rscja.deviceapi.interfaces">IGPIStateCallback</a>&nbsp;uhfGPIOStateCallback)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setGPIStateReverse-boolean-">setGPIStateReverse</a></span>(boolean&nbsp;isReverse)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a></span>(<a href="../../../../com/rscja/deviceapi/interfaces/IUHFInventoryCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFInventoryCallback</a>&nbsp;inventoryCallback)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setTcpServicePort-int-">setTcpServicePort</a></span>(int&nbsp;port)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setUpgradeProgress-com.rscja.deviceapi.interfaces.IUpgradeProgress-">setUpgradeProgress</a></span>(<a href="../../../../com/rscja/deviceapi/interfaces/IUpgradeProgress.html" title="interface in com.rscja.deviceapi.interfaces">IUpgradeProgress</a>&nbsp;iUpgradeProgress)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setUpgradeTcpServiceData-byte:A-">setUpgradeTcpServiceData</a></span>(byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setWifiConfigInfo-com.rscja.deviceapi.entity.WifiConfig-">setWifiConfigInfo</a></span>(<a href="../../../../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a>&nbsp;wificonfig)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#startUpgradeTcpService--">startUpgradeTcpService</a></span>()</code>
<div class="block">升级TCP服务</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="outputOnAndOff-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>outputOnAndOff</h4>
<pre>void&nbsp;outputOnAndOff(java.util.List&lt;<a href="../../../../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a>&gt;&nbsp;list)</pre>
</li>
</ul>
<a name="setGPIStateReverse-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGPIStateReverse</h4>
<pre>void&nbsp;setGPIStateReverse(boolean&nbsp;isReverse)</pre>
</li>
</ul>
<a name="getTcpServiceVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTcpServiceVersion</h4>
<pre>java.lang.String&nbsp;getTcpServiceVersion()</pre>
</li>
</ul>
<a name="setEthernetConfigInfo-com.rscja.deviceapi.entity.ReaderIPEntity-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEthernetConfigInfo</h4>
<pre>boolean&nbsp;setEthernetConfigInfo(<a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;ipconfig)</pre>
</li>
</ul>
<a name="setWifiConfigInfo-com.rscja.deviceapi.entity.WifiConfig-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWifiConfigInfo</h4>
<pre>boolean&nbsp;setWifiConfigInfo(<a href="../../../../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a>&nbsp;wificonfig)</pre>
</li>
</ul>
<a name="setTcpServicePort-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTcpServicePort</h4>
<pre>boolean&nbsp;setTcpServicePort(int&nbsp;port)</pre>
</li>
</ul>
<a name="getWifiInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWifiInfo</h4>
<pre>java.lang.String&nbsp;getWifiInfo()</pre>
</li>
</ul>
<a name="openWifi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openWifi</h4>
<pre>boolean&nbsp;openWifi()</pre>
</li>
</ul>
<a name="closeWifi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeWifi</h4>
<pre>boolean&nbsp;closeWifi()</pre>
</li>
</ul>
<a name="getAndroidDeviceHardwareVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAndroidDeviceHardwareVersion</h4>
<pre>java.lang.String&nbsp;getAndroidDeviceHardwareVersion()</pre>
</li>
</ul>
<a name="readyUpgradeTcpService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readyUpgradeTcpService</h4>
<pre>void&nbsp;readyUpgradeTcpService()</pre>
</li>
</ul>
<a name="setUpgradeTcpServiceData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUpgradeTcpServiceData</h4>
<pre>boolean&nbsp;setUpgradeTcpServiceData(byte[]&nbsp;data)</pre>
</li>
</ul>
<a name="startUpgradeTcpService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startUpgradeTcpService</h4>
<pre>boolean&nbsp;startUpgradeTcpService()</pre>
<div class="block">升级TCP服务</div>
</li>
</ul>
<a name="setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInventoryCallback</h4>
<pre>void&nbsp;setInventoryCallback(<a href="../../../../com/rscja/deviceapi/interfaces/IUHFInventoryCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFInventoryCallback</a>&nbsp;inventoryCallback)</pre>
</li>
</ul>
<a name="setUpgradeProgress-com.rscja.deviceapi.interfaces.IUpgradeProgress-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUpgradeProgress</h4>
<pre>void&nbsp;setUpgradeProgress(<a href="../../../../com/rscja/deviceapi/interfaces/IUpgradeProgress.html" title="interface in com.rscja.deviceapi.interfaces">IUpgradeProgress</a>&nbsp;iUpgradeProgress)</pre>
</li>
</ul>
<a name="setGPIStateCallback-com.rscja.deviceapi.interfaces.IGPIStateCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGPIStateCallback</h4>
<pre>void&nbsp;setGPIStateCallback(<a href="../../../../com/rscja/deviceapi/interfaces/IGPIStateCallback.html" title="interface in com.rscja.deviceapi.interfaces">IGPIStateCallback</a>&nbsp;uhfGPIOStateCallback)</pre>
</li>
</ul>
<a name="getEthernetIpConfig--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEthernetIpConfig</h4>
<pre>java.lang.String&nbsp;getEthernetIpConfig()</pre>
</li>
</ul>
<a name="getWifiIpConfig--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWifiIpConfig</h4>
<pre>java.lang.String&nbsp;getWifiIpConfig()</pre>
</li>
</ul>
<a name="rebootDevice--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rebootDevice</h4>
<pre>boolean&nbsp;rebootDevice()</pre>
</li>
</ul>
<a name="setEthernetIpDynamicAssign--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEthernetIpDynamicAssign</h4>
<pre>boolean&nbsp;setEthernetIpDynamicAssign()</pre>
</li>
</ul>
<a name="getReaderCurrentIp--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReaderCurrentIp</h4>
<pre><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;getReaderCurrentIp()</pre>
</li>
</ul>
<a name="setBuzzerOff--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuzzerOff</h4>
<pre>void&nbsp;setBuzzerOff()</pre>
</li>
</ul>
<a name="setBuzzerOn-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuzzerOn</h4>
<pre>void&nbsp;setBuzzerOn(int&nbsp;time)</pre>
<div class="block">单位:毫秒,time:100-65535</div>
</li>
</ul>
<a name="disableBeep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disableBeep</h4>
<pre>boolean&nbsp;disableBeep()</pre>
</li>
</ul>
<a name="isEnableBeep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableBeep</h4>
<pre>boolean&nbsp;isEnableBeep()</pre>
</li>
</ul>
<a name="enableBeep--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>enableBeep</h4>
<pre>boolean&nbsp;enableBeep()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFURAxExtend.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" target="_top">Frames</a></li>
<li><a href="IUHFURAxExtend.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
