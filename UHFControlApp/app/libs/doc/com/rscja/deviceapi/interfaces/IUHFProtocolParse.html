<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IUHFProtocolParse</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IUHFProtocolParse";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6,"i38":6,"i39":6,"i40":6,"i41":6,"i42":6,"i43":6,"i44":6,"i45":6,"i46":6,"i47":6,"i48":6,"i49":6,"i50":6,"i51":6,"i52":6,"i53":6,"i54":6,"i55":6,"i56":6,"i57":6,"i58":6,"i59":6,"i60":6,"i61":6,"i62":6,"i63":6,"i64":6,"i65":6,"i66":6,"i67":6,"i68":6,"i69":6,"i70":6,"i71":6,"i72":6,"i73":6,"i74":6,"i75":6,"i76":6,"i77":6,"i78":6,"i79":6,"i80":6,"i81":6,"i82":6,"i83":6,"i84":6,"i85":6,"i86":6,"i87":6,"i88":6,"i89":6,"i90":6,"i91":6,"i92":6,"i93":6,"i94":6,"i95":6,"i96":6,"i97":6,"i98":6,"i99":6,"i100":6,"i101":6,"i102":6,"i103":6,"i104":6,"i105":6,"i106":6,"i107":6,"i108":6,"i109":6,"i110":6,"i111":6,"i112":6,"i113":6,"i114":6,"i115":6,"i116":6,"i117":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFProtocolParse.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" target="_top">Frames</a></li>
<li><a href="IUHFProtocolParse.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IUHFProtocolParse" class="title">Interface IUHFProtocolParse</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParseUrx</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseBleByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseBleByJava_qcom</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a>, <a href="../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA4_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrA4_qcom</a>, <a href="../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA8_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrA8_qcom</a>, <a href="../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseUSBByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUSBByJava_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IUHFProtocolParse</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#blinkOfLedSendData-int-int-int-">blinkOfLedSendData</a></span>(int&nbsp;duration,
                  int&nbsp;interval,
                  int&nbsp;count)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#blockEraseDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-">blockEraseDataSendData</a></span>(java.lang.String&nbsp;pszuAccessPwd,
                      char&nbsp;ufBank,
                      int&nbsp;ufPtr,
                      int&nbsp;ufCnt,
                      java.lang.String&nbsp;ufData,
                      char&nbsp;uBank,
                      int&nbsp;uPtr,
                      char&nbsp;uCnt)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#blockWriteDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-java.lang.String-">blockWriteDataSendData</a></span>(java.lang.String&nbsp;pszuAccessPwd,
                      char&nbsp;ufBank,
                      int&nbsp;ufPtr,
                      int&nbsp;ufCnt,
                      java.lang.String&nbsp;ufData,
                      char&nbsp;uBank,
                      int&nbsp;uPtr,
                      char&nbsp;uCnt,
                      java.lang.String&nbsp;writeDatabuf)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btDeleteAllTagToFlashSendData--">btDeleteAllTagToFlashSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btGetAllTagNumFromFlashSendData--">btGetAllTagNumFromFlashSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btGetNewTagNumFromFlashSendData--">btGetNewTagNumFromFlashSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btGetTagDataFromFlashSendData--">btGetTagDataFromFlashSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#closeLedSendData--">closeLedSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#GBTagLockSendData-java.lang.String-char-int-int-java.lang.String-char-char-char-">GBTagLockSendData</a></span>(java.lang.String&nbsp;pszuAccessPwd,
                 char&nbsp;ufBank,
                 int&nbsp;ufPtr,
                 int&nbsp;ufCnt,
                 java.lang.String&nbsp;ufData,
                 char&nbsp;jmemory,
                 char&nbsp;jconfig,
                 char&nbsp;jaction)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a></span>(java.util.ArrayList&lt;java.lang.Integer&gt;&nbsp;lockBank,
                int&nbsp;lockMode)</code>
<div class="block">获取锁标签的锁定码</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getBatterySendData--">getBatterySendData</a></span>()</code>
<div class="block">获取电量的发送数据</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getBeepSendData-boolean-">getBeepSendData</a></span>(boolean&nbsp;isOpen)</code>
<div class="block">获取设置蜂鸣器的发送数据</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getCWSendData--">getCWSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getEPCTIDModeSendData-char-char-">getEPCTIDModeSendData</a></span>(char&nbsp;rev1,
                     char&nbsp;rev2)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getFastIDSendData--">getFastIDSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getFrequencyModeSendData--">getFrequencyModeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getGen2SendData--">getGen2SendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getInventorySingleTagSendData--">getInventorySingleTagSendData</a></span>()</code>
<div class="block">获取开启单次盘点标签的发送数据</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getKillSendData-java.lang.String-int-int-int-java.lang.String-">getKillSendData</a></span>(java.lang.String&nbsp;accessPwd,
               int&nbsp;filterBank,
               int&nbsp;filterPtr,
               int&nbsp;filterCnt,
               java.lang.String&nbsp;filterData)</code>
<div class="block">获取销毁标签的发送数据</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getLockSendData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">getLockSendData</a></span>(java.lang.String&nbsp;accessPwd,
               int&nbsp;filterBank,
               int&nbsp;filterPtr,
               int&nbsp;filterCnt,
               java.lang.String&nbsp;filterData,
               java.lang.String&nbsp;lockCode)</code>
<div class="block">获取锁标签需要发送的数据 <br></div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getPowerSendData--">getPowerSendData</a></span>()</code>
<div class="block">获取功率的发送数据<br></div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getProtocolSendData--">getProtocolSendData</a></span>()</code>
<div class="block">获取协议需要发送数据<br></div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReaderAwaitSleepTimeSendData--">getReaderAwaitSleepTimeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReadSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-">getReadSendData</a></span>(java.lang.String&nbsp;accessPwd,
               int&nbsp;filterBank,
               int&nbsp;filterPtr,
               int&nbsp;filterCnt,
               java.lang.String&nbsp;filterData,
               int&nbsp;bank,
               int&nbsp;ptr,
               int&nbsp;cnt)</code>
<div class="block">获取读标签的发送数据</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReadTagSendData--">getReadTagSendData</a></span>()</code>
<div class="block">获取在循环盘点标签的模式中,读取缓存标签的发送数据</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getRFLinkSendData--">getRFLinkSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getScanBarcodeSendData--">getScanBarcodeSendData</a></span>()</code>
<div class="block">获取扫描条码的发送数据</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getStartInventoryTagSendData--">getStartInventoryTagSendData</a></span>()</code>
<div class="block">获取循环盘点标签的发送数据</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getSTM32VersionSendData--">getSTM32VersionSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getStopInventorySendData--">getStopInventorySendData</a></span>()</code>
<div class="block">获取停止循环盘点标签的发送数据</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getTagfocusSendData--">getTagfocusSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getTemperatureSendData--">getTemperatureSendData</a></span>()</code>
<div class="block">获取模块温度</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getVersionSendData--">getVersionSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getWriteSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">getWriteSendData</a></span>(java.lang.String&nbsp;accessPwd,
                int&nbsp;filterBank,
                int&nbsp;filterPtr,
                int&nbsp;filterCnt,
                java.lang.String&nbsp;filterData,
                int&nbsp;bank,
                int&nbsp;ptr,
                int&nbsp;cnt,
                java.lang.String&nbsp;writeData)</code>
<div class="block">获取写标签的发送数据<br></div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#openLedSendData--">openLedSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBarcodeData-byte:A-">parseBarcodeData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析扫描条码返回的数据</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBatteryData-byte:A-">parseBatteryData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析获取电量返回的数据</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBeepData-byte:A-">parseBeepData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析设置蜂鸣器返回的数据</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBlinkOfLedData-byte:A-">parseBlinkOfLedData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBlockEraseDataData-byte:A-">parseBlockEraseDataData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBlockWriteData-byte:A-">parseBlockWriteData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBtDeleteAllTagToFlashData-byte:A-">parseBtDeleteAllTagToFlashData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBtGetAllTagNumFromFlashData-byte:A-">parseBtGetAllTagNumFromFlashData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBtGetNewTagNumFromFlashData-byte:A-">parseBtGetNewTagNumFromFlashData</a></span>(byte[]&nbsp;indata)</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBtGetTagDataFromFlashData-byte:A-">parseBtGetTagDataFromFlashData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseCloseLedData-byte:A-">parseCloseLedData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseContinuousInventoryTagData-byte:A-">parseContinuousInventoryTagData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">循环盘点标签返回的数据</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseFastIdData-byte:A-">parseFastIdData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGBTagLockData-byte:A-">parseGBTagLockData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetCWData-byte:A-">parseGetCWData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetEPCTIDModeData-byte:A-">parseGetEPCTIDModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetFastIdData-byte:A-">parseGetFastIdData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetFrequencyModeData-byte:A-">parseGetFrequencyModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetGen2Data-byte:A-">parseGetGen2Data</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetPowerData-byte:A-">parseGetPowerData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析获取功率返回的数据</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetProtocolData-byte:A-">parseGetProtocolData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析获取协议返回的数据</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetReaderAwaitSleepTimeData-byte:A-">parseGetReaderAwaitSleepTimeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetRFLinkData-byte:A-">parseGetRFLinkData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetTagfocusData-byte:A-">parseGetTagfocusData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseInventorySingleTagData-byte:A-">parseInventorySingleTagData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析单次盘点标签返回的数据</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseKillData-byte:A-">parseKillData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析销毁标签返回的数据</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseLockData-byte:A-">parseLockData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析锁标签返回的数据</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseOpenLedData-byte:A-">parseOpenLedData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseReadData-byte:A-">parseReadData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析读标签返回的数据</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseReadTagData_EPC-byte:A-">parseReadTagData_EPC</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析循环盘点标签返回的标签数据</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetBeepTimeOfDuration-byte:A-">parseSetBeepTimeOfDuration</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetCWData-byte:A-">parseSetCWData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetEPCAndTIDModeData-byte:A-">parseSetEPCAndTIDModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetEPCAndTIDUserModeData-byte:A-">parseSetEPCAndTIDUserModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetEPCModeData-byte:A-">parseSetEPCModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetFilterData-byte:A-">parseSetFilterData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetFrequencyModeData-byte:A-">parseSetFrequencyModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetGen2Data-byte:A-">parseSetGen2Data</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetJumpFrequencyData-byte:A-">parseSetJumpFrequencyData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetPowerData-byte:A-">parseSetPowerData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析设置功率返回的数据</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetPowerOnDynamicData-byte:A-">parseSetPowerOnDynamicData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetProtocolData-byte:A-">parseSetProtocolData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析设置协议返回的数据</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetR6WorkModeData-byte:A-">parseSetR6WorkModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetReaderAwaitSleepTimeData-byte:A-">parseSetReaderAwaitSleepTimeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetRFLinkData-byte:A-">parseSetRFLinkData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetTagfocusData-byte:A-">parseSetTagfocusData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseStartInventoryTagData-byte:A-">parseStartInventoryTagData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析开始盘点标签返回的数据</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSTM32VersionData-byte:A-">parseSTM32VersionData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseStopInventoryData-byte:A-">parseStopInventoryData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析停止盘点标签返回的数据</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseTemperatureData-byte:A-">parseTemperatureData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析模块温度返回的数据</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseUHFJump2BootData-byte:A-">parseUHFJump2BootData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseUHFStartUpdateData-byte:A-">parseUHFStartUpdateData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseUHFStopUpdateData-byte:A-">parseUHFStopUpdateData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseUHFUpdatingData-byte:A-">parseUHFUpdatingData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseVersionData-byte:A-">parseVersionData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseWriteData-byte:A-">parseWriteData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析写标签返回的数据</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#requestGetEx10SDKFirmware--">requestGetEx10SDKFirmware</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#requestGetFastInventoryMode--">requestGetFastInventoryMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#requestSetFastInventoryMode-boolean-">requestSetFastInventoryMode</a></span>(boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#responseGetEx10SDKFirmware-byte:A-">responseGetEx10SDKFirmware</a></span>(byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#responseGetFastInventoryMode-byte:A-">responseGetFastInventoryMode</a></span>(byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#responseSetFastInventoryMode-byte:A-">responseSetFastInventoryMode</a></span>(byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setBeepTimeOfDurationSendData-int-">setBeepTimeOfDurationSendData</a></span>(int&nbsp;time)</code>&nbsp;</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setCWSendData-char-">setCWSendData</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setEPCAndTIDModeSendData--">setEPCAndTIDModeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setEPCAndTIDUserModeSendData-int-int-">setEPCAndTIDUserModeSendData</a></span>(int&nbsp;user_prt,
                            int&nbsp;user_len)</code>&nbsp;</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setEPCModeSendData--">setEPCModeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setFastIdSendData-int-">setFastIdSendData</a></span>(int&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setFilterSendData-char-int-int-java.lang.String-">setFilterSendData</a></span>(char&nbsp;ufBank,
                 int&nbsp;ufPtr,
                 int&nbsp;datalen,
                 java.lang.String&nbsp;databuf)</code>&nbsp;</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setFrequencyModeSendData-int-">setFrequencyModeSendData</a></span>(int&nbsp;freMode)</code>&nbsp;</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setGen2SendData-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">setGen2SendData</a></span>(char&nbsp;Target,
               char&nbsp;Action,
               char&nbsp;T,
               char&nbsp;Q_Q,
               char&nbsp;StartQ,
               char&nbsp;MinQ,
               char&nbsp;MaxQ,
               char&nbsp;D_D,
               char&nbsp;C_C,
               char&nbsp;P_P,
               char&nbsp;Sel,
               char&nbsp;Session,
               char&nbsp;G_G,
               char&nbsp;LF)</code>&nbsp;</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setJumpFrequencySendData-int-">setJumpFrequencySendData</a></span>(int&nbsp;Freqbuf)</code>&nbsp;</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setPowerOnDynamicSendData-int-">setPowerOnDynamicSendData</a></span>(int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setPowerSendData-int-">setPowerSendData</a></span>(int&nbsp;power)</code>
<div class="block">获取设置功率的发送数据<br></div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setProtocolSendData-int-">setProtocolSendData</a></span>(int&nbsp;protocol)</code>
<div class="block">获取设置协议的发送数据<br></div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setR6WorkmodeSendData-char-">setR6WorkmodeSendData</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setReaderAwaitSleepTimeSendData-char-">setReaderAwaitSleepTimeSendData</a></span>(char&nbsp;time)</code>&nbsp;</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setRFLinkSendData-int-">setRFLinkSendData</a></span>(int&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setTagfocusSendData-char-">setTagfocusSendData</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#uhfJump2BootSendData-char-">uhfJump2BootSendData</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#uhfStartUpdateSendData--">uhfStartUpdateSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#UHFStopUpdateSendData--">UHFStopUpdateSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#uhfUpdatingSendData-byte:A-">uhfUpdatingSendData</a></span>(byte[]&nbsp;buf)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getBeepSendData-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBeepSendData</h4>
<pre>byte[]&nbsp;getBeepSendData(boolean&nbsp;isOpen)</pre>
<div class="block">获取设置蜂鸣器的发送数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>isOpen</code> - true:表示打开蜂鸣器， false:表示关闭蜂鸣器</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseBeepData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBeepData</h4>
<pre>boolean&nbsp;parseBeepData(byte[]&nbsp;inData)</pre>
<div class="block">解析设置蜂鸣器返回的数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 设置蜂鸣器返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true: 设置成功  ,flase:设置设备</dd>
</dl>
</li>
</ul>
<a name="parseSetBeepTimeOfDuration-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetBeepTimeOfDuration</h4>
<pre>boolean&nbsp;parseSetBeepTimeOfDuration(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="parseBarcodeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBarcodeData</h4>
<pre>byte[]&nbsp;parseBarcodeData(byte[]&nbsp;inData)</pre>
<div class="block">解析扫描条码返回的数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的条码原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回解析后的条码数据</dd>
</dl>
</li>
</ul>
<a name="parseBatteryData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBatteryData</h4>
<pre>int&nbsp;parseBatteryData(byte[]&nbsp;inData)</pre>
<div class="block">解析获取电量返回的数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回解析后的电量</dd>
</dl>
</li>
</ul>
<a name="parseWriteData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseWriteData</h4>
<pre>boolean&nbsp;parseWriteData(byte[]&nbsp;inData)</pre>
<div class="block">解析写标签返回的数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:写标签成功    false:写标签失败</dd>
</dl>
</li>
</ul>
<a name="parseReadData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseReadData</h4>
<pre>java.lang.String&nbsp;parseReadData(byte[]&nbsp;inData)</pre>
<div class="block">解析读标签返回的数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回解析后的标签数据</dd>
</dl>
</li>
</ul>
<a name="parseLockData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseLockData</h4>
<pre>boolean&nbsp;parseLockData(byte[]&nbsp;inData)</pre>
<div class="block">解析锁标签返回的数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:锁成功， false:锁失败</dd>
</dl>
</li>
</ul>
<a name="parseKillData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseKillData</h4>
<pre>boolean&nbsp;parseKillData(byte[]&nbsp;inData)</pre>
<div class="block">解析销毁标签返回的数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:销毁标签成功， false:销毁标签失败</dd>
</dl>
</li>
</ul>
<a name="parseInventorySingleTagData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseInventorySingleTagData</h4>
<pre><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;parseInventorySingleTagData(byte[]&nbsp;inData)</pre>
<div class="block">解析单次盘点标签返回的数据</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回单次盘点标签的数据</dd>
</dl>
</li>
</ul>
<a name="parseContinuousInventoryTagData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseContinuousInventoryTagData</h4>
<pre><a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;parseContinuousInventoryTagData(byte[]&nbsp;inData)</pre>
<div class="block">循环盘点标签返回的数据</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回循环盘点标签的数据</dd>
</dl>
</li>
</ul>
<a name="parseSetProtocolData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetProtocolData</h4>
<pre>boolean&nbsp;parseSetProtocolData(byte[]&nbsp;inData)</pre>
<div class="block">解析设置协议返回的数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:设置协议成功， false:设置协议失败</dd>
</dl>
</li>
</ul>
<a name="parseGetProtocolData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetProtocolData</h4>
<pre>int&nbsp;parseGetProtocolData(byte[]&nbsp;inData)</pre>
<div class="block">解析获取协议返回的数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:获取协议成功， false:获取协议失败</dd>
</dl>
</li>
</ul>
<a name="getStartInventoryTagSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartInventoryTagSendData</h4>
<pre>byte[]&nbsp;getStartInventoryTagSendData()</pre>
<div class="block">获取循环盘点标签的发送数据</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseStartInventoryTagData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseStartInventoryTagData</h4>
<pre>boolean&nbsp;parseStartInventoryTagData(byte[]&nbsp;inData)</pre>
<div class="block">解析开始盘点标签返回的数据</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:开始盘点成功，false:开始盘点失败</dd>
</dl>
</li>
</ul>
<a name="getInventorySingleTagSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInventorySingleTagSendData</h4>
<pre>byte[]&nbsp;getInventorySingleTagSendData()</pre>
<div class="block">获取开启单次盘点标签的发送数据</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="getStopInventorySendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStopInventorySendData</h4>
<pre>byte[]&nbsp;getStopInventorySendData()</pre>
<div class="block">获取停止循环盘点标签的发送数据</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="getReadTagSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReadTagSendData</h4>
<pre>byte[]&nbsp;getReadTagSendData()</pre>
<div class="block">获取在循环盘点标签的模式中,读取缓存标签的发送数据</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseReadTagData_EPC-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseReadTagData_EPC</h4>
<pre>java.util.List&lt;<a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;&nbsp;parseReadTagData_EPC(byte[]&nbsp;inData)</pre>
<div class="block">解析循环盘点标签返回的标签数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回解析后的标签数据</dd>
</dl>
</li>
</ul>
<a name="parseStopInventoryData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseStopInventoryData</h4>
<pre>boolean&nbsp;parseStopInventoryData(byte[]&nbsp;inData)</pre>
<div class="block">解析停止盘点标签返回的数据</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:停止盘点成功，false:停止盘点失败</dd>
</dl>
</li>
</ul>
<a name="getPowerSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPowerSendData</h4>
<pre>byte[]&nbsp;getPowerSendData()</pre>
<div class="block">获取功率的发送数据<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="setPowerSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPowerSendData</h4>
<pre>byte[]&nbsp;setPowerSendData(int&nbsp;power)</pre>
<div class="block">获取设置功率的发送数据<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>power</code> - 功率</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseSetPowerData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetPowerData</h4>
<pre>boolean&nbsp;parseSetPowerData(byte[]&nbsp;inData)</pre>
<div class="block">解析设置功率返回的数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:设置功率成功, flase:设置功率失败</dd>
</dl>
</li>
</ul>
<a name="parseGetPowerData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetPowerData</h4>
<pre>int&nbsp;parseGetPowerData(byte[]&nbsp;inData)</pre>
<div class="block">解析获取功率返回的数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回功率</dd>
</dl>
</li>
</ul>
<a name="getVersionSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersionSendData</h4>
<pre>byte[]&nbsp;getVersionSendData()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="parseVersionData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseVersionData</h4>
<pre>java.lang.String&nbsp;parseVersionData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getFrequencyModeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrequencyModeSendData</h4>
<pre>byte[]&nbsp;getFrequencyModeSendData()</pre>
</li>
</ul>
<a name="setFrequencyModeSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFrequencyModeSendData</h4>
<pre>byte[]&nbsp;setFrequencyModeSendData(int&nbsp;freMode)</pre>
</li>
</ul>
<a name="parseGetFrequencyModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetFrequencyModeData</h4>
<pre>byte&nbsp;parseGetFrequencyModeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="parseSetFrequencyModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetFrequencyModeData</h4>
<pre>boolean&nbsp;parseSetFrequencyModeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setBeepTimeOfDurationSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBeepTimeOfDurationSendData</h4>
<pre>byte[]&nbsp;setBeepTimeOfDurationSendData(int&nbsp;time)</pre>
</li>
</ul>
<a name="getScanBarcodeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScanBarcodeSendData</h4>
<pre>byte[]&nbsp;getScanBarcodeSendData()</pre>
<div class="block">获取扫描条码的发送数据</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="getBatterySendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBatterySendData</h4>
<pre>byte[]&nbsp;getBatterySendData()</pre>
<div class="block">获取电量的发送数据</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="getWriteSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWriteSendData</h4>
<pre>byte[]&nbsp;getWriteSendData(java.lang.String&nbsp;accessPwd,
                        int&nbsp;filterBank,
                        int&nbsp;filterPtr,
                        int&nbsp;filterCnt,
                        java.lang.String&nbsp;filterData,
                        int&nbsp;bank,
                        int&nbsp;ptr,
                        int&nbsp;cnt,
                        java.lang.String&nbsp;writeData)</pre>
<div class="block">获取写标签的发送数据<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - 标签的ACCESS PASSWORD（4字 节）<br></dd>
<dd><code>filterBank</code> - 过滤的数据块<br></dd>
<dd><code>filterPtr</code> - 过滤的起始地址(单位:bit)<br></dd>
<dd><code>filterCnt</code> - 过滤的数据长度(单位:bit)<br></dd>
<dd><code>filterData</code> - 过滤的数据<br></dd>
<dd><code>bank</code> - 写入的数据块<br></dd>
<dd><code>ptr</code> - 写入的起始地址(单位:字)<br></dd>
<dd><code>cnt</code> - 写入的数据长度(单位:字)<br></dd>
<dd><code>writeData</code> - 写入的数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="getReadSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReadSendData</h4>
<pre>byte[]&nbsp;getReadSendData(java.lang.String&nbsp;accessPwd,
                       int&nbsp;filterBank,
                       int&nbsp;filterPtr,
                       int&nbsp;filterCnt,
                       java.lang.String&nbsp;filterData,
                       int&nbsp;bank,
                       int&nbsp;ptr,
                       int&nbsp;cnt)</pre>
<div class="block">获取读标签的发送数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - 访问密码<br></dd>
<dd><code>filterBank</code> - 过滤的数据块<br></dd>
<dd><code>filterPtr</code> - 过滤的起始地址(单位:bit)<br></dd>
<dd><code>filterCnt</code> - 过滤的数据长度(单位:bit)<br></dd>
<dd><code>filterData</code> - 过滤的数据<br></dd>
<dd><code>bank</code> - 读取的数据块<br></dd>
<dd><code>ptr</code> - 读取的起始地址(单位:字)<br></dd>
<dd><code>cnt</code> - 读取的数据长度(单位:字)<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据<br></dd>
</dl>
</li>
</ul>
<a name="getLockSendData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLockSendData</h4>
<pre>byte[]&nbsp;getLockSendData(java.lang.String&nbsp;accessPwd,
                       int&nbsp;filterBank,
                       int&nbsp;filterPtr,
                       int&nbsp;filterCnt,
                       java.lang.String&nbsp;filterData,
                       java.lang.String&nbsp;lockCode)</pre>
<div class="block">获取锁标签需要发送的数据 <br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - 标签的ACCESS PASSWORD（4字 节）<br></dd>
<dd><code>filterBank</code> - 标签的存储区<br></dd>
<dd><code>filterPtr</code> - 过滤起始地址(单位:bit)<br></dd>
<dd><code>filterCnt</code> - 过滤数据长度(单位:bit)<br></dd>
<dd><code>filterData</code> - 过滤数据<br></dd>
<dd><code>lockCode</code> - 锁定码<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据<br></dd>
</dl>
</li>
</ul>
<a name="generateLockCode-java.util.ArrayList-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateLockCode</h4>
<pre>java.lang.String&nbsp;generateLockCode(java.util.ArrayList&lt;java.lang.Integer&gt;&nbsp;lockBank,
                                  int&nbsp;lockMode)</pre>
<div class="block">获取锁标签的锁定码</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>lockBank</code> - 要锁定的区域</dd>
<dd><code>lockMode</code> - 锁定的模式</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回null 表示失败</dd>
</dl>
</li>
</ul>
<a name="getKillSendData-java.lang.String-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKillSendData</h4>
<pre>byte[]&nbsp;getKillSendData(java.lang.String&nbsp;accessPwd,
                       int&nbsp;filterBank,
                       int&nbsp;filterPtr,
                       int&nbsp;filterCnt,
                       java.lang.String&nbsp;filterData)</pre>
<div class="block">获取销毁标签的发送数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - 标签的ACCESS PASSWORD（4字 节）<br></dd>
<dd><code>filterBank</code> - 标签的存储区<br></dd>
<dd><code>filterPtr</code> - 过滤起始地址(单位:bit)<br></dd>
<dd><code>filterCnt</code> - 过滤数据长度(单位:bit)<br></dd>
<dd><code>filterData</code> - 过滤数据<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据<br></dd>
</dl>
</li>
</ul>
<a name="getProtocolSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProtocolSendData</h4>
<pre>byte[]&nbsp;getProtocolSendData()</pre>
<div class="block">获取协议需要发送数据<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="setProtocolSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProtocolSendData</h4>
<pre>byte[]&nbsp;setProtocolSendData(int&nbsp;protocol)</pre>
<div class="block">获取设置协议的发送数据<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>protocol</code> - 0x00 表示 ISO18000-6C 协议,  0x01 表示 GB/T 29768 国标协议,  0x02 表示 GJB 7377.1 国军标协议</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="getTemperatureSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTemperatureSendData</h4>
<pre>byte[]&nbsp;getTemperatureSendData()</pre>
<div class="block">获取模块温度</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="parseTemperatureData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseTemperatureData</h4>
<pre>int&nbsp;parseTemperatureData(byte[]&nbsp;inData)</pre>
<div class="block">解析模块温度返回的数据</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setEPCModeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCModeSendData</h4>
<pre>byte[]&nbsp;setEPCModeSendData()</pre>
</li>
</ul>
<a name="parseSetEPCModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetEPCModeData</h4>
<pre>boolean&nbsp;parseSetEPCModeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setEPCAndTIDModeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCAndTIDModeSendData</h4>
<pre>byte[]&nbsp;setEPCAndTIDModeSendData()</pre>
</li>
</ul>
<a name="parseSetEPCAndTIDModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetEPCAndTIDModeData</h4>
<pre>boolean&nbsp;parseSetEPCAndTIDModeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setEPCAndTIDUserModeSendData-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCAndTIDUserModeSendData</h4>
<pre>byte[]&nbsp;setEPCAndTIDUserModeSendData(int&nbsp;user_prt,
                                    int&nbsp;user_len)</pre>
</li>
</ul>
<a name="parseSetEPCAndTIDUserModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetEPCAndTIDUserModeData</h4>
<pre>boolean&nbsp;parseSetEPCAndTIDUserModeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setGen2SendData-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGen2SendData</h4>
<pre>byte[]&nbsp;setGen2SendData(char&nbsp;Target,
                       char&nbsp;Action,
                       char&nbsp;T,
                       char&nbsp;Q_Q,
                       char&nbsp;StartQ,
                       char&nbsp;MinQ,
                       char&nbsp;MaxQ,
                       char&nbsp;D_D,
                       char&nbsp;C_C,
                       char&nbsp;P_P,
                       char&nbsp;Sel,
                       char&nbsp;Session,
                       char&nbsp;G_G,
                       char&nbsp;LF)</pre>
</li>
</ul>
<a name="parseSetGen2Data-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetGen2Data</h4>
<pre>boolean&nbsp;parseSetGen2Data(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getGen2SendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGen2SendData</h4>
<pre>byte[]&nbsp;getGen2SendData()</pre>
</li>
</ul>
<a name="parseGetGen2Data-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetGen2Data</h4>
<pre>byte[]&nbsp;parseGetGen2Data(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="blockWriteDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blockWriteDataSendData</h4>
<pre>byte[]&nbsp;blockWriteDataSendData(java.lang.String&nbsp;pszuAccessPwd,
                              char&nbsp;ufBank,
                              int&nbsp;ufPtr,
                              int&nbsp;ufCnt,
                              java.lang.String&nbsp;ufData,
                              char&nbsp;uBank,
                              int&nbsp;uPtr,
                              char&nbsp;uCnt,
                              java.lang.String&nbsp;writeDatabuf)</pre>
</li>
</ul>
<a name="parseBlockWriteData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBlockWriteData</h4>
<pre>boolean&nbsp;parseBlockWriteData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="blockEraseDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blockEraseDataSendData</h4>
<pre>byte[]&nbsp;blockEraseDataSendData(java.lang.String&nbsp;pszuAccessPwd,
                              char&nbsp;ufBank,
                              int&nbsp;ufPtr,
                              int&nbsp;ufCnt,
                              java.lang.String&nbsp;ufData,
                              char&nbsp;uBank,
                              int&nbsp;uPtr,
                              char&nbsp;uCnt)</pre>
</li>
</ul>
<a name="parseBlockEraseDataData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBlockEraseDataData</h4>
<pre>boolean&nbsp;parseBlockEraseDataData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="GBTagLockSendData-java.lang.String-char-int-int-java.lang.String-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GBTagLockSendData</h4>
<pre>byte[]&nbsp;GBTagLockSendData(java.lang.String&nbsp;pszuAccessPwd,
                         char&nbsp;ufBank,
                         int&nbsp;ufPtr,
                         int&nbsp;ufCnt,
                         java.lang.String&nbsp;ufData,
                         char&nbsp;jmemory,
                         char&nbsp;jconfig,
                         char&nbsp;jaction)</pre>
</li>
</ul>
<a name="parseGBTagLockData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGBTagLockData</h4>
<pre>boolean&nbsp;parseGBTagLockData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setFilterSendData-char-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFilterSendData</h4>
<pre>byte[]&nbsp;setFilterSendData(char&nbsp;ufBank,
                         int&nbsp;ufPtr,
                         int&nbsp;datalen,
                         java.lang.String&nbsp;databuf)</pre>
</li>
</ul>
<a name="parseSetFilterData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetFilterData</h4>
<pre>boolean&nbsp;parseSetFilterData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setCWSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCWSendData</h4>
<pre>byte[]&nbsp;setCWSendData(char&nbsp;flag)</pre>
</li>
</ul>
<a name="parseSetCWData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetCWData</h4>
<pre>boolean&nbsp;parseSetCWData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getCWSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCWSendData</h4>
<pre>byte[]&nbsp;getCWSendData()</pre>
</li>
</ul>
<a name="parseGetCWData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetCWData</h4>
<pre>int&nbsp;parseGetCWData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setJumpFrequencySendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJumpFrequencySendData</h4>
<pre>byte[]&nbsp;setJumpFrequencySendData(int&nbsp;Freqbuf)</pre>
</li>
</ul>
<a name="parseSetJumpFrequencyData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetJumpFrequencyData</h4>
<pre>boolean&nbsp;parseSetJumpFrequencyData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="btDeleteAllTagToFlashSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>btDeleteAllTagToFlashSendData</h4>
<pre>byte[]&nbsp;btDeleteAllTagToFlashSendData()</pre>
</li>
</ul>
<a name="parseBtDeleteAllTagToFlashData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBtDeleteAllTagToFlashData</h4>
<pre>boolean&nbsp;parseBtDeleteAllTagToFlashData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="btGetAllTagNumFromFlashSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>btGetAllTagNumFromFlashSendData</h4>
<pre>byte[]&nbsp;btGetAllTagNumFromFlashSendData()</pre>
</li>
</ul>
<a name="btGetNewTagNumFromFlashSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>btGetNewTagNumFromFlashSendData</h4>
<pre>byte[]&nbsp;btGetNewTagNumFromFlashSendData()</pre>
</li>
</ul>
<a name="parseBtGetAllTagNumFromFlashData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBtGetAllTagNumFromFlashData</h4>
<pre>int&nbsp;parseBtGetAllTagNumFromFlashData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="parseBtGetNewTagNumFromFlashData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBtGetNewTagNumFromFlashData</h4>
<pre>int&nbsp;parseBtGetNewTagNumFromFlashData(byte[]&nbsp;indata)</pre>
</li>
</ul>
<a name="btGetTagDataFromFlashSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>btGetTagDataFromFlashSendData</h4>
<pre>byte[]&nbsp;btGetTagDataFromFlashSendData()</pre>
</li>
</ul>
<a name="parseBtGetTagDataFromFlashData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBtGetTagDataFromFlashData</h4>
<pre>byte[]&nbsp;parseBtGetTagDataFromFlashData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setR6WorkmodeSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setR6WorkmodeSendData</h4>
<pre>byte[]&nbsp;setR6WorkmodeSendData(char&nbsp;flag)</pre>
</li>
</ul>
<a name="parseSetR6WorkModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetR6WorkModeData</h4>
<pre>boolean&nbsp;parseSetR6WorkModeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="uhfJump2BootSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uhfJump2BootSendData</h4>
<pre>byte[]&nbsp;uhfJump2BootSendData(char&nbsp;flag)</pre>
</li>
</ul>
<a name="parseUHFJump2BootData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseUHFJump2BootData</h4>
<pre>boolean&nbsp;parseUHFJump2BootData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="uhfStartUpdateSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uhfStartUpdateSendData</h4>
<pre>byte[]&nbsp;uhfStartUpdateSendData()</pre>
</li>
</ul>
<a name="parseUHFStartUpdateData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseUHFStartUpdateData</h4>
<pre>boolean&nbsp;parseUHFStartUpdateData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="uhfUpdatingSendData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uhfUpdatingSendData</h4>
<pre>byte[]&nbsp;uhfUpdatingSendData(byte[]&nbsp;buf)</pre>
</li>
</ul>
<a name="parseUHFUpdatingData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseUHFUpdatingData</h4>
<pre>boolean&nbsp;parseUHFUpdatingData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="UHFStopUpdateSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStopUpdateSendData</h4>
<pre>byte[]&nbsp;UHFStopUpdateSendData()</pre>
</li>
</ul>
<a name="parseUHFStopUpdateData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseUHFStopUpdateData</h4>
<pre>boolean&nbsp;parseUHFStopUpdateData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getEPCTIDModeSendData-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEPCTIDModeSendData</h4>
<pre>byte[]&nbsp;getEPCTIDModeSendData(char&nbsp;rev1,
                             char&nbsp;rev2)</pre>
</li>
</ul>
<a name="parseGetEPCTIDModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetEPCTIDModeData</h4>
<pre>byte[]&nbsp;parseGetEPCTIDModeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getSTM32VersionSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSTM32VersionSendData</h4>
<pre>byte[]&nbsp;getSTM32VersionSendData()</pre>
</li>
</ul>
<a name="parseSTM32VersionData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSTM32VersionData</h4>
<pre>java.lang.String&nbsp;parseSTM32VersionData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setReaderAwaitSleepTimeSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReaderAwaitSleepTimeSendData</h4>
<pre>byte[]&nbsp;setReaderAwaitSleepTimeSendData(char&nbsp;time)</pre>
</li>
</ul>
<a name="parseSetReaderAwaitSleepTimeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetReaderAwaitSleepTimeData</h4>
<pre>boolean&nbsp;parseSetReaderAwaitSleepTimeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getReaderAwaitSleepTimeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReaderAwaitSleepTimeSendData</h4>
<pre>byte[]&nbsp;getReaderAwaitSleepTimeSendData()</pre>
</li>
</ul>
<a name="parseGetReaderAwaitSleepTimeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetReaderAwaitSleepTimeData</h4>
<pre>int&nbsp;parseGetReaderAwaitSleepTimeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setTagfocusSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTagfocusSendData</h4>
<pre>byte[]&nbsp;setTagfocusSendData(char&nbsp;flag)</pre>
</li>
</ul>
<a name="parseSetTagfocusData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetTagfocusData</h4>
<pre>boolean&nbsp;parseSetTagfocusData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getTagfocusSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTagfocusSendData</h4>
<pre>byte[]&nbsp;getTagfocusSendData()</pre>
</li>
</ul>
<a name="parseGetTagfocusData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetTagfocusData</h4>
<pre>int&nbsp;parseGetTagfocusData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setRFLinkSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRFLinkSendData</h4>
<pre>byte[]&nbsp;setRFLinkSendData(int&nbsp;mode)</pre>
</li>
</ul>
<a name="parseSetRFLinkData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetRFLinkData</h4>
<pre>boolean&nbsp;parseSetRFLinkData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getRFLinkSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRFLinkSendData</h4>
<pre>byte[]&nbsp;getRFLinkSendData()</pre>
</li>
</ul>
<a name="parseGetRFLinkData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetRFLinkData</h4>
<pre>int&nbsp;parseGetRFLinkData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setFastIdSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFastIdSendData</h4>
<pre>byte[]&nbsp;setFastIdSendData(int&nbsp;flag)</pre>
</li>
</ul>
<a name="parseFastIdData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseFastIdData</h4>
<pre>boolean&nbsp;parseFastIdData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getFastIDSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFastIDSendData</h4>
<pre>byte[]&nbsp;getFastIDSendData()</pre>
</li>
</ul>
<a name="parseGetFastIdData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetFastIdData</h4>
<pre>int&nbsp;parseGetFastIdData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="openLedSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openLedSendData</h4>
<pre>byte[]&nbsp;openLedSendData()</pre>
</li>
</ul>
<a name="closeLedSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeLedSendData</h4>
<pre>byte[]&nbsp;closeLedSendData()</pre>
</li>
</ul>
<a name="blinkOfLedSendData-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blinkOfLedSendData</h4>
<pre>byte[]&nbsp;blinkOfLedSendData(int&nbsp;duration,
                          int&nbsp;interval,
                          int&nbsp;count)</pre>
</li>
</ul>
<a name="parseOpenLedData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseOpenLedData</h4>
<pre>boolean&nbsp;parseOpenLedData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="parseCloseLedData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseCloseLedData</h4>
<pre>boolean&nbsp;parseCloseLedData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="parseBlinkOfLedData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBlinkOfLedData</h4>
<pre>boolean&nbsp;parseBlinkOfLedData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setPowerOnDynamicSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPowerOnDynamicSendData</h4>
<pre>byte[]&nbsp;setPowerOnDynamicSendData(int&nbsp;power)</pre>
</li>
</ul>
<a name="parseSetPowerOnDynamicData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetPowerOnDynamicData</h4>
<pre>boolean&nbsp;parseSetPowerOnDynamicData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="requestSetFastInventoryMode-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestSetFastInventoryMode</h4>
<pre>byte[]&nbsp;requestSetFastInventoryMode(boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="responseSetFastInventoryMode-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>responseSetFastInventoryMode</h4>
<pre>boolean&nbsp;responseSetFastInventoryMode(byte[]&nbsp;data)</pre>
</li>
</ul>
<a name="requestGetFastInventoryMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestGetFastInventoryMode</h4>
<pre>byte[]&nbsp;requestGetFastInventoryMode()</pre>
</li>
</ul>
<a name="responseGetFastInventoryMode-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>responseGetFastInventoryMode</h4>
<pre>int&nbsp;responseGetFastInventoryMode(byte[]&nbsp;data)</pre>
</li>
</ul>
<a name="requestGetEx10SDKFirmware--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestGetEx10SDKFirmware</h4>
<pre>byte[]&nbsp;requestGetEx10SDKFirmware()</pre>
</li>
</ul>
<a name="responseGetEx10SDKFirmware-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>responseGetEx10SDKFirmware</h4>
<pre>java.lang.String&nbsp;responseGetEx10SDKFirmware(byte[]&nbsp;data)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFProtocolParse.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" target="_top">Frames</a></li>
<li><a href="IUHFProtocolParse.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
