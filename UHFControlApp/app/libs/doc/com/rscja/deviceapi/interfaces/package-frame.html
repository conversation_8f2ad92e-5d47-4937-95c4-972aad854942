<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>com.rscja.deviceapi.interfaces</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html" target="classFrame">com.rscja.deviceapi.interfaces</a></h1>
<div class="indexContainer">
<h2 title="Interfaces">Interfaces</h2>
<ul title="Interfaces">
<li><a href="ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ConnectionStatusCallback</span></a></li>
<li><a href="IBarcode1D.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcode1D</span></a></li>
<li><a href="IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcode2D</span></a></li>
<li><a href="IBarcodePhoto.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcodePhoto</span></a></li>
<li><a href="IBarcodePictureCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcodePictureCallback</span></a></li>
<li><a href="IBarcodeSymbolUtility.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcodeSymbolUtility</span></a></li>
<li><a href="IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcodeUtility</span></a></li>
<li><a href="IBarcodeVideoCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcodeVideoCallback</span></a></li>
<li><a href="IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBleDevice</span></a></li>
<li><a href="IBluetoothData.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBluetoothData</span></a></li>
<li><a href="IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBluetoothReader</span></a></li>
<li><a href="ICardWithBYL.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ICardWithBYL</span></a></li>
<li><a href="IConnectionStatusChangedListener.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IConnectionStatusChangedListener</span></a></li>
<li><a href="IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IFingerprint</span></a></li>
<li><a href="IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IFingerprintSM206B</span></a></li>
<li><a href="IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IFingerprintWithFIPS</span></a></li>
<li><a href="IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IFingerprintWithMorpho</span></a></li>
<li><a href="IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IFingerprintWithTLK1NC</span></a></li>
<li><a href="IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IFingerprintWithZAZ</span></a></li>
<li><a href="IGPIStateCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IGPIStateCallback</span></a></li>
<li><a href="IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IHandheldRFID</span></a></li>
<li><a href="IHF14443A.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IHF14443A</span></a></li>
<li><a href="IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IHF14443B</span></a></li>
<li><a href="IHF15693.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IHF15693</span></a></li>
<li><a href="IInfrared.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IInfrared</span></a></li>
<li><a href="ILedLight.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ILedLight</span></a></li>
<li><a href="IModule.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IModule</span></a></li>
<li><a href="IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IMultipleAntenna</span></a></li>
<li><a href="IPrinter.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IPrinter</span></a></li>
<li><a href="IPSAM.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IPSAM</span></a></li>
<li><a href="IReader.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IReader</span></a></li>
<li><a href="IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDBase</span></a></li>
<li><a href="IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithISO14443A</span></a></li>
<li><a href="IRFIDWithISO14443A4CPU.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithISO14443A4CPU</span></a></li>
<li><a href="IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithISO14443B</span></a></li>
<li><a href="IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithISO15693</span></a></li>
<li><a href="IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithLF</span></a></li>
<li><a href="IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA4</span></a></li>
<li><a href="IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA4NetWork</span></a></li>
<li><a href="IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA4RS232</span></a></li>
<li><a href="IRFIDWithUHFA4Uart.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA4Uart</span></a></li>
<li><a href="IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA8</span></a></li>
<li><a href="IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA8NetWork</span></a></li>
<li><a href="IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA8RS232</span></a></li>
<li><a href="IRFIDWithUHFA8Uart.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA8Uart</span></a></li>
<li><a href="IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFRLM</span></a></li>
<li><a href="IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFUrxNetwork</span></a></li>
<li><a href="IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFUrxUart</span></a></li>
<li><a href="IRFIDWithUHFUrxUsbToUart.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFUrxUsbToUart</span></a></li>
<li><a href="IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFUSB</span></a></li>
<li><a href="IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IScanerLedLight</span></a></li>
<li><a href="ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ISingleAntenna</span></a></li>
<li><a href="ITagLocate.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ITagLocate</span></a></li>
<li><a href="ITagLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ITagLocationCallback</span></a></li>
<li><a href="IUHF.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHF</span></a></li>
<li><a href="IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFA4</span></a></li>
<li><a href="IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFA8</span></a></li>
<li><a href="IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUhfBle</span></a></li>
<li><a href="IUHFInventoryCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFInventoryCallback</span></a></li>
<li><a href="IUHFLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFLocationCallback</span></a></li>
<li><a href="IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFOfAndroidUart</span></a></li>
<li><a href="IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFProtocolParse</span></a></li>
<li><a href="IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFProtocolParseUrx</span></a></li>
<li><a href="IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFRadarLocationCallback</span></a></li>
<li><a href="IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUhfReader</span></a></li>
<li><a href="IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFURAxExtend</span></a></li>
<li><a href="IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFURx</span></a></li>
<li><a href="IUHFUrxAutoInventoryTag.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFUrxAutoInventoryTag</span></a></li>
<li><a href="IUpgradeProgress.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUpgradeProgress</span></a></li>
<li><a href="IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IURAxOfAndroidUart</span></a></li>
<li><a href="IUsbFingerprint.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUsbFingerprint</span></a></li>
<li><a href="KeyEventCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">KeyEventCallback</span></a></li>
<li><a href="OnLowBatteryListener.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">OnLowBatteryListener</span></a></li>
<li><a href="ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ScanBTCallback</span></a></li>
</ul>
<h2 title="Enums">Enums</h2>
<ul title="Enums">
<li><a href="ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces" target="classFrame">ConnectionStatus</a></li>
</ul>
</div>
</body>
</html>
