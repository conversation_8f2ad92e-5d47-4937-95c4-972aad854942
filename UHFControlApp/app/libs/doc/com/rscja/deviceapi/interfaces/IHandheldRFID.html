<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IHandheldRFID</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IHandheldRFID";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IHandheldRFID.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IGPIStateCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443A.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IHandheldRFID.html" target="_top">Frames</a></li>
<li><a href="IHandheldRFID.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IHandheldRFID" class="title">Interface IHandheldRFID</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUSB</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a>, <a href="../../../../com/rscja/custom/M775Authenticate.html" title="class in com.rscja.custom">M775Authenticate</a>, <a href="../../../../com/rscja/team/mtk/custom/M775Authenticate_mtk.html" title="class in com.rscja.team.mtk.custom">M775Authenticate_mtk</a>, <a href="../../../../com/rscja/team/qcom/custom/M775Authenticate_qcom.html" title="class in com.rscja.team.qcom.custom">M775Authenticate_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFAxBase.html" title="class in com.rscja.deviceapi">RFIDWithUHFAxBase</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFAxBase_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a>, <a href="../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html" title="class in com.rscja.team.qcom.ble">RFIDWithUHFBLEN51_qcom</a>, <a href="../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html" title="class in com.rscja.team.qcom.ble">RFIDWithUHFBLEN52_qcom</a>, <a href="../../../../com/rscja/custom/RFIDWithUHFJieCe.html" title="class in com.rscja.custom">RFIDWithUHFJieCe</a>, <a href="../../../../com/rscja/team/qcom/custom/RFIDWithUHFJieCe_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFJieCe_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi">RFIDWithUHFRLM</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a>, <a href="../../../../com/rscja/team/qcom/custom/RFIDWithUHFShuangYingDianZi_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFShuangYingDianZi_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a>, <a href="../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a>, <a href="../../../../com/rscja/team/qcom/custom/RFIDWithUHFUARTUAE_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFUARTUAE_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a>, <a href="../../../../com/rscja/custom/UHFCSYX.html" title="class in com.rscja.custom">UHFCSYX</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFCSYX_qcom.html" title="class in com.rscja.team.qcom.custom">UHFCSYX_qcom</a>, <a href="../../../../com/rscja/custom/UHFTamperAPI.html" title="class in com.rscja.custom">UHFTamperAPI</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFTamperAPI_qcom.html" title="class in com.rscja.team.qcom.custom">UHFTamperAPI_qcom</a>, <a href="../../../../com/rscja/custom/UHFTemperatureSensors.html" title="class in com.rscja.custom">UHFTemperatureSensors</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTag.html" title="class in com.rscja.custom">UHFTemperatureTag</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN51.html" title="class in com.rscja.custom">UHFTemperatureTag.RFIDWithUHFBLEN51</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN52.html" title="class in com.rscja.custom">UHFTemperatureTag.RFIDWithUHFBLEN52</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTagsAPI.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI</a>, <a href="../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html" title="class in com.rscja.team.mtk.custom">UHFTemperatureTagsAPI_mtk</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFTemperatureTagsAPI_qcom.html" title="class in com.rscja.team.qcom.custom">UHFTemperatureTagsAPI_qcom</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFTemperatureTagsBLEAPI.html" title="class in com.rscja.team.qcom.custom">UHFTemperatureTagsBLEAPI</a>, <a href="../../../../com/rscja/custom/UHFUartFoxconn.html" title="class in com.rscja.custom">UHFUartFoxconn</a>, <a href="../../../../com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html" title="class in com.rscja.team.mtk.custom">UHFUartFoxconn_mtk</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFUartFoxconn_qcom.html" title="class in com.rscja.team.qcom.custom">UHFUartFoxconn_qcom</a>, <a href="../../../../com/rscja/custom/UHFUartTemperatureTag.html" title="class in com.rscja.custom">UHFUartTemperatureTag</a>, <a href="../../../../com/rscja/custom/UHFXSAPI.html" title="class in com.rscja.custom">UHFXSAPI</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IHandheldRFID</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/interfaces/ITagLocate.html" title="interface in com.rscja.deviceapi.interfaces">ITagLocate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html#getTagLocate-android.content.Context-">getTagLocate</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html#setDynamicDistance-int-">setDynamicDistance</a></span>(int&nbsp;value)</code>
<div class="block">Radar positioning dynamically adjusts distance.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html#startLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFLocationCallback-">startLocation</a></span>(android.content.Context&nbsp;context,
             java.lang.String&nbsp;label,
             int&nbsp;bank,
             int&nbsp;ptr,
             <a href="../../../../com/rscja/deviceapi/interfaces/IUHFLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFLocationCallback</a>&nbsp;locationCallback)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html#startRadarLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFRadarLocationCallback-">startRadarLocation</a></span>(android.content.Context&nbsp;context,
                  java.lang.String&nbsp;tag,
                  int&nbsp;bank,
                  int&nbsp;ptr,
                  <a href="../../../../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFRadarLocationCallback</a>&nbsp;locationCallback)</code>
<div class="block">开始定位 <br>
 Start location</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html#stopLocation--">stopLocation</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html#stopRadarLocation--">stopRadarLocation</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getTagLocate-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTagLocate</h4>
<pre><a href="../../../../com/rscja/deviceapi/interfaces/ITagLocate.html" title="interface in com.rscja.deviceapi.interfaces">ITagLocate</a>&nbsp;getTagLocate(android.content.Context&nbsp;context)</pre>
</li>
</ul>
<a name="stopLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopLocation</h4>
<pre>boolean&nbsp;stopLocation()</pre>
</li>
</ul>
<a name="startLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFLocationCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startLocation</h4>
<pre>boolean&nbsp;startLocation(android.content.Context&nbsp;context,
                      java.lang.String&nbsp;label,
                      int&nbsp;bank,
                      int&nbsp;ptr,
                      <a href="../../../../com/rscja/deviceapi/interfaces/IUHFLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFLocationCallback</a>&nbsp;locationCallback)</pre>
</li>
</ul>
<a name="stopRadarLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopRadarLocation</h4>
<pre>boolean&nbsp;stopRadarLocation()</pre>
</li>
</ul>
<a name="startRadarLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFRadarLocationCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startRadarLocation</h4>
<pre>boolean&nbsp;startRadarLocation(android.content.Context&nbsp;context,
                           java.lang.String&nbsp;tag,
                           int&nbsp;bank,
                           int&nbsp;ptr,
                           <a href="../../../../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFRadarLocationCallback</a>&nbsp;locationCallback)</pre>
<div class="block">开始定位 <br>
 Start location</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - context</dd>
<dd><code>tag</code> - 要定位的标签(locate to the label)</dd>
<dd><code>bank</code> - 标签的存储区(Storage area):<code>#Bank_RESERVED</code>、<code>#Bank_EPC</code>、<code>#Bank_TID</code>、<code>#Bank_USER</code></dd>
<dd><code>ptr</code> - 起始地址的偏移量(start address)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="setDynamicDistance-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setDynamicDistance</h4>
<pre>boolean&nbsp;setDynamicDistance(int&nbsp;value)</pre>
<div class="block">Radar positioning dynamically adjusts distance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - 1-30</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IHandheldRFID.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IGPIStateCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IHF14443A.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IHandheldRFID.html" target="_top">Frames</a></li>
<li><a href="IHandheldRFID.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
