<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IFingerprintWithFIPS</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IFingerprintWithFIPS";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IFingerprintWithFIPS.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" target="_top">Frames</a></li>
<li><a href="IFingerprintWithFIPS.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IFingerprintWithFIPS" class="title">Interface IFingerprintWithFIPS</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS</a>, <a href="../../../../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IFingerprintWithFIPS</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#deleteAllFingers--">deleteAllFingers</a></span>()</code>
<div class="block">删除所有指纹信息<br>
 delete all fingerprint information<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#enroll--">enroll</a></span>()</code>
<div class="block">采集指纹<br>
 Acquire fingerprint<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#free--">free</a></span>()</code>
<div class="block">释放指纹模块<br>
 Release fingerprint module<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#generateImg-byte:A-java.lang.String-">generateImg</a></span>(byte[]&nbsp;data,
           java.lang.String&nbsp;filePath)</code>
<div class="block">生成bmp图片<br>
 Generate bmp<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#getFingersCount--">getFingersCount</a></span>()</code>
<div class="block">获取模块中采集的指纹数量<br>
 Acquire collected fingerprint amounts in module.<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#getID--">getID</a></span>()</code>
<div class="block">获取指纹ID<br>
 Acquire fingerprint ID<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.FingerprintInfo.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS.FingerprintInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#getPTInfo--">getPTInfo</a></span>()</code>
<div class="block">获取模块信息<br>
 Acquire module information<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#grab--">grab</a></span>()</code>
<div class="block">采集图像<br>
 Acquire image<br></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#init--">init</a></span>()</code>
<div class="block">初始化指纹模块<br>
 Initialize fingerprint module<br></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#init-android.content.Context-">init</a></span>(android.content.Context&nbsp;mContext)</code>
<div class="block">初始化指纹模块<br>
 Initialize fingerprint module<br></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#isPowerOn--">isPowerOn</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#ptCapture--">ptCapture</a></span>()</code>
<div class="block">获取指纹模版<br>
 Acquire fingerprint template<br></div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#ptConvertTemplateEx-com.rscja.deviceapi.FingerprintWithFIPS.DataFormat-byte:A-int-">ptConvertTemplateEx</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.DataFormat.html" title="enum in com.rscja.deviceapi">FingerprintWithFIPS.DataFormat</a>&nbsp;jtarget_type,
                   byte[]&nbsp;srcbuf,
                   int&nbsp;jsrclen)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#responseCancel--">responseCancel</a></span>()</code>
<div class="block">取消当前操作<br>
 Cancel current operation<br></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#responseContinue--">responseContinue</a></span>()</code>
<div class="block">继续当前操作<br>
 Continue current operation<br></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setEnrollCallBack-com.rscja.deviceapi.FingerprintWithFIPS.EnrollCallBack-">setEnrollCallBack</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.EnrollCallBack</a>&nbsp;callBack)</code>
<div class="block">设置采集指纹回调接口<br>
 Setup call-back contact for acquiring fingerprint<br></div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setGrabCallBack-com.rscja.deviceapi.FingerprintWithFIPS.GRABCallBack-">setGrabCallBack</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.GRABCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.GRABCallBack</a>&nbsp;callBack)</code>
<div class="block">设置获取图片回调接口<br>
 Setup call-back contact for acquiring picture<br></div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setID-java.lang.String-">setID</a></span>(java.lang.String&nbsp;hexID)</code>
<div class="block">设置指纹模块ID<br>
 Setup fingerprint module ID<br></div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setIdentificationCallBack-com.rscja.deviceapi.FingerprintWithFIPS.IdentificationCallBack-">setIdentificationCallBack</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.IdentificationCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.IdentificationCallBack</a>&nbsp;callBack)</code>
<div class="block">设置指纹验证回调接口<br>
 Setup fingerprint verification<br></div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setPtCaptureCallBack-com.rscja.deviceapi.FingerprintWithFIPS.PtCaptureCallBack-">setPtCaptureCallBack</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.PtCaptureCallBack</a>&nbsp;callBack)</code>
<div class="block">设置获取指纹模版回调接口<br>
 Setup call-back contact for acquiring fingerprint template<br></div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setTemplateVerifyCallBack-com.rscja.deviceapi.FingerprintWithFIPS.TemplateVerifyCallBack-">setTemplateVerifyCallBack</a></span>(<a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.TemplateVerifyCallBack</a>&nbsp;callBack)</code>
<div class="block">设置模版比对回调接口<br>
 Setup call-back contact for template comparison<br></div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#startEnroll--">startEnroll</a></span>()</code>
<div class="block">开始采集指纹,注意:请调用<br>
 start acquire fingerprint, attention: call out<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setEnrollCallBack-com.rscja.deviceapi.FingerprintWithFIPS.EnrollCallBack-"><code>setEnrollCallBack(FingerprintWithFIPS.EnrollCallBack callBack)</code></a></b> 方法设置接收回调数据<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setEnrollCallBack-com.rscja.deviceapi.FingerprintWithFIPS.EnrollCallBack-"><code>setEnrollCallBack(FingerprintWithFIPS.EnrollCallBack callBack)</code></a></b>setup receviced call-back data <br></div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#startGRAB--">startGRAB</a></span>()</code>
<div class="block">开始获取图片,注意:请调用<br>
 start acquire picture, attention: call out<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setGrabCallBack-com.rscja.deviceapi.FingerprintWithFIPS.GRABCallBack-"><code>setGrabCallBack(FingerprintWithFIPS.GRABCallBack callBack)</code></a></b> 方法设置接收回调数据<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setGrabCallBack-com.rscja.deviceapi.FingerprintWithFIPS.GRABCallBack-"><code>setGrabCallBack(FingerprintWithFIPS.GRABCallBack callBack)</code></a></b>method to setup received call-back data.<br></div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#startIdentification--">startIdentification</a></span>()</code>
<div class="block">开始验证指纹,注意:请调用<br>
 start verify finerprint, attention: call out<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setIdentificationCallBack-com.rscja.deviceapi.FingerprintWithFIPS.IdentificationCallBack-"><code>setIdentificationCallBack(FingerprintWithFIPS.IdentificationCallBack callBack)</code></a></b> 方法设置接收回调数据<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setIdentificationCallBack-com.rscja.deviceapi.FingerprintWithFIPS.IdentificationCallBack-"><code>setIdentificationCallBack(FingerprintWithFIPS.IdentificationCallBack callBack)</code></a></b> setup receviced call-back data <br></div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#startPtCapture--">startPtCapture</a></span>()</code>
<div class="block">开始获取指纹模版,注意:请调用<br>
 Start acquire fingerprint template, attention: call up <br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setPtCaptureCallBack-com.rscja.deviceapi.FingerprintWithFIPS.PtCaptureCallBack-"><code>setPtCaptureCallBack(FingerprintWithFIPS.PtCaptureCallBack callBack)</code></a></b> 方法设置接收回调数据<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setPtCaptureCallBack-com.rscja.deviceapi.FingerprintWithFIPS.PtCaptureCallBack-"><code>setPtCaptureCallBack(FingerprintWithFIPS.PtCaptureCallBack callBack)</code></a></b> method to setup received call-back data.<br></div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#startTemplateVerify-char:A-">startTemplateVerify</a></span>(char[]&nbsp;template)</code>
<div class="block">开始模版验证,注意:请调用<br>
 start template verification, attention: call out<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setTemplateVerifyCallBack-com.rscja.deviceapi.FingerprintWithFIPS.TemplateVerifyCallBack-"><code>setTemplateVerifyCallBack(FingerprintWithFIPS.TemplateVerifyCallBack callBack)</code></a></b> 方法设置接收回调数据<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setTemplateVerifyCallBack-com.rscja.deviceapi.FingerprintWithFIPS.TemplateVerifyCallBack-"><code>setTemplateVerifyCallBack(FingerprintWithFIPS.TemplateVerifyCallBack callBack)</code></a></b>method to setup received call-back data.<br></div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#stopEnroll--">stopEnroll</a></span>()</code>
<div class="block">停止采集指纹<br>
 Stop acquiring fingerprint<br></div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#stopGRAB--">stopGRAB</a></span>()</code>
<div class="block">停止获取图片<br>
 stop acquiring picture<br></div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#stopIdentification--">stopIdentification</a></span>()</code>
<div class="block">停止指纹验证<br>
 Stop fingerprint verification<br></div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#stopPtCapture--">stopPtCapture</a></span>()</code>
<div class="block">停止获取指纹模版<br>
 Stop acquiring fingerprint template<br></div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#stopTemplateVerify--">stopTemplateVerify</a></span>()</code>
<div class="block">停止模版比对<br>
 Stop template comparison<br></div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#storeFinger-byte:A-">storeFinger</a></span>(byte[]&nbsp;templateData)</code>
<div class="block">将指纹模板导入到指纹模块中</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#Verify-char:A-int-">Verify</a></span>(char[]&nbsp;data,
      int&nbsp;len)</code>
<div class="block">模版比对指纹,导入模版数据和当前指纹比对<br>
 Compare template and fingerprint, import template data and current fingerprint comparison.<br></div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#verifyALL--">verifyALL</a></span>()</code>
<div class="block">验证指纹,验证当前指纹是否在指纹模块库里面存在<br>
 Verify fingerprint, verify current fingerprint exist in fingerprint library or not.<br></div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="init-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>boolean&nbsp;init(android.content.Context&nbsp;mContext)</pre>
<div class="block">初始化指纹模块<br>
 Initialize fingerprint module<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true means success, false means failed<br></dd>
</dl>
</li>
</ul>
<a name="init--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>boolean&nbsp;init()</pre>
<div class="block">初始化指纹模块<br>
 Initialize fingerprint module<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 truen means success, false means failed<br></dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>boolean&nbsp;free()</pre>
<div class="block">释放指纹模块<br>
 Release fingerprint module<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true means, false means failed<br></dd>
</dl>
</li>
</ul>
<a name="enroll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enroll</h4>
<pre>int&nbsp;enroll()</pre>
<div class="block">采集指纹<br>
 Acquire fingerprint<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0表示操作成功，其他值表示操作失败<br>
 0 means operation success, other means operation failed.<br></dd>
</dl>
</li>
</ul>
<a name="verifyALL--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>verifyALL</h4>
<pre>int&nbsp;verifyALL()</pre>
<div class="block">验证指纹,验证当前指纹是否在指纹模块库里面存在<br>
 Verify fingerprint, verify current fingerprint exist in fingerprint library or not.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0表示操作成功，其他值表示操作失败<br>
 0 means operation success, other means operation failed.<br></dd>
</dl>
</li>
</ul>
<a name="Verify-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Verify</h4>
<pre>int&nbsp;Verify(char[]&nbsp;data,
           int&nbsp;len)</pre>
<div class="block">模版比对指纹,导入模版数据和当前指纹比对<br>
 Compare template and fingerprint, import template data and current fingerprint comparison.<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - 模版数据<br>
             data template data<br></dd>
<dd><code>len</code> - 数据长度<br>
             len data length<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0表示操作成功，其他值表示操作失败<br>
 0 means operation success, others means operation failed<br></dd>
</dl>
</li>
</ul>
<a name="deleteAllFingers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteAllFingers</h4>
<pre>int&nbsp;deleteAllFingers()</pre>
<div class="block">删除所有指纹信息<br>
 delete all fingerprint information<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0表示操作成功，其他值表示操作失败<br>
 0 means operation success, others means operation failed<br></dd>
</dl>
</li>
</ul>
<a name="storeFinger-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>storeFinger</h4>
<pre>int&nbsp;storeFinger(byte[]&nbsp;templateData)</pre>
<div class="block">将指纹模板导入到指纹模块中</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>templateData</code> - 模板数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回id; success: >=0    faile:-1</dd>
</dl>
</li>
</ul>
<a name="getFingersCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFingersCount</h4>
<pre>int&nbsp;getFingersCount()</pre>
<div class="block">获取模块中采集的指纹数量<br>
 Acquire collected fingerprint amounts in module.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>指纹数量, 返回-1为获取失败<br>
 return fingerprint amounts, return -1 means acquire failed<br></dd>
</dl>
</li>
</ul>
<a name="responseCancel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>responseCancel</h4>
<pre>int&nbsp;responseCancel()</pre>
<div class="block">取消当前操作<br>
 Cancel current operation<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0表示操作成功，其他值表示操作失败<br>
 return 0 means operation success, others means operation failed<br></dd>
</dl>
</li>
</ul>
<a name="responseContinue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>responseContinue</h4>
<pre>byte[]&nbsp;responseContinue()</pre>
<div class="block">继续当前操作<br>
 Continue current operation<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>索引0~4 是状态，4~8是后面数据的长度，8~12是指纹ID；如果是采集指纹，后面的数据就是指纹特征数据<br>
 return index 0 to 4 means status, 4 to 8 means back data length, 8 to 12 means fingerprint ID, if it is acquired fingerprint, back data means fingerprint characteristic data.<br></dd>
</dl>
</li>
</ul>
<a name="grab--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>grab</h4>
<pre>int&nbsp;grab()</pre>
<div class="block">采集图像<br>
 Acquire image<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0表示操作成功，其他值表示操作失败<br>
 return 0 means operation success, others means operation failed<br></dd>
</dl>
</li>
</ul>
<a name="ptCapture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ptCapture</h4>
<pre>int&nbsp;ptCapture()</pre>
<div class="block">获取指纹模版<br>
 Acquire fingerprint template<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0表示操作成功，其他值表示操作失败<br>
 return 0 means operation success, others means operation failed<br></dd>
</dl>
</li>
</ul>
<a name="ptConvertTemplateEx-com.rscja.deviceapi.FingerprintWithFIPS.DataFormat-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ptConvertTemplateEx</h4>
<pre>byte[]&nbsp;ptConvertTemplateEx(<a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.DataFormat.html" title="enum in com.rscja.deviceapi">FingerprintWithFIPS.DataFormat</a>&nbsp;jtarget_type,
                           byte[]&nbsp;srcbuf,
                           int&nbsp;jsrclen)</pre>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>jtarget_type</code> - 转换后的格式类型<br>
                     param jtarget_type data format after transformed<br></dd>
<dd><code>srcbuf</code> - 转换之前的数据<br>
                     param srcbuf before transformed<br></dd>
<dd><code>jsrclen</code> - 转换之前的数据长度<br>
                     param jsrclen data length before transformed<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回转换后的数据<br>
 return transfered data<br></dd>
</dl>
</li>
</ul>
<a name="getPTInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPTInfo</h4>
<pre><a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.FingerprintInfo.html" title="class in com.rscja.deviceapi">FingerprintWithFIPS.FingerprintInfo</a>&nbsp;getPTInfo()</pre>
<div class="block">获取模块信息<br>
 Acquire module information<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回fips指纹模块信息<br>
 return fips fingerprint module information<br></dd>
</dl>
</li>
</ul>
<a name="generateImg-byte:A-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateImg</h4>
<pre>boolean&nbsp;generateImg(byte[]&nbsp;data,
                    java.lang.String&nbsp;filePath)</pre>
<div class="block">生成bmp图片<br>
 Generate bmp<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - 数据<br>
                 parame data<br></dd>
<dd><code>filePath</code> - 生成后图片保存的文件路径<br>
                 param filePath file indirectory after picture generated<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true 成功，false 失败<br>
 return ture means success, false means failed<br></dd>
</dl>
</li>
</ul>
<a name="setID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setID</h4>
<pre>boolean&nbsp;setID(java.lang.String&nbsp;hexID)</pre>
<div class="block">设置指纹模块ID<br>
 Setup fingerprint module ID<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>设置成功返回ture，设置失败返回false<br>
 setup success return ture, setup failed return false<br></dd>
</dl>
</li>
</ul>
<a name="getID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getID</h4>
<pre>java.lang.String&nbsp;getID()</pre>
<div class="block">获取指纹ID<br>
 Acquire fingerprint ID<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回指纹ID<br>
 return fingerprint ID<br></dd>
</dl>
</li>
</ul>
<a name="startPtCapture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startPtCapture</h4>
<pre>void&nbsp;startPtCapture()</pre>
<div class="block">开始获取指纹模版,注意:请调用<br>
 Start acquire fingerprint template, attention: call up <br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setPtCaptureCallBack-com.rscja.deviceapi.FingerprintWithFIPS.PtCaptureCallBack-"><code>setPtCaptureCallBack(FingerprintWithFIPS.PtCaptureCallBack callBack)</code></a></b> 方法设置接收回调数据<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setPtCaptureCallBack-com.rscja.deviceapi.FingerprintWithFIPS.PtCaptureCallBack-"><code>setPtCaptureCallBack(FingerprintWithFIPS.PtCaptureCallBack callBack)</code></a></b> method to setup received call-back data.<br></div>
</li>
</ul>
<a name="startGRAB--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startGRAB</h4>
<pre>void&nbsp;startGRAB()</pre>
<div class="block">开始获取图片,注意:请调用<br>
 start acquire picture, attention: call out<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setGrabCallBack-com.rscja.deviceapi.FingerprintWithFIPS.GRABCallBack-"><code>setGrabCallBack(FingerprintWithFIPS.GRABCallBack callBack)</code></a></b> 方法设置接收回调数据<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setGrabCallBack-com.rscja.deviceapi.FingerprintWithFIPS.GRABCallBack-"><code>setGrabCallBack(FingerprintWithFIPS.GRABCallBack callBack)</code></a></b>method to setup received call-back data.<br></div>
</li>
</ul>
<a name="startTemplateVerify-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startTemplateVerify</h4>
<pre>void&nbsp;startTemplateVerify(char[]&nbsp;template)</pre>
<div class="block">开始模版验证,注意:请调用<br>
 start template verification, attention: call out<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setTemplateVerifyCallBack-com.rscja.deviceapi.FingerprintWithFIPS.TemplateVerifyCallBack-"><code>setTemplateVerifyCallBack(FingerprintWithFIPS.TemplateVerifyCallBack callBack)</code></a></b> 方法设置接收回调数据<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setTemplateVerifyCallBack-com.rscja.deviceapi.FingerprintWithFIPS.TemplateVerifyCallBack-"><code>setTemplateVerifyCallBack(FingerprintWithFIPS.TemplateVerifyCallBack callBack)</code></a></b>method to setup received call-back data.<br></div>
</li>
</ul>
<a name="startEnroll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startEnroll</h4>
<pre>void&nbsp;startEnroll()</pre>
<div class="block">开始采集指纹,注意:请调用<br>
 start acquire fingerprint, attention: call out<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setEnrollCallBack-com.rscja.deviceapi.FingerprintWithFIPS.EnrollCallBack-"><code>setEnrollCallBack(FingerprintWithFIPS.EnrollCallBack callBack)</code></a></b> 方法设置接收回调数据<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setEnrollCallBack-com.rscja.deviceapi.FingerprintWithFIPS.EnrollCallBack-"><code>setEnrollCallBack(FingerprintWithFIPS.EnrollCallBack callBack)</code></a></b>setup receviced call-back data <br></div>
</li>
</ul>
<a name="startIdentification--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startIdentification</h4>
<pre>void&nbsp;startIdentification()</pre>
<div class="block">开始验证指纹,注意:请调用<br>
 start verify finerprint, attention: call out<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setIdentificationCallBack-com.rscja.deviceapi.FingerprintWithFIPS.IdentificationCallBack-"><code>setIdentificationCallBack(FingerprintWithFIPS.IdentificationCallBack callBack)</code></a></b> 方法设置接收回调数据<br>
 <b><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html#setIdentificationCallBack-com.rscja.deviceapi.FingerprintWithFIPS.IdentificationCallBack-"><code>setIdentificationCallBack(FingerprintWithFIPS.IdentificationCallBack callBack)</code></a></b> setup receviced call-back data <br></div>
</li>
</ul>
<a name="stopGRAB--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopGRAB</h4>
<pre>void&nbsp;stopGRAB()</pre>
<div class="block">停止获取图片<br>
 stop acquiring picture<br></div>
</li>
</ul>
<a name="stopEnroll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopEnroll</h4>
<pre>void&nbsp;stopEnroll()</pre>
<div class="block">停止采集指纹<br>
 Stop acquiring fingerprint<br></div>
</li>
</ul>
<a name="stopPtCapture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopPtCapture</h4>
<pre>void&nbsp;stopPtCapture()</pre>
<div class="block">停止获取指纹模版<br>
 Stop acquiring fingerprint template<br></div>
</li>
</ul>
<a name="stopTemplateVerify--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopTemplateVerify</h4>
<pre>void&nbsp;stopTemplateVerify()</pre>
<div class="block">停止模版比对<br>
 Stop template comparison<br></div>
</li>
</ul>
<a name="stopIdentification--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopIdentification</h4>
<pre>void&nbsp;stopIdentification()</pre>
<div class="block">停止指纹验证<br>
 Stop fingerprint verification<br></div>
</li>
</ul>
<a name="setGrabCallBack-com.rscja.deviceapi.FingerprintWithFIPS.GRABCallBack-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGrabCallBack</h4>
<pre>void&nbsp;setGrabCallBack(<a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.GRABCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.GRABCallBack</a>&nbsp;callBack)</pre>
<div class="block">设置获取图片回调接口<br>
 Setup call-back contact for acquiring picture<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>callBack</code> - 回调方法<br>
                 callBack call-back method<br></dd>
</dl>
</li>
</ul>
<a name="setEnrollCallBack-com.rscja.deviceapi.FingerprintWithFIPS.EnrollCallBack-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnrollCallBack</h4>
<pre>void&nbsp;setEnrollCallBack(<a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.EnrollCallBack</a>&nbsp;callBack)</pre>
<div class="block">设置采集指纹回调接口<br>
 Setup call-back contact for acquiring fingerprint<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>callBack</code> - 回调方法<br>
                 callBack call-back method<br></dd>
</dl>
</li>
</ul>
<a name="setPtCaptureCallBack-com.rscja.deviceapi.FingerprintWithFIPS.PtCaptureCallBack-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPtCaptureCallBack</h4>
<pre>void&nbsp;setPtCaptureCallBack(<a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.PtCaptureCallBack</a>&nbsp;callBack)</pre>
<div class="block">设置获取指纹模版回调接口<br>
 Setup call-back contact for acquiring fingerprint template<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>callBack</code> - 回调方法<br>
                 callBack call-back method<br></dd>
</dl>
</li>
</ul>
<a name="setIdentificationCallBack-com.rscja.deviceapi.FingerprintWithFIPS.IdentificationCallBack-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIdentificationCallBack</h4>
<pre>void&nbsp;setIdentificationCallBack(<a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.IdentificationCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.IdentificationCallBack</a>&nbsp;callBack)</pre>
<div class="block">设置指纹验证回调接口<br>
 Setup fingerprint verification<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>callBack</code> - 回调方法<br>
                 callBack call-back method<br></dd>
</dl>
</li>
</ul>
<a name="setTemplateVerifyCallBack-com.rscja.deviceapi.FingerprintWithFIPS.TemplateVerifyCallBack-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTemplateVerifyCallBack</h4>
<pre>void&nbsp;setTemplateVerifyCallBack(<a href="../../../../com/rscja/deviceapi/FingerprintWithFIPS.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithFIPS.TemplateVerifyCallBack</a>&nbsp;callBack)</pre>
<div class="block">设置模版比对回调接口<br>
 Setup call-back contact for template comparison<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>callBack</code> - 回调方法<br>
                 callBack call-back method<br></dd>
</dl>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>boolean&nbsp;isPowerOn()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IFingerprintWithFIPS.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" target="_top">Frames</a></li>
<li><a href="IFingerprintWithFIPS.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
