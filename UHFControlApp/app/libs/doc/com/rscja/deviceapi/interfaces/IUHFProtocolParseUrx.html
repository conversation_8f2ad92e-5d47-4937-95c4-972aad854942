<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IUHFProtocolParseUrx</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IUHFProtocolParseUrx";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFProtocolParseUrx.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" target="_top">Frames</a></li>
<li><a href="IUHFProtocolParseUrx.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IUHFProtocolParseUrx" class="title">Interface IUHFProtocolParseUrx</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IUHFProtocolParseUrx</span>
extends <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getAntennaConnectStateSendData--">getAntennaConnectStateSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getAntSendData--">getAntSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getAntWorkTimeSendData-byte-">getAntWorkTimeSendData</a></span>(byte&nbsp;antnum)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getBeepSendData-boolean-">getBeepSendData</a></span>(boolean&nbsp;isOpen)</code>
<div class="block">获取设置蜂鸣器的发送数据</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getIOControlSendData--">getIOControlSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getReaderBeepStatusSendData--">getReaderBeepStatusSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getReaderDestIpSendData--">getReaderDestIpSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getReaderIpSendData--">getReaderIpSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getSetSoftResetSendData--">getSetSoftResetSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getTriggerWorkModeParaSendData--">getTriggerWorkModeParaSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#getWorkModeSendData--">getWorkModeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseBeepData-byte:A-">parseBeepData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析设置蜂鸣器返回的数据</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseGetAntData-byte:A-">parseGetAntData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/rscja/deviceapi/entity/AntennaConnectState.html" title="class in com.rscja.deviceapi.entity">AntennaConnectState</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseGetAntennaConnectStateReceiveData-byte:A-">parseGetAntennaConnectStateReceiveData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseGetAntWorkTimeReceiveData-byte:A-">parseGetAntWorkTimeReceiveData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseGetIOControlReceiveData-byte:A-">parseGetIOControlReceiveData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseGetReaderBeepStatusData-byte:A-">parseGetReaderBeepStatusData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseGetReaderDestIpData-byte:A-">parseGetReaderDestIpData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseGetReaderIpData-byte:A-">parseGetReaderIpData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseGetTriggerWorkModeParaReceiveData-byte:A-">parseGetTriggerWorkModeParaReceiveData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseGetWorkModeReceiveData-byte:A-">parseGetWorkModeReceiveData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseSetAntData-byte:A-">parseSetAntData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseSetAntWorkTimeReceiveData-byte:A-">parseSetAntWorkTimeReceiveData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseSetIOControlReceiveData-byte:A-">parseSetIOControlReceiveData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseSetReaderDestIpData-byte:A-">parseSetReaderDestIpData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseSetReaderIpData-byte:A-">parseSetReaderIpData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseSetSoftResetData-byte:A-">parseSetSoftResetData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseSetTriggerWorkModeParaReceiveData-byte:A-">parseSetTriggerWorkModeParaReceiveData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#parseSetWorkModeReceiveData-byte:A-">parseSetWorkModeReceiveData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#setAntSendData-char-byte:A-">setAntSendData</a></span>(char&nbsp;saveflag,
              byte[]&nbsp;antStatus)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#setAntWorkTimeSendData-byte-int-">setAntWorkTimeSendData</a></span>(byte&nbsp;antnum,
                      int&nbsp;WorkTime)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#setIOControlSendData-byte-byte-byte-">setIOControlSendData</a></span>(byte&nbsp;output1,
                    byte&nbsp;output2,
                    byte&nbsp;relayStatus)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#setReaderDestIpSendData-com.rscja.deviceapi.entity.ReaderIPEntity-">setReaderDestIpSendData</a></span>(<a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;uhfconfig)</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#setReaderIpSendData-com.rscja.deviceapi.entity.ReaderIPEntity-">setReaderIpSendData</a></span>(<a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;uhfconfig)</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#setTriggerWorkModeParaSendData-int-int-int-int-">setTriggerWorkModeParaSendData</a></span>(int&nbsp;input,
                              int&nbsp;workTime,
                              int&nbsp;waitTime,
                              int&nbsp;receiveMode)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html#setWorkModeSendData-int-">setWorkModeSendData</a></span>(int&nbsp;mode)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUHFProtocolParse">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></h3>
<code><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#blinkOfLedSendData-int-int-int-">blinkOfLedSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#blockEraseDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-">blockEraseDataSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#blockWriteDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-java.lang.String-">blockWriteDataSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btDeleteAllTagToFlashSendData--">btDeleteAllTagToFlashSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btGetAllTagNumFromFlashSendData--">btGetAllTagNumFromFlashSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btGetNewTagNumFromFlashSendData--">btGetNewTagNumFromFlashSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btGetTagDataFromFlashSendData--">btGetTagDataFromFlashSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#closeLedSendData--">closeLedSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#GBTagLockSendData-java.lang.String-char-int-int-java.lang.String-char-char-char-">GBTagLockSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getBatterySendData--">getBatterySendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getCWSendData--">getCWSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getEPCTIDModeSendData-char-char-">getEPCTIDModeSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getFastIDSendData--">getFastIDSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getFrequencyModeSendData--">getFrequencyModeSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getGen2SendData--">getGen2SendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getInventorySingleTagSendData--">getInventorySingleTagSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getKillSendData-java.lang.String-int-int-int-java.lang.String-">getKillSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getLockSendData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">getLockSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getPowerSendData--">getPowerSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getProtocolSendData--">getProtocolSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReaderAwaitSleepTimeSendData--">getReaderAwaitSleepTimeSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReadSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-">getReadSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReadTagSendData--">getReadTagSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getRFLinkSendData--">getRFLinkSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getScanBarcodeSendData--">getScanBarcodeSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getStartInventoryTagSendData--">getStartInventoryTagSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getSTM32VersionSendData--">getSTM32VersionSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getStopInventorySendData--">getStopInventorySendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getTagfocusSendData--">getTagfocusSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getTemperatureSendData--">getTemperatureSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getVersionSendData--">getVersionSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getWriteSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">getWriteSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#openLedSendData--">openLedSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBarcodeData-byte:A-">parseBarcodeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBatteryData-byte:A-">parseBatteryData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBlinkOfLedData-byte:A-">parseBlinkOfLedData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBlockEraseDataData-byte:A-">parseBlockEraseDataData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBlockWriteData-byte:A-">parseBlockWriteData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBtDeleteAllTagToFlashData-byte:A-">parseBtDeleteAllTagToFlashData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBtGetAllTagNumFromFlashData-byte:A-">parseBtGetAllTagNumFromFlashData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBtGetNewTagNumFromFlashData-byte:A-">parseBtGetNewTagNumFromFlashData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBtGetTagDataFromFlashData-byte:A-">parseBtGetTagDataFromFlashData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseCloseLedData-byte:A-">parseCloseLedData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseContinuousInventoryTagData-byte:A-">parseContinuousInventoryTagData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseFastIdData-byte:A-">parseFastIdData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGBTagLockData-byte:A-">parseGBTagLockData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetCWData-byte:A-">parseGetCWData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetEPCTIDModeData-byte:A-">parseGetEPCTIDModeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetFastIdData-byte:A-">parseGetFastIdData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetFrequencyModeData-byte:A-">parseGetFrequencyModeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetGen2Data-byte:A-">parseGetGen2Data</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetPowerData-byte:A-">parseGetPowerData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetProtocolData-byte:A-">parseGetProtocolData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetReaderAwaitSleepTimeData-byte:A-">parseGetReaderAwaitSleepTimeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetRFLinkData-byte:A-">parseGetRFLinkData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetTagfocusData-byte:A-">parseGetTagfocusData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseInventorySingleTagData-byte:A-">parseInventorySingleTagData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseKillData-byte:A-">parseKillData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseLockData-byte:A-">parseLockData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseOpenLedData-byte:A-">parseOpenLedData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseReadData-byte:A-">parseReadData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseReadTagData_EPC-byte:A-">parseReadTagData_EPC</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetBeepTimeOfDuration-byte:A-">parseSetBeepTimeOfDuration</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetCWData-byte:A-">parseSetCWData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetEPCAndTIDModeData-byte:A-">parseSetEPCAndTIDModeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetEPCAndTIDUserModeData-byte:A-">parseSetEPCAndTIDUserModeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetEPCModeData-byte:A-">parseSetEPCModeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetFilterData-byte:A-">parseSetFilterData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetFrequencyModeData-byte:A-">parseSetFrequencyModeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetGen2Data-byte:A-">parseSetGen2Data</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetJumpFrequencyData-byte:A-">parseSetJumpFrequencyData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetPowerData-byte:A-">parseSetPowerData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetPowerOnDynamicData-byte:A-">parseSetPowerOnDynamicData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetProtocolData-byte:A-">parseSetProtocolData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetR6WorkModeData-byte:A-">parseSetR6WorkModeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetReaderAwaitSleepTimeData-byte:A-">parseSetReaderAwaitSleepTimeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetRFLinkData-byte:A-">parseSetRFLinkData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetTagfocusData-byte:A-">parseSetTagfocusData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseStartInventoryTagData-byte:A-">parseStartInventoryTagData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSTM32VersionData-byte:A-">parseSTM32VersionData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseStopInventoryData-byte:A-">parseStopInventoryData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseTemperatureData-byte:A-">parseTemperatureData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseUHFJump2BootData-byte:A-">parseUHFJump2BootData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseUHFStartUpdateData-byte:A-">parseUHFStartUpdateData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseUHFStopUpdateData-byte:A-">parseUHFStopUpdateData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseUHFUpdatingData-byte:A-">parseUHFUpdatingData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseVersionData-byte:A-">parseVersionData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseWriteData-byte:A-">parseWriteData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#requestGetEx10SDKFirmware--">requestGetEx10SDKFirmware</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#requestGetFastInventoryMode--">requestGetFastInventoryMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#requestSetFastInventoryMode-boolean-">requestSetFastInventoryMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#responseGetEx10SDKFirmware-byte:A-">responseGetEx10SDKFirmware</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#responseGetFastInventoryMode-byte:A-">responseGetFastInventoryMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#responseSetFastInventoryMode-byte:A-">responseSetFastInventoryMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setBeepTimeOfDurationSendData-int-">setBeepTimeOfDurationSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setCWSendData-char-">setCWSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setEPCAndTIDModeSendData--">setEPCAndTIDModeSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setEPCAndTIDUserModeSendData-int-int-">setEPCAndTIDUserModeSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setEPCModeSendData--">setEPCModeSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setFastIdSendData-int-">setFastIdSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setFilterSendData-char-int-int-java.lang.String-">setFilterSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setFrequencyModeSendData-int-">setFrequencyModeSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setGen2SendData-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">setGen2SendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setJumpFrequencySendData-int-">setJumpFrequencySendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setPowerOnDynamicSendData-int-">setPowerOnDynamicSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setPowerSendData-int-">setPowerSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setProtocolSendData-int-">setProtocolSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setR6WorkmodeSendData-char-">setR6WorkmodeSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setReaderAwaitSleepTimeSendData-char-">setReaderAwaitSleepTimeSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setRFLinkSendData-int-">setRFLinkSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setTagfocusSendData-char-">setTagfocusSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#uhfJump2BootSendData-char-">uhfJump2BootSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#uhfStartUpdateSendData--">uhfStartUpdateSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#UHFStopUpdateSendData--">UHFStopUpdateSendData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#uhfUpdatingSendData-byte:A-">uhfUpdatingSendData</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setReaderIpSendData-com.rscja.deviceapi.entity.ReaderIPEntity-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReaderIpSendData</h4>
<pre>byte[]&nbsp;setReaderIpSendData(<a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;uhfconfig)</pre>
</li>
</ul>
<a name="parseSetReaderIpData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetReaderIpData</h4>
<pre>boolean&nbsp;parseSetReaderIpData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getReaderIpSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReaderIpSendData</h4>
<pre>byte[]&nbsp;getReaderIpSendData()</pre>
</li>
</ul>
<a name="parseGetReaderIpData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetReaderIpData</h4>
<pre><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;parseGetReaderIpData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setReaderDestIpSendData-com.rscja.deviceapi.entity.ReaderIPEntity-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReaderDestIpSendData</h4>
<pre>byte[]&nbsp;setReaderDestIpSendData(<a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;uhfconfig)</pre>
</li>
</ul>
<a name="parseSetReaderDestIpData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetReaderDestIpData</h4>
<pre>boolean&nbsp;parseSetReaderDestIpData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getReaderDestIpSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReaderDestIpSendData</h4>
<pre>byte[]&nbsp;getReaderDestIpSendData()</pre>
</li>
</ul>
<a name="parseGetReaderDestIpData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetReaderDestIpData</h4>
<pre><a href="../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;parseGetReaderDestIpData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getReaderBeepStatusSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReaderBeepStatusSendData</h4>
<pre>byte[]&nbsp;getReaderBeepStatusSendData()</pre>
</li>
</ul>
<a name="parseGetReaderBeepStatusData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetReaderBeepStatusData</h4>
<pre>int&nbsp;parseGetReaderBeepStatusData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getBeepSendData-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBeepSendData</h4>
<pre>byte[]&nbsp;getBeepSendData(boolean&nbsp;isOpen)</pre>
<div class="block">获取设置蜂鸣器的发送数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getBeepSendData-boolean-">getBeepSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>isOpen</code> - true:表示打开蜂鸣器， false:表示关闭蜂鸣器</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseBeepData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBeepData</h4>
<pre>boolean&nbsp;parseBeepData(byte[]&nbsp;inData)</pre>
<div class="block">解析设置蜂鸣器返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBeepData-byte:A-">parseBeepData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 设置蜂鸣器返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true: 设置成功  ,flase:设置设备</dd>
</dl>
</li>
</ul>
<a name="getSetSoftResetSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSetSoftResetSendData</h4>
<pre>byte[]&nbsp;getSetSoftResetSendData()</pre>
</li>
</ul>
<a name="parseSetSoftResetData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetSoftResetData</h4>
<pre>boolean&nbsp;parseSetSoftResetData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setAntSendData-char-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAntSendData</h4>
<pre>byte[]&nbsp;setAntSendData(char&nbsp;saveflag,
                      byte[]&nbsp;antStatus)</pre>
</li>
</ul>
<a name="parseSetAntData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetAntData</h4>
<pre>boolean&nbsp;parseSetAntData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getAntSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAntSendData</h4>
<pre>byte[]&nbsp;getAntSendData()</pre>
</li>
</ul>
<a name="parseGetAntData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetAntData</h4>
<pre>byte[]&nbsp;parseGetAntData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setAntWorkTimeSendData-byte-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAntWorkTimeSendData</h4>
<pre>byte[]&nbsp;setAntWorkTimeSendData(byte&nbsp;antnum,
                              int&nbsp;WorkTime)</pre>
</li>
</ul>
<a name="parseSetAntWorkTimeReceiveData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetAntWorkTimeReceiveData</h4>
<pre>boolean&nbsp;parseSetAntWorkTimeReceiveData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getAntWorkTimeSendData-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAntWorkTimeSendData</h4>
<pre>byte[]&nbsp;getAntWorkTimeSendData(byte&nbsp;antnum)</pre>
</li>
</ul>
<a name="parseGetAntWorkTimeReceiveData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetAntWorkTimeReceiveData</h4>
<pre>int&nbsp;parseGetAntWorkTimeReceiveData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getWorkModeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkModeSendData</h4>
<pre>byte[]&nbsp;getWorkModeSendData()</pre>
</li>
</ul>
<a name="parseGetWorkModeReceiveData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetWorkModeReceiveData</h4>
<pre>int&nbsp;parseGetWorkModeReceiveData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setWorkModeSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkModeSendData</h4>
<pre>byte[]&nbsp;setWorkModeSendData(int&nbsp;mode)</pre>
</li>
</ul>
<a name="parseSetWorkModeReceiveData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetWorkModeReceiveData</h4>
<pre>boolean&nbsp;parseSetWorkModeReceiveData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setTriggerWorkModeParaSendData-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTriggerWorkModeParaSendData</h4>
<pre>byte[]&nbsp;setTriggerWorkModeParaSendData(int&nbsp;input,
                                      int&nbsp;workTime,
                                      int&nbsp;waitTime,
                                      int&nbsp;receiveMode)</pre>
</li>
</ul>
<a name="parseSetTriggerWorkModeParaReceiveData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetTriggerWorkModeParaReceiveData</h4>
<pre>boolean&nbsp;parseSetTriggerWorkModeParaReceiveData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getTriggerWorkModeParaSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTriggerWorkModeParaSendData</h4>
<pre>byte[]&nbsp;getTriggerWorkModeParaSendData()</pre>
</li>
</ul>
<a name="parseGetTriggerWorkModeParaReceiveData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetTriggerWorkModeParaReceiveData</h4>
<pre>int[]&nbsp;parseGetTriggerWorkModeParaReceiveData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getIOControlSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIOControlSendData</h4>
<pre>byte[]&nbsp;getIOControlSendData()</pre>
</li>
</ul>
<a name="parseGetIOControlReceiveData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetIOControlReceiveData</h4>
<pre>byte[]&nbsp;parseGetIOControlReceiveData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setIOControlSendData-byte-byte-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIOControlSendData</h4>
<pre>byte[]&nbsp;setIOControlSendData(byte&nbsp;output1,
                            byte&nbsp;output2,
                            byte&nbsp;relayStatus)</pre>
</li>
</ul>
<a name="parseSetIOControlReceiveData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetIOControlReceiveData</h4>
<pre>boolean&nbsp;parseSetIOControlReceiveData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getAntennaConnectStateSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAntennaConnectStateSendData</h4>
<pre>byte[]&nbsp;getAntennaConnectStateSendData()</pre>
</li>
</ul>
<a name="parseGetAntennaConnectStateReceiveData-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>parseGetAntennaConnectStateReceiveData</h4>
<pre>java.util.List&lt;<a href="../../../../com/rscja/deviceapi/entity/AntennaConnectState.html" title="class in com.rscja.deviceapi.entity">AntennaConnectState</a>&gt;&nbsp;parseGetAntennaConnectStateReceiveData(byte[]&nbsp;inData)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFProtocolParseUrx.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" target="_top">Frames</a></li>
<li><a href="IUHFProtocolParseUrx.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
