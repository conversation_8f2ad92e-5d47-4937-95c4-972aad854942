<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IUhfReader</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IUhfReader";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":38,"i8":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUhfReader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IUhfReader.html" target="_top">Frames</a></li>
<li><a href="IUhfReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IUhfReader" class="title">Interface IUhfReader</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUSB</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a>, <a href="../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html" title="class in com.rscja.team.qcom.ble">RFIDWithUHFBLEN51_qcom</a>, <a href="../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html" title="class in com.rscja.team.qcom.ble">RFIDWithUHFBLEN52_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTag.html" title="class in com.rscja.custom">UHFTemperatureTag</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN51.html" title="class in com.rscja.custom">UHFTemperatureTag.RFIDWithUHFBLEN51</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN52.html" title="class in com.rscja.custom">UHFTemperatureTag.RFIDWithUHFBLEN52</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFTemperatureTagsBLEAPI.html" title="class in com.rscja.team.qcom.custom">UHFTemperatureTagsBLEAPI</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IUhfReader</span>
extends <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></pre>
<div class="block">R2、R5、R6  蓝牙和USB读写器包含的uhf特有内容</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Administrator</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IReader">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></h3>
<code><a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#UPDATE_STM32">UPDATE_STM32</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#deleteAllTagToFlash--">deleteAllTagToFlash</a></span>()</code>
<div class="block">删除R2、R6缓存的标签<br>
 Delete tag in buffer of R2 and R6</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#getAllTagTotalFromFlash--">getAllTagTotalFromFlash</a></span>()</code>
<div class="block">获取R2、R6 缓存的标签数量<br>
 Acquire tag amounts in buffer of R2 and R6.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#getFastID--">getFastID</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#getNewTagTotalFromFlash--">getNewTagTotalFromFlash</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#getReaderAwaitSleepTime--">getReaderAwaitSleepTime</a></span>()</code>
<div class="block">获取读写器等待休眠的时间</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#getTagDataFromFlash--">getTagDataFromFlash</a></span>()</code>
<div class="block">获取R2、R6缓存的标签信息<br>
 Acquire tag information in buffer of R2 and R6</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#getTagfocus--">getTagfocus</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#readTagFromBufferList--">readTagFromBufferList</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#setReaderAwaitSleepTime-int-">setReaderAwaitSleepTime</a></span>(int&nbsp;time)</code>
<div class="block">设置读写器等待休眠的时间。在读写器断开连接的状态下，即将进入休眠的时间。</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IReader">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></h3>
<code><a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#blinkOfLed-int-int-int-">blinkOfLed</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#closeLed--">closeLed</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#getBattery--">getBattery</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#getParameter-byte:A-">getParameter</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#getSTM32Version--">getSTM32Version</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#openLed--">openLed</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#scanBarcode--">scanBarcode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#scanBarcodeToBytes--">scanBarcodeToBytes</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#setBarcodeTypeInSSIID-boolean-">setBarcodeTypeInSSIID</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#setBeep-boolean-">setBeep</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#setKeyEventCallback-com.rscja.deviceapi.interfaces.KeyEventCallback-">setKeyEventCallback</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#setParameter-byte:A-byte:A-">setParameter</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#startScanBarcode--">startScanBarcode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IReader.html#triggerBeep-int-">triggerBeep</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="readTagFromBufferList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagFromBufferList</h4>
<pre>@Deprecated
java.util.List&lt;<a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;&nbsp;readTagFromBufferList()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">Deprecated. Use <code>#setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code>  instead .</div>
</li>
</ul>
<a name="getAllTagTotalFromFlash--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllTagTotalFromFlash</h4>
<pre>int&nbsp;getAllTagTotalFromFlash()</pre>
<div class="block">获取R2、R6 缓存的标签数量<br>
 Acquire tag amounts in buffer of R2 and R6.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回缓存标签的数量(return tag amounts in buffer.)</dd>
</dl>
</li>
</ul>
<a name="deleteAllTagToFlash--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteAllTagToFlash</h4>
<pre>boolean&nbsp;deleteAllTagToFlash()</pre>
<div class="block">删除R2、R6缓存的标签<br>
 Delete tag in buffer of R2 and R6</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="getTagDataFromFlash--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTagDataFromFlash</h4>
<pre>java.util.List&lt;<a href="../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;&nbsp;getTagDataFromFlash()</pre>
<div class="block">获取R2、R6缓存的标签信息<br>
 Acquire tag information in buffer of R2 and R6</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回缓存的标签列表(Return tag list of buffer)</dd>
</dl>
</li>
</ul>
<a name="setReaderAwaitSleepTime-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReaderAwaitSleepTime</h4>
<pre>boolean&nbsp;setReaderAwaitSleepTime(int&nbsp;time)</pre>
<div class="block">设置读写器等待休眠的时间。在读写器断开连接的状态下，即将进入休眠的时间。</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>time</code> - (1-254)等待休眠时间单位:分钟，默认5分钟</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功  false:失败</dd>
</dl>
</li>
</ul>
<a name="getReaderAwaitSleepTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReaderAwaitSleepTime</h4>
<pre>int&nbsp;getReaderAwaitSleepTime()</pre>
<div class="block">获取读写器等待休眠的时间</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回等待休眠的时间, 返回-1表示获取失败</dd>
</dl>
</li>
</ul>
<a name="getNewTagTotalFromFlash--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewTagTotalFromFlash</h4>
<pre>int&nbsp;getNewTagTotalFromFlash()</pre>
</li>
</ul>
<a name="getFastID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFastID</h4>
<pre>int&nbsp;getFastID()</pre>
</li>
</ul>
<a name="getTagfocus--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTagfocus</h4>
<pre>int&nbsp;getTagfocus()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUhfReader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IUhfReader.html" target="_top">Frames</a></li>
<li><a href="IUhfReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
