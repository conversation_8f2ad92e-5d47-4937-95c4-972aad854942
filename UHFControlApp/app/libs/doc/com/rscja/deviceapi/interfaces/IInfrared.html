<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IInfrared</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IInfrared";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IInfrared.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/ILedLight.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IInfrared.html" target="_top">Frames</a></li>
<li><a href="IInfrared.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IInfrared" class="title">Interface IInfrared</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/deviceapi/Infrared.html" title="class in com.rscja.deviceapi">Infrared</a>, <a href="../../../../com/rscja/team/mtk/deviceapi/Infrared_mtk.html" title="class in com.rscja.team.mtk.deviceapi">Infrared_mtk</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IInfrared</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html#close--">close</a></span>()</code>
<div class="block">关闭红外模块<br>
 Switch off infared module<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html#closeLED--">closeLED</a></span>()</code>
<div class="block">关闭辅助灯<br>
 Close LED</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html#getIDAndPowerWithWattmeter--">getIDAndPowerWithWattmeter</a></span>()</code>
<div class="block">获取电表ID和电能值,97标准<br>
 Acquire powermeter ID and energy value, 97 standard<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html#getIDAndPowerWithWattmeter-int-">getIDAndPowerWithWattmeter</a></span>(int&nbsp;protocol)</code>
<div class="block">获取电表ID和电能值<br>
 Acquire powermeter ID and energy value<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html#isPowerOn--">isPowerOn</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html#open--">open</a></span>()</code>
<div class="block">打开红外模块,默认1200波特率<br>
 Switch on infared module, default 1200 baud rate<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html#open-int-">open</a></span>(int&nbsp;baudrate)</code>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html#open-int-int-">open</a></span>(int&nbsp;baudrate,
    int&nbsp;check)</code>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html#open-int-int-int-int-">open</a></span>(int&nbsp;baudrate,
    int&nbsp;dataBits,
    int&nbsp;stopBits,
    int&nbsp;check)</code>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html#openLED--">openLED</a></span>()</code>
<div class="block">打开辅助灯<br>
 Open LED</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html#receive--">receive</a></span>()</code>
<div class="block">接收数据<br>
 Receive data<br></div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html#send-byte:A-">send</a></span>(byte[]&nbsp;data)</code>
<div class="block">发送数据<br>
 send Data</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html#switchBaudrate-int-int-int-int-">switchBaudrate</a></span>(int&nbsp;baudrate,
              int&nbsp;dataBits,
              int&nbsp;stopBits,
              int&nbsp;check)</code>
<div class="block">切换波特率</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="open--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>boolean&nbsp;open()</pre>
<div class="block">打开红外模块,默认1200波特率<br>
 Switch on infared module, default 1200 baud rate<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="open-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>boolean&nbsp;open(int&nbsp;baudrate)</pre>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baudrate</code> - 波特率，如：1200、4800、2400<br>
                 baud rate, e.g.: 1200, 4800, 2400<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="open-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>boolean&nbsp;open(int&nbsp;baudrate,
             int&nbsp;check)</pre>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baudrate</code> - 波特率，如：1200、4800、2400<br>
                 baud rate, e.g.: 1200, 4800, 2400<br></dd>
<dd><code>check</code> - 0,不校验;1,奇校验;2,偶校验<br>
                 0, no verification;1, odd verification, even verification<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="open-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>boolean&nbsp;open(int&nbsp;baudrate,
             int&nbsp;dataBits,
             int&nbsp;stopBits,
             int&nbsp;check)</pre>
<div class="block">打开红外模块<br>
 Switch on infared module<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baudrate</code> - 波特率，如：1200、4800、2400<br>
                 baud rate, e.g.: 1200, 4800, 2400<br></dd>
<dd><code>dataBits</code> - 数据位        取值为  7 或者8</dd>
<dd><code>stopBits</code> - 停止位        取值为  1 或者2</dd>
<dd><code>check</code> - 校验位    0,不校验;1,奇校验;2,偶校验<br>
                 0, no verification;1, odd verification, even verification<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="switchBaudrate-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>switchBaudrate</h4>
<pre>boolean&nbsp;switchBaudrate(int&nbsp;baudrate,
                       int&nbsp;dataBits,
                       int&nbsp;stopBits,
                       int&nbsp;check)</pre>
<div class="block">切换波特率</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baudrate</code> - 波特率</dd>
<dd><code>dataBits</code> - 数据位</dd>
<dd><code>stopBits</code> - 停止位</dd>
<dd><code>check</code> - 校验位</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>boolean&nbsp;close()</pre>
<div class="block">关闭红外模块<br>
 Switch off infared module<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="send-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>send</h4>
<pre>boolean&nbsp;send(byte[]&nbsp;data)</pre>
<div class="block">发送数据<br>
 send Data</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - 数据<br>
             Data<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="receive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>receive</h4>
<pre>byte[]&nbsp;receive()</pre>
<div class="block">接收数据<br>
 Receive data<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>数据<br>
 data<br></dd>
</dl>
</li>
</ul>
<a name="getIDAndPowerWithWattmeter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIDAndPowerWithWattmeter</h4>
<pre>java.lang.String&nbsp;getIDAndPowerWithWattmeter()</pre>
<div class="block">获取电表ID和电能值,97标准<br>
 Acquire powermeter ID and energy value, 97 standard<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>电表ID和电能值以“:”分割，返回null表示失败<br>
 powermeter ID and energy value should be divided by ':', return null means failure<br></dd>
</dl>
</li>
</ul>
<a name="getIDAndPowerWithWattmeter-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIDAndPowerWithWattmeter</h4>
<pre>java.lang.String&nbsp;getIDAndPowerWithWattmeter(int&nbsp;protocol)</pre>
<div class="block">获取电表ID和电能值<br>
 Acquire powermeter ID and energy value<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>protocol</code> - 97,DL/T 645—1997标准;07,DL/T 645—2007标准<br></dd>
<dd><code>protocol</code> - 97, DL/T 645-1997 standard;07,DL/T 645-2007 standard<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="openLED--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openLED</h4>
<pre>void&nbsp;openLED()</pre>
<div class="block">打开辅助灯<br>
 Open LED</div>
</li>
</ul>
<a name="closeLED--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeLED</h4>
<pre>void&nbsp;closeLED()</pre>
<div class="block">关闭辅助灯<br>
 Close LED</div>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>boolean&nbsp;isPowerOn()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IInfrared.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/ILedLight.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IInfrared.html" target="_top">Frames</a></li>
<li><a href="IInfrared.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
