<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Package com.rscja.deviceapi.interfaces</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package com.rscja.deviceapi.interfaces";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package com.rscja.deviceapi.interfaces" class="title">Uses of Package<br>com.rscja.deviceapi.interfaces</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.barcode">com.rscja.barcode</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.custom">com.rscja.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.custom.interfaces">com.rscja.custom.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.interfaces">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.barcode">com.rscja.team.mtk.barcode</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.custom">com.rscja.team.mtk.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.deviceapi">com.rscja.team.mtk.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.utility">com.rscja.team.mtk.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.barcode">com.rscja.team.qcom.barcode</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.ble">com.rscja.team.qcom.ble</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.custom">com.rscja.team.qcom.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.r1.hf">com.rscja.team.qcom.r1.hf</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.rs232utils">com.rscja.team.qcom.rs232utils</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.service">com.rscja.team.qcom.service</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.socket">com.rscja.team.qcom.socket</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.uhfhandler">com.rscja.team.qcom.uhfhandler</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.uhfparse">com.rscja.team.qcom.uhfparse</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.usb">com.rscja.team.qcom.usb</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.usb.pl2302">com.rscja.team.qcom.usb.pl2302</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.barcode">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/barcode/package-summary.html">com.rscja.barcode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBarcodeUtility.html#com.rscja.barcode">IBarcodeUtility</a>
<div class="block">条码操作类
 barcode operate class



 注意：
 Attention:
 1、使用前请确认您的机器已安装键盘助手v1.9.0 之后的版本。
 1.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/custom/package-summary.html">com.rscja.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatusCallback.html#com.rscja.custom">ConnectionStatusCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBluetoothReader.html#com.rscja.custom">IBluetoothReader</a>
<div class="block">蓝牙读写器的接口<br>
 Interface of Bluetooth reader.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHandheldRFID.html#com.rscja.custom">IHandheldRFID</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IMultipleAntenna.html#com.rscja.custom">IMultipleAntenna</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IReader.html#com.rscja.custom">IReader</a>
<div class="block">R2、R5、R6  蓝牙和USB读写器
 有些客户只需要我们R6的背夹，不需要uhf模块，所以将R6背夹接口独立出来</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA4.html#com.rscja.custom">IRFIDWithUHFA4</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA4NetWork.html#com.rscja.custom">IRFIDWithUHFA4NetWork</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFUrxNetwork.html#com.rscja.custom">IRFIDWithUHFUrxNetwork</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFUrxUart.html#com.rscja.custom">IRFIDWithUHFUrxUart</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ISingleAntenna.html#com.rscja.custom">ISingleAntenna</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHF.html#com.rscja.custom">IUHF</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFA4.html#com.rscja.custom">IUHFA4</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUhfBle.html#com.rscja.custom">IUhfBle</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFInventoryCallback.html#com.rscja.custom">IUHFInventoryCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFOfAndroidUart.html#com.rscja.custom">IUHFOfAndroidUart</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUhfReader.html#com.rscja.custom">IUhfReader</a>
<div class="block">R2、R5、R6  蓝牙和USB读写器包含的uhf特有内容</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFURAxExtend.html#com.rscja.custom">IUHFURAxExtend</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFURx.html#com.rscja.custom">IUHFURx</a>
<div class="block">UHF UR4特有接口</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.custom.interfaces">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/custom/interfaces/package-summary.html">com.rscja.custom.interfaces</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHF.html#com.rscja.custom.interfaces">IUHF</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFOfAndroidUart.html#com.rscja.custom.interfaces">IUHFOfAndroidUart</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatus.html#com.rscja.deviceapi">ConnectionStatus</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatusCallback.html#com.rscja.deviceapi">ConnectionStatusCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBarcode1D.html#com.rscja.deviceapi">IBarcode1D</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBarcode2D.html#com.rscja.deviceapi">IBarcode2D</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBleDevice.html#com.rscja.deviceapi">IBleDevice</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBluetoothReader.html#com.rscja.deviceapi">IBluetoothReader</a>
<div class="block">蓝牙读写器的接口<br>
 Interface of Bluetooth reader.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ICardWithBYL.html#com.rscja.deviceapi">ICardWithBYL</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprint.html#com.rscja.deviceapi">IFingerprint</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprintSM206B.html#com.rscja.deviceapi">IFingerprintSM206B</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprintWithFIPS.html#com.rscja.deviceapi">IFingerprintWithFIPS</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprintWithMorpho.html#com.rscja.deviceapi">IFingerprintWithMorpho</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprintWithTLK1NC.html#com.rscja.deviceapi">IFingerprintWithTLK1NC</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprintWithZAZ.html#com.rscja.deviceapi">IFingerprintWithZAZ</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IGPIStateCallback.html#com.rscja.deviceapi">IGPIStateCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHandheldRFID.html#com.rscja.deviceapi">IHandheldRFID</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHF14443A.html#com.rscja.deviceapi">IHF14443A</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHF14443B.html#com.rscja.deviceapi">IHF14443B</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHF15693.html#com.rscja.deviceapi">IHF15693</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IInfrared.html#com.rscja.deviceapi">IInfrared</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ILedLight.html#com.rscja.deviceapi">ILedLight</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IModule.html#com.rscja.deviceapi">IModule</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IMultipleAntenna.html#com.rscja.deviceapi">IMultipleAntenna</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IPrinter.html#com.rscja.deviceapi">IPrinter</a>
<div class="block">打印机操作类<br>
 Printer operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IPSAM.html#com.rscja.deviceapi">IPSAM</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IReader.html#com.rscja.deviceapi">IReader</a>
<div class="block">R2、R5、R6  蓝牙和USB读写器
 有些客户只需要我们R6的背夹，不需要uhf模块，所以将R6背夹接口独立出来</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDBase.html#com.rscja.deviceapi">IRFIDBase</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithISO14443A.html#com.rscja.deviceapi">IRFIDWithISO14443A</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithISO14443A4CPU.html#com.rscja.deviceapi">IRFIDWithISO14443A4CPU</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithISO14443B.html#com.rscja.deviceapi">IRFIDWithISO14443B</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithISO15693.html#com.rscja.deviceapi">IRFIDWithISO15693</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithLF.html#com.rscja.deviceapi">IRFIDWithLF</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA4.html#com.rscja.deviceapi">IRFIDWithUHFA4</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA4NetWork.html#com.rscja.deviceapi">IRFIDWithUHFA4NetWork</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA4RS232.html#com.rscja.deviceapi">IRFIDWithUHFA4RS232</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA8.html#com.rscja.deviceapi">IRFIDWithUHFA8</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA8NetWork.html#com.rscja.deviceapi">IRFIDWithUHFA8NetWork</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA8RS232.html#com.rscja.deviceapi">IRFIDWithUHFA8RS232</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFRLM.html#com.rscja.deviceapi">IRFIDWithUHFRLM</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFUrxNetwork.html#com.rscja.deviceapi">IRFIDWithUHFUrxNetwork</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFUrxUart.html#com.rscja.deviceapi">IRFIDWithUHFUrxUart</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFUrxUsbToUart.html#com.rscja.deviceapi">IRFIDWithUHFUrxUsbToUart</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFUSB.html#com.rscja.deviceapi">IRFIDWithUHFUSB</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IScanerLedLight.html#com.rscja.deviceapi">IScanerLedLight</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ISingleAntenna.html#com.rscja.deviceapi">ISingleAntenna</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ITagLocate.html#com.rscja.deviceapi">ITagLocate</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHF.html#com.rscja.deviceapi">IUHF</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFA4.html#com.rscja.deviceapi">IUHFA4</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFA8.html#com.rscja.deviceapi">IUHFA8</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUhfBle.html#com.rscja.deviceapi">IUhfBle</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFInventoryCallback.html#com.rscja.deviceapi">IUHFInventoryCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFLocationCallback.html#com.rscja.deviceapi">IUHFLocationCallback</a>
<div class="block">UHF 定位回调接口 （UHF location callback interface）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFOfAndroidUart.html#com.rscja.deviceapi">IUHFOfAndroidUart</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFRadarLocationCallback.html#com.rscja.deviceapi">IUHFRadarLocationCallback</a>
<div class="block">回调定位过程产生的数据 (Callback of data generated by the positioning process)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUhfReader.html#com.rscja.deviceapi">IUhfReader</a>
<div class="block">R2、R5、R6  蓝牙和USB读写器包含的uhf特有内容</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFURAxExtend.html#com.rscja.deviceapi">IUHFURAxExtend</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFURx.html#com.rscja.deviceapi">IUHFURx</a>
<div class="block">UHF UR4特有接口</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUpgradeProgress.html#com.rscja.deviceapi">IUpgradeProgress</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IURAxOfAndroidUart.html#com.rscja.deviceapi">IURAxOfAndroidUart</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUsbFingerprint.html#com.rscja.deviceapi">IUsbFingerprint</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/KeyEventCallback.html#com.rscja.deviceapi">KeyEventCallback</a>
<div class="block">接收扫描按键的回调
 Receive call back of SCAN Key</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/OnLowBatteryListener.html#com.rscja.deviceapi">OnLowBatteryListener</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ScanBTCallback.html#com.rscja.deviceapi">ScanBTCallback</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.interfaces">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatus.html#com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatusCallback.html#com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBarcodePictureCallback.html#com.rscja.deviceapi.interfaces">IBarcodePictureCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBarcodeVideoCallback.html#com.rscja.deviceapi.interfaces">IBarcodeVideoCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBluetoothReader.html#com.rscja.deviceapi.interfaces">IBluetoothReader</a>
<div class="block">蓝牙读写器的接口<br>
 Interface of Bluetooth reader.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IGPIStateCallback.html#com.rscja.deviceapi.interfaces">IGPIStateCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHandheldRFID.html#com.rscja.deviceapi.interfaces">IHandheldRFID</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IMultipleAntenna.html#com.rscja.deviceapi.interfaces">IMultipleAntenna</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IReader.html#com.rscja.deviceapi.interfaces">IReader</a>
<div class="block">R2、R5、R6  蓝牙和USB读写器
 有些客户只需要我们R6的背夹，不需要uhf模块，所以将R6背夹接口独立出来</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDBase.html#com.rscja.deviceapi.interfaces">IRFIDBase</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA4.html#com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA8.html#com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ISingleAntenna.html#com.rscja.deviceapi.interfaces">ISingleAntenna</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ITagLocate.html#com.rscja.deviceapi.interfaces">ITagLocate</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ITagLocationCallback.html#com.rscja.deviceapi.interfaces">ITagLocationCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHF.html#com.rscja.deviceapi.interfaces">IUHF</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFA4.html#com.rscja.deviceapi.interfaces">IUHFA4</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFA8.html#com.rscja.deviceapi.interfaces">IUHFA8</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFInventoryCallback.html#com.rscja.deviceapi.interfaces">IUHFInventoryCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFLocationCallback.html#com.rscja.deviceapi.interfaces">IUHFLocationCallback</a>
<div class="block">UHF 定位回调接口 （UHF location callback interface）</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFOfAndroidUart.html#com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFProtocolParse.html#com.rscja.deviceapi.interfaces">IUHFProtocolParse</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFRadarLocationCallback.html#com.rscja.deviceapi.interfaces">IUHFRadarLocationCallback</a>
<div class="block">回调定位过程产生的数据 (Callback of data generated by the positioning process)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUhfReader.html#com.rscja.deviceapi.interfaces">IUhfReader</a>
<div class="block">R2、R5、R6  蓝牙和USB读写器包含的uhf特有内容</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFURAxExtend.html#com.rscja.deviceapi.interfaces">IUHFURAxExtend</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFURx.html#com.rscja.deviceapi.interfaces">IUHFURx</a>
<div class="block">UHF UR4特有接口</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUpgradeProgress.html#com.rscja.deviceapi.interfaces">IUpgradeProgress</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IURAxOfAndroidUart.html#com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/KeyEventCallback.html#com.rscja.deviceapi.interfaces">KeyEventCallback</a>
<div class="block">接收扫描按键的回调
 Receive call back of SCAN Key</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/OnLowBatteryListener.html#com.rscja.deviceapi.interfaces">OnLowBatteryListener</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ScanBTCallback.html#com.rscja.deviceapi.interfaces">ScanBTCallback</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.barcode">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/mtk/barcode/package-summary.html">com.rscja.team.mtk.barcode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBarcodeSymbolUtility.html#com.rscja.team.mtk.barcode">IBarcodeSymbolUtility</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBarcodeUtility.html#com.rscja.team.mtk.barcode">IBarcodeUtility</a>
<div class="block">条码操作类
 barcode operate class



 注意：
 Attention:
 1、使用前请确认您的机器已安装键盘助手v1.9.0 之后的版本。
 1.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/mtk/custom/package-summary.html">com.rscja.team.mtk.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHandheldRFID.html#com.rscja.team.mtk.custom">IHandheldRFID</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ISingleAntenna.html#com.rscja.team.mtk.custom">ISingleAntenna</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHF.html#com.rscja.team.mtk.custom">IUHF</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFOfAndroidUart.html#com.rscja.team.mtk.custom">IUHFOfAndroidUart</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatus.html#com.rscja.team.mtk.deviceapi">ConnectionStatus</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatusCallback.html#com.rscja.team.mtk.deviceapi">ConnectionStatusCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBarcode1D.html#com.rscja.team.mtk.deviceapi">IBarcode1D</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBarcode2D.html#com.rscja.team.mtk.deviceapi">IBarcode2D</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprintWithFIPS.html#com.rscja.team.mtk.deviceapi">IFingerprintWithFIPS</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprintWithMorpho.html#com.rscja.team.mtk.deviceapi">IFingerprintWithMorpho</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprintWithTLK1NC.html#com.rscja.team.mtk.deviceapi">IFingerprintWithTLK1NC</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHandheldRFID.html#com.rscja.team.mtk.deviceapi">IHandheldRFID</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IInfrared.html#com.rscja.team.mtk.deviceapi">IInfrared</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ILedLight.html#com.rscja.team.mtk.deviceapi">ILedLight</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IModule.html#com.rscja.team.mtk.deviceapi">IModule</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IPrinter.html#com.rscja.team.mtk.deviceapi">IPrinter</a>
<div class="block">打印机操作类<br>
 Printer operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IPSAM.html#com.rscja.team.mtk.deviceapi">IPSAM</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDBase.html#com.rscja.team.mtk.deviceapi">IRFIDBase</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithISO14443A.html#com.rscja.team.mtk.deviceapi">IRFIDWithISO14443A</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithISO14443A4CPU.html#com.rscja.team.mtk.deviceapi">IRFIDWithISO14443A4CPU</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithISO14443B.html#com.rscja.team.mtk.deviceapi">IRFIDWithISO14443B</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithISO15693.html#com.rscja.team.mtk.deviceapi">IRFIDWithISO15693</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IScanerLedLight.html#com.rscja.team.mtk.deviceapi">IScanerLedLight</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ISingleAntenna.html#com.rscja.team.mtk.deviceapi">ISingleAntenna</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ITagLocate.html#com.rscja.team.mtk.deviceapi">ITagLocate</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHF.html#com.rscja.team.mtk.deviceapi">IUHF</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFInventoryCallback.html#com.rscja.team.mtk.deviceapi">IUHFInventoryCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFLocationCallback.html#com.rscja.team.mtk.deviceapi">IUHFLocationCallback</a>
<div class="block">UHF 定位回调接口 （UHF location callback interface）</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFOfAndroidUart.html#com.rscja.team.mtk.deviceapi">IUHFOfAndroidUart</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFRadarLocationCallback.html#com.rscja.team.mtk.deviceapi">IUHFRadarLocationCallback</a>
<div class="block">回调定位过程产生的数据 (Callback of data generated by the positioning process)</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUsbFingerprint.html#com.rscja.team.mtk.deviceapi">IUsbFingerprint</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/OnLowBatteryListener.html#com.rscja.team.mtk.deviceapi">OnLowBatteryListener</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.utility">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/mtk/utility/package-summary.html">com.rscja.team.mtk.utility</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHF.html#com.rscja.team.mtk.utility">IUHF</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/OnLowBatteryListener.html#com.rscja.team.mtk.utility">OnLowBatteryListener</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.barcode">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/qcom/barcode/package-summary.html">com.rscja.team.qcom.barcode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBarcodeSymbolUtility.html#com.rscja.team.qcom.barcode">IBarcodeSymbolUtility</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBarcodeUtility.html#com.rscja.team.qcom.barcode">IBarcodeUtility</a>
<div class="block">条码操作类
 barcode operate class



 注意：
 Attention:
 1、使用前请确认您的机器已安装键盘助手v1.9.0 之后的版本。
 1.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.ble">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/qcom/ble/package-summary.html">com.rscja.team.qcom.ble</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatus.html#com.rscja.team.qcom.ble">ConnectionStatus</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatusCallback.html#com.rscja.team.qcom.ble">ConnectionStatusCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBluetoothData.html#com.rscja.team.qcom.ble">IBluetoothData</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBluetoothReader.html#com.rscja.team.qcom.ble">IBluetoothReader</a>
<div class="block">蓝牙读写器的接口<br>
 Interface of Bluetooth reader.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHandheldRFID.html#com.rscja.team.qcom.ble">IHandheldRFID</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IReader.html#com.rscja.team.qcom.ble">IReader</a>
<div class="block">R2、R5、R6  蓝牙和USB读写器
 有些客户只需要我们R6的背夹，不需要uhf模块，所以将R6背夹接口独立出来</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ISingleAntenna.html#com.rscja.team.qcom.ble">ISingleAntenna</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ITagLocate.html#com.rscja.team.qcom.ble">ITagLocate</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHF.html#com.rscja.team.qcom.ble">IUHF</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUhfBle.html#com.rscja.team.qcom.ble">IUhfBle</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFInventoryCallback.html#com.rscja.team.qcom.ble">IUHFInventoryCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFLocationCallback.html#com.rscja.team.qcom.ble">IUHFLocationCallback</a>
<div class="block">UHF 定位回调接口 （UHF location callback interface）</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFRadarLocationCallback.html#com.rscja.team.qcom.ble">IUHFRadarLocationCallback</a>
<div class="block">回调定位过程产生的数据 (Callback of data generated by the positioning process)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUhfReader.html#com.rscja.team.qcom.ble">IUhfReader</a>
<div class="block">R2、R5、R6  蓝牙和USB读写器包含的uhf特有内容</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/KeyEventCallback.html#com.rscja.team.qcom.ble">KeyEventCallback</a>
<div class="block">接收扫描按键的回调
 Receive call back of SCAN Key</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ScanBTCallback.html#com.rscja.team.qcom.ble">ScanBTCallback</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBluetoothReader.html#com.rscja.team.qcom.custom">IBluetoothReader</a>
<div class="block">蓝牙读写器的接口<br>
 Interface of Bluetooth reader.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHandheldRFID.html#com.rscja.team.qcom.custom">IHandheldRFID</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IMultipleAntenna.html#com.rscja.team.qcom.custom">IMultipleAntenna</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IReader.html#com.rscja.team.qcom.custom">IReader</a>
<div class="block">R2、R5、R6  蓝牙和USB读写器
 有些客户只需要我们R6的背夹，不需要uhf模块，所以将R6背夹接口独立出来</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA4.html#com.rscja.team.qcom.custom">IRFIDWithUHFA4</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ISingleAntenna.html#com.rscja.team.qcom.custom">ISingleAntenna</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHF.html#com.rscja.team.qcom.custom">IUHF</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUhfBle.html#com.rscja.team.qcom.custom">IUhfBle</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFOfAndroidUart.html#com.rscja.team.qcom.custom">IUHFOfAndroidUart</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUhfReader.html#com.rscja.team.qcom.custom">IUhfReader</a>
<div class="block">R2、R5、R6  蓝牙和USB读写器包含的uhf特有内容</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IURAxOfAndroidUart.html#com.rscja.team.qcom.custom">IURAxOfAndroidUart</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatus.html#com.rscja.team.qcom.deviceapi">ConnectionStatus</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatusCallback.html#com.rscja.team.qcom.deviceapi">ConnectionStatusCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBarcode1D.html#com.rscja.team.qcom.deviceapi">IBarcode1D</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBarcode2D.html#com.rscja.team.qcom.deviceapi">IBarcode2D</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBleDevice.html#com.rscja.team.qcom.deviceapi">IBleDevice</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBluetoothReader.html#com.rscja.team.qcom.deviceapi">IBluetoothReader</a>
<div class="block">蓝牙读写器的接口<br>
 Interface of Bluetooth reader.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ICardWithBYL.html#com.rscja.team.qcom.deviceapi">ICardWithBYL</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprint.html#com.rscja.team.qcom.deviceapi">IFingerprint</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprintSM206B.html#com.rscja.team.qcom.deviceapi">IFingerprintSM206B</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprintWithFIPS.html#com.rscja.team.qcom.deviceapi">IFingerprintWithFIPS</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprintWithMorpho.html#com.rscja.team.qcom.deviceapi">IFingerprintWithMorpho</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprintWithTLK1NC.html#com.rscja.team.qcom.deviceapi">IFingerprintWithTLK1NC</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IFingerprintWithZAZ.html#com.rscja.team.qcom.deviceapi">IFingerprintWithZAZ</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHandheldRFID.html#com.rscja.team.qcom.deviceapi">IHandheldRFID</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ILedLight.html#com.rscja.team.qcom.deviceapi">ILedLight</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IModule.html#com.rscja.team.qcom.deviceapi">IModule</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IMultipleAntenna.html#com.rscja.team.qcom.deviceapi">IMultipleAntenna</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IPSAM.html#com.rscja.team.qcom.deviceapi">IPSAM</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IReader.html#com.rscja.team.qcom.deviceapi">IReader</a>
<div class="block">R2、R5、R6  蓝牙和USB读写器
 有些客户只需要我们R6的背夹，不需要uhf模块，所以将R6背夹接口独立出来</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDBase.html#com.rscja.team.qcom.deviceapi">IRFIDBase</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithISO14443A.html#com.rscja.team.qcom.deviceapi">IRFIDWithISO14443A</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithISO14443A4CPU.html#com.rscja.team.qcom.deviceapi">IRFIDWithISO14443A4CPU</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithISO14443B.html#com.rscja.team.qcom.deviceapi">IRFIDWithISO14443B</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithISO15693.html#com.rscja.team.qcom.deviceapi">IRFIDWithISO15693</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithLF.html#com.rscja.team.qcom.deviceapi">IRFIDWithLF</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA4.html#com.rscja.team.qcom.deviceapi">IRFIDWithUHFA4</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA4NetWork.html#com.rscja.team.qcom.deviceapi">IRFIDWithUHFA4NetWork</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA4RS232.html#com.rscja.team.qcom.deviceapi">IRFIDWithUHFA4RS232</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA8.html#com.rscja.team.qcom.deviceapi">IRFIDWithUHFA8</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA8NetWork.html#com.rscja.team.qcom.deviceapi">IRFIDWithUHFA8NetWork</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFA8RS232.html#com.rscja.team.qcom.deviceapi">IRFIDWithUHFA8RS232</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFRLM.html#com.rscja.team.qcom.deviceapi">IRFIDWithUHFRLM</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFUrxNetwork.html#com.rscja.team.qcom.deviceapi">IRFIDWithUHFUrxNetwork</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFUrxUart.html#com.rscja.team.qcom.deviceapi">IRFIDWithUHFUrxUart</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFUrxUsbToUart.html#com.rscja.team.qcom.deviceapi">IRFIDWithUHFUrxUsbToUart</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IRFIDWithUHFUSB.html#com.rscja.team.qcom.deviceapi">IRFIDWithUHFUSB</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ISingleAntenna.html#com.rscja.team.qcom.deviceapi">ISingleAntenna</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ITagLocate.html#com.rscja.team.qcom.deviceapi">ITagLocate</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ITagLocationCallback.html#com.rscja.team.qcom.deviceapi">ITagLocationCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHF.html#com.rscja.team.qcom.deviceapi">IUHF</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFA4.html#com.rscja.team.qcom.deviceapi">IUHFA4</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFA8.html#com.rscja.team.qcom.deviceapi">IUHFA8</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUhfBle.html#com.rscja.team.qcom.deviceapi">IUhfBle</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFInventoryCallback.html#com.rscja.team.qcom.deviceapi">IUHFInventoryCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFLocationCallback.html#com.rscja.team.qcom.deviceapi">IUHFLocationCallback</a>
<div class="block">UHF 定位回调接口 （UHF location callback interface）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFOfAndroidUart.html#com.rscja.team.qcom.deviceapi">IUHFOfAndroidUart</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFProtocolParse.html#com.rscja.team.qcom.deviceapi">IUHFProtocolParse</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFProtocolParseUrx.html#com.rscja.team.qcom.deviceapi">IUHFProtocolParseUrx</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFRadarLocationCallback.html#com.rscja.team.qcom.deviceapi">IUHFRadarLocationCallback</a>
<div class="block">回调定位过程产生的数据 (Callback of data generated by the positioning process)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUhfReader.html#com.rscja.team.qcom.deviceapi">IUhfReader</a>
<div class="block">R2、R5、R6  蓝牙和USB读写器包含的uhf特有内容</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFURAxExtend.html#com.rscja.team.qcom.deviceapi">IUHFURAxExtend</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFURx.html#com.rscja.team.qcom.deviceapi">IUHFURx</a>
<div class="block">UHF UR4特有接口</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFUrxAutoInventoryTag.html#com.rscja.team.qcom.deviceapi">IUHFUrxAutoInventoryTag</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IURAxOfAndroidUart.html#com.rscja.team.qcom.deviceapi">IURAxOfAndroidUart</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUsbFingerprint.html#com.rscja.team.qcom.deviceapi">IUsbFingerprint</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/KeyEventCallback.html#com.rscja.team.qcom.deviceapi">KeyEventCallback</a>
<div class="block">接收扫描按键的回调
 Receive call back of SCAN Key</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ScanBTCallback.html#com.rscja.team.qcom.deviceapi">ScanBTCallback</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.r1.hf">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/qcom/r1/hf/package-summary.html">com.rscja.team.qcom.r1.hf</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHF14443A.html#com.rscja.team.qcom.r1.hf">IHF14443A</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHF14443B.html#com.rscja.team.qcom.r1.hf">IHF14443B</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IHF15693.html#com.rscja.team.qcom.r1.hf">IHF15693</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.rs232utils">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/qcom/rs232utils/package-summary.html">com.rscja.team.qcom.rs232utils</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IConnectionStatusChangedListener.html#com.rscja.team.qcom.rs232utils">IConnectionStatusChangedListener</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFProtocolParse.html#com.rscja.team.qcom.rs232utils">IUHFProtocolParse</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.service">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/qcom/service/package-summary.html">com.rscja.team.qcom.service</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatus.html#com.rscja.team.qcom.service">ConnectionStatus</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatusCallback.html#com.rscja.team.qcom.service">ConnectionStatusCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ScanBTCallback.html#com.rscja.team.qcom.service">ScanBTCallback</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.socket">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/qcom/socket/package-summary.html">com.rscja.team.qcom.socket</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatus.html#com.rscja.team.qcom.socket">ConnectionStatus</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IConnectionStatusChangedListener.html#com.rscja.team.qcom.socket">IConnectionStatusChangedListener</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFInventoryCallback.html#com.rscja.team.qcom.socket">IUHFInventoryCallback</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.uhfhandler">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/qcom/uhfhandler/package-summary.html">com.rscja.team.qcom.uhfhandler</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IBluetoothReader.html#com.rscja.team.qcom.uhfhandler">IBluetoothReader</a>
<div class="block">蓝牙读写器的接口<br>
 Interface of Bluetooth reader.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/KeyEventCallback.html#com.rscja.team.qcom.uhfhandler">KeyEventCallback</a>
<div class="block">接收扫描按键的回调
 Receive call back of SCAN Key</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.uhfparse">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/qcom/uhfparse/package-summary.html">com.rscja.team.qcom.uhfparse</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/IUHFInventoryCallback.html#com.rscja.team.qcom.uhfparse">IUHFInventoryCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/KeyEventCallback.html#com.rscja.team.qcom.uhfparse">KeyEventCallback</a>
<div class="block">接收扫描按键的回调
 Receive call back of SCAN Key</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.usb">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/qcom/usb/package-summary.html">com.rscja.team.qcom.usb</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatus.html#com.rscja.team.qcom.usb">ConnectionStatus</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatusCallback.html#com.rscja.team.qcom.usb">ConnectionStatusCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/KeyEventCallback.html#com.rscja.team.qcom.usb">KeyEventCallback</a>
<div class="block">接收扫描按键的回调
 Receive call back of SCAN Key</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.usb.pl2302">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> used by <a href="../../../../com/rscja/team/qcom/usb/pl2302/package-summary.html">com.rscja.team.qcom.usb.pl2302</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/rscja/deviceapi/interfaces/class-use/ConnectionStatus.html#com.rscja.team.qcom.usb.pl2302">ConnectionStatus</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
