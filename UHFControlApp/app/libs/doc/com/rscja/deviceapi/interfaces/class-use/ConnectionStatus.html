<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Class com.rscja.deviceapi.interfaces.ConnectionStatus</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.rscja.deviceapi.interfaces.ConnectionStatus";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/deviceapi/interfaces/class-use/ConnectionStatus.html" target="_top">Frames</a></li>
<li><a href="ConnectionStatus.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.rscja.deviceapi.interfaces.ConnectionStatus" class="title">Uses of Class<br>com.rscja.deviceapi.interfaces.ConnectionStatus</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.interfaces">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.deviceapi">com.rscja.team.mtk.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.ble">com.rscja.team.qcom.ble</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.service">com.rscja.team.qcom.service</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.socket">com.rscja.team.qcom.socket</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.usb">com.rscja.team.qcom.usb</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.usb.pl2302">com.rscja.team.qcom.usb.pl2302</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a> in <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> that return <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">R1HFAndPsamManage.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/R1HFAndPsamManage.html#getConnectionStatus--">getConnectionStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA8NetWork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#getConnectStatus--">getConnectStatus</a></span>()</code>
<div class="block">获取连接状态(Acquire UHF connection status)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUSB.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUart.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#getConnectStatus--">getConnectStatus</a></span>()</code>
<div class="block">获取UHF连接状态<br>
 Acquire UHF connection status</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUsbToUart.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">BleDevice.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/BleDevice.html#getConnectStatus--">getConnectStatus</a></span>()</code>
<div class="block">获取UHF连接状态<br>
 Acquire UHF connection status</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxNetwork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">BluetoothReader.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/BluetoothReader.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA4NetWork.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#getConnectStatus--">getConnectStatus</a></span>()</code>
<div class="block">获取连接状态(Acquire UHF connection status)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>abstract <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">UhfBase.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/UhfBase.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUART.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA4RS232.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#getConnectStatus--">getConnectStatus</a></span>()</code>
<div class="block">获取连接状态(Acquire UHF connection status)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFA8RS232.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#getConnectStatus--">getConnectStatus</a></span>()</code>
<div class="block">获取连接状态(Acquire UHF connection status)</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.interfaces">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a> in <a href="../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> that return <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">IUHF.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getConnectStatus--">getConnectStatus</a></span>()</code>
<div class="block">获取UHF连接状态<br>
 Acquire UHF connection status</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">IBluetoothReader.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getConnectStatus--">getConnectStatus</a></span>()</code>
<div class="block">获取蓝牙连接状态(Acquire Bluetooth connection status)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">IBleDevice.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IBleDevice.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">ConnectionStatus.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html#valueOf-int-">valueOf</a></span>(int&nbsp;value)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">ConnectionStatus.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">ConnectionStatus.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ConnectionStatusCallback.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html#getStatus-com.rscja.deviceapi.interfaces.ConnectionStatus-T-">getStatus</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;status,
         <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="type parameter in ConnectionStatusCallback">T</a>&nbsp;device)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">IConnectionStatusChangedListener.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IConnectionStatusChangedListener.html#onStatusChanged-com.rscja.deviceapi.interfaces.ConnectionStatus-">onStatusChanged</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;status)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a> in <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a> that return <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUART_mtk.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.ble">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a> in <a href="../../../../../com/rscja/team/qcom/ble/package-summary.html">com.rscja.team.qcom.ble</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/ble/package-summary.html">com.rscja.team.qcom.ble</a> that return <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">EmptyUhfBle.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/ble/package-summary.html">com.rscja.team.qcom.ble</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFBLEN52_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html#getStatus-com.rscja.deviceapi.interfaces.ConnectionStatus-java.lang.Object-">getStatus</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;status,
         java.lang.Object&nbsp;device)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFBLEN51_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#getStatus-com.rscja.deviceapi.interfaces.ConnectionStatus-java.lang.Object-">getStatus</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;status,
         java.lang.Object&nbsp;device)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a> in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> that return <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUSB_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUsbToUart_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUart_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html#getConnectStatus--">getConnectStatus</a></span>()</code>
<div class="block">获取UHF连接状态<br>
 Acquire UHF connection status</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxUart2_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUrxNetwork_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">RFIDWithUHFUART_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">BluetoothReader_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">BleDevice_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/BleDevice_qcom.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.service">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a> in <a href="../../../../../com/rscja/team/qcom/service/package-summary.html">com.rscja.team.qcom.service</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/service/package-summary.html">com.rscja.team.qcom.service</a> that return <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">BLEService_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#getBTConnectStatus--">getBTConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.socket">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a> in <a href="../../../../../com/rscja/team/qcom/socket/package-summary.html">com.rscja.team.qcom.socket</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/socket/package-summary.html">com.rscja.team.qcom.socket</a> that return <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">SocketManageUR4.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/socket/SocketManageUR4.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">SocketManageA4.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/socket/SocketManageA4.html#getConnectStatus--">getConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/socket/package-summary.html">com.rscja.team.qcom.socket</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">SocketManageUR4.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/socket/SocketManageUR4.html#update-com.rscja.deviceapi.interfaces.ConnectionStatus-">update</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;arg)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">SocketManageA4.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/socket/SocketManageA4.html#update-com.rscja.deviceapi.interfaces.ConnectionStatus-">update</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;status)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.usb">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a> in <a href="../../../../../com/rscja/team/qcom/usb/package-summary.html">com.rscja.team.qcom.usb</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/usb/package-summary.html">com.rscja.team.qcom.usb</a> that return <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">UsbBase_qcom.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html#getConnectionStatus--">getConnectionStatus</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.usb.pl2302">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a> in <a href="../../../../../com/rscja/team/qcom/usb/pl2302/package-summary.html">com.rscja.team.qcom.usb.pl2302</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/qcom/usb/pl2302/package-summary.html">com.rscja.team.qcom.usb.pl2302</a> that return <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><span class="typeNameLabel">UsbPL2302.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html#getConnectionStatus--">getConnectionStatus</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/deviceapi/interfaces/class-use/ConnectionStatus.html" target="_top">Frames</a></li>
<li><a href="ConnectionStatus.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
