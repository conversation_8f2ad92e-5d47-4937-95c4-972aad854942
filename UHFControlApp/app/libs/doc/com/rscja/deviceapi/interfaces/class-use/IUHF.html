<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Interface com.rscja.deviceapi.interfaces.IUHF</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface com.rscja.deviceapi.interfaces.IUHF";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/deviceapi/interfaces/class-use/IUHF.html" target="_top">Frames</a></li>
<li><a href="IUHF.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface com.rscja.deviceapi.interfaces.IUHF" class="title">Uses of Interface<br>com.rscja.deviceapi.interfaces.IUHF</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.custom">com.rscja.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.custom.interfaces">com.rscja.custom.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.interfaces">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.custom">com.rscja.team.mtk.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.deviceapi">com.rscja.team.mtk.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.utility">com.rscja.team.mtk.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.ble">com.rscja.team.qcom.ble</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.custom">com.rscja.team.qcom.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.rscja.custom">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a> in <a href="../../../../../com/rscja/custom/package-summary.html">com.rscja.custom</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/custom/package-summary.html">com.rscja.custom</a> that implement <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/M775Authenticate.html" title="class in com.rscja.custom">M775Authenticate</a></span></code>
<div class="block">英频杰特殊标签定制<br>
Special label customization<br>

 第一步:通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        盘点标签之前先要设置回调函数<a href="../../../../../com/rscja/custom/M775Authenticate.html#setInventoryCallback-com.rscja.custom.M775Authenticate.IUHFInventoryCallback-"><code>M775Authenticate.setInventoryCallback(IUHFInventoryCallback)</code></a>,然后调用盘点函数<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html#startInventoryTag--"><code>RFIDWithUHFUART.startInventoryTag()</code></a>开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html#stopInventory--"><code>RFIDWithUHFUART.stopInventory()</code></a>函数。<br>
 For parameter setting, after the connection is successful, call the corresponding function to set parameters and read/write operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/RFIDWithUHFJieCe.html" title="class in com.rscja.custom">RFIDWithUHFJieCe</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFCSYX.html" title="class in com.rscja.custom">UHFCSYX</a></span></code>
<div class="block">MQTT是长沙盈芯，定制接口</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFCSYX_A4NetWork.html" title="class in com.rscja.custom">UHFCSYX_A4NetWork</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFCSYXForURx.html" title="class in com.rscja.custom">UHFCSYXForURx</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFSFForUrxNetwork.html" title="class in com.rscja.custom">UHFSFForUrxNetwork</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFTamperAPI.html" title="class in com.rscja.custom">UHFTamperAPI</a></span></code>
<div class="block">阿联酋Acube 定制接口</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFTemperatureSensors.html" title="class in com.rscja.custom">UHFTemperatureSensors</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFTemperatureTag.html" title="class in com.rscja.custom">UHFTemperatureTag</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN51.html" title="class in com.rscja.custom">UHFTemperatureTag.RFIDWithUHFBLEN51</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN52.html" title="class in com.rscja.custom">UHFTemperatureTag.RFIDWithUHFBLEN52</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFTemperatureTagsAPI.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFUartFoxconn.html" title="class in com.rscja.custom">UHFUartFoxconn</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFUartTemperatureTag.html" title="class in com.rscja.custom">UHFUartTemperatureTag</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFXSAPI.html" title="class in com.rscja.custom">UHFXSAPI</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.custom.interfaces">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a> in <a href="../../../../../com/rscja/custom/interfaces/package-summary.html">com.rscja.custom.interfaces</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subinterfaces, and an explanation">
<caption><span>Subinterfaces of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a> in <a href="../../../../../com/rscja/custom/interfaces/package-summary.html">com.rscja.custom.interfaces</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Interface and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/interfaces/IM775Authenticate.html" title="interface in com.rscja.custom.interfaces">IM775Authenticate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/interfaces/IRFIDWithUHFJieCe.html" title="interface in com.rscja.custom.interfaces">IRFIDWithUHFJieCe</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/interfaces/IRFIDWithUHFShuangYingDianZi.html" title="interface in com.rscja.custom.interfaces">IRFIDWithUHFShuangYingDianZi</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/interfaces/IRFIDWithUHFUARTUAE.html" title="interface in com.rscja.custom.interfaces">IRFIDWithUHFUARTUAE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/interfaces/IUHFCSYX.html" title="interface in com.rscja.custom.interfaces">IUHFCSYX</a></span></code>
<div class="block">MQTT是长沙盈芯，定制接口</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/interfaces/IUHFTamperAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTamperAPI</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/interfaces/IUHFUartFoxconn.html" title="interface in com.rscja.custom.interfaces">IUHFUartFoxconn</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a> in <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> that implement <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a></span></code>
<div class="block">UHF模块 A4操作类<br>
 UHF module operation type<br>

 第一步:通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4.html#init-android.content.Context-"><code>RFIDWithUHFA4.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4.html#init-android.content.Context-"><code>RFIDWithUHFA4.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点标签先调用<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>RFIDWithUHFA4.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>设置标签回调接口，有标签数据就会回调这个接口。
        然后在调用<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4.html#startInventoryTag--"><code>RFIDWithUHFA4.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4.html#stopInventory--"><code>RFIDWithUHFA4.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></span></code>
<div class="block">操作URA4设备以及UHF模块相关接口。(通过其他android设备控制A4)<br>
 Operate URA4 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFA4NetWork.setIPAndPort(String ip, int port)</code></a>设置要连接的IP地址, 再通过 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#init-android.content.Context-"><code>RFIDWithUHFA4NetWork.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA4NetWork.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFA4NetWork.setIPAndPort(String ip, int port)</code></a>-> <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#init-android.content.Context-"><code>RFIDWithUHFA4NetWork.init(Context context)</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></span></code>
<div class="block">操作URA4设备以及UHF模块相关接口。(通过其他android设备控制A4)<br>
 Operate URA4 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA4RS232.setUart(String path)</code></a>设置要连接的IP地址, 再通过 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#init-android.content.Context-"><code>RFIDWithUHFA4RS232.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA4RS232.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA4RS232.setUart(String path)</code></a>-> <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#init-android.content.Context-"><code>RFIDWithUHFA4RS232.init(Context context)</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a></span></code>
<div class="block">UHF模块 A8操作类<br>
 UHF module operation type<br>

 第一步:通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8.html#init-android.content.Context-"><code>RFIDWithUHFA8.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8.html#init-android.content.Context-"><code>RFIDWithUHFA8.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点标签先调用<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>RFIDWithUHFA8.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>设置标签回调接口，有标签数据就会回调这个接口。
        然后在调用<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8.html#startInventoryTag--"><code>RFIDWithUHFA8.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8.html#stopInventory--"><code>RFIDWithUHFA8.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></span></code>
<div class="block">操作URA8设备以及UHF模块相关接口。(通过其他android设备控制A8)<br>
 Operate URA8 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFA8NetWork.setIPAndPort(String ip, int port)</code></a>设置要连接的IP地址, 再通过 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#init-android.content.Context-"><code>RFIDWithUHFA8NetWork.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA8NetWork.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFA8NetWork.setIPAndPort(String ip, int port)</code></a>-> <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#init-android.content.Context-"><code>RFIDWithUHFA8NetWork.init(Context context)</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></span></code>
<div class="block">操作URA8设备以及UHF模块相关接口。(通过其他android设备控制A8)<br>
 Operate URA8 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA8RS232.setUart(String path)</code></a>设置要连接的IP地址, 再通过 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#init-android.content.Context-"><code>RFIDWithUHFA8RS232.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA8RS232.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA8RS232.setUart(String path)</code></a>-> <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#init-android.content.Context-"><code>RFIDWithUHFA8RS232.init(Context context)</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFAxBase.html" title="class in com.rscja.deviceapi">RFIDWithUHFAxBase</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a></span></code>
<div class="block">UHF模块低功耗蓝牙操作类<br>
 UHF module operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi">RFIDWithUHFRLM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a></span></code>
<div class="block">UHF模块手持机，串口通信操作类<br>
 UHF module handheld, serial communication operation interface <br>

 第一步:通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点标签先调用<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>RFIDWithUHFUART.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>设置标签回调接口，有标签数据就会回调这个接口。
        然后在调用<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html#startInventoryTag--"><code>RFIDWithUHFUART.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUART.html#stopInventory--"><code>RFIDWithUHFUART.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxNetwork</a></span></code>
<div class="block">UHF模块URx网口通信操作类<br>
 URx network operation of UHF module <br>

 第一步：先通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFUrxNetwork.setIPAndPort(String ip, int port)</code></a>设置要连接的IP地址, 再通过 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#init-android.content.Context-"><code>RFIDWithUHFUrxNetwork.init(Context context)</code></a>连接读写器串口。
 Step 1: Connect to the serial port of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFUrxNetwork.setIPAndPort(String ip, int port)</code></a>-> <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#init-android.content.Context-"><code>RFIDWithUHFUrxNetwork.init(Context context)</code></a>.<br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点调用<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#startInventoryTag--"><code>RFIDWithUHFUrxNetwork.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html#stopInventory--"><code>RFIDWithUHFUrxNetwork.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUart</a></span></code>
<div class="block">URx 模块，串口通信操作类<br>
 URx module , serial communication operation interface <br>

 第一步:通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#init-android.content.Context-"><code>RFIDWithUHFUrxUart.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#init-android.content.Context-"><code>RFIDWithUHFUrxUart.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点调用<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#startInventoryTag--"><code>RFIDWithUHFUrxUart.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUart.html#stopInventory--"><code>RFIDWithUHFUrxUart.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi">RFIDWithUHFUrxUsbToUart</a></span></code>
<div class="block">URx 模块，串口通信操作类<br>
 URx module , serial communication operation interface <br>

 第一步:通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#init-android.content.Context-"><code>RFIDWithUHFUrxUsbToUart.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#init-android.content.Context-"><code>RFIDWithUHFUrxUsbToUart.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点调用<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#startInventoryTag--"><code>RFIDWithUHFUrxUsbToUart.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html#stopInventory--"><code>RFIDWithUHFUrxUsbToUart.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a></span></code>
<div class="block">UHF模块手持机，USB通信操作类<br>
 UHF module handheld, USB operation interface <br>

 第一步:通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#init-android.content.Context-"><code>RFIDWithUHFUSB.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#init-android.content.Context-"><code>RFIDWithUHFUSB.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        如果是盘点调用<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#startInventoryTag--"><code>RFIDWithUHFUSB.startInventoryTag()</code></a>函数开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFUSB.html#stopInventory--"><code>RFIDWithUHFUSB.stopInventory()</code></a>函数。<br>
 Step 2: If it is to set parameters, after the connection is successful, call the corresponding function to set parameters, read and write operations.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.interfaces">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a> in <a href="../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subinterfaces, and an explanation">
<caption><span>Subinterfaces of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a> in <a href="../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Interface and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4NetWork</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4RS232</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4Uart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4Uart</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8NetWork</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8RS232</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8Uart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8Uart</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxNetwork</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxUart</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUsbToUart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxUsbToUart</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUSB</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IUHFA4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IUHFA8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a></span></code>
<div class="block">UHF UR4特有接口</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.custom">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a> in <a href="../../../../../com/rscja/team/mtk/custom/package-summary.html">com.rscja.team.mtk.custom</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/mtk/custom/package-summary.html">com.rscja.team.mtk.custom</a> that implement <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/M775Authenticate_mtk.html" title="class in com.rscja.team.mtk.custom">M775Authenticate_mtk</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html" title="class in com.rscja.team.mtk.custom">UHFTemperatureTagsAPI_mtk</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html" title="class in com.rscja.team.mtk.custom">UHFUartFoxconn_mtk</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a> in <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a> that implement <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.utility">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a> in <a href="../../../../../com/rscja/team/mtk/utility/package-summary.html">com.rscja.team.mtk.utility</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/rscja/team/mtk/utility/package-summary.html">com.rscja.team.mtk.utility</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">BatteryManage.</span><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/utility/BatteryManage.html#registerReceiver-android.content.Context-com.rscja.deviceapi.interfaces.OnLowBatteryListener-com.rscja.deviceapi.interfaces.IUHF-">registerReceiver</a></span>(android.content.Context&nbsp;context,
                <a href="../../../../../com/rscja/deviceapi/interfaces/OnLowBatteryListener.html" title="interface in com.rscja.deviceapi.interfaces">OnLowBatteryListener</a>&nbsp;onLowBatteryListener,
                <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>&nbsp;iuhf)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.ble">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a> in <a href="../../../../../com/rscja/team/qcom/ble/package-summary.html">com.rscja.team.qcom.ble</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/qcom/ble/package-summary.html">com.rscja.team.qcom.ble</a> that implement <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html" title="class in com.rscja.team.qcom.ble">RFIDWithUHFBLEN51_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html" title="class in com.rscja.team.qcom.ble">RFIDWithUHFBLEN52_qcom</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.custom">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a> in <a href="../../../../../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a> that implement <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/custom/M775Authenticate_qcom.html" title="class in com.rscja.team.qcom.custom">M775Authenticate_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/custom/RFIDWithUHFJieCe_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFJieCe_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/custom/RFIDWithUHFShuangYingDianZi_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFShuangYingDianZi_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/custom/RFIDWithUHFUARTUAE_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFUARTUAE_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/custom/UHFCSYX_qcom.html" title="class in com.rscja.team.qcom.custom">UHFCSYX_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/custom/UHFTamperAPI_qcom.html" title="class in com.rscja.team.qcom.custom">UHFTamperAPI_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/custom/UHFTemperatureTagsAPI_qcom.html" title="class in com.rscja.team.qcom.custom">UHFTemperatureTagsAPI_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/custom/UHFTemperatureTagsBLEAPI.html" title="class in com.rscja.team.qcom.custom">UHFTemperatureTagsBLEAPI</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/custom/UHFUartFoxconn_qcom.html" title="class in com.rscja.team.qcom.custom">UHFUartFoxconn_qcom</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a> in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> that implement <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a></span></code>
<div class="block">UHF模块 A4操作类<br>
 UHF module operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></span></code>
<div class="block">操作URA4设备以及UHF模块相关接口。<br>

 第一步：连接通过 <code>RFIDWithUHFAxNetWorkBase_qcom.setIPAndPort(String host, int port)</code>设置连接的读写IP地址。然后调用 <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#init-android.content.Context-"><code>RFIDWithUHFA4NetWork_qcom.init(Context)</code></a>连接读写器。
         同时可以设置回调接口 <code>RFIDWithUHFAxNetWorkBase_qcom.setConnectionStatusCallback(ConnectionStatusCallback)</code>监听连接状态<br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数。<br>
         如果是盘点标签先调用<code>RFIDWithUHFAxNetWorkBase_qcom.setInventoryCallback(IUHFInventoryCallback)</code>设置标签回调接口，标签数据会上传到这个接口函数。<br>
         然后在调用<code>RFIDWithUHFAxNetWorkBase_qcom.startInventoryTag()</code>函数开始执行盘点。注意:在盘点标签的时候rfid模块只能响应<code>RFIDWithUHFAxNetWorkBase_qcom.stopInventory()</code>函数。<br>

 第三步：退出app调用 <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#free--"><code>RFIDWithUHFA4NetWork_qcom.free()</code></a>断开连接，如果断开之前正在盘点，请先停止盘点，在断开连接。<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8_qcom</a></span></code>
<div class="block">UHF模块 A8操作类<br>
 UHF module operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8NetWork_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFAxBase_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></span></code>
<div class="block">UHF模块低功耗蓝牙操作类<br>
 UHF module operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a></span></code>
<div class="block">UR4 设备USB转串口,目前已经适配 pl2302芯片的usb转接线</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> with parameters of type <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/TagLocate_qcom.html#TagLocate_qcom-android.content.Context-com.rscja.deviceapi.interfaces.IUHF-">TagLocate_qcom</a></span>(android.content.Context&nbsp;context,
              <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>&nbsp;iuhf)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UhfLocation_qcom.html#UhfLocation_qcom-android.content.Context-com.rscja.deviceapi.interfaces.IUHF-">UhfLocation_qcom</a></span>(android.content.Context&nbsp;context,
                <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>&nbsp;iuhf)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UhfRadarLocation_qcom.html#UhfRadarLocation_qcom-android.content.Context-com.rscja.deviceapi.interfaces.IUHF-">UhfRadarLocation_qcom</a></span>(android.content.Context&nbsp;context,
                     <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>&nbsp;iuhf)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/deviceapi/interfaces/class-use/IUHF.html" target="_top">Frames</a></li>
<li><a href="IUHF.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
