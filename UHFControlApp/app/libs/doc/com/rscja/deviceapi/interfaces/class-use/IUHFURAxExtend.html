<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Interface com.rscja.deviceapi.interfaces.IUHFURAxExtend</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface com.rscja.deviceapi.interfaces.IUHFURAxExtend";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/deviceapi/interfaces/class-use/IUHFURAxExtend.html" target="_top">Frames</a></li>
<li><a href="IUHFURAxExtend.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface com.rscja.deviceapi.interfaces.IUHFURAxExtend" class="title">Uses of Interface<br>com.rscja.deviceapi.interfaces.IUHFURAxExtend</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.custom">com.rscja.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.interfaces">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.rscja.custom">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a> in <a href="../../../../../com/rscja/custom/package-summary.html">com.rscja.custom</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/custom/package-summary.html">com.rscja.custom</a> that implement <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/custom/UHFCSYX_A4NetWork.html" title="class in com.rscja.custom">UHFCSYX_A4NetWork</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a> in <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> that implement <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4NetWork</a></span></code>
<div class="block">操作URA4设备以及UHF模块相关接口。(通过其他android设备控制A4)<br>
 Operate URA4 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFA4NetWork.setIPAndPort(String ip, int port)</code></a>设置要连接的IP地址, 再通过 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#init-android.content.Context-"><code>RFIDWithUHFA4NetWork.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA4NetWork.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFA4NetWork.setIPAndPort(String ip, int port)</code></a>-> <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4NetWork.html#init-android.content.Context-"><code>RFIDWithUHFA4NetWork.init(Context context)</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4RS232</a></span></code>
<div class="block">操作URA4设备以及UHF模块相关接口。(通过其他android设备控制A4)<br>
 Operate URA4 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA4RS232.setUart(String path)</code></a>设置要连接的IP地址, 再通过 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#init-android.content.Context-"><code>RFIDWithUHFA4RS232.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA4RS232.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA4RS232.setUart(String path)</code></a>-> <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4RS232.html#init-android.content.Context-"><code>RFIDWithUHFA4RS232.init(Context context)</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8NetWork</a></span></code>
<div class="block">操作URA8设备以及UHF模块相关接口。(通过其他android设备控制A8)<br>
 Operate URA8 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFA8NetWork.setIPAndPort(String ip, int port)</code></a>设置要连接的IP地址, 再通过 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#init-android.content.Context-"><code>RFIDWithUHFA8NetWork.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA8NetWork.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#setIPAndPort-java.lang.String-int-"><code>RFIDWithUHFA8NetWork.setIPAndPort(String ip, int port)</code></a>-> <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8NetWork.html#init-android.content.Context-"><code>RFIDWithUHFA8NetWork.init(Context context)</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8RS232</a></span></code>
<div class="block">操作URA8设备以及UHF模块相关接口。(通过其他android设备控制A8)<br>
 Operate URA8 devices and related interfaces of UHF modules.<br>

 操作步骤：<br/>
 Steps:<br/>
 第一步：先通过<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA8RS232.setUart(String path)</code></a>设置要连接的IP地址, 再通过 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#init-android.content.Context-"><code>RFIDWithUHFA8RS232.init(Context context)</code></a>连接读写器串口。同时可以设置回调接口 <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-"><code>RFIDWithUHFA8RS232.setConnectionStatusCallback(ConnectionStatusCallback btStateCallback)</code></a>监听连接状态。<br>
 Step 1: Connect to the serial port of the reader via<a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#setUart-java.lang.String-"><code>RFIDWithUHFA8RS232.setUart(String path)</code></a>-> <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8RS232.html#init-android.content.Context-"><code>RFIDWithUHFA8RS232.init(Context context)</code></a>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.interfaces">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a> in <a href="../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subinterfaces, and an explanation">
<caption><span>Subinterfaces of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a> in <a href="../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Interface and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4NetWork</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4RS232</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8NetWork</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8RS232</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IUHFA4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IUHFA8</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a> in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> that implement <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4NetWork_qcom</a></span></code>
<div class="block">操作URA4设备以及UHF模块相关接口。<br>

 第一步：连接通过 <code>RFIDWithUHFAxNetWorkBase_qcom.setIPAndPort(String host, int port)</code>设置连接的读写IP地址。然后调用 <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#init-android.content.Context-"><code>RFIDWithUHFA4NetWork_qcom.init(Context)</code></a>连接读写器。
         同时可以设置回调接口 <code>RFIDWithUHFAxNetWorkBase_qcom.setConnectionStatusCallback(ConnectionStatusCallback)</code>监听连接状态<br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数。<br>
         如果是盘点标签先调用<code>RFIDWithUHFAxNetWorkBase_qcom.setInventoryCallback(IUHFInventoryCallback)</code>设置标签回调接口，标签数据会上传到这个接口函数。<br>
         然后在调用<code>RFIDWithUHFAxNetWorkBase_qcom.startInventoryTag()</code>函数开始执行盘点。注意:在盘点标签的时候rfid模块只能响应<code>RFIDWithUHFAxNetWorkBase_qcom.stopInventory()</code>函数。<br>

 第三步：退出app调用 <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#free--"><code>RFIDWithUHFA4NetWork_qcom.free()</code></a>断开连接，如果断开之前正在盘点，请先停止盘点，在断开连接。<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8NetWork_qcom</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/deviceapi/interfaces/class-use/IUHFURAxExtend.html" target="_top">Frames</a></li>
<li><a href="IUHFURAxExtend.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
