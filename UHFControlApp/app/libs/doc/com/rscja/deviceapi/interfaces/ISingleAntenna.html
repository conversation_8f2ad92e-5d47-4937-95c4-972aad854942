<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>ISingleAntenna</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ISingleAntenna";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ISingleAntenna.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/ITagLocate.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/ISingleAntenna.html" target="_top">Frames</a></li>
<li><a href="ISingleAntenna.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface ISingleAntenna" class="title">Interface ISingleAntenna</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUSB</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a>, <a href="../../../../com/rscja/custom/M775Authenticate.html" title="class in com.rscja.custom">M775Authenticate</a>, <a href="../../../../com/rscja/team/mtk/custom/M775Authenticate_mtk.html" title="class in com.rscja.team.mtk.custom">M775Authenticate_mtk</a>, <a href="../../../../com/rscja/team/qcom/custom/M775Authenticate_qcom.html" title="class in com.rscja.team.qcom.custom">M775Authenticate_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFAxBase.html" title="class in com.rscja.deviceapi">RFIDWithUHFAxBase</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFAxBase_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi">RFIDWithUHFBLE</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a>, <a href="../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html" title="class in com.rscja.team.qcom.ble">RFIDWithUHFBLEN51_qcom</a>, <a href="../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html" title="class in com.rscja.team.qcom.ble">RFIDWithUHFBLEN52_qcom</a>, <a href="../../../../com/rscja/custom/RFIDWithUHFJieCe.html" title="class in com.rscja.custom">RFIDWithUHFJieCe</a>, <a href="../../../../com/rscja/team/qcom/custom/RFIDWithUHFJieCe_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFJieCe_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi">RFIDWithUHFRLM</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a>, <a href="../../../../com/rscja/team/qcom/custom/RFIDWithUHFShuangYingDianZi_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFShuangYingDianZi_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi">RFIDWithUHFUART</a>, <a href="../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a>, <a href="../../../../com/rscja/team/qcom/custom/RFIDWithUHFUARTUAE_qcom.html" title="class in com.rscja.team.qcom.custom">RFIDWithUHFUARTUAE_qcom</a>, <a href="../../../../com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi">RFIDWithUHFUSB</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a>, <a href="../../../../com/rscja/custom/UHFCSYX.html" title="class in com.rscja.custom">UHFCSYX</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFCSYX_qcom.html" title="class in com.rscja.team.qcom.custom">UHFCSYX_qcom</a>, <a href="../../../../com/rscja/custom/UHFTamperAPI.html" title="class in com.rscja.custom">UHFTamperAPI</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFTamperAPI_qcom.html" title="class in com.rscja.team.qcom.custom">UHFTamperAPI_qcom</a>, <a href="../../../../com/rscja/custom/UHFTemperatureSensors.html" title="class in com.rscja.custom">UHFTemperatureSensors</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTag.html" title="class in com.rscja.custom">UHFTemperatureTag</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN51.html" title="class in com.rscja.custom">UHFTemperatureTag.RFIDWithUHFBLEN51</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN52.html" title="class in com.rscja.custom">UHFTemperatureTag.RFIDWithUHFBLEN52</a>, <a href="../../../../com/rscja/custom/UHFTemperatureTagsAPI.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI</a>, <a href="../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html" title="class in com.rscja.team.mtk.custom">UHFTemperatureTagsAPI_mtk</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFTemperatureTagsAPI_qcom.html" title="class in com.rscja.team.qcom.custom">UHFTemperatureTagsAPI_qcom</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFTemperatureTagsBLEAPI.html" title="class in com.rscja.team.qcom.custom">UHFTemperatureTagsBLEAPI</a>, <a href="../../../../com/rscja/custom/UHFUartFoxconn.html" title="class in com.rscja.custom">UHFUartFoxconn</a>, <a href="../../../../com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html" title="class in com.rscja.team.mtk.custom">UHFUartFoxconn_mtk</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFUartFoxconn_qcom.html" title="class in com.rscja.team.qcom.custom">UHFUartFoxconn_qcom</a>, <a href="../../../../com/rscja/custom/UHFUartTemperatureTag.html" title="class in com.rscja.custom">UHFUartTemperatureTag</a>, <a href="../../../../com/rscja/custom/UHFXSAPI.html" title="class in com.rscja.custom">UHFXSAPI</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ISingleAntenna</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html#getPower--">getPower</a></span>()</code>
<div class="block">读取模块的功率<br>
 Read module output power</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html#setPower-int-">setPower</a></span>(int&nbsp;power)</code>
<div class="block">设置模块的功率<br>
 Setup output power</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPower</h4>
<pre>int&nbsp;getPower()</pre>
<div class="block">读取模块的功率<br>
 Read module output power</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回功率，返回-1为失败(Return output power, return -1 means failure.)</dd>
</dl>
</li>
</ul>
<a name="setPower-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setPower</h4>
<pre>boolean&nbsp;setPower(int&nbsp;power)</pre>
<div class="block">设置模块的功率<br>
 Setup output power</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>power</code> - 功率值(Power values)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ISingleAntenna.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/ITagLocate.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/ISingleAntenna.html" target="_top">Frames</a></li>
<li><a href="ISingleAntenna.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
