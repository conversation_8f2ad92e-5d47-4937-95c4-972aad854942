<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>com.rscja.deviceapi.interfaces Class Hierarchy</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.rscja.deviceapi.interfaces Class Hierarchy";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/exception/package-tree.html">Prev</a></li>
<li><a href="../../../../com/rscja/scanner/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package com.rscja.deviceapi.interfaces</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ConnectionStatusCallback</span></a>&lt;T&gt;</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBarcode1D.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcode1D</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcode2D</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBarcodePhoto.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcodePhoto</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBarcodePictureCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcodePictureCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcodeSymbolUtility</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcodeUtility</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBarcodeVideoCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBarcodeVideoCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBleDevice</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothData.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBluetoothData</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ICardWithBYL.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ICardWithBYL</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IConnectionStatusChangedListener.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IConnectionStatusChangedListener</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IFingerprint</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IFingerprintSM206B</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IFingerprintWithFIPS</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IFingerprintWithMorpho</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IFingerprintWithTLK1NC</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IFingerprintWithZAZ</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IGPIStateCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IGPIStateCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IHandheldRFID</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUSB</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUhfBle</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IHF14443A.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IHF14443A</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IHF14443B</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IHF15693</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IInfrared</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ILedLight.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ILedLight</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IModule</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IMultipleAntenna</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA4</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4RS232</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA8</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8RS232</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFURx</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxNetwork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxUart</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUsbToUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxUsbToUart</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IPrinter</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IPSAM</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IReader</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IBluetoothReader</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUhfBle</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUhfReader</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUSB</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUhfBle</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>)</li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDBase</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithISO14443A</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A4CPU.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithISO14443A4CPU</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithISO14443B</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithISO15693</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithLF</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IScanerLedLight</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ISingleAntenna</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUSB</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUhfBle</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ITagLocate.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ITagLocate</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ITagLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ITagLocationCallback</span></a>&lt;T&gt;</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHF</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUSB</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA4</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4RS232</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA8</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8RS232</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUhfBle</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFOfAndroidUart</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFRLM</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFURx</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxNetwork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxUart</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUsbToUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFUrxUsbToUart</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFInventoryCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFInventoryCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFLocationCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFProtocolParse</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFProtocolParseUrx</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFRadarLocationCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFURAxExtend</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA4</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4RS232</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFA8</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8NetWork</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8RS232</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFUrxAutoInventoryTag.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUHFUrxAutoInventoryTag</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUpgradeProgress.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUpgradeProgress</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IURAxOfAndroidUart</span></a>
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA4Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>)</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8Uart.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IRFIDWithUHFA8Uart</span></a> (also extends com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUsbFingerprint.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">IUsbFingerprint</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/KeyEventCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">KeyEventCallback</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/OnLowBatteryListener.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">OnLowBatteryListener</span></a></li>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ScanBTCallback</span></a></li>
</ul>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li type="circle">com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces"><span class="typeNameLink">ConnectionStatus</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/exception/package-tree.html">Prev</a></li>
<li><a href="../../../../com/rscja/scanner/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
