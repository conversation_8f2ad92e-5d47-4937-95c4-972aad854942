<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IRFIDWithLF</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IRFIDWithLF";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IRFIDWithLF.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IRFIDWithLF.html" target="_top">Frames</a></li>
<li><a href="IRFIDWithLF.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi.interfaces</div>
<h2 title="Interface IRFIDWithLF" class="title">Interface IRFIDWithLF</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi">RFIDWithLF</a>, <a href="../../../../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithLF_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IRFIDWithLF</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#configFDXTag-char:A-char:A-">configFDXTag</a></span>(char[]&nbsp;cidbuf,
            char[]&nbsp;nidbuf)</code>
<div class="block">EM4305 配置成动物标签<br>
 EM4305 configurate animal tag<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#EM125k_ReadHitag1--">EM125k_ReadHitag1</a></span>()</code>
<div class="block">获取所有页的数据<br>
 Acquire data of all pages<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#free--">free</a></span>()</code>
<div class="block">释放RFID低频模块<br>
 Release RFID LF module<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#getHardwareVersion--">getHardwareVersion</a></span>()</code>
<div class="block">读取硬件版本<br>
 read hardware version<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#getUIDWith4450Card--">getUIDWith4450Card</a></span>()</code>
<div class="block">获取UID,用于4450Card<br>
 Acquire UID, used for 4450 card<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#getUIDWithHID--">getUIDWithHID</a></span>()</code>
<div class="block">读取HID卡<br>
 Read HID card<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#getUIDWithHitagS--">getUIDWithHitagS</a></span>()</code>
<div class="block">获取UID,用于hitag S<br>
 Acquire UID, used for hitag S<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#init--">init</a></span>()</code>
<div class="block">初始化RFID低频模块<br>
 Initialize RFID LF module<br></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#initWithNeedleTag--">initWithNeedleTag</a></span>()</code>
<div class="block">初始化RFID低频模块，针对针管标签<br>
 Initialize RFID LF module, for needle tube tag<br></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#isPowerOn--">isPowerOn</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity">AnimalEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#readAnimalTags-int-">readAnimalTags</a></span>(int&nbsp;iMode)</code>
<div class="block">读取动物标签信息<br>
 read animal tag infor<br></div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#readDataWith4305Card-int-">readDataWith4305Card</a></span>(int&nbsp;nPage)</code>
<div class="block">读4305卡的某一页（0~31）数据<br>
 read card 4305 data of random page (0-31)<br></div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#readDataWithHitagS-int-">readDataWithHitagS</a></span>(int&nbsp;nPage)</code>
<div class="block">读取Hitag S卡中的某一页的数据<br>
 Read data of random page in Hitag S card<br></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#readDataWithIDCard-int-">readDataWithIDCard</a></span>(int&nbsp;iMode)</code>
<div class="block">读卡,用于ID卡<br>
 card reading, used for ID card<br></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#readWithNeedleTag--">readWithNeedleTag</a></span>()</code>
<div class="block">读数据，针对针管标签<br>
 read data, for needle tube tag<br></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#writeDataWith4305Card-int-java.lang.String-">writeDataWith4305Card</a></span>(int&nbsp;nPage,
                     java.lang.String&nbsp;hexData)</code>
<div class="block">写4305卡的某一页（0~31）数据<br>
 write data in card 4305 at random page (0-31)<br></div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithLF.html#writeDataWithHitagS-int-java.lang.String-">writeDataWithHitagS</a></span>(int&nbsp;nPage,
                   java.lang.String&nbsp;hexData)</code>
<div class="block">写Hitag S卡中的某一页的数据<br>
 Write data of random page in Hitag S card<br></div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="init--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>boolean&nbsp;init()</pre>
<div class="block">初始化RFID低频模块<br>
 Initialize RFID LF module<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>config</code> - 配置信息实例<br>
               Configuration Infor example<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true success, false failed<br></dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>boolean&nbsp;free()</pre>
<div class="block">释放RFID低频模块<br>
 Release RFID LF module<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 ture success, false failure<br></dd>
</dl>
</li>
</ul>
<a name="getHardwareVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHardwareVersion</h4>
<pre>java.lang.String&nbsp;getHardwareVersion()</pre>
<div class="block">读取硬件版本<br>
 read hardware version<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>硬件版本, null表示读取失败<br>
 hardware version, null means reading failure<br></dd>
</dl>
</li>
</ul>
<a name="readDataWithIDCard-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readDataWithIDCard</h4>
<pre>java.lang.String&nbsp;readDataWithIDCard(int&nbsp;iMode)</pre>
<div class="block">读卡,用于ID卡<br>
 card reading, used for ID card<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>iMode</code> - 模式切换 0 为只读ID；1 为只读动物标签 2为读半双工动物标签<br>
              Profile switching 0 is read-only ID; 1 is read-only animal tag. 2 is read half-duplex animal tag<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>读取到的数据信息 ,失败则返回null<br>
 read data infor, failure return null<br></dd>
</dl>
</li>
</ul>
<a name="readAnimalTags-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readAnimalTags</h4>
<pre><a href="../../../../com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity">AnimalEntity</a>&nbsp;readAnimalTags(int&nbsp;iMode)</pre>
<div class="block">读取动物标签信息<br>
 read animal tag infor<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>iMode</code> - 1,全双工 ；2半双工<br>
              1. full duplex; 2 half duplex<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>动物标签信息，失败则返回null<br>
 animal tag infor, failure return null<br></dd>
</dl>
</li>
</ul>
<a name="readDataWithHitagS-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readDataWithHitagS</h4>
<pre>java.lang.String&nbsp;readDataWithHitagS(int&nbsp;nPage)</pre>
<div class="block">读取Hitag S卡中的某一页的数据<br>
 Read data of random page in Hitag S card<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nPage</code> - 页码<br>
              page<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>读取到的数据信息 ,失败则返回null<br>
 read data, failure return null<br></dd>
</dl>
</li>
</ul>
<a name="writeDataWithHitagS-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeDataWithHitagS</h4>
<pre>boolean&nbsp;writeDataWithHitagS(int&nbsp;nPage,
                            java.lang.String&nbsp;hexData)</pre>
<div class="block">写Hitag S卡中的某一页的数据<br>
 Write data of random page in Hitag S card<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nPage</code> - 页码<br>
                Page<br></dd>
<dd><code>hexData</code> - 要写入的十六进制数据<br>
                write hexdecimal data<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true success, false failed<br></dd>
</dl>
</li>
</ul>
<a name="readDataWith4305Card-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readDataWith4305Card</h4>
<pre>java.lang.String&nbsp;readDataWith4305Card(int&nbsp;nPage)</pre>
<div class="block">读4305卡的某一页（0~31）数据<br>
 read card 4305 data of random page (0-31)<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nPage</code> - 页码<br>
              page<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>读取到的数据信息 ,失败则返回null<br>
 read data, failed return null<br></dd>
</dl>
</li>
</ul>
<a name="writeDataWith4305Card-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeDataWith4305Card</h4>
<pre>boolean&nbsp;writeDataWith4305Card(int&nbsp;nPage,
                              java.lang.String&nbsp;hexData)</pre>
<div class="block">写4305卡的某一页（0~31）数据<br>
 write data in card 4305 at random page (0-31)<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>nPage</code> - 页码<br>
                page<br></dd>
<dd><code>hexData</code> - 要写入的十六进制数据<br>
                hexdecimal data needs to be written<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 ture success, false failed<br></dd>
</dl>
</li>
</ul>
<a name="getUIDWithHID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUIDWithHID</h4>
<pre>int&nbsp;getUIDWithHID()</pre>
<div class="block">读取HID卡<br>
 Read HID card<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>卡号，返回-1为读取失败<br>
 card, return -1 means reading failed<br></dd>
</dl>
</li>
</ul>
<a name="getUIDWithHitagS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUIDWithHitagS</h4>
<pre>java.lang.String&nbsp;getUIDWithHitagS()</pre>
<div class="block">获取UID,用于hitag S<br>
 Acquire UID, used for hitag S<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>读取到的UID信息, 失败则返回null<br>
 read UID infor, failure return null<br></dd>
</dl>
</li>
</ul>
<a name="getUIDWith4450Card--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUIDWith4450Card</h4>
<pre>java.lang.String&nbsp;getUIDWith4450Card()</pre>
<div class="block">获取UID,用于4450Card<br>
 Acquire UID, used for 4450 card<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>读取到的UID信息, 失败则返回null<br>
 read UID infor, failure return null<br></dd>
</dl>
</li>
</ul>
<a name="initWithNeedleTag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initWithNeedleTag</h4>
<pre>boolean&nbsp;initWithNeedleTag()</pre>
<div class="block">初始化RFID低频模块，针对针管标签<br>
 Initialize RFID LF module, for needle tube tag<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>config</code> - 配置信息实例<br>
               configuration info example<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true succes, false failed<br></dd>
</dl>
</li>
</ul>
<a name="readWithNeedleTag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readWithNeedleTag</h4>
<pre>java.lang.String&nbsp;readWithNeedleTag()</pre>
<div class="block">读数据，针对针管标签<br>
 read data, for needle tube tag<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>读取到的数据信息(十六进制) ,失败则返回null<br>
 read hexdecimal data, failure return null<br></dd>
</dl>
</li>
</ul>
<a name="configFDXTag-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>configFDXTag</h4>
<pre>int&nbsp;configFDXTag(char[]&nbsp;cidbuf,
                 char[]&nbsp;nidbuf)</pre>
<div class="block">EM4305 配置成动物标签<br>
 EM4305 configurate animal tag<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cidbuf</code> - 16进制 2字节， 如cid 为156， 转成 0x00 0x9C<br>
               cidbuf hexdecimal 2 byte, e.g.cid is 156, transform into 0x00 0x9C<br></dd>
<dd><code>nidbuf</code> - 16进制 5字节， 如nid 为123456789 ,转成 0x00 0x07 0x5B 0xCD 0x15<br>
               nidbuf hexdecimal 5 byte, e.g. nid is 1234456789, transform into 0x00 0x07 0x5B 0xCD 0x15<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0成功，非零失败<br>
 0 success, NZ failure<br></dd>
</dl>
</li>
</ul>
<a name="EM125k_ReadHitag1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM125k_ReadHitag1</h4>
<pre>java.lang.String&nbsp;EM125k_ReadHitag1()</pre>
<div class="block">获取所有页的数据<br>
 Acquire data of all pages<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>读取到的数据信息 ,失败则返回null<br>
 read data, failure return null<br></dd>
</dl>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>boolean&nbsp;isPowerOn()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IRFIDWithLF.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/deviceapi/interfaces/IRFIDWithLF.html" target="_top">Frames</a></li>
<li><a href="IRFIDWithLF.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
