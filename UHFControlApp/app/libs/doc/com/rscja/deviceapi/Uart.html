<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Fri Sep 27 10:50:22 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uart</title>
<meta name="date" content="2024-09-27">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uart";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Uart.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/Trigger.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/Vehicle_Listener.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/Uart.html" target="_top">Frames</a></li>
<li><a href="Uart.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.deviceapi</div>
<h2 title="Class Uart" class="title">Class Uart</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.deviceapi.Uart</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Uart</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Uart.html#MODULE_RS232">MODULE_RS232</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Uart.html#close--">close</a></span>()</code>
<div class="block">关闭串口</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/deviceapi/Uart.html" title="class in com.rscja.deviceapi">Uart</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Uart.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Uart.html#open-int-java.lang.String-int-int-int-int-">open</a></span>(int&nbsp;module,
    java.lang.String&nbsp;path,
    int&nbsp;baudrate,
    int&nbsp;databits,
    int&nbsp;stopbits,
    int&nbsp;check)</code>
<div class="block">打开串口</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Uart.html#open-java.lang.String-int-">open</a></span>(java.lang.String&nbsp;path,
    int&nbsp;baudrate)</code>
<div class="block">打开串口(databits:8  stopbits:1  check:0)</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Uart.html#open-java.lang.String-int-int-int-int-">open</a></span>(java.lang.String&nbsp;path,
    int&nbsp;baudrate,
    int&nbsp;databits,
    int&nbsp;stopbits,
    int&nbsp;check)</code>
<div class="block">打开串口</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Uart.html#receive--">receive</a></span>()</code>
<div class="block">接收数据</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Uart.html#send-byte:A-">send</a></span>(byte[]&nbsp;data)</code>
<div class="block">发送数据 <br/>
 Send data.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/deviceapi/Uart.html#sendAndReceive-byte:A-byte:A-">sendAndReceive</a></span>(byte[]&nbsp;sendData,
              byte[]&nbsp;outData)</code>
<div class="block">发送接收数据</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="MODULE_RS232">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MODULE_RS232</h4>
<pre>public static final&nbsp;int MODULE_RS232</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.deviceapi.Uart.MODULE_RS232">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/deviceapi/Uart.html" title="class in com.rscja.deviceapi">Uart</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="open-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(java.lang.String&nbsp;path,
                    int&nbsp;baudrate)</pre>
<div class="block">打开串口(databits:8  stopbits:1  check:0)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - 串口路径</dd>
<dd><code>baudrate</code> - 波特率</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success false:fail</dd>
</dl>
</li>
</ul>
<a name="open-java.lang.String-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(java.lang.String&nbsp;path,
                    int&nbsp;baudrate,
                    int&nbsp;databits,
                    int&nbsp;stopbits,
                    int&nbsp;check)</pre>
<div class="block">打开串口</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - 串口路径</dd>
<dd><code>baudrate</code> - 波特率</dd>
<dd><code>databits</code> - 数据位   取值为  7 或者8</dd>
<dd><code>stopbits</code> - 停止位   取值为  1 或者2</dd>
<dd><code>check</code> - 效验类型 取值为  0无, 1奇, 2偶</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success false:fail</dd>
</dl>
</li>
</ul>
<a name="open-int-java.lang.String-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(int&nbsp;module,
                    java.lang.String&nbsp;path,
                    int&nbsp;baudrate,
                    int&nbsp;databits,
                    int&nbsp;stopbits,
                    int&nbsp;check)</pre>
<div class="block">打开串口</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>module</code> - 传递-1</dd>
<dd><code>path</code> - 串口路径</dd>
<dd><code>baudrate</code> - 波特率</dd>
<dd><code>databits</code> - 数据位   取值为  7 或者8</dd>
<dd><code>stopbits</code> - 停止位   取值为  1 或者2</dd>
<dd><code>check</code> - 效验类型 取值为  0无, 1奇, 2偶</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success false:fail</dd>
</dl>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;boolean&nbsp;close()</pre>
<div class="block">关闭串口</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="send-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>send</h4>
<pre>public&nbsp;boolean&nbsp;send(byte[]&nbsp;data)</pre>
<div class="block">发送数据 <br/>
 Send data.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - 数据 <br/>
 Data</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败 <br/>
 If sucess,return true, if failure, return false.</dd>
</dl>
</li>
</ul>
<a name="receive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>receive</h4>
<pre>public&nbsp;byte[]&nbsp;receive()</pre>
<div class="block">接收数据</div>
</li>
</ul>
<a name="sendAndReceive-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>sendAndReceive</h4>
<pre>public&nbsp;int&nbsp;sendAndReceive(byte[]&nbsp;sendData,
                          byte[]&nbsp;outData)</pre>
<div class="block">发送接收数据</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Uart.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/deviceapi/Trigger.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/deviceapi/Vehicle_Listener.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/deviceapi/Uart.html" target="_top">Frames</a></li>
<li><a href="Uart.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
