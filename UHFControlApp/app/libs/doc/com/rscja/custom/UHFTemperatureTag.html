<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>UHFTemperatureTag</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UHFTemperatureTag";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFTemperatureTag.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html" title="class in com.rscja.custom"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/custom/UHFTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/custom/UHFTemperatureTag.html" target="_top">Frames</a></li>
<li><a href="UHFTemperatureTag.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.rscja.team.qcom.deviceapi.BluetoothReader_qcom">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.custom</div>
<h2 title="Class UHFTemperatureTag" class="title">Class UHFTemperatureTag</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi.BluetoothReader_qcom</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi.RFIDWithUHFBLE_qcom</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.custom.UHFTemperatureTag</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>, <a href="../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a>, <a href="../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">UHFTemperatureTag</span>
extends <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/UHFTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom">UHFTemperatureTag.InventoryTemperatureTagCallback</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN51.html" title="class in com.rscja.custom">UHFTemperatureTag.RFIDWithUHFBLEN51</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN52.html" title="class in com.rscja.custom">UHFTemperatureTag.RFIDWithUHFBLEN52</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom">UHFTemperatureTag.TemperatureTagInfo</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.team.qcom.deviceapi.BluetoothReader_qcom">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.rscja.team.qcom.deviceapi.<a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></h3>
<code><a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#isBarcodeDataContainCodeID">isBarcodeDataContainCodeID</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#isBarcodeDataContainSSIID">isBarcodeDataContainSSIID</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC">Bank_EPC</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED">Bank_RESERVED</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID">Bank_TID</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER">Bank_USER</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_ACCESS">LockBank_ACCESS</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_EPC">LockBank_EPC</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_KILL">LockBank_KILL</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_TID">LockBank_TID</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_USER">LockBank_USER</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_LOCK">LockMode_LOCK</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_OPEN">LockMode_OPEN</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_PLOCK">LockMode_PLOCK</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_POPEN">LockMode_POPEN</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#UPDATE_UHF">UPDATE_UHF</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IBluetoothReader">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></h3>
<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_FIRMWARE">VERSION_BT_FIRMWARE</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_HARDWARE">VERSION_BT_HARDWARE</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_SOFTWARE">VERSION_BT_SOFTWARE</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IReader">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></h3>
<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#UPDATE_STM32">UPDATE_STM32</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/custom/UHFTemperatureTag.html" title="class in com.rscja.custom">UHFTemperatureTag</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/UHFTemperatureTag.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/UHFTemperatureTag.html#inventoryTemperatureTag-int-">inventoryTemperatureTag</a></span>(int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/UHFTemperatureTag.html#setInventoryTemperatureTagCallback-com.rscja.custom.UHFTemperatureTag.InventoryTemperatureTagCallback-">setInventoryTemperatureTagCallback</a></span>(<a href="../../../com/rscja/custom/UHFTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom">UHFTemperatureTag.InventoryTemperatureTagCallback</a>&nbsp;inventoryTemperatureTagCallback)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/UHFTemperatureTag.html#stopInventory--">stopInventory</a></span>()</code>
<div class="block">停止循环识别，在调用此函数之后应当退出循环获取缓冲区的标签信息的子线程<br>
 Stop auto reading, after call this function to exit sub threads of tag data of buffer.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.qcom.deviceapi.RFIDWithUHFBLE_qcom">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.qcom.deviceapi.<a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></h3>
<code><a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#connect-java.lang.String-">connect</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#connect-java.lang.String-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">connect</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#deleteAllTagToFlash--">deleteAllTagToFlash</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#eraseData-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#factoryReset--">factoryReset</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getAllTagTotalFromFlash--">getAllTagTotalFromFlash</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getCW--">getCW</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getEx10SDKFirmware--">getEx10SDKFirmware</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getFastID--">getFastID</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getFastInventoryMode--">getFastInventoryMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getFrequencyMode--">getFrequencyMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getGen2--">getGen2</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getNewTagTotalFromFlash--">getNewTagTotalFromFlash</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getPower--">getPower</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getProtocol--">getProtocol</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getReaderAwaitSleepTime--">getReaderAwaitSleepTime</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getRFLink--">getRFLink</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getTagDataFromFlash--">getTagDataFromFlash</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getTagfocus--">getTagfocus</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getTagLocate-android.content.Context-">getTagLocate</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getTemperature--">getTemperature</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#getVersion--">getVersion</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#init-android.content.Context-">init</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#inventorySingleTag--">inventorySingleTag</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#inventorySingleTag-com.rscja.deviceapi.entity.InventoryParameter-">inventorySingleTag</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#isInventorying--">isInventorying</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#isSupportRssi--">isSupportRssi</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#killTag-java.lang.String-">killTag</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#readData-java.lang.String-int-int-int-">readData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">readData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#readTagFromBuffer--">readTagFromBuffer</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#readTagFromBufferList_EpcTidUser--">readTagFromBufferList_EpcTidUser</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#readTagFromBufferList--">readTagFromBufferList</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setCW-int-">setCW</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setDynamicDistance-int-">setDynamicDistance</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setEPCAndTIDMode--">setEPCAndTIDMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setEPCAndTIDUserMode-int-int-">setEPCAndTIDUserMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setEPCMode--">setEPCMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setFastID-boolean-">setFastID</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setFastInventoryMode-boolean-">setFastInventoryMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setFilter-int-int-int-java.lang.String-">setFilter</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setFreHop-float-">setFreHop</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setFrequencyMode-int-">setFrequencyMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">setGen2</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setPower-int-">setPower</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setProtocol-int-">setProtocol</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setReaderAwaitSleepTime-int-">setReaderAwaitSleepTime</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setRFLink-int-">setRFLink</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setSupportRssi-boolean-">setSupportRssi</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#setTagFocus-boolean-">setTagFocus</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#startInventoryTag--">startInventoryTag</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#startInventoryTag-com.rscja.deviceapi.entity.InventoryParameter-">startInventoryTag</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#startLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFLocationCallback-">startLocation</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#startRadarLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFRadarLocationCallback-">startRadarLocation</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#stopLocation--">stopLocation</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#stopRadarLocation--">stopRadarLocation</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfJump2Boot--">uhfJump2Boot</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfJump2Boot-int-">uhfJump2Boot</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfJump2BootSTM32--">uhfJump2BootSTM32</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfStartUpdate--">uhfStartUpdate</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfStopUpdate--">uhfStopUpdate</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#uhfUpdating-byte:A-">uhfUpdating</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.qcom.deviceapi.BluetoothReader_qcom">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.qcom.deviceapi.<a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a></h3>
<code><a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#blinkOfLed-int-int-int-">blinkOfLed</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#closeLed--">closeLed</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#disconnect--">disconnect</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#free--">free</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBarcodeDataCodeIdContainType--">getBarcodeDataCodeIdContainType</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBattery--">getBattery</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBeep--">getBeep</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBleHardwareVersion--">getBleHardwareVersion</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBluetoothDeviceAddress--">getBluetoothDeviceAddress</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getBluetoothVersion--">getBluetoothVersion</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getConnectStatus--">getConnectStatus</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getKeyMode--">getKeyMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getParameter-byte:A-">getParameter</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getSTM32Version--">getSTM32Version</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#getTurnkey--">getTurnkey</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#openLed--">openLed</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#scanBarcode--">scanBarcode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#scanBarcodeToBytes--">scanBarcodeToBytes</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#sendData-byte:A-">sendData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#sendDataNotSyn-byte:A-">sendDataNotSyn</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#setBarcodeTypeInSSIID-boolean-">setBarcodeTypeInSSIID</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#setBeep-boolean-">setBeep</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#setEnableKeyMode-boolean-">setEnableKeyMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#setEnableTurnkey-boolean-">setEnableTurnkey</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#setKeyEventCallback-com.rscja.deviceapi.interfaces.KeyEventCallback-">setKeyEventCallback</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#setOnDataChangeListener-com.rscja.deviceapi.BluetoothReader.OnDataChangeListener-">setOnDataChangeListener</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#setParameter-byte:A-byte:A-">setParameter</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#setRemoteBluetoothName-java.lang.String-">setRemoteBluetoothName</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#startScanBarcode--">startScanBarcode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#startScanBarcodeInBlinkMode-com.rscja.deviceapi.BluetoothReader.DecodeCallback-">startScanBarcodeInBlinkMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#startScanBarcodeInTriggleMode-com.rscja.deviceapi.BluetoothReader.DecodeCallback-">startScanBarcodeInTriggleMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-">startScanBTDevices</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#stopScanBarcode--">stopScanBarcode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#stopScanBTDevices--">stopScanBTDevices</a>, <a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#triggerBeep-int-">triggerBeep</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#free--">free</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#getConnectStatus--">getConnectStatus</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IBluetoothReader">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></h3>
<code><a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#disconnect--">disconnect</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#free--">free</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBeep--">getBeep</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBleHardwareVersion--">getBleHardwareVersion</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBluetoothVersion--">getBluetoothVersion</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getConnectStatus--">getConnectStatus</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#sendData-byte:A-">sendData</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#setOnDataChangeListener-com.rscja.deviceapi.BluetoothReader.OnDataChangeListener-">setOnDataChangeListener</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#setRemoteBluetoothName-java.lang.String-">setRemoteBluetoothName</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-">startScanBTDevices</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#stopScanBarcode--">stopScanBarcode</a>, <a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#stopScanBTDevices--">stopScanBTDevices</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IReader">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></h3>
<code><a href="../../../com/rscja/deviceapi/interfaces/IReader.html#blinkOfLed-int-int-int-">blinkOfLed</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#closeLed--">closeLed</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#getBattery--">getBattery</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#getParameter-byte:A-">getParameter</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#getSTM32Version--">getSTM32Version</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#openLed--">openLed</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#scanBarcode--">scanBarcode</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#scanBarcodeToBytes--">scanBarcodeToBytes</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#setBarcodeTypeInSSIID-boolean-">setBarcodeTypeInSSIID</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#setBeep-boolean-">setBeep</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#setKeyEventCallback-com.rscja.deviceapi.interfaces.KeyEventCallback-">setKeyEventCallback</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#setParameter-byte:A-byte:A-">setParameter</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#startScanBarcode--">startScanBarcode</a>, <a href="../../../com/rscja/deviceapi/interfaces/IReader.html#triggerBeep-int-">triggerBeep</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/custom/UHFTemperatureTag.html" title="class in com.rscja.custom">UHFTemperatureTag</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="stopInventory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopInventory</h4>
<pre>public&nbsp;boolean&nbsp;stopInventory()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--">IUHF</a></code></span></div>
<div class="block">停止循环识别，在调用此函数之后应当退出循环获取缓冲区的标签信息的子线程<br>
 Stop auto reading, after call this function to exit sub threads of tag data of buffer. <br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--">stopInventory</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html#stopInventory--">stopInventory</a></code>&nbsp;in class&nbsp;<code><a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="inventoryTemperatureTag-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inventoryTemperatureTag</h4>
<pre>public&nbsp;boolean&nbsp;inventoryTemperatureTag(int&nbsp;power)</pre>
</li>
</ul>
<a name="setInventoryTemperatureTagCallback-com.rscja.custom.UHFTemperatureTag.InventoryTemperatureTagCallback-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setInventoryTemperatureTagCallback</h4>
<pre>public&nbsp;void&nbsp;setInventoryTemperatureTagCallback(<a href="../../../com/rscja/custom/UHFTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom">UHFTemperatureTag.InventoryTemperatureTagCallback</a>&nbsp;inventoryTemperatureTagCallback)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFTemperatureTag.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html" title="class in com.rscja.custom"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/custom/UHFTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/custom/UHFTemperatureTag.html" target="_top">Frames</a></li>
<li><a href="UHFTemperatureTag.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.rscja.team.qcom.deviceapi.BluetoothReader_qcom">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
