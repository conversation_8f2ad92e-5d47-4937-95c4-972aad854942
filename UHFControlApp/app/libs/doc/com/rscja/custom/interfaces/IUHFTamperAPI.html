<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IUHFTamperAPI</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IUHFTamperAPI";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFTamperAPI.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/custom/interfaces/IUHFTamperAPI.html" target="_top">Frames</a></li>
<li><a href="IUHFTamperAPI.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.custom.interfaces</div>
<h2 title="Interface IUHFTamperAPI" class="title">Interface IUHFTamperAPI</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/custom/UHFTamperAPI.html" title="class in com.rscja.custom">UHFTamperAPI</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFTamperAPI_qcom.html" title="class in com.rscja.team.qcom.custom">UHFTamperAPI_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IUHFTamperAPI</span>
extends <a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC">Bank_EPC</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED">Bank_RESERVED</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID">Bank_TID</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER">Bank_USER</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_ACCESS">LockBank_ACCESS</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_EPC">LockBank_EPC</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_KILL">LockBank_KILL</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_TID">LockBank_TID</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_USER">LockBank_USER</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_LOCK">LockMode_LOCK</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_OPEN">LockMode_OPEN</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_PLOCK">LockMode_PLOCK</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_POPEN">LockMode_POPEN</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#UPDATE_UHF">UPDATE_UHF</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/custom/UHFTamperAPI.TamperInfo.html" title="class in com.rscja.custom">UHFTamperAPI.TamperInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTamperAPI.html#readTamperTagFromBuffer--">readTamperTagFromBuffer</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTamperAPI.html#setEPCAndTamperMode--">setEPCAndTamperMode</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUHFOfAndroidUart">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a></h3>
<code><a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#getErrCode--">getErrCode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#getHardwareVersion--">getHardwareVersion</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#isPowerOn--">isPowerOn</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#setOnLowBatteryListener-android.content.Context-com.rscja.deviceapi.entity.LowBatteryEntity-com.rscja.deviceapi.interfaces.OnLowBatteryListener-">setOnLowBatteryListener</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#setPowerOnBySystem-android.content.Context-">setPowerOnBySystem</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#setUart-java.lang.String-">setUart</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#factoryReset--">factoryReset</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#free--">free</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getConnectStatus--">getConnectStatus</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getCW--">getCW</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getFastInventoryMode--">getFastInventoryMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getFrequencyMode--">getFrequencyMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getGen2--">getGen2</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getProtocol--">getProtocol</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getRFLink--">getRFLink</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getTemperature--">getTemperature</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#getVersion--">getVersion</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#init-android.content.Context-">init</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#inventorySingleTag--">inventorySingleTag</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#isInventorying--">isInventorying</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-">killTag</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-">readData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">readData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#readTagFromBuffer--">readTagFromBuffer</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setCW-int-">setCW</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDMode--">setEPCAndTIDMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-int-int-">setEPCAndTIDUserMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCMode--">setEPCMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFastID-boolean-">setFastID</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFastInventoryMode-boolean-">setFastInventoryMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFilter-int-int-int-java.lang.String-">setFilter</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFreHop-float-">setFreHop</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFrequencyMode-int-">setFrequencyMode</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">setGen2</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setProtocol-int-">setProtocol</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setRFLink-int-">setRFLink</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#setTagFocus-boolean-">setTagFocus</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--">startInventoryTag</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--">stopInventory</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot--">uhfJump2Boot</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot-int-">uhfJump2Boot</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStartUpdate--">uhfStartUpdate</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStopUpdate--">uhfStopUpdate</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfUpdating-byte:A-">uhfUpdating</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc</a>, <a href="../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setEPCAndTamperMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCAndTamperMode</h4>
<pre>boolean&nbsp;setEPCAndTamperMode()</pre>
</li>
</ul>
<a name="readTamperTagFromBuffer--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>readTamperTagFromBuffer</h4>
<pre><a href="../../../../com/rscja/custom/UHFTamperAPI.TamperInfo.html" title="class in com.rscja.custom">UHFTamperAPI.TamperInfo</a>&nbsp;readTamperTagFromBuffer()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFTamperAPI.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/custom/interfaces/IUHFTamperAPI.html" target="_top">Frames</a></li>
<li><a href="IUHFTamperAPI.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
