<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IUHFTemperatureTagsAPI</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IUHFTemperatureTagsAPI";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFTemperatureTagsAPI.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/custom/interfaces/IUHFTamperAPI.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/custom/interfaces/IUHFUartFoxconn.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" target="_top">Frames</a></li>
<li><a href="IUHFTemperatureTagsAPI.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.custom.interfaces</div>
<h2 title="Interface IUHFTemperatureTagsAPI" class="title">Interface IUHFTemperatureTagsAPI</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/custom/UHFTemperatureTagsAPI.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI</a>, <a href="../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html" title="class in com.rscja.team.mtk.custom">UHFTemperatureTagsAPI_mtk</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFTemperatureTagsAPI_qcom.html" title="class in com.rscja.team.qcom.custom">UHFTemperatureTagsAPI_qcom</a>, <a href="../../../../com/rscja/team/qcom/custom/UHFTemperatureTagsBLEAPI.html" title="class in com.rscja.team.qcom.custom">UHFTemperatureTagsBLEAPI</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IUHFTemperatureTagsAPI</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom">UHFTemperatureTagsAPI.TagState</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#checkTagState--">checkTagState</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom">UHFTemperatureTagsAPI.TagState</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#checkTagState-int-int-int-java.lang.String-">checkTagState</a></span>(int&nbsp;filterBank,
             int&nbsp;filterPtr,
             int&nbsp;filterCnt,
             java.lang.String&nbsp;filterData)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#initRegFile-int-int-int-java.lang.String-">initRegFile</a></span>(int&nbsp;filterBank,
           int&nbsp;filterPtr,
           int&nbsp;filterCnt,
           java.lang.String&nbsp;filterData)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#readMultipleTemperature-int-int-">readMultipleTemperature</a></span>(int&nbsp;jstart,
                       int&nbsp;jnum)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#readMultipleTemperature-int-int-int-java.lang.String-int-int-">readMultipleTemperature</a></span>(int&nbsp;filterBank,
                       int&nbsp;filterPtr,
                       int&nbsp;filterCnt,
                       java.lang.String&nbsp;filterData,
                       int&nbsp;jstart,
                       int&nbsp;jnum)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#readTagTemp-int-int-int-java.lang.String-float:A-">readTagTemp</a></span>(int&nbsp;filterBank,
           int&nbsp;filterPtr,
           int&nbsp;filterCnt,
           java.lang.String&nbsp;filterData,
           float[]&nbsp;readTemp)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#readTagVoltage-float:A-">readTagVoltage</a></span>(float[]&nbsp;voltage)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#readTagVoltage-int-int-int-java.lang.String-float:A-">readTagVoltage</a></span>(int&nbsp;filterBank,
              int&nbsp;filterPtr,
              int&nbsp;filterCnt,
              java.lang.String&nbsp;filterData,
              float[]&nbsp;voltage)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#setEPCAndTemperatureMode--">setEPCAndTemperatureMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#setInventoryCallback-com.rscja.custom.UHFTemperatureTagsAPI.IUHFInventoryTempCallback-">setInventoryCallback</a></span>(<a href="../../../../com/rscja/custom/UHFTemperatureTagsAPI.IUHFInventoryTempCallback.html" title="interface in com.rscja.custom">UHFTemperatureTagsAPI.IUHFInventoryTempCallback</a>&nbsp;tempCallback)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#startLogging-float-float-int-int-">startLogging</a></span>(float&nbsp;min_temp,
            float&nbsp;max_temp,
            int&nbsp;work_delay,
            int&nbsp;work_interval)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#startLogging-int-int-int-java.lang.String-float-float-int-int-">startLogging</a></span>(int&nbsp;filterBank,
            int&nbsp;filterPtr,
            int&nbsp;filterCnt,
            java.lang.String&nbsp;filterData,
            float&nbsp;min_temp,
            float&nbsp;max_temp,
            int&nbsp;work_delay,
            int&nbsp;work_interval)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#stopLogging-int-int-int-java.lang.String-java.lang.String-">stopLogging</a></span>(int&nbsp;filterBank,
           int&nbsp;filterPtr,
           int&nbsp;filterCnt,
           java.lang.String&nbsp;filterData,
           java.lang.String&nbsp;pwd)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#stopLogging-java.lang.String-">stopLogging</a></span>(java.lang.String&nbsp;pwd)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="initRegFile-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initRegFile</h4>
<pre>boolean&nbsp;initRegFile(int&nbsp;filterBank,
                    int&nbsp;filterPtr,
                    int&nbsp;filterCnt,
                    java.lang.String&nbsp;filterData)</pre>
</li>
</ul>
<a name="readTagTemp-int-int-int-java.lang.String-float:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagTemp</h4>
<pre>boolean&nbsp;readTagTemp(int&nbsp;filterBank,
                    int&nbsp;filterPtr,
                    int&nbsp;filterCnt,
                    java.lang.String&nbsp;filterData,
                    float[]&nbsp;readTemp)</pre>
</li>
</ul>
<a name="startLogging-float-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startLogging</h4>
<pre>boolean&nbsp;startLogging(float&nbsp;min_temp,
                     float&nbsp;max_temp,
                     int&nbsp;work_delay,
                     int&nbsp;work_interval)</pre>
</li>
</ul>
<a name="startLogging-int-int-int-java.lang.String-float-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startLogging</h4>
<pre>boolean&nbsp;startLogging(int&nbsp;filterBank,
                     int&nbsp;filterPtr,
                     int&nbsp;filterCnt,
                     java.lang.String&nbsp;filterData,
                     float&nbsp;min_temp,
                     float&nbsp;max_temp,
                     int&nbsp;work_delay,
                     int&nbsp;work_interval)</pre>
</li>
</ul>
<a name="stopLogging-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopLogging</h4>
<pre>boolean&nbsp;stopLogging(java.lang.String&nbsp;pwd)</pre>
</li>
</ul>
<a name="stopLogging-int-int-int-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopLogging</h4>
<pre>boolean&nbsp;stopLogging(int&nbsp;filterBank,
                    int&nbsp;filterPtr,
                    int&nbsp;filterCnt,
                    java.lang.String&nbsp;filterData,
                    java.lang.String&nbsp;pwd)</pre>
</li>
</ul>
<a name="checkTagState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkTagState</h4>
<pre>java.util.List&lt;<a href="../../../../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom">UHFTemperatureTagsAPI.TagState</a>&gt;&nbsp;checkTagState()</pre>
</li>
</ul>
<a name="checkTagState-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkTagState</h4>
<pre>java.util.List&lt;<a href="../../../../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom">UHFTemperatureTagsAPI.TagState</a>&gt;&nbsp;checkTagState(int&nbsp;filterBank,
                                                             int&nbsp;filterPtr,
                                                             int&nbsp;filterCnt,
                                                             java.lang.String&nbsp;filterData)</pre>
</li>
</ul>
<a name="setEPCAndTemperatureMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCAndTemperatureMode</h4>
<pre>boolean&nbsp;setEPCAndTemperatureMode()</pre>
</li>
</ul>
<a name="setInventoryCallback-com.rscja.custom.UHFTemperatureTagsAPI.IUHFInventoryTempCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInventoryCallback</h4>
<pre>void&nbsp;setInventoryCallback(<a href="../../../../com/rscja/custom/UHFTemperatureTagsAPI.IUHFInventoryTempCallback.html" title="interface in com.rscja.custom">UHFTemperatureTagsAPI.IUHFInventoryTempCallback</a>&nbsp;tempCallback)</pre>
</li>
</ul>
<a name="readMultipleTemperature-int-int-int-java.lang.String-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readMultipleTemperature</h4>
<pre><a href="../../../../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a>&nbsp;readMultipleTemperature(int&nbsp;filterBank,
                                                                      int&nbsp;filterPtr,
                                                                      int&nbsp;filterCnt,
                                                                      java.lang.String&nbsp;filterData,
                                                                      int&nbsp;jstart,
                                                                      int&nbsp;jnum)</pre>
</li>
</ul>
<a name="readMultipleTemperature-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readMultipleTemperature</h4>
<pre><a href="../../../../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a>&nbsp;readMultipleTemperature(int&nbsp;jstart,
                                                                      int&nbsp;jnum)</pre>
</li>
</ul>
<a name="readTagVoltage-int-int-int-java.lang.String-float:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagVoltage</h4>
<pre>boolean&nbsp;readTagVoltage(int&nbsp;filterBank,
                       int&nbsp;filterPtr,
                       int&nbsp;filterCnt,
                       java.lang.String&nbsp;filterData,
                       float[]&nbsp;voltage)</pre>
</li>
</ul>
<a name="readTagVoltage-float:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>readTagVoltage</h4>
<pre>boolean&nbsp;readTagVoltage(float[]&nbsp;voltage)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFTemperatureTagsAPI.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/custom/interfaces/IUHFTamperAPI.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/custom/interfaces/IUHFUartFoxconn.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" target="_top">Frames</a></li>
<li><a href="IUHFTemperatureTagsAPI.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
