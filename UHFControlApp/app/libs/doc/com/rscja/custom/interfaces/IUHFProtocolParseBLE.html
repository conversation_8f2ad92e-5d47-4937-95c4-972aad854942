<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IUHFProtocolParseBLE</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IUHFProtocolParseBLE";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFProtocolParseBLE.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/custom/interfaces/IUHFCSYX.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/custom/interfaces/IUHFTamperAPI.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/custom/interfaces/IUHFProtocolParseBLE.html" target="_top">Frames</a></li>
<li><a href="IUHFProtocolParseBLE.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.custom.interfaces</div>
<h2 title="Interface IUHFProtocolParseBLE" class="title">Interface IUHFProtocolParseBLE</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseBLE.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseBLE</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IUHFProtocolParseBLE</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#checkTagStateRequest-int-int-int-byte:A-">checkTagStateRequest</a></span>(int&nbsp;filterBank,
                    int&nbsp;filterPtr,
                    int&nbsp;filterCnt,
                    byte[]&nbsp;filterData)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#checkTagStateResponse-byte:A-">checkTagStateResponse</a></span>(byte[]&nbsp;receiveData)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#initRegFileRequest-char-int-int-byte:A-">initRegFileRequest</a></span>(char&nbsp;filter_bank,
                  int&nbsp;filter_ptr,
                  int&nbsp;filter_cnt,
                  byte[]&nbsp;filter_data)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#initRegFileResponse-byte:A-">initRegFileResponse</a></span>(byte[]&nbsp;receiveData)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#readMultipleTemperatureRequest-int-int-int-byte:A-int-int-">readMultipleTemperatureRequest</a></span>(int&nbsp;filterBank,
                              int&nbsp;filterPtr,
                              int&nbsp;filterCnt,
                              byte[]&nbsp;filterData,
                              int&nbsp;jstart,
                              int&nbsp;jnum)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#readMultipleTemperatureResponse-byte:A-int:A-int:A-float:A-">readMultipleTemperatureResponse</a></span>(byte[]&nbsp;receiveData,
                               int[]&nbsp;jtotalnum,
                               int[]&nbsp;jreturned,
                               float[]&nbsp;jtemp)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#readTagTempRequest-char-int-int-byte:A-">readTagTempRequest</a></span>(char&nbsp;filter_bank,
                  int&nbsp;filter_ptr,
                  int&nbsp;filter_cnt,
                  byte[]&nbsp;filter_data)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#readTagTempResponse-byte:A-float:A-">readTagTempResponse</a></span>(byte[]&nbsp;receiveData,
                   float[]&nbsp;readTemp)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#readTagVoltageRequest-int-int-int-byte:A-">readTagVoltageRequest</a></span>(int&nbsp;filterBank,
                     int&nbsp;filterPtr,
                     int&nbsp;filterCnt,
                     byte[]&nbsp;filterData)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#readTagVoltageResponse-byte:A-float:A-">readTagVoltageResponse</a></span>(byte[]&nbsp;receiveData,
                      float[]&nbsp;voltage)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#setEPCAndTemperatureModeRequest-char-int-int-int-">setEPCAndTemperatureModeRequest</a></span>(char&nbsp;memory,
                               int&nbsp;address,
                               int&nbsp;length,
                               int&nbsp;dbyte0)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#setEPCAndTemperatureModeResponse-byte:A-">setEPCAndTemperatureModeResponse</a></span>(byte[]&nbsp;receiveData)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#startLoggingRequest-int-int-int-byte:A-float-float-int-int-">startLoggingRequest</a></span>(int&nbsp;filterBank,
                   int&nbsp;filterPtr,
                   int&nbsp;filterCnt,
                   byte[]&nbsp;filterData,
                   float&nbsp;min_temp,
                   float&nbsp;max_temp,
                   int&nbsp;work_delay,
                   int&nbsp;work_interval)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#startLoggingResponse-byte:A-">startLoggingResponse</a></span>(byte[]&nbsp;receiveData)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#stopLoggingRequest-int-int-int-byte:A-byte:A-">stopLoggingRequest</a></span>(int&nbsp;filterBank,
                  int&nbsp;filterPtr,
                  int&nbsp;filterCnt,
                  byte[]&nbsp;filterData,
                  byte[]&nbsp;pwd)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/custom/interfaces/IUHFProtocolParseBLE.html#stopLoggingResponse-byte:A-">stopLoggingResponse</a></span>(byte[]&nbsp;receiveData)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="initRegFileRequest-char-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initRegFileRequest</h4>
<pre>byte[]&nbsp;initRegFileRequest(char&nbsp;filter_bank,
                          int&nbsp;filter_ptr,
                          int&nbsp;filter_cnt,
                          byte[]&nbsp;filter_data)</pre>
</li>
</ul>
<a name="initRegFileResponse-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initRegFileResponse</h4>
<pre>boolean&nbsp;initRegFileResponse(byte[]&nbsp;receiveData)</pre>
</li>
</ul>
<a name="readTagTempRequest-char-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagTempRequest</h4>
<pre>byte[]&nbsp;readTagTempRequest(char&nbsp;filter_bank,
                          int&nbsp;filter_ptr,
                          int&nbsp;filter_cnt,
                          byte[]&nbsp;filter_data)</pre>
</li>
</ul>
<a name="readTagTempResponse-byte:A-float:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagTempResponse</h4>
<pre>boolean&nbsp;readTagTempResponse(byte[]&nbsp;receiveData,
                            float[]&nbsp;readTemp)</pre>
</li>
</ul>
<a name="startLoggingRequest-int-int-int-byte:A-float-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startLoggingRequest</h4>
<pre>byte[]&nbsp;startLoggingRequest(int&nbsp;filterBank,
                           int&nbsp;filterPtr,
                           int&nbsp;filterCnt,
                           byte[]&nbsp;filterData,
                           float&nbsp;min_temp,
                           float&nbsp;max_temp,
                           int&nbsp;work_delay,
                           int&nbsp;work_interval)</pre>
</li>
</ul>
<a name="startLoggingResponse-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startLoggingResponse</h4>
<pre>boolean&nbsp;startLoggingResponse(byte[]&nbsp;receiveData)</pre>
</li>
</ul>
<a name="stopLoggingRequest-int-int-int-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopLoggingRequest</h4>
<pre>byte[]&nbsp;stopLoggingRequest(int&nbsp;filterBank,
                          int&nbsp;filterPtr,
                          int&nbsp;filterCnt,
                          byte[]&nbsp;filterData,
                          byte[]&nbsp;pwd)</pre>
</li>
</ul>
<a name="stopLoggingResponse-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopLoggingResponse</h4>
<pre>boolean&nbsp;stopLoggingResponse(byte[]&nbsp;receiveData)</pre>
</li>
</ul>
<a name="checkTagStateRequest-int-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkTagStateRequest</h4>
<pre>byte[]&nbsp;checkTagStateRequest(int&nbsp;filterBank,
                            int&nbsp;filterPtr,
                            int&nbsp;filterCnt,
                            byte[]&nbsp;filterData)</pre>
</li>
</ul>
<a name="checkTagStateResponse-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkTagStateResponse</h4>
<pre>int&nbsp;checkTagStateResponse(byte[]&nbsp;receiveData)</pre>
</li>
</ul>
<a name="readMultipleTemperatureRequest-int-int-int-byte:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readMultipleTemperatureRequest</h4>
<pre>byte[]&nbsp;readMultipleTemperatureRequest(int&nbsp;filterBank,
                                      int&nbsp;filterPtr,
                                      int&nbsp;filterCnt,
                                      byte[]&nbsp;filterData,
                                      int&nbsp;jstart,
                                      int&nbsp;jnum)</pre>
</li>
</ul>
<a name="readMultipleTemperatureResponse-byte:A-int:A-int:A-float:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readMultipleTemperatureResponse</h4>
<pre>int&nbsp;readMultipleTemperatureResponse(byte[]&nbsp;receiveData,
                                    int[]&nbsp;jtotalnum,
                                    int[]&nbsp;jreturned,
                                    float[]&nbsp;jtemp)</pre>
</li>
</ul>
<a name="readTagVoltageRequest-int-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagVoltageRequest</h4>
<pre>byte[]&nbsp;readTagVoltageRequest(int&nbsp;filterBank,
                             int&nbsp;filterPtr,
                             int&nbsp;filterCnt,
                             byte[]&nbsp;filterData)</pre>
</li>
</ul>
<a name="readTagVoltageResponse-byte:A-float:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagVoltageResponse</h4>
<pre>boolean&nbsp;readTagVoltageResponse(byte[]&nbsp;receiveData,
                               float[]&nbsp;voltage)</pre>
</li>
</ul>
<a name="setEPCAndTemperatureModeRequest-char-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCAndTemperatureModeRequest</h4>
<pre>byte[]&nbsp;setEPCAndTemperatureModeRequest(char&nbsp;memory,
                                       int&nbsp;address,
                                       int&nbsp;length,
                                       int&nbsp;dbyte0)</pre>
</li>
</ul>
<a name="setEPCAndTemperatureModeResponse-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setEPCAndTemperatureModeResponse</h4>
<pre>boolean&nbsp;setEPCAndTemperatureModeResponse(byte[]&nbsp;receiveData)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IUHFProtocolParseBLE.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/rscja/custom/interfaces/IUHFCSYX.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/rscja/custom/interfaces/IUHFTamperAPI.html" title="interface in com.rscja.custom.interfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/custom/interfaces/IUHFProtocolParseBLE.html" target="_top">Frames</a></li>
<li><a href="IUHFProtocolParseBLE.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
