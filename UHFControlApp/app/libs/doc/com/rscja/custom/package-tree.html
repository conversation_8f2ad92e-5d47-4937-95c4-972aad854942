<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>com.rscja.custom Class Hierarchy</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.rscja.custom Class Hierarchy";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/barcode/package-tree.html">Prev</a></li>
<li><a href="../../../com/rscja/custom/interfaces/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/custom/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package com.rscja.custom</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">BluetoothReader_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>)
<ul>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFBLE_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTemperatureTag.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/M775Authenticate.AuthenticateInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">M775Authenticate.AuthenticateInfo</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">RFIDWithUHFJieCe.TemperatureTagInfo</span></a></li>
<li type="circle">com.rscja.deviceapi.<a href="../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">UhfBase</span></a>
<ul>
<li type="circle">com.rscja.team.qcom.ble.<a href="../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble"><span class="typeNameLink">EmptyUhfBle</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a>)
<ul>
<li type="circle">com.rscja.team.qcom.ble.<a href="../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html" title="class in com.rscja.team.qcom.ble"><span class="typeNameLink">RFIDWithUHFBLEN51_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;T&gt;, com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN51.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag.RFIDWithUHFBLEN51</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.ble.<a href="../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html" title="class in com.rscja.team.qcom.ble"><span class="typeNameLink">RFIDWithUHFBLEN52_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;T&gt;, com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN52.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag.RFIDWithUHFBLEN52</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFA4NetWork_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA4NetWork</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFCSYX_A4NetWork.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFCSYX_A4NetWork</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.deviceapi.<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">RFIDWithUHFUART</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/M775Authenticate.html" title="class in com.rscja.custom"><span class="typeNameLink">M775Authenticate</span></a> (implements com.rscja.custom.interfaces.<a href="../../../com/rscja/custom/interfaces/IM775Authenticate.html" title="interface in com.rscja.custom.interfaces">IM775Authenticate</a>)</li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/RFIDWithUHFJieCe.html" title="class in com.rscja.custom"><span class="typeNameLink">RFIDWithUHFJieCe</span></a> (implements com.rscja.custom.interfaces.<a href="../../../com/rscja/custom/interfaces/IRFIDWithUHFJieCe.html" title="interface in com.rscja.custom.interfaces">IRFIDWithUHFJieCe</a>)</li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFCSYX.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFCSYX</span></a> (implements com.rscja.custom.interfaces.<a href="../../../com/rscja/custom/interfaces/IUHFCSYX.html" title="interface in com.rscja.custom.interfaces">IUHFCSYX</a>)</li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTamperAPI.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTamperAPI</span></a> (implements com.rscja.custom.interfaces.<a href="../../../com/rscja/custom/interfaces/IUHFTamperAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTamperAPI</a>)</li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTemperatureTagsAPI.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI</span></a> (implements com.rscja.custom.interfaces.<a href="../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a>)</li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFUartFoxconn.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFUartFoxconn</span></a> (implements com.rscja.custom.interfaces.<a href="../../../com/rscja/custom/interfaces/IUHFUartFoxconn.html" title="interface in com.rscja.custom.interfaces">IUHFUartFoxconn</a>)</li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFXSAPI.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFXSAPI</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUHFInventoryCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFInventoryCallback</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDWithUHFUART_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTemperatureSensors.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureSensors</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFUART_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFUartTemperatureTag.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFUartTemperatureTag</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFUrxNetwork_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxNetwork</a>, java.util.Observer)
<ul>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFSFForUrxNetwork.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFSFForUrxNetwork</span></a></li>
</ul>
</li>
<li type="circle">com.rscja.team.qcom.deviceapi.<a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">RFIDWithUHFUrxUart_qcom</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxUart</a>)
<ul>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFCSYXForURx.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFCSYXForURx</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFCSYX.TagAuthenticationResponseInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFCSYX.TagAuthenticationResponseInfo</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFSFForUrxNetwork.AntInfo</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTamperAPI.TamperInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTamperAPI.TamperInfo</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureSensors.TemperatureTag</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag.TemperatureTagInfo</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI.MultipleTemperatureInfo</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTemperatureTagsAPI.TempertureInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI.TempertureInfo</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">UHFUartTemperatureTag.TemperatureTagInfo</span></a></li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/M775Authenticate.IUHFInventoryCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">M775Authenticate.IUHFInventoryCallback</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFCSYX.IUHFInventoryCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFCSYX.IUHFInventoryCallback</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTemperatureSensors.IUHFInventoryTemperatureTagCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFTemperatureSensors.IUHFInventoryTemperatureTagCallback</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTag.InventoryTemperatureTagCallback</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTemperatureTagsAPI.IUHFInventoryTempCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI.IUHFInventoryTempCallback</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFUartTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom"><span class="typeNameLink">UHFUartTemperatureTag.InventoryTemperatureTagCallback</span></a></li>
</ul>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFXSAPI.Bank.html" title="enum in com.rscja.custom"><span class="typeNameLink">UHFXSAPI.Bank</span></a></li>
<li type="circle">com.rscja.custom.<a href="../../../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom"><span class="typeNameLink">UHFTemperatureTagsAPI.TagState</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/barcode/package-tree.html">Prev</a></li>
<li><a href="../../../com/rscja/custom/interfaces/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/custom/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
