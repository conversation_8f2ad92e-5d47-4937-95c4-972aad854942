<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Package com.rscja.custom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package com.rscja.custom";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/custom/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package com.rscja.custom" class="title">Uses of Package<br>com.rscja.custom</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../com/rscja/custom/package-summary.html">com.rscja.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.custom">com.rscja.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.custom.interfaces">com.rscja.custom.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.custom">com.rscja.team.mtk.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.custom">com.rscja.team.qcom.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/custom/package-summary.html">com.rscja.custom</a> used by <a href="../../../com/rscja/custom/package-summary.html">com.rscja.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/M775Authenticate.html#com.rscja.custom">M775Authenticate</a>
<div class="block">英频杰特殊标签定制<br>
Special label customization<br>

 第一步:通过<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a> 连接读写器。<br>
 Step 1: Connect to the usb of the reader via<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#init-android.content.Context-"><code>RFIDWithUHFUART.init(Context context)</code></a><br><br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数、读写操作。
        盘点标签之前先要设置回调函数<a href="../../../com/rscja/custom/M775Authenticate.html#setInventoryCallback-com.rscja.custom.M775Authenticate.IUHFInventoryCallback-"><code>M775Authenticate.setInventoryCallback(IUHFInventoryCallback)</code></a>,然后调用盘点函数<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#startInventoryTag--"><code>RFIDWithUHFUART.startInventoryTag()</code></a>开始执行盘点。
        注意: 在盘点标签的时候rfid模块只能响应<a href="../../../com/rscja/deviceapi/RFIDWithUHFUART.html#stopInventory--"><code>RFIDWithUHFUART.stopInventory()</code></a>函数。<br>
 For parameter setting, after the connection is successful, call the corresponding function to set parameters and read/write operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/M775Authenticate.AuthenticateInfo.html#com.rscja.custom">M775Authenticate.AuthenticateInfo</a>
<div class="block">AuthenticateInfo对象实体(AuthenticateInfo object entity)</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/M775Authenticate.IUHFInventoryCallback.html#com.rscja.custom">M775Authenticate.IUHFInventoryCallback</a>
<div class="block">盘点回调函数（Inventory callback function）</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/RFIDWithUHFJieCe.html#com.rscja.custom">RFIDWithUHFJieCe</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/RFIDWithUHFJieCe.TemperatureTagInfo.html#com.rscja.custom">RFIDWithUHFJieCe.TemperatureTagInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFCSYX.html#com.rscja.custom">UHFCSYX</a>
<div class="block">MQTT是长沙盈芯，定制接口</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFCSYX.IUHFInventoryCallback.html#com.rscja.custom">UHFCSYX.IUHFInventoryCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFCSYX.TagAuthenticationResponseInfo.html#com.rscja.custom">UHFCSYX.TagAuthenticationResponseInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFCSYXForURx.html#com.rscja.custom">UHFCSYXForURx</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFSFForUrxNetwork.AntInfo.html#com.rscja.custom">UHFSFForUrxNetwork.AntInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTamperAPI.html#com.rscja.custom">UHFTamperAPI</a>
<div class="block">阿联酋Acube 定制接口</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTamperAPI.TamperInfo.html#com.rscja.custom">UHFTamperAPI.TamperInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureSensors.html#com.rscja.custom">UHFTemperatureSensors</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureSensors.IUHFInventoryTemperatureTagCallback.html#com.rscja.custom">UHFTemperatureSensors.IUHFInventoryTemperatureTagCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureSensors.TemperatureTag.html#com.rscja.custom">UHFTemperatureSensors.TemperatureTag</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTag.html#com.rscja.custom">UHFTemperatureTag</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTag.InventoryTemperatureTagCallback.html#com.rscja.custom">UHFTemperatureTag.InventoryTemperatureTagCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTag.TemperatureTagInfo.html#com.rscja.custom">UHFTemperatureTag.TemperatureTagInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.html#com.rscja.custom">UHFTemperatureTagsAPI</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.IUHFInventoryTempCallback.html#com.rscja.custom">UHFTemperatureTagsAPI.IUHFInventoryTempCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html#com.rscja.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.TagState.html#com.rscja.custom">UHFTemperatureTagsAPI.TagState</a>
<div class="block">温度标签状态枚举值</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.TempertureInfo.html#com.rscja.custom">UHFTemperatureTagsAPI.TempertureInfo</a>
<div class="block">温度标签实体对象</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFUartFoxconn.html#com.rscja.custom">UHFUartFoxconn</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFUartTemperatureTag.html#com.rscja.custom">UHFUartTemperatureTag</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFUartTemperatureTag.InventoryTemperatureTagCallback.html#com.rscja.custom">UHFUartTemperatureTag.InventoryTemperatureTagCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFUartTemperatureTag.TemperatureTagInfo.html#com.rscja.custom">UHFUartTemperatureTag.TemperatureTagInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFXSAPI.html#com.rscja.custom">UHFXSAPI</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFXSAPI.Bank.html#com.rscja.custom">UHFXSAPI.Bank</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.custom.interfaces">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/custom/package-summary.html">com.rscja.custom</a> used by <a href="../../../com/rscja/custom/interfaces/package-summary.html">com.rscja.custom.interfaces</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/M775Authenticate.AuthenticateInfo.html#com.rscja.custom.interfaces">M775Authenticate.AuthenticateInfo</a>
<div class="block">AuthenticateInfo对象实体(AuthenticateInfo object entity)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/M775Authenticate.IUHFInventoryCallback.html#com.rscja.custom.interfaces">M775Authenticate.IUHFInventoryCallback</a>
<div class="block">盘点回调函数（Inventory callback function）</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/RFIDWithUHFJieCe.TemperatureTagInfo.html#com.rscja.custom.interfaces">RFIDWithUHFJieCe.TemperatureTagInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFCSYX.IUHFInventoryCallback.html#com.rscja.custom.interfaces">UHFCSYX.IUHFInventoryCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFCSYX.TagAuthenticationResponseInfo.html#com.rscja.custom.interfaces">UHFCSYX.TagAuthenticationResponseInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTamperAPI.TamperInfo.html#com.rscja.custom.interfaces">UHFTamperAPI.TamperInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.IUHFInventoryTempCallback.html#com.rscja.custom.interfaces">UHFTemperatureTagsAPI.IUHFInventoryTempCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html#com.rscja.custom.interfaces">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.TagState.html#com.rscja.custom.interfaces">UHFTemperatureTagsAPI.TagState</a>
<div class="block">温度标签状态枚举值</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/custom/package-summary.html">com.rscja.custom</a> used by <a href="../../../com/rscja/team/mtk/custom/package-summary.html">com.rscja.team.mtk.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/M775Authenticate.AuthenticateInfo.html#com.rscja.team.mtk.custom">M775Authenticate.AuthenticateInfo</a>
<div class="block">AuthenticateInfo对象实体(AuthenticateInfo object entity)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/M775Authenticate.IUHFInventoryCallback.html#com.rscja.team.mtk.custom">M775Authenticate.IUHFInventoryCallback</a>
<div class="block">盘点回调函数（Inventory callback function）</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.IUHFInventoryTempCallback.html#com.rscja.team.mtk.custom">UHFTemperatureTagsAPI.IUHFInventoryTempCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html#com.rscja.team.mtk.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.TagState.html#com.rscja.team.mtk.custom">UHFTemperatureTagsAPI.TagState</a>
<div class="block">温度标签状态枚举值</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/custom/package-summary.html">com.rscja.custom</a> used by <a href="../../../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/M775Authenticate.AuthenticateInfo.html#com.rscja.team.qcom.custom">M775Authenticate.AuthenticateInfo</a>
<div class="block">AuthenticateInfo对象实体(AuthenticateInfo object entity)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/M775Authenticate.IUHFInventoryCallback.html#com.rscja.team.qcom.custom">M775Authenticate.IUHFInventoryCallback</a>
<div class="block">盘点回调函数（Inventory callback function）</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/RFIDWithUHFJieCe.TemperatureTagInfo.html#com.rscja.team.qcom.custom">RFIDWithUHFJieCe.TemperatureTagInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFCSYX.IUHFInventoryCallback.html#com.rscja.team.qcom.custom">UHFCSYX.IUHFInventoryCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFCSYX.TagAuthenticationResponseInfo.html#com.rscja.team.qcom.custom">UHFCSYX.TagAuthenticationResponseInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTamperAPI.TamperInfo.html#com.rscja.team.qcom.custom">UHFTamperAPI.TamperInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.IUHFInventoryTempCallback.html#com.rscja.team.qcom.custom">UHFTemperatureTagsAPI.IUHFInventoryTempCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html#com.rscja.team.qcom.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/custom/class-use/UHFTemperatureTagsAPI.TagState.html#com.rscja.team.qcom.custom">UHFTemperatureTagsAPI.TagState</a>
<div class="block">温度标签状态枚举值</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/custom/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
