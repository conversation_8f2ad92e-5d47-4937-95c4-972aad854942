<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>RFIDWithUHFJieCe.TemperatureTagInfo</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RFIDWithUHFJieCe.TemperatureTagInfo";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithUHFJieCe.TemperatureTagInfo.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.html" title="class in com.rscja.custom"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/custom/UHFCSYX.html" title="class in com.rscja.custom"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html" target="_top">Frames</a></li>
<li><a href="RFIDWithUHFJieCe.TemperatureTagInfo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.custom</div>
<h2 title="Class RFIDWithUHFJieCe.TemperatureTagInfo" class="title">Class RFIDWithUHFJieCe.TemperatureTagInfo</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.custom.RFIDWithUHFJieCe.TemperatureTagInfo</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.html" title="class in com.rscja.custom">RFIDWithUHFJieCe</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">RFIDWithUHFJieCe.TemperatureTagInfo</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#TemperatureTagInfo--">TemperatureTagInfo</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getAnt--">getAnt</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getCalibrationData--">getCalibrationData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getEpc--">getEpc</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getRssi--">getRssi</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getRssiCode--">getRssiCode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getSensorCode--">getSensorCode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#getTempeCode--">getTempeCode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#setAnt-int-">setAnt</a></span>(int&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#setCalibrationData-java.lang.String-">setCalibrationData</a></span>(java.lang.String&nbsp;calibrationData)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#setEpc-java.lang.String-">setEpc</a></span>(java.lang.String&nbsp;epc)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#setRssi-java.lang.String-">setRssi</a></span>(java.lang.String&nbsp;rssi)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#setRssiCode-java.lang.String-">setRssiCode</a></span>(java.lang.String&nbsp;rssiCode)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#setSensorCode-java.lang.String-">setSensorCode</a></span>(java.lang.String&nbsp;sensorCode)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html#setTempeCode-java.lang.String-">setTempeCode</a></span>(java.lang.String&nbsp;tempeCode)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TemperatureTagInfo--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TemperatureTagInfo</h4>
<pre>public&nbsp;TemperatureTagInfo()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getEpc--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEpc</h4>
<pre>public&nbsp;java.lang.String&nbsp;getEpc()</pre>
</li>
</ul>
<a name="setEpc-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEpc</h4>
<pre>public&nbsp;void&nbsp;setEpc(java.lang.String&nbsp;epc)</pre>
</li>
</ul>
<a name="getCalibrationData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalibrationData</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCalibrationData()</pre>
</li>
</ul>
<a name="setCalibrationData-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalibrationData</h4>
<pre>public&nbsp;void&nbsp;setCalibrationData(java.lang.String&nbsp;calibrationData)</pre>
</li>
</ul>
<a name="getSensorCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSensorCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getSensorCode()</pre>
</li>
</ul>
<a name="setSensorCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSensorCode</h4>
<pre>public&nbsp;void&nbsp;setSensorCode(java.lang.String&nbsp;sensorCode)</pre>
</li>
</ul>
<a name="getRssiCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRssiCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getRssiCode()</pre>
</li>
</ul>
<a name="setRssiCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRssiCode</h4>
<pre>public&nbsp;void&nbsp;setRssiCode(java.lang.String&nbsp;rssiCode)</pre>
</li>
</ul>
<a name="getTempeCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTempeCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTempeCode()</pre>
</li>
</ul>
<a name="setTempeCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTempeCode</h4>
<pre>public&nbsp;void&nbsp;setTempeCode(java.lang.String&nbsp;tempeCode)</pre>
</li>
</ul>
<a name="getRssi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRssi</h4>
<pre>public&nbsp;java.lang.String&nbsp;getRssi()</pre>
</li>
</ul>
<a name="setRssi-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRssi</h4>
<pre>public&nbsp;void&nbsp;setRssi(java.lang.String&nbsp;rssi)</pre>
</li>
</ul>
<a name="getAnt--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnt</h4>
<pre>public&nbsp;int&nbsp;getAnt()</pre>
</li>
</ul>
<a name="setAnt-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setAnt</h4>
<pre>public&nbsp;void&nbsp;setAnt(int&nbsp;ant)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithUHFJieCe.TemperatureTagInfo.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/custom/RFIDWithUHFJieCe.html" title="class in com.rscja.custom"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/custom/UHFCSYX.html" title="class in com.rscja.custom"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html" target="_top">Frames</a></li>
<li><a href="RFIDWithUHFJieCe.TemperatureTagInfo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
