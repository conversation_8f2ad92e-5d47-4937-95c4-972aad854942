<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>com.rscja.custom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../com/rscja/custom/package-summary.html" target="classFrame">com.rscja.custom</a></h1>
<div class="indexContainer">
<h2 title="Interfaces">Interfaces</h2>
<ul title="Interfaces">
<li><a href="M775Authenticate.IUHFInventoryCallback.html" title="interface in com.rscja.custom" target="classFrame"><span class="interfaceName">M775Authenticate.IUHFInventoryCallback</span></a></li>
<li><a href="UHFCSYX.IUHFInventoryCallback.html" title="interface in com.rscja.custom" target="classFrame"><span class="interfaceName">UHFCSYX.IUHFInventoryCallback</span></a></li>
<li><a href="UHFTemperatureSensors.IUHFInventoryTemperatureTagCallback.html" title="interface in com.rscja.custom" target="classFrame"><span class="interfaceName">UHFTemperatureSensors.IUHFInventoryTemperatureTagCallback</span></a></li>
<li><a href="UHFTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom" target="classFrame"><span class="interfaceName">UHFTemperatureTag.InventoryTemperatureTagCallback</span></a></li>
<li><a href="UHFTemperatureTagsAPI.IUHFInventoryTempCallback.html" title="interface in com.rscja.custom" target="classFrame"><span class="interfaceName">UHFTemperatureTagsAPI.IUHFInventoryTempCallback</span></a></li>
<li><a href="UHFUartTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom" target="classFrame"><span class="interfaceName">UHFUartTemperatureTag.InventoryTemperatureTagCallback</span></a></li>
</ul>
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="M775Authenticate.html" title="class in com.rscja.custom" target="classFrame">M775Authenticate</a></li>
<li><a href="M775Authenticate.AuthenticateInfo.html" title="class in com.rscja.custom" target="classFrame">M775Authenticate.AuthenticateInfo</a></li>
<li><a href="RFIDWithUHFJieCe.html" title="class in com.rscja.custom" target="classFrame">RFIDWithUHFJieCe</a></li>
<li><a href="RFIDWithUHFJieCe.TemperatureTagInfo.html" title="class in com.rscja.custom" target="classFrame">RFIDWithUHFJieCe.TemperatureTagInfo</a></li>
<li><a href="UHFCSYX.html" title="class in com.rscja.custom" target="classFrame">UHFCSYX</a></li>
<li><a href="UHFCSYX_A4NetWork.html" title="class in com.rscja.custom" target="classFrame">UHFCSYX_A4NetWork</a></li>
<li><a href="UHFCSYX.TagAuthenticationResponseInfo.html" title="class in com.rscja.custom" target="classFrame">UHFCSYX.TagAuthenticationResponseInfo</a></li>
<li><a href="UHFCSYXForURx.html" title="class in com.rscja.custom" target="classFrame">UHFCSYXForURx</a></li>
<li><a href="UHFSFForUrxNetwork.html" title="class in com.rscja.custom" target="classFrame">UHFSFForUrxNetwork</a></li>
<li><a href="UHFTamperAPI.html" title="class in com.rscja.custom" target="classFrame">UHFTamperAPI</a></li>
<li><a href="UHFTamperAPI.TamperInfo.html" title="class in com.rscja.custom" target="classFrame">UHFTamperAPI.TamperInfo</a></li>
<li><a href="UHFTemperatureSensors.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureSensors</a></li>
<li><a href="UHFTemperatureSensors.TemperatureTag.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureSensors.TemperatureTag</a></li>
<li><a href="UHFTemperatureTag.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureTag</a></li>
<li><a href="UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureTag.TemperatureTagInfo</a></li>
<li><a href="UHFTemperatureTagsAPI.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureTagsAPI</a></li>
<li><a href="UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a></li>
<li><a href="UHFTemperatureTagsAPI.TempertureInfo.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureTagsAPI.TempertureInfo</a></li>
<li><a href="UHFUartFoxconn.html" title="class in com.rscja.custom" target="classFrame">UHFUartFoxconn</a></li>
<li><a href="UHFUartTemperatureTag.html" title="class in com.rscja.custom" target="classFrame">UHFUartTemperatureTag</a></li>
<li><a href="UHFUartTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom" target="classFrame">UHFUartTemperatureTag.TemperatureTagInfo</a></li>
<li><a href="UHFXSAPI.html" title="class in com.rscja.custom" target="classFrame">UHFXSAPI</a></li>
</ul>
<h2 title="Enums">Enums</h2>
<ul title="Enums">
<li><a href="UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom" target="classFrame">UHFTemperatureTagsAPI.TagState</a></li>
<li><a href="UHFXSAPI.Bank.html" title="enum in com.rscja.custom" target="classFrame">UHFXSAPI.Bank</a></li>
</ul>
</div>
</body>
</html>
