<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>UHFSFForUrxNetwork</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UHFSFForUrxNetwork";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFSFForUrxNetwork.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/custom/UHFCSYXForURx.html" title="class in com.rscja.custom"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/custom/UHFSFForUrxNetwork.html" target="_top">Frames</a></li>
<li><a href="UHFSFForUrxNetwork.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.custom</div>
<h2 title="Class UHFSFForUrxNetwork" class="title">Class UHFSFForUrxNetwork</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">com.rscja.deviceapi.UhfBase</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi.RFIDWithUHFUrxNetwork_qcom</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.custom.UHFSFForUrxNetwork</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a>, <a href="../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFUrxNetwork</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURx</a>, java.util.Observer</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">UHFSFForUrxNetwork</span>
extends <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html" title="class in com.rscja.custom">UHFSFForUrxNetwork.AntInfo</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.rscja.deviceapi.<a href="../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></h3>
<code><a href="../../../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC">Bank_EPC</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED">Bank_RESERVED</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID">Bank_TID</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER">Bank_USER</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_ACCESS">LockBank_ACCESS</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_EPC">LockBank_EPC</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_KILL">LockBank_KILL</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_TID">LockBank_TID</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_USER">LockBank_USER</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_LOCK">LockMode_LOCK</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_OPEN">LockMode_OPEN</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_PLOCK">LockMode_PLOCK</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_POPEN">LockMode_POPEN</a>, <a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#UPDATE_UHF">UPDATE_UHF</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/UHFSFForUrxNetwork.html#UHFSFForUrxNetwork--">UHFSFForUrxNetwork</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html" title="class in com.rscja.custom">UHFSFForUrxNetwork.AntInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/custom/UHFSFForUrxNetwork.html#checkAntStatus-int-int-">checkAntStatus</a></span>(int&nbsp;time,
              int&nbsp;antCount)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.qcom.deviceapi.RFIDWithUHFUrxNetwork_qcom">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.qcom.deviceapi.<a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxNetwork_qcom</a></h3>
<code><a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#disableBeep--">disableBeep</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#enableBeep--">enableBeep</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#eraseData-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#factoryReset--">factoryReset</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#free--">free</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getANT--">getANT</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getAntennaConnectState--">getAntennaConnectState</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getAntennaPower--">getAntennaPower</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getConnectStatus--">getConnectStatus</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getCW--">getCW</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getDestIP--">getDestIP</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getErrCode--">getErrCode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getFastInventoryMode--">getFastInventoryMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getFrequencyMode--">getFrequencyMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getGen2--">getGen2</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getInputStatus--">getInputStatus</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getPower--">getPower</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getProtocol--">getProtocol</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getRFLink--">getRFLink</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getSTM32Version--">getSTM32Version</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getTemperature--">getTemperature</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getTriggerWorkModePara--">getTriggerWorkModePara</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getUhfReaderIP--">getUhfReaderIP</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getVersion--">getVersion</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#getWorkMode--">getWorkMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#init-android.content.Context-">init</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#init-java.lang.String-">init</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#init-java.lang.String-int-">init</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#init-java.lang.String-int-boolean-">init</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#inventorySingleTag--">inventorySingleTag</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#isEnableBeep--">isEnableBeep</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#isInventorying--">isInventorying</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#killTag-java.lang.String-">killTag</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#readData-java.lang.String-int-int-int-">readData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">readData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#readTagFromBuffer--">readTagFromBuffer</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setANT-java.util.List-">setANT</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setCW-int-">setCW</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setDestIP-com.rscja.deviceapi.entity.ReaderIPEntity-">setDestIP</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setEPCAndTIDMode--">setEPCAndTIDMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setEPCAndTIDUserMode-int-int-">setEPCAndTIDUserMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setEPCMode--">setEPCMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setFastID-boolean-">setFastID</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setFastInventoryMode-boolean-">setFastInventoryMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setFilter-int-int-int-java.lang.String-">setFilter</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setFreHop-float-">setFreHop</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setFrequencyMode-byte-">setFrequencyMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setFrequencyMode-int-">setFrequencyMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">setGen2</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setIPAndPort-java.lang.String-int-">setIPAndPort</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setPower-int-">setPower</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setProtocol-int-">setProtocol</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setRelayStatus-byte-">setRelayStatus</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setRFLink-int-">setRFLink</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setTagFocus-boolean-">setTagFocus</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setTriggerWorkModePara-int-int-int-int-">setTriggerWorkModePara</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setUhfReaderIP-com.rscja.deviceapi.entity.ReaderIPEntity-">setUhfReaderIP</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#setWorkMode-int-">setWorkMode</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#startInventoryTag--">startInventoryTag</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#startInventoryTag-com.rscja.deviceapi.entity.InventoryParameter-">startInventoryTag</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#stopInventory--">stopInventory</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfJump2Boot--">uhfJump2Boot</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfJump2Boot-int-">uhfJump2Boot</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfJump2BootSTM32--">uhfJump2BootSTM32</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfStartUpdate--">uhfStartUpdate</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfStopUpdate--">uhfStopUpdate</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#uhfUpdating-byte:A-">uhfUpdating</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#update-java.util.Observable-java.lang.Object-">update</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc</a>, <a href="../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.UhfBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.deviceapi.<a href="../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></h3>
<code><a href="../../../com/rscja/deviceapi/UhfBase.html#readTcpServiceState--">readTcpServiceState</a>, <a href="../../../com/rscja/deviceapi/UhfBase.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a>, <a href="../../../com/rscja/deviceapi/UhfBase.html#setOnLowBatteryListener-android.content.Context-com.rscja.deviceapi.entity.LowBatteryEntity-com.rscja.deviceapi.interfaces.OnLowBatteryListener-">setOnLowBatteryListener</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="UHFSFForUrxNetwork--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UHFSFForUrxNetwork</h4>
<pre>public&nbsp;UHFSFForUrxNetwork()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="checkAntStatus-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>checkAntStatus</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html" title="class in com.rscja.custom">UHFSFForUrxNetwork.AntInfo</a>&gt;&nbsp;checkAntStatus(int&nbsp;time,
                                                                 int&nbsp;antCount)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFSFForUrxNetwork.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/custom/UHFCSYXForURx.html" title="class in com.rscja.custom"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/custom/UHFSFForUrxNetwork.AntInfo.html" title="class in com.rscja.custom"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/custom/UHFSFForUrxNetwork.html" target="_top">Frames</a></li>
<li><a href="UHFSFForUrxNetwork.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
