<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>DeviceConfiguration_mtk</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DeviceConfiguration_mtk";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":10,"i12":9,"i13":10,"i14":9,"i15":9,"i16":10,"i17":10,"i18":10,"i19":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DeviceConfiguration_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.Platform.html" title="class in com.rscja.team.mtk"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/team/mtk/DeviceConfiguration_mtk.html" target="_top">Frames</a></li>
<li><a href="DeviceConfiguration_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.mtk</div>
<h2 title="Class DeviceConfiguration_mtk" class="title">Class DeviceConfiguration_mtk</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.mtk.DeviceConfiguration_mtk</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DeviceConfiguration_mtk</span>
extends java.lang.Object</pre>
<div class="block">设备参数配置类<br>
 Device parameter config type<br></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>liuruifeng</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.Platform.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk.Platform</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C4000_6577">C4000_6577</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C4000_6582">C4000_6582</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C4050_6582">C4050_6582</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C4050_8909">C4050_8909</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C5_6765">C5_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C6000_6735">C6000_6735</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C6000_6762">C6000_6762</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C6000_8909">C6000_8909</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C70_6735">C70_6735</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C70_6763">C70_6763</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C70_6765">C70_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C71_6763">C71_6763</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C71_6765">C71_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C72_6735">C72_6735</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C72_6763">C72_6763</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C72_6765">C72_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C75_6765">C75_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C76_6735">C76_6735</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C76_6765">C76_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#C90_6762_10">C90_6762_10</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#CW103_6765">CW103_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#CW95_6762">CW95_6762</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#H100_6735">H100_6735</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#isLoadLibrary">isLoadLibrary</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builder1DConfiguration--">builder1DConfiguration</a></span>()</code>
<div class="block">创建一维条码配置信息<br>
 Create 1D barcode config infor.<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builder2DConfiguration--">builder2DConfiguration</a></span>()</code>
<div class="block">创建二维条码配置信息<br>
 Create 2D barcode config infor.<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderA8ExtendedUartConfiguration--">builderA8ExtendedUartConfiguration</a></span>()</code>
<div class="block">创建A8扩展串口配置信息<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderBDConfiguration--">builderBDConfiguration</a></span>()</code>
<div class="block">创建北斗配置信息<br>
 Create Beidou config infor.<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderDefaultConfiguration--">builderDefaultConfiguration</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderFingerprintConfiguration--">builderFingerprintConfiguration</a></span>()</code>
<div class="block">创建Fingerprint配置信息<br>
 Create Fingerprint config infor.<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderInfraredConfiguration--">builderInfraredConfiguration</a></span>()</code>
<div class="block">创建红外配置信息<br>
 Create infrared config infor.<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderLFConfiguration--">builderLFConfiguration</a></span>()</code>
<div class="block">创建低频配置信息<br>
 Create LF config infor.<br></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderPrinterConfiguration--">builderPrinterConfiguration</a></span>()</code>
<div class="block">创建打印机配置信息<br>
 Create printer config infor.<br></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderRFIDConfiguration--">builderRFIDConfiguration</a></span>()</code>
<div class="block">创建RFID配置信息<br>
 Create RFID config infor.<br></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#builderUHFConfiguration--">builderUHFConfiguration</a></span>()</code>
<div class="block">创建UHF配置信息<br>
 Create UHF config infor.<br></div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#getBaudrate--">getBaudrate</a></span>()</code>
<div class="block">获取波特率<br>
 Acquire baud rate<br></div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static com.rscja.team.mtk.DeviceConfiguration_mtk.DeviceInfo</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#getDeviceInfoFromFile--">getDeviceInfoFromFile</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#getDeviceName--">getDeviceName</a></span>()</code>
<div class="block">获取设备名<br>
 Acquire device name<br></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#getModel--">getModel</a></span>()</code>
<div class="block">获取设备型号<br>
 Acquire device model NO.<br></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#getPlatform--">getPlatform</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#getUart--">getUart</a></span>()</code>
<div class="block">获取串口路径<br>
 Acquire serial port directory<br></div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#setBaudrate-int-">setBaudrate</a></span>(int&nbsp;baudrate)</code>
<div class="block">设置波特率<br>
 Setup baudrate<br></div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#setDeviceName-java.lang.String-">setDeviceName</a></span>(java.lang.String&nbsp;deviceName)</code>
<div class="block">设置设备名<br>
 Setup device name<br></div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html#setUart-java.lang.String-">setUart</a></span>(java.lang.String&nbsp;uart)</code>
<div class="block">设置串口路径<br>
 Setup serial port directory.<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="isLoadLibrary">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isLoadLibrary</h4>
<pre>public static&nbsp;boolean isLoadLibrary</pre>
</li>
</ul>
<a name="C6000_6735">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C6000_6735</h4>
<pre>public static&nbsp;java.lang.String C6000_6735</pre>
</li>
</ul>
<a name="H100_6735">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>H100_6735</h4>
<pre>public static&nbsp;java.lang.String H100_6735</pre>
</li>
</ul>
<a name="C70_6735">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C70_6735</h4>
<pre>public static&nbsp;java.lang.String C70_6735</pre>
</li>
</ul>
<a name="C72_6735">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C72_6735</h4>
<pre>public static&nbsp;java.lang.String C72_6735</pre>
</li>
</ul>
<a name="C76_6735">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C76_6735</h4>
<pre>public static&nbsp;java.lang.String C76_6735</pre>
</li>
</ul>
<a name="C70_6763">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C70_6763</h4>
<pre>public static&nbsp;java.lang.String C70_6763</pre>
</li>
</ul>
<a name="C71_6763">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C71_6763</h4>
<pre>public static&nbsp;java.lang.String C71_6763</pre>
</li>
</ul>
<a name="C72_6763">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C72_6763</h4>
<pre>public static&nbsp;java.lang.String C72_6763</pre>
</li>
</ul>
<a name="C70_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C70_6765</h4>
<pre>public static&nbsp;java.lang.String C70_6765</pre>
</li>
</ul>
<a name="C71_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C71_6765</h4>
<pre>public static&nbsp;java.lang.String C71_6765</pre>
</li>
</ul>
<a name="C72_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C72_6765</h4>
<pre>public static&nbsp;java.lang.String C72_6765</pre>
</li>
</ul>
<a name="C75_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C75_6765</h4>
<pre>public static&nbsp;java.lang.String C75_6765</pre>
</li>
</ul>
<a name="C76_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C76_6765</h4>
<pre>public static&nbsp;java.lang.String C76_6765</pre>
</li>
</ul>
<a name="C5_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C5_6765</h4>
<pre>public static&nbsp;java.lang.String C5_6765</pre>
</li>
</ul>
<a name="CW103_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CW103_6765</h4>
<pre>public static&nbsp;java.lang.String CW103_6765</pre>
</li>
</ul>
<a name="C6000_6762">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C6000_6762</h4>
<pre>public static final&nbsp;java.lang.String C6000_6762</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.team.mtk.DeviceConfiguration_mtk.C6000_6762">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C90_6762_10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C90_6762_10</h4>
<pre>public static final&nbsp;java.lang.String C90_6762_10</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.rscja.team.mtk.DeviceConfiguration_mtk.C90_6762_10">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C6000_8909">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C6000_8909</h4>
<pre>public static&nbsp;java.lang.String C6000_8909</pre>
</li>
</ul>
<a name="C4050_8909">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C4050_8909</h4>
<pre>public static&nbsp;java.lang.String C4050_8909</pre>
</li>
</ul>
<a name="C4000_6582">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C4000_6582</h4>
<pre>public static&nbsp;java.lang.String C4000_6582</pre>
</li>
</ul>
<a name="C4050_6582">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C4050_6582</h4>
<pre>public static&nbsp;java.lang.String C4050_6582</pre>
</li>
</ul>
<a name="C4000_6577">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C4000_6577</h4>
<pre>public static&nbsp;java.lang.String C4000_6577</pre>
</li>
</ul>
<a name="CW95_6762">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CW95_6762</h4>
<pre>public static&nbsp;java.lang.String CW95_6762</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getModel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModel</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getModel()</pre>
<div class="block">获取设备型号<br>
 Acquire device model NO.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="builder1DConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>builder1DConfiguration</h4>
<pre>public static&nbsp;<a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a>&nbsp;builder1DConfiguration()
                                                      throws <a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">创建一维条码配置信息<br>
 Create 1D barcode config infor.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code></dd>
</dl>
</li>
</ul>
<a name="builder2DConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>builder2DConfiguration</h4>
<pre>public static&nbsp;<a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a>&nbsp;builder2DConfiguration()
                                                      throws <a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">创建二维条码配置信息<br>
 Create 2D barcode config infor.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code></dd>
</dl>
</li>
</ul>
<a name="builderRFIDConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>builderRFIDConfiguration</h4>
<pre>public static&nbsp;<a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a>&nbsp;builderRFIDConfiguration()
                                                        throws <a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">创建RFID配置信息<br>
 Create RFID config infor.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code></dd>
</dl>
</li>
</ul>
<a name="builderA8ExtendedUartConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>builderA8ExtendedUartConfiguration</h4>
<pre>public static&nbsp;<a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a>&nbsp;builderA8ExtendedUartConfiguration()
                                                                  throws <a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">创建A8扩展串口配置信息<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code></dd>
</dl>
</li>
</ul>
<a name="builderUHFConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>builderUHFConfiguration</h4>
<pre>public static&nbsp;<a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a>&nbsp;builderUHFConfiguration()
                                                       throws <a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">创建UHF配置信息<br>
 Create UHF config infor.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code></dd>
</dl>
</li>
</ul>
<a name="builderPrinterConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>builderPrinterConfiguration</h4>
<pre>public static&nbsp;<a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a>&nbsp;builderPrinterConfiguration()
                                                           throws <a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">创建打印机配置信息<br>
 Create printer config infor.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code></dd>
</dl>
</li>
</ul>
<a name="builderFingerprintConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>builderFingerprintConfiguration</h4>
<pre>public static&nbsp;<a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a>&nbsp;builderFingerprintConfiguration()
                                                               throws <a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">创建Fingerprint配置信息<br>
 Create Fingerprint config infor.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code></dd>
</dl>
</li>
</ul>
<a name="builderLFConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>builderLFConfiguration</h4>
<pre>public static&nbsp;<a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a>&nbsp;builderLFConfiguration()
                                                      throws <a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">创建低频配置信息<br>
 Create LF config infor.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code></dd>
</dl>
</li>
</ul>
<a name="builderBDConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>builderBDConfiguration</h4>
<pre>public static&nbsp;<a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a>&nbsp;builderBDConfiguration()
                                                      throws <a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">创建北斗配置信息<br>
 Create Beidou config infor.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code></dd>
</dl>
</li>
</ul>
<a name="builderInfraredConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>builderInfraredConfiguration</h4>
<pre>public static&nbsp;<a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a>&nbsp;builderInfraredConfiguration()
                                                            throws <a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">创建红外配置信息<br>
 Create infrared config infor.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code></dd>
</dl>
</li>
</ul>
<a name="builderDefaultConfiguration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>builderDefaultConfiguration</h4>
<pre>public static&nbsp;<a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk">DeviceConfiguration_mtk</a>&nbsp;builderDefaultConfiguration()
                                                           throws <a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code></dd>
</dl>
</li>
</ul>
<a name="setDeviceName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeviceName</h4>
<pre>public&nbsp;void&nbsp;setDeviceName(java.lang.String&nbsp;deviceName)</pre>
<div class="block">设置设备名<br>
 Setup device name<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>deviceName</code> - 设备名，如："C4000"<br>
        Device name, e.g.: C4000.<br></dd>
</dl>
</li>
</ul>
<a name="setUart-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUart</h4>
<pre>public&nbsp;void&nbsp;setUart(java.lang.String&nbsp;uart)</pre>
<div class="block">设置串口路径<br>
 Setup serial port directory.<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>uart</code> - 串口路径，如："/dev/ttyMT3"<br>
        Serial port directory, e.g.: '/dev/ttyMT3'<br></dd>
</dl>
</li>
</ul>
<a name="setBaudrate-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaudrate</h4>
<pre>public&nbsp;void&nbsp;setBaudrate(int&nbsp;baudrate)</pre>
<div class="block">设置波特率<br>
 Setup baudrate<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baudrate</code> - 波特率，如：9600<br>
        Baud rate, e.g.:9600<br></dd>
</dl>
</li>
</ul>
<a name="getDeviceName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeviceName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDeviceName()</pre>
<div class="block">获取设备名<br>
 Acquire device name<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>设备名<br></dd>
</dl>
</li>
</ul>
<a name="getUart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUart</h4>
<pre>public&nbsp;java.lang.String&nbsp;getUart()</pre>
<div class="block">获取串口路径<br>
 Acquire serial port directory<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>串口路径<br></dd>
</dl>
</li>
</ul>
<a name="getBaudrate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaudrate</h4>
<pre>public&nbsp;int&nbsp;getBaudrate()</pre>
<div class="block">获取波特率<br>
 Acquire baud rate<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>波特率<br></dd>
</dl>
</li>
</ul>
<a name="getPlatform--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlatform</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getPlatform()</pre>
</li>
</ul>
<a name="getDeviceInfoFromFile--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getDeviceInfoFromFile</h4>
<pre>public static&nbsp;com.rscja.team.mtk.DeviceConfiguration_mtk.DeviceInfo&nbsp;getDeviceInfoFromFile()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DeviceConfiguration_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../com/rscja/team/mtk/DeviceConfiguration_mtk.Platform.html" title="class in com.rscja.team.mtk"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/rscja/team/mtk/DeviceConfiguration_mtk.html" target="_top">Frames</a></li>
<li><a href="DeviceConfiguration_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
