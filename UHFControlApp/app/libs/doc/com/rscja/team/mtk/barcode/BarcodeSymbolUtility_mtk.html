<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>BarcodeSymbolUtility_mtk</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BarcodeSymbolUtility_mtk";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BarcodeSymbolUtility_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeFactory_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html" target="_top">Frames</a></li>
<li><a href="BarcodeSymbolUtility_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.mtk.barcode</div>
<h2 title="Class BarcodeSymbolUtility_mtk" class="title">Class BarcodeSymbolUtility_mtk</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeSymbolUtility</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">BarcodeSymbolUtility_mtk</span>
extends java.lang.Object
implements <a href="../../../../../com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeSymbolUtility</a></pre>
<div class="block">条码符号工具类</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#INT_GM">INT_GM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#INT_INDUSTRIAL_2_5">INT_INDUSTRIAL_2_5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#INT_ISBT_128">INT_ISBT_128</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#INT_OCR">INT_OCR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#INT_STANDARD_2_5">INT_STANDARD_2_5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#STR_GM">STR_GM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#STR_INDUSTRIAL_2_5">STR_INDUSTRIAL_2_5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#STR_ISBT_128">STR_ISBT_128</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#STR_OCR">STR_OCR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#STR_STANDARD_2_5">STR_STANDARD_2_5</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#getBarcodeName-int-">getBarcodeName</a></span>(int&nbsp;symbol)</code>
<div class="block">获取条码名称(get barcode name)</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeSymbolUtility_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="STR_ISBT_128">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_ISBT_128</h4>
<pre>public static final&nbsp;java.lang.String STR_ISBT_128</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.STR_ISBT_128">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_STANDARD_2_5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_STANDARD_2_5</h4>
<pre>public static final&nbsp;java.lang.String STR_STANDARD_2_5</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.STR_STANDARD_2_5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_INDUSTRIAL_2_5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_INDUSTRIAL_2_5</h4>
<pre>public static final&nbsp;java.lang.String STR_INDUSTRIAL_2_5</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.STR_INDUSTRIAL_2_5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_GM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_GM</h4>
<pre>public static final&nbsp;java.lang.String STR_GM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.STR_GM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_OCR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_OCR</h4>
<pre>public static final&nbsp;java.lang.String STR_OCR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.STR_OCR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_ISBT_128">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_ISBT_128</h4>
<pre>public static final&nbsp;int INT_ISBT_128</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.INT_ISBT_128">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_STANDARD_2_5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_STANDARD_2_5</h4>
<pre>public static final&nbsp;int INT_STANDARD_2_5</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.INT_STANDARD_2_5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_INDUSTRIAL_2_5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_INDUSTRIAL_2_5</h4>
<pre>public static final&nbsp;int INT_INDUSTRIAL_2_5</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.INT_INDUSTRIAL_2_5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_GM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_GM</h4>
<pre>public static final&nbsp;int INT_GM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.INT_GM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_OCR">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>INT_OCR</h4>
<pre>public static final&nbsp;int INT_OCR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeSymbolUtility_mtk.INT_OCR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html" title="class in com.rscja.team.mtk.barcode">BarcodeSymbolUtility_mtk</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="getBarcodeName-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getBarcodeName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBarcodeName(int&nbsp;symbol)</pre>
<div class="block">获取条码名称(get barcode name)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html#getBarcodeName-int-">getBarcodeName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeSymbolUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>symbol</code> - 条码符号(barcode symbol)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>条码名称(barcode name)</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BarcodeSymbolUtility_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeFactory_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html" target="_top">Frames</a></li>
<li><a href="BarcodeSymbolUtility_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
