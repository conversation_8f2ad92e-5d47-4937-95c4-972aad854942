<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>SystemSettingsDBData_mtk</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SystemSettingsDBData_mtk";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SystemSettingsDBData_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/system/SystemPropValues_mtk.html" title="class in com.rscja.team.mtk.system"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html" target="_top">Frames</a></li>
<li><a href="SystemSettingsDBData_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.mtk.system</div>
<h2 title="Class SystemSettingsDBData_mtk" class="title">Class SystemSettingsDBData_mtk</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.mtk.system.SystemSettingsDBData_mtk</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SystemSettingsDBData_mtk</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#SystemSettingsDBData_mtk--">SystemSettingsDBData_mtk</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html" title="class in com.rscja.team.mtk.system">SystemSettingsDBData_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#isEnableBackKey-android.content.Context-">isEnableBackKey</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#isEnableHomeKey-android.content.Context-">isEnableHomeKey</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#isEnableMenuKey-android.content.Context-">isEnableMenuKey</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#isEnableNavigationBar-android.content.Context-">isEnableNavigationBar</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#isEnableOtg-android.content.Context-">isEnableOtg</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#isEnablePanelBar-android.content.Context-">isEnablePanelBar</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#isEnableRecentKey-android.content.Context-">isEnableRecentKey</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#isEnableScapKey-android.content.Context-">isEnableScapKey</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#setEnableBackKey-android.content.Context-boolean-">setEnableBackKey</a></span>(android.content.Context&nbsp;context,
                boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#setEnableHomeKey-android.content.Context-boolean-">setEnableHomeKey</a></span>(android.content.Context&nbsp;context,
                boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#setEnableMenuKey-android.content.Context-boolean-">setEnableMenuKey</a></span>(android.content.Context&nbsp;context,
                boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#setEnableNavigationBar-android.content.Context-boolean-">setEnableNavigationBar</a></span>(android.content.Context&nbsp;context,
                      boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#setEnableOtg-android.content.Context-boolean-">setEnableOtg</a></span>(android.content.Context&nbsp;context,
            boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#setEnablePanelBar-android.content.Context-boolean-">setEnablePanelBar</a></span>(android.content.Context&nbsp;context,
                 boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#setEnableRecentKey-android.content.Context-boolean-">setEnableRecentKey</a></span>(android.content.Context&nbsp;context,
                  boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html#setEnableScapKey-android.content.Context-boolean-">setEnableScapKey</a></span>(android.content.Context&nbsp;context,
                boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SystemSettingsDBData_mtk--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SystemSettingsDBData_mtk</h4>
<pre>public&nbsp;SystemSettingsDBData_mtk()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html" title="class in com.rscja.team.mtk.system">SystemSettingsDBData_mtk</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="isEnableHomeKey-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableHomeKey</h4>
<pre>public&nbsp;boolean&nbsp;isEnableHomeKey(android.content.Context&nbsp;context)</pre>
</li>
</ul>
<a name="setEnableHomeKey-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableHomeKey</h4>
<pre>public&nbsp;boolean&nbsp;setEnableHomeKey(android.content.Context&nbsp;context,
                                boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnableMenuKey-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableMenuKey</h4>
<pre>public&nbsp;boolean&nbsp;isEnableMenuKey(android.content.Context&nbsp;context)</pre>
</li>
</ul>
<a name="setEnableMenuKey-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableMenuKey</h4>
<pre>public&nbsp;boolean&nbsp;setEnableMenuKey(android.content.Context&nbsp;context,
                                boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnableScapKey-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableScapKey</h4>
<pre>public&nbsp;boolean&nbsp;isEnableScapKey(android.content.Context&nbsp;context)</pre>
</li>
</ul>
<a name="setEnableScapKey-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableScapKey</h4>
<pre>public&nbsp;boolean&nbsp;setEnableScapKey(android.content.Context&nbsp;context,
                                boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnablePanelBar-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnablePanelBar</h4>
<pre>public&nbsp;boolean&nbsp;isEnablePanelBar(android.content.Context&nbsp;context)</pre>
</li>
</ul>
<a name="setEnablePanelBar-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnablePanelBar</h4>
<pre>public&nbsp;boolean&nbsp;setEnablePanelBar(android.content.Context&nbsp;context,
                                 boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnableBackKey-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableBackKey</h4>
<pre>public&nbsp;boolean&nbsp;isEnableBackKey(android.content.Context&nbsp;context)</pre>
</li>
</ul>
<a name="setEnableBackKey-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableBackKey</h4>
<pre>public&nbsp;boolean&nbsp;setEnableBackKey(android.content.Context&nbsp;context,
                                boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnableRecentKey-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableRecentKey</h4>
<pre>public&nbsp;boolean&nbsp;isEnableRecentKey(android.content.Context&nbsp;context)</pre>
</li>
</ul>
<a name="setEnableRecentKey-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableRecentKey</h4>
<pre>public&nbsp;boolean&nbsp;setEnableRecentKey(android.content.Context&nbsp;context,
                                  boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnableNavigationBar-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableNavigationBar</h4>
<pre>public&nbsp;boolean&nbsp;isEnableNavigationBar(android.content.Context&nbsp;context)</pre>
</li>
</ul>
<a name="setEnableNavigationBar-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableNavigationBar</h4>
<pre>public&nbsp;boolean&nbsp;setEnableNavigationBar(android.content.Context&nbsp;context,
                                      boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnableOtg-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableOtg</h4>
<pre>public&nbsp;boolean&nbsp;isEnableOtg(android.content.Context&nbsp;context)</pre>
</li>
</ul>
<a name="setEnableOtg-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setEnableOtg</h4>
<pre>public&nbsp;boolean&nbsp;setEnableOtg(android.content.Context&nbsp;context,
                            boolean&nbsp;enable)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SystemSettingsDBData_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/system/SystemPropValues_mtk.html" title="class in com.rscja.team.mtk.system"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html" target="_top">Frames</a></li>
<li><a href="SystemSettingsDBData_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
