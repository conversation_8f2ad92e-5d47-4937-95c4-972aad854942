<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Barcode2DSoftCommon_mtk</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Barcode2DSoftCommon_mtk";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":41,"i2":10,"i3":10,"i4":9,"i5":9,"i6":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Barcode2DSoftCommon_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html" target="_top">Frames</a></li>
<li><a href="Barcode2DSoftCommon_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.mtk.barcode</div>
<h2 title="Class Barcode2DSoftCommon_mtk" class="title">Class Barcode2DSoftCommon_mtk</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.mtk.barcode.Barcode2DSoftCommon_mtk</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Barcode2DSoftCommon_mtk</span>
extends java.lang.Object</pre>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Administrator</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#CameraID">CameraID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#CameraStateFile">CameraStateFile</a></span></code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#isIris">isIris</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#isMTK">isMTK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#ScannerCameraIdFile">ScannerCameraIdFile</a></span></code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#ScannerStateFile">ScannerStateFile</a></span></code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#Barcode2DSoftCommon_mtk--">Barcode2DSoftCommon_mtk</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#Barcode2DSoftCommon-android.content.Context-">Barcode2DSoftCommon</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#readCameraState--">readCameraState</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#readCameraStateNew--">readCameraStateNew</a></span>()</code>
<div class="block">读取摄像头工作状态</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#readFlashlightStateNew--">readFlashlightStateNew</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#ReadMTKScanType--">ReadMTKScanType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#writeScannerState-int-">writeScannerState</a></span>(int&nbsp;id)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html#writeScannerStateNew-int-">writeScannerStateNew</a></span>(int&nbsp;id)</code>
<div class="block">写入扫描头是否在工作的状态值<br>
 Write in status value that scanning module is in working or not<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="isMTK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMTK</h4>
<pre>public static&nbsp;boolean isMTK</pre>
</li>
</ul>
<a name="CameraID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CameraID</h4>
<pre>public static&nbsp;int CameraID</pre>
</li>
</ul>
<a name="isIris">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIris</h4>
<pre>public static&nbsp;boolean isIris</pre>
</li>
</ul>
<a name="ScannerCameraIdFile">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ScannerCameraIdFile</h4>
<pre>@Deprecated
public static&nbsp;java.lang.String ScannerCameraIdFile</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">获取扫描头类型<br>
 acquire scanning module type<br></div>
</li>
</ul>
<a name="CameraStateFile">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CameraStateFile</h4>
<pre>@Deprecated
public static&nbsp;java.lang.String CameraStateFile</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</li>
</ul>
<a name="ScannerStateFile">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ScannerStateFile</h4>
<pre>@Deprecated
public static&nbsp;java.lang.String ScannerStateFile</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Barcode2DSoftCommon_mtk--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Barcode2DSoftCommon_mtk</h4>
<pre>public&nbsp;Barcode2DSoftCommon_mtk()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Barcode2DSoftCommon-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Barcode2DSoftCommon</h4>
<pre>public&nbsp;void&nbsp;Barcode2DSoftCommon(android.content.Context&nbsp;context)</pre>
</li>
</ul>
<a name="writeScannerStateNew-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeScannerStateNew</h4>
<pre>public&nbsp;void&nbsp;writeScannerStateNew(int&nbsp;id)</pre>
<div class="block">写入扫描头是否在工作的状态值<br>
 Write in status value that scanning module is in working or not<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - 0:扫描头没有打开,1:表示扫描头已经打开<br></dd>
<dd><code>id</code> - 0: scanning module is not switched on, 1 means scanning module is ON<br></dd>
</dl>
</li>
</ul>
<a name="writeScannerState-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeScannerState</h4>
<pre>public static&nbsp;void&nbsp;writeScannerState(int&nbsp;id)</pre>
</li>
</ul>
<a name="readCameraStateNew--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readCameraStateNew</h4>
<pre>public&nbsp;int&nbsp;readCameraStateNew()</pre>
<div class="block">读取摄像头工作状态</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回摄像头状态值，1:表示摄像头被打开，其他值表示未打开。</dd>
</dl>
</li>
</ul>
<a name="readCameraState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readCameraState</h4>
<pre>@Deprecated
public static&nbsp;int&nbsp;readCameraState()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</li>
</ul>
<a name="readFlashlightStateNew--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readFlashlightStateNew</h4>
<pre>public&nbsp;int&nbsp;readFlashlightStateNew()</pre>
</li>
</ul>
<a name="ReadMTKScanType--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ReadMTKScanType</h4>
<pre>public static&nbsp;java.lang.String&nbsp;ReadMTKScanType()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Barcode2DSoftCommon_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html" target="_top">Frames</a></li>
<li><a href="Barcode2DSoftCommon_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
