<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>RFIDWithISO14443B_mtk</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RFIDWithISO14443B_mtk";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithISO14443B_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A4CPU_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html" target="_top">Frames</a></li>
<li><a href="RFIDWithISO14443B_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.mtk.deviceapi</div>
<h2 title="Class RFIDWithISO14443B_mtk" class="title">Class RFIDWithISO14443B_mtk</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html" title="class in com.rscja.team.mtk.deviceapi">com.rscja.team.mtk.deviceapi.RFIDBase_mtk</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.mtk.deviceapi.RFIDWithISO14443B_mtk</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDBase</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443B</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">RFIDWithISO14443B_mtk</span>
extends <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDBase_mtk</a>
implements <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443B</a></pre>
<div class="block">RFID模块ISO14443B协议操作类<br>
 RFID module ISO14443B protocol operation type<br>
 <p>
 注意： <br>
 Attention: <br>
 1、使用前请确认您的机器已安装此模块。 <br>
 1. Make sure this module is installed before using.<br>
 2、要正常使用模块需要在\libs\armeabi\目录放置libDeviceAPI.so文件 <br>
 2. Put libDeviceAPI.so file in directoy \libs\armeabi\ then module can be used normally.<br>
 3、在操作设备前需要调用 <b><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html#init--"><code>RFIDBase_mtk.init()</code></a></b> 打开设备，使用完后调用 <b><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html#free--"><code>RFIDBase_mtk.free()</code></a></b> 关闭设备<br>
 3. call <b><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html#init--"><code>RFIDBase_mtk.init()</code></a></b> to switch on the device before operating, call <b><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html#free--"><code>RFIDBase_mtk.free()</code></a></b> to switch off the device after using.<br></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>liuruifeng</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443B_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO14443B协议操作实例<br>
 Acquire ISO14443B protocol operation Instance<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html#getUID--">getUID</a></span>()</code>
<div class="block">获取卡片ID<br>
 Acquire card ID<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html#getUIDBytes--">getUIDBytes</a></span>()</code>
<div class="block">获取卡片ID<br>
 Acquire card ID<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html#isPowerOn--">isPowerOn</a></span>()</code>
<div class="block">判断设备是否上电<br>
 Judge the device is powered on or not.<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html#reset--">reset</a></span>()</code>
<div class="block">CPU卡复位操作指令<br>
 CPU card reset operation commande<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html#sendCommand-java.lang.String-">sendCommand</a></span>(java.lang.String&nbsp;cmd)</code>
<div class="block">CPU卡 T=CL发送COS指令<br>
 CPU card T=CL send COS command<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.mtk.deviceapi.RFIDBase_mtk">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDBase_mtk</a></h3>
<code><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html#free--">free</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html#getVersion--">getVersion</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html#init--">init</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html#init-boolean-">init</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html#rfidUpgrade-int-int-int-byte:A-">rfidUpgrade</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IRFIDBase">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDBase</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#free--">free</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#getVersion--">getVersion</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#init--">init</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#init-boolean-">init</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#isPowerOn--">isPowerOn</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#rfidUpgrade-int-int-int-byte:A-">rfidUpgrade</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithISO14443B_mtk</a>&nbsp;getInstance()
                                         throws <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">获取ISO14443B协议操作实例<br>
 Acquire ISO14443B protocol operation Instance<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ISO14443B协议操作实例<br>
 ISO14443B protocol operation Instance<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code> - 配置错误异常<br>
                                Configuration err.<br></dd>
</dl>
</li>
</ul>
<a name="getUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUID</h4>
<pre>public&nbsp;java.lang.String&nbsp;getUID()</pre>
<div class="block">获取卡片ID<br>
 Acquire card ID<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html#getUID--">getUID</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443B</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>失败返回null<br>
 failure return null<br></dd>
</dl>
</li>
</ul>
<a name="getUIDBytes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUIDBytes</h4>
<pre>public&nbsp;byte[]&nbsp;getUIDBytes()</pre>
<div class="block">获取卡片ID<br>
 Acquire card ID<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html#getUIDBytes--">getUIDBytes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443B</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>失败返回null<br>
 failure return null<br></dd>
</dl>
</li>
</ul>
<a name="sendCommand-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sendCommand</h4>
<pre>public&nbsp;java.lang.String&nbsp;sendCommand(java.lang.String&nbsp;cmd)</pre>
<div class="block">CPU卡 T=CL发送COS指令<br>
 CPU card T=CL send COS command<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html#sendCommand-java.lang.String-">sendCommand</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443B</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cmd</code> - COS指令内容<br>
            COS command content<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>CPU卡返回的COS指令内容<br>
 CPU card return COS command<br></dd>
</dl>
</li>
</ul>
<a name="reset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reset</h4>
<pre>public&nbsp;java.lang.String&nbsp;reset()</pre>
<div class="block">CPU卡复位操作指令<br>
 CPU card reset operation commande<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html#reset--">reset</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443B</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>CPU卡返回的reset操作数据<br>
 CPU card return reset operation data<br></dd>
</dl>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>public&nbsp;boolean&nbsp;isPowerOn()</pre>
<div class="block">判断设备是否上电<br>
 Judge the device is powered on or not.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithISO14443B_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A4CPU_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html" target="_top">Frames</a></li>
<li><a href="RFIDWithISO14443B_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
