<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>ScanerLedLight_mtk</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ScanerLedLight_mtk";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":9,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ScanerLedLight_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" target="_top">Frames</a></li>
<li><a href="ScanerLedLight_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.mtk.deviceapi</div>
<h2 title="Class ScanerLedLight_mtk" class="title">Class ScanerLedLight_mtk</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.mtk.deviceapi.ScanerLedLight_mtk</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces">IScanerLedLight</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ScanerLedLight_mtk</span>
extends java.lang.Object
implements <a href="../../../../../com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces">IScanerLedLight</a></pre>
<div class="block">扫描LED灯控制类（仅C6000有效）<br>
 Scanning LED light control type ( valid for C6000 only)<br></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>liuruifeng</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#ScanerLedLight_mtk--">ScanerLedLight_mtk</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#blink-android.content.Context-">blink</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">亮0.5S后熄灭<br>
 light up for 0.5s then OFF<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#blink-android.content.Context-int-int-">blink</a></span>(android.content.Context&nbsp;context,
     int&nbsp;lightTime,
     int&nbsp;interval)</code>
<div class="block">控制亮灯后自动熄灭</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#closeAuxiliaryLight-android.content.Context-">closeAuxiliaryLight</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">关闭扫描辅助灯，目前只支持C70系列<br>
 Switch on scanning light, support C70 series only<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#free--">free</a></span>()</code>
<div class="block">释放设备资源<br>
 release device source<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi">ScanerLedLight_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取LED灯操作实例<br>
 Acquire LED light operation example<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#init--">init</a></span>()</code>
<div class="block">初始化设备资源<br>
 initialize device source<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#isPowerOn--">isPowerOn</a></span>()</code>
<div class="block">判断设备是否上电<br>
 Judge the device is powered on or not.<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#off-android.content.Context-">off</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">关闭LED灯<br>
 Switch off LED light<br></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#On-android.content.Context-">On</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">打开LED灯<br>
 Switch on LED light<br></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html#openAuxiliaryLight-android.content.Context-">openAuxiliaryLight</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">打开扫描辅助灯，目前只支持C70系列<br>
 Switch on scanning light, support C70 series only<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ScanerLedLight_mtk--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ScanerLedLight_mtk</h4>
<pre>public&nbsp;ScanerLedLight_mtk()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi">ScanerLedLight_mtk</a>&nbsp;getInstance()</pre>
<div class="block">获取LED灯操作实例<br>
 Acquire LED light operation example<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>LED灯操作实例<br>
 LED light operation example<br></dd>
</dl>
</li>
</ul>
<a name="init--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;void&nbsp;init()</pre>
<div class="block">初始化设备资源<br>
 initialize device source<br></div>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;void&nbsp;free()</pre>
<div class="block">释放设备资源<br>
 release device source<br></div>
</li>
</ul>
<a name="On-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>On</h4>
<pre>public&nbsp;void&nbsp;On(android.content.Context&nbsp;context)</pre>
<div class="block">打开LED灯<br>
 Switch on LED light<br></div>
</li>
</ul>
<a name="off-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>off</h4>
<pre>public&nbsp;void&nbsp;off(android.content.Context&nbsp;context)</pre>
<div class="block">关闭LED灯<br>
 Switch off LED light<br></div>
</li>
</ul>
<a name="blink-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blink</h4>
<pre>public&nbsp;void&nbsp;blink(android.content.Context&nbsp;context)</pre>
<div class="block">亮0.5S后熄灭<br>
 light up for 0.5s then OFF<br></div>
</li>
</ul>
<a name="blink-android.content.Context-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blink</h4>
<pre>public&nbsp;void&nbsp;blink(android.content.Context&nbsp;context,
                  int&nbsp;lightTime,
                  int&nbsp;interval)</pre>
<div class="block">控制亮灯后自动熄灭</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>lightTime</code> - 亮度时间(不低于50ms),到达此时间之后自动熄灭,单位:毫秒</dd>
<dd><code>interval</code> - 间隔时间(不低于50ms),单位:毫秒</dd>
</dl>
</li>
</ul>
<a name="openAuxiliaryLight-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openAuxiliaryLight</h4>
<pre>public&nbsp;void&nbsp;openAuxiliaryLight(android.content.Context&nbsp;context)</pre>
<div class="block">打开扫描辅助灯，目前只支持C70系列<br>
 Switch on scanning light, support C70 series only<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IScanerLedLight.html#openAuxiliaryLight-android.content.Context-">openAuxiliaryLight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces">IScanerLedLight</a></code></dd>
</dl>
</li>
</ul>
<a name="closeAuxiliaryLight-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeAuxiliaryLight</h4>
<pre>public&nbsp;void&nbsp;closeAuxiliaryLight(android.content.Context&nbsp;context)</pre>
<div class="block">关闭扫描辅助灯，目前只支持C70系列<br>
 Switch on scanning light, support C70 series only<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IScanerLedLight.html#closeAuxiliaryLight-android.content.Context-">closeAuxiliaryLight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces">IScanerLedLight</a></code></dd>
</dl>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>public&nbsp;boolean&nbsp;isPowerOn()</pre>
<div class="block">判断设备是否上电<br>
 Judge the device is powered on or not.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ScanerLedLight_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" target="_top">Frames</a></li>
<li><a href="ScanerLedLight_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
