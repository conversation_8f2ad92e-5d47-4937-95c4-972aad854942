<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Package com.rscja.team.mtk.deviceapi</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package com.rscja.team.mtk.deviceapi";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/deviceapi/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package com.rscja.team.mtk.deviceapi" class="title">Uses of Package<br>com.rscja.team.mtk.deviceapi</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.custom">com.rscja.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.custom">com.rscja.team.mtk.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.deviceapi">com.rscja.team.mtk.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a> used by <a href="../../../../../com/rscja/custom/package-summary.html">com.rscja.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/RFIDWithUHFUART_mtk.html#com.rscja.custom">RFIDWithUHFUART_mtk</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a> used by <a href="../../../../../com/rscja/team/mtk/custom/package-summary.html">com.rscja.team.mtk.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/RFIDWithUHFUART_mtk.html#com.rscja.team.mtk.custom">RFIDWithUHFUART_mtk</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a> used by <a href="../../../../../com/rscja/team/mtk/deviceapi/package-summary.html">com.rscja.team.mtk.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/Barcode1D_mtk.html#com.rscja.team.mtk.deviceapi">Barcode1D_mtk</a>
<div class="block">一维条码操作类<br>1D barcode operation class<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/Barcode2D_mtk.html#com.rscja.team.mtk.deviceapi">Barcode2D_mtk</a>
<div class="block">二维条码操作类 <br>
 2D barcode operation class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/DeviceAPI.html#com.rscja.team.mtk.deviceapi">DeviceAPI</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/FingerprintWithFIPS_mtk.html#com.rscja.team.mtk.deviceapi">FingerprintWithFIPS_mtk</a>
<div class="block">FIPS指纹识别模块操作类,<br>
 FIPS fingerprint indentify module operation type,<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/FingerprintWithMorpho_mtk.html#com.rscja.team.mtk.deviceapi">FingerprintWithMorpho_mtk</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/FingerprintWithTLK1NC_mtk.html#com.rscja.team.mtk.deviceapi">FingerprintWithTLK1NC_mtk</a>
<div class="block">迪安杰</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/Infrared_mtk.html#com.rscja.team.mtk.deviceapi">Infrared_mtk</a>
<div class="block">红外模块操作类
 Infared module operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/LedLight_mtk.html#com.rscja.team.mtk.deviceapi">LedLight_mtk</a>
<div class="block">手柄LED灯控制类<br>
 Handdeld LED control type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/Module_mtk.html#com.rscja.team.mtk.deviceapi">Module_mtk</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/Printer_mtk.html#com.rscja.team.mtk.deviceapi">Printer_mtk</a>
<div class="block">打印机操作类<br>
 Printer operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/PSAM_mtk.html#com.rscja.team.mtk.deviceapi">PSAM_mtk</a>
<div class="block">PSAM操作类<br>
 PSAM operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/RFIDBase_mtk.html#com.rscja.team.mtk.deviceapi">RFIDBase_mtk</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/RFIDWithISO14443A_mtk.html#com.rscja.team.mtk.deviceapi">RFIDWithISO14443A_mtk</a>
<div class="block">RFID模块ISO14443A协议操作类<br>
 RFID module ISO 14443A protocol operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/RFIDWithISO14443A4CPU_mtk.html#com.rscja.team.mtk.deviceapi">RFIDWithISO14443A4CPU_mtk</a>
<div class="block">RFID模块ISO14443A CPU卡协议操作类<br>
 RFID module ISO14443A CPU card protocol operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/RFIDWithISO14443B_mtk.html#com.rscja.team.mtk.deviceapi">RFIDWithISO14443B_mtk</a>
<div class="block">RFID模块ISO14443B协议操作类<br>
 RFID module ISO14443B protocol operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/RFIDWithISO15693_mtk.html#com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk</a>
<div class="block">RFID模块ISO15693协议操作类,<br>
 RFID module ISO15639 protocol operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/RFIDWithISO15693_mtk.TagType.html#com.rscja.team.mtk.deviceapi">RFIDWithISO15693_mtk.TagType</a>
<div class="block">标签类型<br>
 Tag type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/RFIDWithUHFUART_mtk.html#com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/ScanerLedLight_mtk.html#com.rscja.team.mtk.deviceapi">ScanerLedLight_mtk</a>
<div class="block">扫描LED灯控制类（仅C6000有效）<br>
 Scanning LED light control type ( valid for C6000 only)<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/mtk/deviceapi/class-use/UsbFingerprint_mtk.html#com.rscja.team.mtk.deviceapi">UsbFingerprint_mtk</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/deviceapi/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
