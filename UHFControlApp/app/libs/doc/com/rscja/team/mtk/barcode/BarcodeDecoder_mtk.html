<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>BarcodeDecoder_mtk</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BarcodeDecoder_mtk";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":10,"i2":10,"i3":6,"i4":6,"i5":10,"i6":6,"i7":6,"i8":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BarcodeDecoder_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.DecodeCallback.html" title="interface in com.rscja.team.mtk.barcode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html" target="_top">Frames</a></li>
<li><a href="BarcodeDecoder_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.mtk.barcode</div>
<h2 title="Class BarcodeDecoder_mtk" class="title">Class BarcodeDecoder_mtk</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.mtk.barcode.BarcodeDecoder_mtk</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">BarcodeDecoder_mtk</span>
extends java.lang.Object</pre>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>zhoupin</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.DecodeCallback.html" title="interface in com.rscja.team.mtk.barcode">BarcodeDecoder_mtk.DecodeCallback</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#DECODE_CANCEL">DECODE_CANCEL</a></span></code>
<div class="block">解码取消(cancel)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#DECODE_ENGINE_ERROR">DECODE_ENGINE_ERROR</a></span></code>
<div class="block">扫描头出现错误(error)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#DECODE_FAILURE">DECODE_FAILURE</a></span></code>
<div class="block">解码失败(failure)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#DECODE_SUCCESS">DECODE_SUCCESS</a></span></code>
<div class="block">解码成功(success)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#DECODE_TIMEOUT">DECODE_TIMEOUT</a></span></code>
<div class="block">解码超时(timeout)</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#BarcodeDecoder_mtk--">BarcodeDecoder_mtk</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#close--">close</a></span>()</code>
<div class="block">关闭二维扫描设备<br>
 Switch off 2D barcode scannning device<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#getDecoderSVersionInfo--">getDecoderSVersionInfo</a></span>()</code>
<div class="block">返回扫描头和解码库信息</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#isOpen--">isOpen</a></span>()</code>
<div class="block">扫描头是否已经打开</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#open-android.content.Context-">open</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">打开二维扫描设备<br>
 Switch on 2D scanning device<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#setDecodeCallback-com.rscja.team.mtk.barcode.BarcodeDecoder_mtk.DecodeCallback-">setDecodeCallback</a></span>(<a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.DecodeCallback.html" title="interface in com.rscja.team.mtk.barcode">BarcodeDecoder_mtk.DecodeCallback</a>&nbsp;scanCallbackListener)</code>
<div class="block">设置回调对象接收条码数据,主线程回调<br>
 Setup call-back target to acquire barcode data.<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#setParameter-int-int-">setParameter</a></span>(int&nbsp;paramNum,
            int&nbsp;paramVal)</code>
<div class="block">设置扫描头参数(Setup scanning module parameter)</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#setTimeOut-int-">setTimeOut</a></span>(int&nbsp;timeOut)</code>
<div class="block">设置超时时间<br>
 setup time-out duration<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#startScan--">startScan</a></span>()</code>
<div class="block">触发二维条码扫描<br>
 Trigger 2D barcode scanning function<br></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html#stopScan--">stopScan</a></span>()</code>
<div class="block">终止扫描<br>
 Scanning terminated<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DECODE_TIMEOUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DECODE_TIMEOUT</h4>
<pre>public static final&nbsp;int DECODE_TIMEOUT</pre>
<div class="block">解码超时(timeout)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeDecoder_mtk.DECODE_TIMEOUT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DECODE_SUCCESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DECODE_SUCCESS</h4>
<pre>public static final&nbsp;int DECODE_SUCCESS</pre>
<div class="block">解码成功(success)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeDecoder_mtk.DECODE_SUCCESS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DECODE_CANCEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DECODE_CANCEL</h4>
<pre>public static final&nbsp;int DECODE_CANCEL</pre>
<div class="block">解码取消(cancel)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeDecoder_mtk.DECODE_CANCEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DECODE_FAILURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DECODE_FAILURE</h4>
<pre>public static final&nbsp;int DECODE_FAILURE</pre>
<div class="block">解码失败(failure)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeDecoder_mtk.DECODE_FAILURE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DECODE_ENGINE_ERROR">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DECODE_ENGINE_ERROR</h4>
<pre>public static final&nbsp;int DECODE_ENGINE_ERROR</pre>
<div class="block">扫描头出现错误(error)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.mtk.barcode.BarcodeDecoder_mtk.DECODE_ENGINE_ERROR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BarcodeDecoder_mtk--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BarcodeDecoder_mtk</h4>
<pre>public&nbsp;BarcodeDecoder_mtk()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="open-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public abstract&nbsp;boolean&nbsp;open(android.content.Context&nbsp;context)</pre>
<div class="block">打开二维扫描设备<br>
 Switch on 2D scanning device<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public abstract&nbsp;void&nbsp;close()</pre>
<div class="block">关闭二维扫描设备<br>
 Switch off 2D barcode scannning device<br></div>
</li>
</ul>
<a name="startScan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startScan</h4>
<pre>public abstract&nbsp;boolean&nbsp;startScan()</pre>
<div class="block">触发二维条码扫描<br>
 Trigger 2D barcode scanning function<br></div>
</li>
</ul>
<a name="stopScan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopScan</h4>
<pre>public abstract&nbsp;void&nbsp;stopScan()</pre>
<div class="block">终止扫描<br>
 Scanning terminated<br></div>
</li>
</ul>
<a name="setDecodeCallback-com.rscja.team.mtk.barcode.BarcodeDecoder_mtk.DecodeCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDecodeCallback</h4>
<pre>public abstract&nbsp;void&nbsp;setDecodeCallback(<a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.DecodeCallback.html" title="interface in com.rscja.team.mtk.barcode">BarcodeDecoder_mtk.DecodeCallback</a>&nbsp;scanCallbackListener)</pre>
<div class="block">设置回调对象接收条码数据,主线程回调<br>
 Setup call-back target to acquire barcode data.<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scanCallbackListener</code> - </dd>
</dl>
</li>
</ul>
<a name="setTimeOut-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimeOut</h4>
<pre>public abstract&nbsp;void&nbsp;setTimeOut(int&nbsp;timeOut)</pre>
<div class="block">设置超时时间<br>
 setup time-out duration<br></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>timeOut</code> - 超时时间,单位:秒(timeOut,Unit: second)</dd>
</dl>
</li>
</ul>
<a name="isOpen--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOpen</h4>
<pre>public&nbsp;boolean&nbsp;isOpen()</pre>
<div class="block">扫描头是否已经打开</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:扫描头已打开, false:扫描头已关闭</dd>
</dl>
</li>
</ul>
<a name="getDecoderSVersionInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDecoderSVersionInfo</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDecoderSVersionInfo()</pre>
<div class="block">返回扫描头和解码库信息</div>
</li>
</ul>
<a name="setParameter-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setParameter</h4>
<pre>public&nbsp;boolean&nbsp;setParameter(int&nbsp;paramNum,
                            int&nbsp;paramVal)</pre>
<div class="block">设置扫描头参数(Setup scanning module parameter)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>paramNum</code> - </dd>
<dd><code>paramVal</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BarcodeDecoder_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html" title="class in com.rscja.team.mtk.barcode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.DecodeCallback.html" title="interface in com.rscja.team.mtk.barcode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html" target="_top">Frames</a></li>
<li><a href="BarcodeDecoder_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
