<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>KeyboardEmulator2DDecoder_mtk</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="KeyboardEmulator2DDecoder_mtk";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":9,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/KeyboardEmulator2DDecoder_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/rscja/team/mtk/barcode/barcode2d/Barcode2DFactory_mtk.html" title="class in com.rscja.team.mtk.barcode.barcode2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html" target="_top">Frames</a></li>
<li><a href="KeyboardEmulator2DDecoder_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.barcode.BarcodeDecoder">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.rscja.barcode.BarcodeDecoder">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.mtk.barcode.barcode2d</div>
<h2 title="Class KeyboardEmulator2DDecoder_mtk" class="title">Class KeyboardEmulator2DDecoder_mtk</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">com.rscja.barcode.BarcodeDecoder</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.mtk.barcode.barcode2d.KeyboardEmulator2DDecoder_mtk</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">KeyboardEmulator2DDecoder_mtk</span>
extends <a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></pre>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>zhoupin

 键盘助手</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.rscja.barcode.BarcodeDecoder">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.rscja.barcode.<a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></h3>
<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.DecodeCallback.html" title="interface in com.rscja.barcode">BarcodeDecoder.DecodeCallback</a>, <a href="../../../../../../com/rscja/barcode/BarcodeDecoder.IBarcodeImageCallback.html" title="interface in com.rscja.barcode">BarcodeDecoder.IBarcodeImageCallback</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.barcode.BarcodeDecoder">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.rscja.barcode.<a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></h3>
<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#DECODE_CANCEL">DECODE_CANCEL</a>, <a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#DECODE_ENGINE_ERROR">DECODE_ENGINE_ERROR</a>, <a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#DECODE_FAILURE">DECODE_FAILURE</a>, <a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#DECODE_SUCCESS">DECODE_SUCCESS</a>, <a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#DECODE_TIMEOUT">DECODE_TIMEOUT</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html#close--">close</a></span>()</code>
<div class="block">关闭二维扫描设备<br>
 Switch off 2D barcode scannning device<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html#isEnable--">isEnable</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html#open-android.content.Context-">open</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">打开二维扫描设备<br>
 Switch on 2D scanning device<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html#setDecodeCallback-com.rscja.barcode.BarcodeDecoder.DecodeCallback-">setDecodeCallback</a></span>(<a href="../../../../../../com/rscja/barcode/BarcodeDecoder.DecodeCallback.html" title="interface in com.rscja.barcode">BarcodeDecoder.DecodeCallback</a>&nbsp;scanCallbackListener)</code>
<div class="block">设置回调对象接收条码数据,主线程回调<br>
 Setup call-back target to acquire barcode data.<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html#setEnable-boolean-">setEnable</a></span>(boolean&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html#setParameter-int-int-">setParameter</a></span>(int&nbsp;paramNum,
            int&nbsp;paramVal)</code>
<div class="block">设置扫描头参数<br>
 Setup scanning module parameter<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html#setTimeOut-int-">setTimeOut</a></span>(int&nbsp;timeOut)</code>
<div class="block">设置超时时间<br>
 setup time-out duration<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html#startScan--">startScan</a></span>()</code>
<div class="block">触发二维条码扫描<br>
 Trigger 2D barcode scanning function<br></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html#stopScan--">stopScan</a></span>()</code>
<div class="block">终止扫描<br>
 Scanning terminated<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.barcode.BarcodeDecoder">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.barcode.<a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></h3>
<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#getDecoderSVersionInfo--">getDecoderSVersionInfo</a>, <a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#getLastDecImage-com.rscja.barcode.BarcodeDecoder.IBarcodeImageCallback-">getLastDecImage</a>, <a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#getParameter-int-">getParameter</a>, <a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#isOpen--">isOpen</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="open-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;boolean&nbsp;open(android.content.Context&nbsp;context)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#open-android.content.Context-">BarcodeDecoder</a></code></span></div>
<div class="block">打开二维扫描设备<br>
 Switch on 2D scanning device<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#open-android.content.Context-">open</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;void&nbsp;close()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#close--">BarcodeDecoder</a></code></span></div>
<div class="block">关闭二维扫描设备<br>
 Switch off 2D barcode scannning device<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#close--">close</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></code></dd>
</dl>
</li>
</ul>
<a name="startScan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startScan</h4>
<pre>public&nbsp;boolean&nbsp;startScan()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#startScan--">BarcodeDecoder</a></code></span></div>
<div class="block">触发二维条码扫描<br>
 Trigger 2D barcode scanning function<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#startScan--">startScan</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></code></dd>
</dl>
</li>
</ul>
<a name="stopScan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopScan</h4>
<pre>public&nbsp;void&nbsp;stopScan()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#stopScan--">BarcodeDecoder</a></code></span></div>
<div class="block">终止扫描<br>
 Scanning terminated<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#stopScan--">stopScan</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></code></dd>
</dl>
</li>
</ul>
<a name="setDecodeCallback-com.rscja.barcode.BarcodeDecoder.DecodeCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDecodeCallback</h4>
<pre>public&nbsp;void&nbsp;setDecodeCallback(<a href="../../../../../../com/rscja/barcode/BarcodeDecoder.DecodeCallback.html" title="interface in com.rscja.barcode">BarcodeDecoder.DecodeCallback</a>&nbsp;scanCallbackListener)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#setDecodeCallback-com.rscja.barcode.BarcodeDecoder.DecodeCallback-">BarcodeDecoder</a></code></span></div>
<div class="block">设置回调对象接收条码数据,主线程回调<br>
 Setup call-back target to acquire barcode data.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#setDecodeCallback-com.rscja.barcode.BarcodeDecoder.DecodeCallback-">setDecodeCallback</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></code></dd>
</dl>
</li>
</ul>
<a name="setTimeOut-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimeOut</h4>
<pre>public&nbsp;void&nbsp;setTimeOut(int&nbsp;timeOut)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#setTimeOut-int-">BarcodeDecoder</a></code></span></div>
<div class="block">设置超时时间<br>
 setup time-out duration<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#setTimeOut-int-">setTimeOut</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>timeOut</code> - 超时时间,单位:秒(timeOut,Unit: second)</dd>
</dl>
</li>
</ul>
<a name="setParameter-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParameter</h4>
<pre>public&nbsp;boolean&nbsp;setParameter(int&nbsp;paramNum,
                            int&nbsp;paramVal)</pre>
<div class="block">设置扫描头参数<br>
 Setup scanning module parameter<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html#setParameter-int-int-">setParameter</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode">BarcodeDecoder</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>paramNum</code> - paramNum</dd>
<dd><code>paramVal</code> - paramVal</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="isEnable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnable</h4>
<pre>public static&nbsp;boolean&nbsp;isEnable()</pre>
</li>
</ul>
<a name="setEnable-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setEnable</h4>
<pre>public static&nbsp;void&nbsp;setEnable(boolean&nbsp;flag)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/KeyboardEmulator2DDecoder_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/rscja/team/mtk/barcode/barcode2d/Barcode2DFactory_mtk.html" title="class in com.rscja.team.mtk.barcode.barcode2d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html" target="_top">Frames</a></li>
<li><a href="KeyboardEmulator2DDecoder_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.barcode.BarcodeDecoder">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.rscja.barcode.BarcodeDecoder">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
