<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>DeviceAPI</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DeviceAPI";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":9,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10,"i142":10,"i143":10,"i144":10,"i145":10,"i146":10,"i147":10,"i148":10,"i149":10,"i150":10,"i151":10,"i152":10,"i153":10,"i154":10,"i155":10,"i156":10,"i157":10,"i158":10,"i159":10,"i160":10,"i161":10,"i162":10,"i163":10,"i164":10,"i165":10,"i166":10,"i167":10,"i168":10,"i169":10,"i170":10,"i171":10,"i172":10,"i173":10,"i174":10,"i175":10,"i176":10,"i177":10,"i178":10,"i179":10,"i180":10,"i181":10,"i182":10,"i183":10,"i184":10,"i185":10,"i186":10,"i187":10,"i188":10,"i189":10,"i190":10,"i191":10,"i192":10,"i193":10,"i194":10,"i195":10,"i196":10,"i197":10,"i198":10,"i199":10,"i200":10,"i201":10,"i202":10,"i203":10,"i204":10,"i205":10,"i206":10,"i207":10,"i208":10,"i209":10,"i210":10,"i211":10,"i212":10,"i213":10,"i214":10,"i215":10,"i216":10,"i217":10,"i218":10,"i219":10,"i220":10,"i221":10,"i222":10,"i223":10,"i224":10,"i225":10,"i226":10,"i227":10,"i228":10,"i229":10,"i230":10,"i231":10,"i232":10,"i233":10,"i234":10,"i235":10,"i236":10,"i237":10,"i238":10,"i239":10,"i240":10,"i241":10,"i242":10,"i243":10,"i244":10,"i245":10,"i246":10,"i247":10,"i248":10,"i249":10,"i250":10,"i251":10,"i252":10,"i253":10,"i254":10,"i255":10,"i256":10,"i257":10,"i258":10,"i259":10,"i260":10,"i261":10,"i262":10,"i263":10,"i264":10,"i265":10,"i266":10,"i267":10,"i268":10,"i269":10,"i270":10,"i271":10,"i272":10,"i273":10,"i274":10,"i275":10,"i276":10,"i277":10,"i278":10,"i279":10,"i280":10,"i281":10,"i282":10,"i283":10,"i284":10,"i285":10,"i286":10,"i287":10,"i288":10,"i289":10,"i290":10,"i291":10,"i292":10,"i293":10,"i294":10,"i295":10,"i296":10,"i297":10,"i298":10,"i299":10,"i300":10,"i301":10,"i302":10,"i303":10,"i304":10,"i305":10,"i306":10,"i307":10,"i308":10,"i309":10,"i310":10,"i311":10,"i312":10,"i313":10,"i314":10,"i315":10,"i316":10,"i317":10,"i318":10,"i319":10,"i320":10,"i321":10,"i322":10,"i323":10,"i324":10,"i325":10,"i326":10,"i327":10,"i328":10,"i329":10,"i330":10,"i331":10,"i332":10,"i333":10,"i334":10,"i335":10,"i336":10,"i337":10,"i338":10,"i339":10,"i340":10,"i341":10,"i342":10,"i343":10,"i344":10,"i345":10,"i346":10,"i347":10,"i348":10,"i349":10,"i350":10,"i351":10,"i352":10,"i353":10,"i354":10,"i355":10,"i356":10,"i357":10,"i358":10,"i359":10,"i360":10,"i361":10,"i362":10,"i363":10,"i364":10,"i365":10,"i366":10,"i367":10,"i368":10,"i369":10,"i370":10,"i371":10,"i372":10,"i373":10,"i374":10,"i375":10,"i376":10,"i377":10,"i378":10,"i379":10,"i380":10,"i381":10,"i382":10,"i383":10,"i384":10,"i385":10,"i386":10,"i387":10,"i388":10,"i389":10,"i390":10,"i391":10,"i392":10,"i393":10,"i394":10,"i395":10,"i396":10,"i397":10,"i398":10,"i399":10,"i400":10,"i401":10,"i402":10,"i403":10,"i404":10,"i405":10,"i406":10,"i407":10,"i408":10,"i409":10,"i410":10,"i411":10,"i412":10,"i413":10,"i414":10,"i415":10,"i416":10,"i417":10,"i418":10,"i419":10,"i420":10,"i421":10,"i422":10,"i423":10,"i424":10,"i425":10,"i426":10,"i427":10,"i428":10,"i429":10,"i430":10,"i431":10,"i432":10,"i433":10,"i434":10,"i435":10,"i436":10,"i437":10,"i438":10,"i439":10,"i440":10,"i441":10,"i442":10,"i443":10,"i444":10,"i445":10,"i446":10,"i447":10,"i448":10,"i449":10,"i450":10,"i451":10,"i452":10,"i453":10,"i454":10,"i455":10,"i456":10,"i457":10,"i458":10,"i459":10,"i460":10,"i461":10,"i462":10,"i463":10,"i464":10,"i465":10,"i466":10,"i467":10,"i468":10,"i469":10,"i470":10,"i471":10,"i472":10,"i473":10,"i474":10,"i475":10,"i476":10,"i477":10,"i478":10,"i479":10,"i480":10,"i481":10,"i482":10,"i483":10,"i484":10,"i485":10,"i486":10,"i487":10,"i488":10,"i489":10,"i490":10,"i491":10,"i492":10,"i493":10,"i494":10,"i495":10,"i496":10,"i497":10,"i498":10,"i499":10,"i500":10,"i501":10,"i502":10,"i503":10,"i504":10,"i505":10,"i506":10,"i507":10,"i508":10,"i509":10,"i510":10,"i511":10,"i512":10,"i513":10,"i514":10,"i515":10,"i516":10,"i517":10,"i518":10,"i519":10,"i520":10,"i521":10,"i522":10,"i523":10,"i524":10,"i525":10,"i526":10,"i527":10,"i528":10,"i529":10,"i530":10,"i531":10,"i532":10,"i533":10,"i534":10,"i535":10,"i536":10,"i537":10,"i538":10,"i539":10,"i540":10,"i541":10,"i542":10,"i543":10,"i544":10,"i545":10,"i546":10,"i547":10,"i548":10,"i549":10,"i550":10,"i551":10,"i552":10,"i553":10,"i554":10,"i555":10,"i556":10,"i557":10,"i558":10,"i559":10,"i560":10,"i561":10,"i562":10,"i563":10,"i564":10,"i565":10,"i566":10,"i567":10,"i568":10,"i569":10,"i570":10,"i571":10,"i572":10,"i573":10,"i574":10,"i575":10,"i576":10,"i577":10,"i578":10,"i579":10,"i580":10,"i581":10,"i582":10,"i583":10,"i584":10,"i585":10,"i586":10,"i587":10,"i588":10,"i589":10,"i590":10,"i591":10,"i592":10,"i593":10,"i594":10,"i595":10,"i596":10,"i597":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DeviceAPI.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/deviceapi/DeviceAPI.html" target="_top">Frames</a></li>
<li><a href="DeviceAPI.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.mtk.deviceapi</div>
<h2 title="Class DeviceAPI" class="title">Class DeviceAPI</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.mtk.deviceapi.DeviceAPI</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">DeviceAPI</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.io.FileDescriptor</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#mFd">mFd</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4OptoCoupler3Off-java.lang.String-">A4OptoCoupler3Off</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4OptoCoupler3On-java.lang.String-">A4OptoCoupler3On</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4OptoCoupler4Off-java.lang.String-">A4OptoCoupler4Off</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4OptoCoupler4On-java.lang.String-">A4OptoCoupler4On</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4WgData0Off-java.lang.String-">A4WgData0Off</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4WgData0On-java.lang.String-">A4WgData0On</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4WgData1Off-java.lang.String-">A4WgData1Off</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A4WgData1On-java.lang.String-">A4WgData1On</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A8UhfOutput3Off-java.lang.String-">A8UhfOutput3Off</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A8UhfOutput3On-java.lang.String-">A8UhfOutput3On</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A8UhfOutput4Off-java.lang.String-">A8UhfOutput4Off</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#A8UhfOutput4On-java.lang.String-">A8UhfOutput4On</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Auth_ActiveFile-byte-byte:A-byte:A-byte:A-">Auth_ActiveFile</a></span>(byte&nbsp;transmode,
               byte[]&nbsp;deviceid,
               byte[]&nbsp;reverse,
               byte[]&nbsp;transbuf)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Auth_CertificationChain-byte-byte-int-byte:A-">Auth_CertificationChain</a></span>(byte&nbsp;level,
                       byte&nbsp;levels,
                       int&nbsp;len,
                       byte[]&nbsp;buf)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Auth_RequestRandom--">Auth_RequestRandom</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Auth_SendData-byte:A-byte:A-byte:A-byte:A-byte:A-int-byte:A-">Auth_SendData</a></span>(byte[]&nbsp;hrandom,
             byte[]&nbsp;srandom,
             byte[]&nbsp;devid,
             byte[]&nbsp;reserve,
             byte[]&nbsp;signature,
             int&nbsp;certlen,
             byte[]&nbsp;certbuf)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Auth_UserInfo-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-">Auth_UserInfo</a></span>(byte[]&nbsp;jprovince,
             byte[]&nbsp;jcity,
             byte[]&nbsp;jjw,
             byte[]&nbsp;jusage,
             byte[]&nbsp;jtype1,
             byte[]&nbsp;jcom_name,
             byte[]&nbsp;jpeople,
             byte[]&nbsp;jtel1,
             byte[]&nbsp;jtel2,
             byte[]&nbsp;jemail,
             byte[]&nbsp;jdata,
             byte[]&nbsp;jps)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_1D_Close-java.lang.String-">Barcode_1D_Close</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_1D_Open-java.lang.String-java.lang.String-int-">Barcode_1D_Open</a></span>(java.lang.String&nbsp;device,
               java.lang.String&nbsp;uart,
               int&nbsp;baudrate)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_1D_Scan-java.lang.String-">Barcode_1D_Scan</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_1D_SetTimeOut-int-">Barcode_1D_SetTimeOut</a></span>(int&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_1D_StopScan-java.lang.String-">Barcode_1D_StopScan</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_2D_Close-java.lang.String-">Barcode_2D_Close</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_2D_Open-java.lang.String-java.lang.String-int-">Barcode_2D_Open</a></span>(java.lang.String&nbsp;device,
               java.lang.String&nbsp;uart,
               int&nbsp;baudrate)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_2D_Scan-java.lang.String-">Barcode_2D_Scan</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_2D_SetTimeOut-int-">Barcode_2D_SetTimeOut</a></span>(int&nbsp;time)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Barcode_2D_StopScan-java.lang.String-">Barcode_2D_StopScan</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#bdOff-java.lang.String-">bdOff</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#bdOn-java.lang.String-">bdOn</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#CardBalance--">CardBalance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#CardConsume-byte:A-char:A-java.lang.String-int-">CardConsume</a></span>(byte[]&nbsp;time,
           char[]&nbsp;money,
           java.lang.String&nbsp;path,
           int&nbsp;islog)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#CardConsumeConfirm-byte:A-char:A-char:A-">CardConsumeConfirm</a></span>(byte[]&nbsp;timer,
                  char[]&nbsp;carddealnum,
                  char[]&nbsp;cardnum)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#CleanVar--">CleanVar</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Config_GetAccess--">Config_GetAccess</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Config_GetPara-int-">Config_GetPara</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Config_ReadRTC--">Config_ReadRTC</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Config_SetPara-int-byte-byte:A-">Config_SetPara</a></span>(int&nbsp;type,
              byte&nbsp;len,
              byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Config_SetRTC-byte:A-">Config_SetRTC</a></span>(byte[]&nbsp;RTC)</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Config_SetWorkMode-byte-byte:A-">Config_SetWorkMode</a></span>(byte&nbsp;nums,
                  byte[]&nbsp;modebuf)</code>&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ConfigFDXTag-char:A-char:A-">ConfigFDXTag</a></span>(char[]&nbsp;cidbuf,
            char[]&nbsp;nidbuf)</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerCancelUPImage--">DAJfingerCancelUPImage</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerCLEARTemplate--">DAJfingerCLEARTemplate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerCLEARTemplateBuffer--">DAJfingerCLEARTemplateBuffer</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerDELTemplate-char-char:A-">DAJfingerDELTemplate</a></span>(char&nbsp;flag,
                    char[]&nbsp;pBufferIDFlashPageID)</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerDOWNTemplate-char-char:A-char:A-">DAJfingerDOWNTemplate</a></span>(char&nbsp;flag,
                     char[]&nbsp;pBufferIDFlashPageID,
                     char[]&nbsp;templateData)</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJFingerFree-java.lang.String-">DAJFingerFree</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerGETImage--">DAJfingerGETImage</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerGETInfo--">DAJfingerGETInfo</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerGETTemplateCount--">DAJfingerGETTemplateCount</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerGRABHalfImage--">DAJfingerGRABHalfImage</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerGRABHalfImageProgress--">DAJfingerGRABHalfImageProgress</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerGRABImage--">DAJfingerGRABImage</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJFingerInit-java.lang.String-java.lang.String-int-">DAJFingerInit</a></span>(java.lang.String&nbsp;device,
             java.lang.String&nbsp;uart,
             int&nbsp;baudrate)</code>&nbsp;</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerPKTemplate-char-char:A-char-char:A-">DAJfingerPKTemplate</a></span>(char&nbsp;flagA,
                   char[]&nbsp;pBufferIDFlashPageIDA,
                   char&nbsp;flagB,
                   char[]&nbsp;pBufferIDFlashPageIDB)</code>&nbsp;</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerSearchTemplate-char:A-char:A-char:A-">DAJfingerSearchTemplate</a></span>(char[]&nbsp;ramBufferId,
                       char[]&nbsp;templateIdStart,
                       char[]&nbsp;templateIdEnd)</code>&nbsp;</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerStopGRABImage--">DAJfingerStopGRABImage</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerStoreChar-char-char:A-">DAJfingerStoreChar</a></span>(char&nbsp;flag,
                  char[]&nbsp;pBufferIDFlashPageID)</code>&nbsp;</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerUPImage-byte-byte:A-">DAJfingerUPImage</a></span>(byte&nbsp;part,
                byte[]&nbsp;img_buf)</code>&nbsp;</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerUPImageProgress--">DAJfingerUPImageProgress</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#DAJfingerUPTemplate-char-char:A-">DAJfingerUPTemplate</a></span>(char&nbsp;flag,
                   char[]&nbsp;pBufferIDFlashPageID)</code>&nbsp;</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM_25kread--">EM_25kread</a></span>()</code>
<div class="block">读半双工的动物标签 ************************ 输出参数：pszData-- 16字节动物标签数据</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_free-java.lang.String-">EM125k_free</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125K_GetEm4450UID--">EM125K_GetEm4450UID</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_init_Ex-java.lang.String-java.lang.String-int-">EM125k_init_Ex</a></span>(java.lang.String&nbsp;device,
              java.lang.String&nbsp;uart,
              int&nbsp;baudrate)</code>&nbsp;</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_init-java.lang.String-java.lang.String-int-">EM125k_init</a></span>(java.lang.String&nbsp;device,
           java.lang.String&nbsp;uart,
           int&nbsp;baudrate)</code>&nbsp;</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_read_Ex--">EM125k_read_Ex</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_read-int-">EM125k_read</a></span>(int&nbsp;iMode)</code>&nbsp;</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_Read4305-int-">EM125k_Read4305</a></span>(int&nbsp;nPage)</code>
<div class="block">读EM4305卡 ******************************** 功能描述：读4305卡的某一页（0~31）数据
 输入参数：nPage -- 页地址(0~31) 第0、3、5~13页可读可写，可供用户存储数据使用 第1页为UID存储区，只读 第2页为密码区
 第4页为参数配置区域 第14、15页为保护区 输出参数：pszData-- 4字节数据（每页可存放4字节）</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_ReadHitag-int-">EM125k_ReadHitag</a></span>(int&nbsp;nPage)</code>&nbsp;</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_ReadHitag1--">EM125k_ReadHitag1</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_UID_REQ--">EM125k_UID_REQ</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_Write4305-int-char:A-">EM125k_Write4305</a></span>(int&nbsp;nPage,
                char[]&nbsp;pszData)</code>
<div class="block">写EM4305卡 ******************************** 功能描述：写4305卡的某一页（0~31）数据
 输入参数：nPage -- 页地址(0~31) 第0、3、5~13页可读可写，可供用户存储数据使用 第1页为UID存储区，只读 第2页为密码区
 第4页为参数配置区域 第14、15页为保护区 pszData-- 4字节数据（每页可存放4字节）</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM125k_WriteHitagPage-int-char:A-">EM125k_WriteHitagPage</a></span>(int&nbsp;nPage,
                     char[]&nbsp;pszData)</code>&nbsp;</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EM4325SensorData-char-int-int-char:A-">EM4325SensorData</a></span>(char&nbsp;uBank,
                int&nbsp;uMSA,
                int&nbsp;uMDL,
                char[]&nbsp;pszuData)</code>&nbsp;</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMAutoEnroll-int-int-">EMAutoEnroll</a></span>(int&nbsp;nTime,
            int&nbsp;UserID)</code>&nbsp;</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMAutoMatch-int-int-int-">EMAutoMatch</a></span>(int&nbsp;nFlag,
           int&nbsp;StartPage,
           int&nbsp;PageNum)</code>&nbsp;</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMDeletChar-int-int-">EMDeletChar</a></span>(int&nbsp;PageID,
           int&nbsp;nNum)</code>&nbsp;</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMDownChar-int-char:A-">EMDownChar</a></span>(int&nbsp;BuffID,
          char[]&nbsp;pszData)</code>&nbsp;</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMEmpty--">EMEmpty</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMFingerFree-java.lang.String-">EMFingerFree</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMFingerInit-java.lang.String-java.lang.String-int-">EMFingerInit</a></span>(java.lang.String&nbsp;device,
            java.lang.String&nbsp;uart,
            int&nbsp;baudrate)</code>&nbsp;</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMFingerMoudleSet-int-">EMFingerMoudleSet</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMGenChar-int-">EMGenChar</a></span>(int&nbsp;GenID)</code>&nbsp;</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMGetImage--">EMGetImage</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMGetRandomData--">EMGetRandomData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMLoadChar-int-int-">EMLoadChar</a></span>(int&nbsp;BuffID,
          int&nbsp;PageID)</code>&nbsp;</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMMatch--">EMMatch</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMReadChipSN--">EMReadChipSN</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMReadSysPara--">EMReadSysPara</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMReadSysParaMore--">EMReadSysParaMore</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMRegModel--">EMRegModel</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMSearch-int-int-int-">EMSearch</a></span>(int&nbsp;BuffID,
        int&nbsp;StartPage,
        int&nbsp;PageNum)</code>&nbsp;</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMSetDeviceName-char:A-">EMSetDeviceName</a></span>(char[]&nbsp;DeviceName)</code>&nbsp;</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMSetManuFacture-char:A-">EMSetManuFacture</a></span>(char[]&nbsp;NameInfo)</code>&nbsp;</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMSetPSW-char:A-">EMSetPSW</a></span>(char[]&nbsp;PassWord)</code>&nbsp;</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMSetReg-int-int-">EMSetReg</a></span>(int&nbsp;RegID,
        int&nbsp;nValue)</code>&nbsp;</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMStorChar-int-int-">EMStorChar</a></span>(int&nbsp;BuffID,
          int&nbsp;PageID)</code>&nbsp;</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMUpChar-int-">EMUpChar</a></span>(int&nbsp;BuffID)</code>&nbsp;</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMUpImage-int-java.lang.String-">EMUpImage</a></span>(int&nbsp;Mode,
         java.lang.String&nbsp;lpFileName)</code>&nbsp;</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMUpImageISO-int-java.lang.String-">EMUpImageISO</a></span>(int&nbsp;Mode,
            java.lang.String&nbsp;lpFileName)</code>&nbsp;</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMValidTempleteNum--">EMValidTempleteNum</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EMVfyPSW-char:A-">EMVfyPSW</a></span>(char[]&nbsp;PassWord)</code>&nbsp;</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#EventReport--">EventReport</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#FactoryReset--">FactoryReset</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#FingerprintSwitchUart-java.lang.String-">FingerprintSwitchUart</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#FingerprintSwitchUsb-java.lang.String-">FingerprintSwitchUsb</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#fips_encryption_decryption_EX-byte:A-int-byte:A-int-byte-java.lang.Object-int-">fips_encryption_decryption_EX</a></span>(byte[]&nbsp;jinbuf,
                             int&nbsp;jdlen,
                             byte[]&nbsp;jkeybuf,
                             int&nbsp;keylen,
                             byte&nbsp;jmode,
                             java.lang.Object&nbsp;mContext,
                             int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#fips_encryption_decryption-byte:A-int-byte:A-int-byte-">fips_encryption_decryption</a></span>(byte[]&nbsp;jinbuf,
                          int&nbsp;jdlen,
                          byte[]&nbsp;jkeybuf,
                          int&nbsp;keylen,
                          byte&nbsp;jmode)</code>&nbsp;</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#GetAccState--">GetAccState</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#GetGen2--">GetGen2</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#GetLastError--">GetLastError</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#getPsamId--">getPsamId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#GetQTPara--">GetQTPara</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#GetTemperature--">GetTemperature</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#HardwareVersion_125k--">HardwareVersion_125k</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#HID_GetUid--">HID_GetUid</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Infared_IDPOWER--">Infared_IDPOWER</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Infared_IDPOWER07--">Infared_IDPOWER07</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Infrared_Close-java.lang.String-">Infrared_Close</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Infrared_Open-java.lang.String-java.lang.String-int-int-int-int-">Infrared_Open</a></span>(java.lang.String&nbsp;device,
             java.lang.String&nbsp;uart,
             int&nbsp;baudrate,
             int&nbsp;databits,
             int&nbsp;stopbits,
             int&nbsp;check)</code>&nbsp;</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#infrared_read--">infrared_read</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Infrared_SwitchSerialPort-java.lang.String-java.lang.String-int-int-int-int-">Infrared_SwitchSerialPort</a></span>(java.lang.String&nbsp;device,
                         java.lang.String&nbsp;uart,
                         int&nbsp;baudrate,
                         int&nbsp;databits,
                         int&nbsp;stopbits,
                         int&nbsp;check)</code>&nbsp;</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#infrared_write-byte:A-int-">infrared_write</a></span>(byte[]&nbsp;data,
              int&nbsp;len)</code>&nbsp;</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ioctl_gpio-java.lang.String-int-int-">ioctl_gpio</a></span>(java.lang.String&nbsp;device,
          int&nbsp;gpio,
          int&nbsp;on)</code>&nbsp;</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_authentication-int-int-char:A-int-">ISO14443A_authentication</a></span>(int&nbsp;iMode,
                        int&nbsp;iBlock,
                        char[]&nbsp;pszKey,
                        int&nbsp;iLenKey)</code>
<div class="block">ISO14443A_authentication 验证A卡秘钥 传入参数：1）iMode密钥验证模式 0 为验证A密钥；1为验证B密钥
 2）iBlock要验证密钥的绝对块号 块号范围 0~633）pszKey密钥内容4）iLenKey密钥长度 最大长度为6字节
 返回值：0为成功，非0为失败状态码</div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_BUS_CMD-char:A-char:A-int-int-">ISO14443A_BUS_CMD</a></span>(char[]&nbsp;jtxtimebuf,
                 char[]&nbsp;jtxuidbuf,
                 int&nbsp;jtxuidnum,
                 int&nbsp;jtxflag)</code>&nbsp;</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_cpu_command-char:A-int-">ISO14443A_cpu_command</a></span>(char[]&nbsp;pszCOS,
                     int&nbsp;iLenCOS)</code>
<div class="block">ISO14443A_cpu_command CPU卡 T=CL发送COS指令 传入参数：1）pszCOS传输的COS指令内容2）iLenCOS
 传输的COS指令内容长度返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据
 2)如果状态位表示成功，第1个元素表明后续还有的数据长度3）从第2个元素开始为CPU卡返回的COS指令内容</div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_cpu_rats--">ISO14443A_cpu_rats</a></span>()</code>
<div class="block">ISO14443A_cpu_rats CPU卡RATS操作指令（TYPE A类型） 传入参数：无
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2)如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始为CPU卡返回的rats操作数据</div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_cpu_reset--">ISO14443A_cpu_reset</a></span>()</code>
<div class="block">ISO14443A_cpu_reset CPU卡复位操作指令（TYPE A类型） 传入参数：无
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2)如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始为CPU卡返回的reset操作数据</div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_decrement-int-int-int-">ISO14443A_decrement</a></span>(int&nbsp;iBlockValue,
                   int&nbsp;iBlockResult,
                   int&nbsp;iValue)</code>
<div class="block">ISO14443A_decrement 电子钱包扣费 传入参数：1）iBlockValue当前金额所在块
 2）iBlockResult扣值后剩余金额保存的块3）iValue金额返回值：0为成功，非0为失败状态码</div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_increment-int-int-int-">ISO14443A_increment</a></span>(int&nbsp;iBlockValue,
                   int&nbsp;iBlockResult,
                   int&nbsp;iValue)</code>
<div class="block">ISO14443A_increment 电子钱包充值 传入参数：1）iBlockValue当前金额所在块2）充值后剩余金额保存的块
 3）iValue金额返回值：0为成功，非0为失败状态码</div>
</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_initval-int-int-">ISO14443A_initval</a></span>(int&nbsp;iBlock,
                 int&nbsp;iValue)</code>
<div class="block">ISO14443A_initval 电子钱包初始化 传入参数：1）iBlock要写入数据的绝对块号 块号范围 0~632）iValue 初始金额
 返回值：0为成功，非0为失败状态码</div>
</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_mifareone_alldata_read-int-char:A-">ISO14443A_mifareone_alldata_read</a></span>(int&nbsp;iBlock,
                                char[]&nbsp;pszKey)</code>&nbsp;</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_read-int-">ISO14443A_read</a></span>(int&nbsp;iBlock)</code>
<div class="block">ISO14443A_read 读取电子标签内容 传入参数：1）iBlock要读取数据的绝对块号 块号范围 0~63
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2）如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始是读到的的内容</div>
</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_readval-int-">ISO14443A_readval</a></span>(int&nbsp;iBlock)</code>
<div class="block">ISO14443A_readval 读取电子钱包余额 传入参数：1）iBlock要读取数据的绝对块号 块号范围 0~63
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2)第1个元素是读取到的余额</div>
</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_request-java.lang.String-int-">ISO14443A_request</a></span>(java.lang.String&nbsp;device,
                 int&nbsp;iMode)</code>
<div class="block">ISO14443A_request 获取A卡ID号 传入参数：1）device 设备类型 "C3000"或者"C4000"2）iMode
 0呼叫未进入休眠状态的标签，1呼叫所有的标签返回值：数组形式返回，1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据
 2）如果状态位表示成功，第1个元素表明后续还有的数据长度3）从第二个元素开始的数据格式为：
 ATQA(2Byte)+UID长度(1Byte)+UID[]+SAK 判断卡片类型通过</div>
</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_restore-int-">ISO14443A_restore</a></span>(int&nbsp;iBlock)</code>
<div class="block">ISO14443A_restore 将EEPROM中的内容 传入卡的内部寄存器传入参数：1）iBlockValue当前金额所在块
 返回值：0为成功，非0为失败状态码</div>
</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_transfer-int-">ISO14443A_transfer</a></span>(int&nbsp;iBlock)</code>
<div class="block">ISO14443A_transfer 将寄存器的内容传送到EEPROM中； 传入参数：1）iBlockValue当前金额所在块
 返回值：0为成功，非0为失败状态码</div>
</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_ul_read-int-">ISO14443A_ul_read</a></span>(int&nbsp;iBlock)</code>
<div class="block">ISO14443A_ul_read 读取电子标签内容 传入参数：1）iBlock要读取数据的绝对块号 块号范围 0~63
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据 2）如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始是读到的的内容</div>
</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_ul_write-int-char:A-int-">ISO14443A_ul_write</a></span>(int&nbsp;iBlock,
                  char[]&nbsp;pszData,
                  int&nbsp;iLenData)</code>
<div class="block">ISO14443A_ul_write 将指定内容写入电子标签 传入参数：1）iBlock 写入数据的绝对块号 （0-3块不能写入数据）
 2)*pszData 写入的数据信息3)iLenData 写入数据信息的长度(4Byte)返回值：0为成功，非0为失败状态码</div>
</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_unvarnished_transfer-byte:A-int-byte:A-">ISO14443A_unvarnished_transfer</a></span>(byte[]&nbsp;sendData,
                              int&nbsp;sendDataLen,
                              byte[]&nbsp;receiveData)</code>&nbsp;</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443A_write-int-char:A-int-">ISO14443A_write</a></span>(int&nbsp;iBlock,
               char[]&nbsp;pszData,
               int&nbsp;iLenData)</code>
<div class="block">ISO14443A_write 将指定内容写入电子标签中 传入参数：1）iBlock要写入数据的绝对块号 块号范围 0~63
 2）pszData要写入的数据内容3）要写入的数据内容的长度返回值：0为成功，非0为失败状态码</div>
</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443B_cpu_command-char:A-int-">ISO14443B_cpu_command</a></span>(char[]&nbsp;pszCOS,
                     int&nbsp;iLenCOS)</code>
<div class="block">ISO14443B_cpu_command CPU卡 T=CL发送COS指令 传入参数：1）pszCOS传输的COS指令内容2）iLenCOS
 传输的COS指令内容长度返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据
 2)如果状态位表示成功，第1个元素表明后续还有的数据长度3）从第2个元素开始为CPU卡返回的COS指令内容</div>
</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443B_cpu_reset--">ISO14443B_cpu_reset</a></span>()</code>
<div class="block">ISO14443B_cpu_reset CPU卡复位操作指令（TYPE B类型） 传入参数：无
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2)如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始为CPU卡返回的reset操作数据</div>
</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO14443B_UID--">ISO14443B_UID</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i146" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_GenericFunction-char-char:A-char-">ISO15693_GenericFunction</a></span>(char&nbsp;Command,
                        char[]&nbsp;jdatabuf,
                        char&nbsp;jdatalen)</code>&nbsp;</td>
</tr>
<tr id="i147" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_GenericFunctionEx-char-char-char:A-char-">ISO15693_GenericFunctionEx</a></span>(char&nbsp;jCommand,
                          char&nbsp;jIcMfg,
                          char[]&nbsp;jdatabuf,
                          char&nbsp;jdatalen)</code>&nbsp;</td>
</tr>
<tr id="i148" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_getMultipleBlockSecurityStatus-int-char:A-int-int-int-">ISO15693_getMultipleBlockSecurityStatus</a></span>(int&nbsp;iMode,
                                       char[]&nbsp;pszUID,
                                       int&nbsp;iLenUID,
                                       int&nbsp;startblock,
                                       int&nbsp;blocknum)</code>
<div class="block">ISO15693_getMultipleBlockSecurityStatus 获取多块安全状态 传入参数：1）iMode 0
 非SELECT状态，不传UID NXP I CODE SLI 标签 1 SELECT状态，不传UID NXP I CODE SLI 标签 2
 非SELECT状态，传UID NXP I CODE SLI 标签 3 SELECT状态，传UID NXP I CODE SLI 标签 4
 非SELECT状态 不传UID TI 标签 5 SELECT状态 不传UID TI 标签 6 非SELECT状态 传UID TI 标签 7
 SELECT状态 传UID TI 标签2）pszUID：UID3）iLenUID：UID长度4）Startblock：起始块号
 5）blocknum：一共读取块数返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据
 2)如果状态位表示成功，第1个元素表明后续还有的数据长度3）从第2个元素开始为多块安全状态信息</div>
</td>
</tr>
<tr id="i149" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_getSystemInformation-int-char:A-int-">ISO15693_getSystemInformation</a></span>(int&nbsp;iMode,
                             char[]&nbsp;pszUID,
                             int&nbsp;iLenUID)</code>
<div class="block">ISO15693_getSystemInformation 获取电子标签信息 传入参数：1）iMode 0 非SELECT状态，不传UID NXP
 I CODE SLI 标签 1 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I
 CODE SLI 标签 3 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5
 SELECT状态 不传UID TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签
 2）pszUID：UID3）iLenUID：UID长度返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据
 2)如果状态位表示成功，第1个元素表明后续还有的数据长度3）从第2个元素开始为电子标签信息</div>
</td>
</tr>
<tr id="i150" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_inventory-int-int-">ISO15693_inventory</a></span>(int&nbsp;iMode,
                  int&nbsp;iAFI)</code>
<div class="block">ISO15693_inventory 读取卡片UID 传入参数：1）iMode 0 多张卡呼叫 不带AFI，1 单张卡呼叫 不带AFI，2
 多张卡呼叫 带AFI ，3 单张卡呼叫 带AFI2）iAFI AFI值
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2)如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始为15693卡的ID号</div>
</td>
</tr>
<tr id="i151" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_lockAFI-int-char:A-int-">ISO15693_lockAFI</a></span>(int&nbsp;iMode,
                char[]&nbsp;pszUID,
                int&nbsp;iLenUID)</code>
<div class="block">ISO15693_lockAFI 锁AFI 传入参数：1）iMode 0 非SELECT状态，不传UID NXP I CODE SLI 标签 1
 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I CODE SLI 标签 3
 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5 SELECT状态 不传UID
 TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签2）pszUID：UID
 3）iLenUID：UID长度返回值：0成功，非0状态码</div>
</td>
</tr>
<tr id="i152" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_lockBlock-int-char:A-int-int-int-">ISO15693_lockBlock</a></span>(int&nbsp;iMode,
                  char[]&nbsp;pszUID,
                  int&nbsp;iLenUID,
                  int&nbsp;startblock,
                  int&nbsp;blocknum)</code>
<div class="block">ISO15693_lockBlock 锁定数据块 传入参数：1）iMode 0 非SELECT状态，不传UID NXP I CODE SLI 标签
 1 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I CODE SLI 标签 3
 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5 SELECT状态 不传UID
 TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签2）pszUID：UID
 3）iLenUID：UID长度4）Startblock：起始块号5）blocknum：一共写入多少块返回值：0成功，非0状态码</div>
</td>
</tr>
<tr id="i153" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_lockDSFID-int-char:A-int-">ISO15693_lockDSFID</a></span>(int&nbsp;iMode,
                  char[]&nbsp;pszUID,
                  int&nbsp;iLenUID)</code>
<div class="block">ISO15693_lockDSFID 锁DSFID 传入参数：1）iMode 0 非SELECT状态，不传UID NXP I CODE SLI
 标签 1 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I CODE SLI 标签
 3 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5 SELECT状态
 不传UID TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签2）pszUID：UID
 3）iLenUID：UID长度返回值：0成功，非0状态码</div>
</td>
</tr>
<tr id="i154" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_read_sm_ex-int-char:A-int-int-int-">ISO15693_read_sm_ex</a></span>(int&nbsp;iMode,
                   char[]&nbsp;pszUID,
                   int&nbsp;iLenUID,
                   int&nbsp;startblock,
                   int&nbsp;blocknum)</code>&nbsp;</td>
</tr>
<tr id="i155" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_read_sm-int-char:A-int-int-int-">ISO15693_read_sm</a></span>(int&nbsp;iMode,
                char[]&nbsp;pszUID,
                int&nbsp;iLenUID,
                int&nbsp;startblock,
                int&nbsp;blocknum)</code>
<div class="block">ISO15693_read_sm 读取卡片内部数据 传入参数：
 1）iMode 0   非SELECT状态，不传UID NXP I CODE SLI标签
 1 SELECT状态，不传UID NXP I CODE SLI 标签
 2 非SELECT状态，传UID NXP I CODE SLI 标签
 3 SELECT状态，传UID NXP I CODE SLI 标签
 4 非SELECT状态 不传UID TI 标签
 5 SELECT状态不传UID TI 标签
 6 非SELECT状态 传UID TI 标签
 7 SELECT状态 传UID TI 标签
 2）pszUID：UID
 3）iLenUID：UID长度
 4）Startblock：起始块号
 5）Blocknum：一共读取多少块（1-10）
 返回值：
 1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据
 2)如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始为读到的数据内容</div>
</td>
</tr>
<tr id="i156" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_transferCommand-char:A-int-">ISO15693_transferCommand</a></span>(char[]&nbsp;pszCmd,
                        int&nbsp;iComLen)</code>
<div class="block">ISO15693_transferCommand 15693透传函数 传入参数：1）UINT8 *pszCmd传输的指令内容2）iComLen
 指令长度返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2)如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始为卡片返回的响应数据</div>
</td>
</tr>
<tr id="i157" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_write_sm_ex-int-char:A-int-int-int-char:A-int-">ISO15693_write_sm_ex</a></span>(int&nbsp;iMode,
                    char[]&nbsp;pszUID,
                    int&nbsp;iLenUID,
                    int&nbsp;startblock,
                    int&nbsp;blocknum,
                    char[]&nbsp;pszData,
                    int&nbsp;iWriteLen)</code>&nbsp;</td>
</tr>
<tr id="i158" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_write_sm-int-char:A-int-int-int-char:A-int-">ISO15693_write_sm</a></span>(int&nbsp;iMode,
                 char[]&nbsp;pszUID,
                 int&nbsp;iLenUID,
                 int&nbsp;startblock,
                 int&nbsp;blocknum,
                 char[]&nbsp;pszData,
                 int&nbsp;iWriteLen)</code>
<div class="block">ISO15693_write_sm 向卡片内部写入数据 传入参数：1）iMode 0 非SELECT状态，不传UID NXP I CODE SLI
 标签 1 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I CODE SLI 标签
 3 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5 SELECT状态
 不传UID TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签2）pszUID：UID
 3）iLenUID：UID长度4）Startblock：起始块号5）blocknum：一共写入多少块6）pszData 写入的数据信息
 7）iWriteLen：写入的数据长度返回值：0成功，非0状态码</div>
</td>
</tr>
<tr id="i159" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_writeAFI-int-char:A-int-int-">ISO15693_writeAFI</a></span>(int&nbsp;iMode,
                 char[]&nbsp;pszUID,
                 int&nbsp;iLenUID,
                 int&nbsp;iAFI)</code>
<div class="block">ISO15693_writeAFI 写AFI 传入参数：1）iMode 0 非SELECT状态，不传UID NXP I CODE SLI 标签 1
 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I CODE SLI 标签 3
 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5 SELECT状态 不传UID
 TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签2）pszUID：UID
 3）iLenUID：UID长度4）iAFI：AFI内容返回值：0成功，非0状态码</div>
</td>
</tr>
<tr id="i160" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ISO15693_writeDSFID-int-char:A-int-int-">ISO15693_writeDSFID</a></span>(int&nbsp;iMode,
                   char[]&nbsp;pszUID,
                   int&nbsp;iLenUID,
                   int&nbsp;iDSFID)</code>
<div class="block">ISO15693_writeDSFID 写DSFID 传入参数：1）iMode 0 非SELECT状态，不传UID NXP I CODE SLI
 标签 1 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I CODE SLI 标签
 3 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5 SELECT状态
 不传UID TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签2）pszUID：UID
 3）iLenUID：UID长度4）iDSFID：DSFID内容返回值：0成功，非0状态码</div>
</td>
</tr>
<tr id="i161" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#LedOff-java.lang.String-int-">LedOff</a></span>(java.lang.String&nbsp;device,
      int&nbsp;led)</code>&nbsp;</td>
</tr>
<tr id="i162" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#LedOn-java.lang.String-int-">LedOn</a></span>(java.lang.String&nbsp;device,
     int&nbsp;led)</code>&nbsp;</td>
</tr>
<tr id="i163" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleFree-java.lang.String-">ModuleFree</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i164" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleInit-java.lang.String-java.lang.String-int-int-">ModuleInit</a></span>(java.lang.String&nbsp;device,
          java.lang.String&nbsp;path,
          int&nbsp;baudrate,
          int&nbsp;module)</code>&nbsp;</td>
</tr>
<tr id="i165" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleInitEX-java.lang.String-java.lang.String-int-int-int-int-int-">ModuleInitEX</a></span>(java.lang.String&nbsp;device,
            java.lang.String&nbsp;path,
            int&nbsp;baudrate,
            int&nbsp;module,
            int&nbsp;databits,
            int&nbsp;stopbits,
            int&nbsp;check)</code>&nbsp;</td>
</tr>
<tr id="i166" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModulePowerOff-java.lang.String-int-">ModulePowerOff</a></span>(java.lang.String&nbsp;device,
              int&nbsp;module)</code>&nbsp;</td>
</tr>
<tr id="i167" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModulePowerOn-java.lang.String-int-">ModulePowerOn</a></span>(java.lang.String&nbsp;device,
             int&nbsp;module)</code>&nbsp;</td>
</tr>
<tr id="i168" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleReceive--">ModuleReceive</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i169" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleReceiveEx-byte:A-">ModuleReceiveEx</a></span>(byte[]&nbsp;receive)</code>&nbsp;</td>
</tr>
<tr id="i170" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleSend-byte:A-int-">ModuleSend</a></span>(byte[]&nbsp;data,
          int&nbsp;len)</code>&nbsp;</td>
</tr>
<tr id="i171" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ModuleSendAndReceive-byte:A-int-byte:A-int-">ModuleSendAndReceive</a></span>(byte[]&nbsp;send,
                    int&nbsp;sendLen,
                    byte[]&nbsp;outData,
                    int&nbsp;outLen)</code>&nbsp;</td>
</tr>
<tr id="i172" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoCancel--">MorphoCancel</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i173" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoCapture-char-char-">MorphoCapture</a></span>(char&nbsp;flag,
             char&nbsp;encryptflag)</code>&nbsp;</td>
</tr>
<tr id="i174" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoCapturePKComp-char-char-">MorphoCapturePKComp</a></span>(char&nbsp;processflag,
                   char&nbsp;encryptflag)</code>&nbsp;</td>
</tr>
<tr id="i175" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoDescriptor--">MorphoDescriptor</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i176" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoEnroll-char-char:A-char:A-java.lang.String-int-">MorphoEnroll</a></span>(char&nbsp;flag,
            char[]&nbsp;id,
            char[]&nbsp;name,
            java.lang.String&nbsp;lpFileName,
            int&nbsp;getImage)</code>&nbsp;</td>
</tr>
<tr id="i177" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoEraseAllBase--">MorphoEraseAllBase</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i178" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoFingerFree-java.lang.String-">MorphoFingerFree</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i179" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoFingerInit-java.lang.String-java.lang.String-int-">MorphoFingerInit</a></span>(java.lang.String&nbsp;device,
                java.lang.String&nbsp;uart,
                int&nbsp;baudrate)</code>&nbsp;</td>
</tr>
<tr id="i180" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoFingerMessage--">MorphoFingerMessage</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i181" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoGetSecurityLevel--">MorphoGetSecurityLevel</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i182" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoGrab-char-java.lang.String-">MorphoGrab</a></span>(char&nbsp;var1,
          java.lang.String&nbsp;imgName)</code>&nbsp;</td>
</tr>
<tr id="i183" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoIdentify-char-">MorphoIdentify</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i184" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoLoadKs-byte:A-">MorphoLoadKs</a></span>(byte[]&nbsp;keybuf)</code>&nbsp;</td>
</tr>
<tr id="i185" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoPIDSN--">MorphoPIDSN</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i186" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoSetSecurityLevel-int-">MorphoSetSecurityLevel</a></span>(int&nbsp;level)</code>&nbsp;</td>
</tr>
<tr id="i187" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoStop--">MorphoStop</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i188" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#MorphoVerifyPKComp-char-byte:A-int-">MorphoVerifyPKComp</a></span>(char&nbsp;processflag,
                  byte[]&nbsp;jfgbuf,
                  int&nbsp;jfglen)</code>&nbsp;</td>
</tr>
<tr id="i189" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#OpenSerail-java.lang.String-int-int-int-int-">OpenSerail</a></span>(java.lang.String&nbsp;path,
          int&nbsp;baudrate,
          int&nbsp;databits,
          int&nbsp;stopbits,
          int&nbsp;check)</code>&nbsp;</td>
</tr>
<tr id="i190" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#OTG_GPIO_OFF-java.lang.String-">OTG_GPIO_OFF</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i191" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#OTG_GPIO_ON-java.lang.String-">OTG_GPIO_ON</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i192" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#POWER_LED_OFF-java.lang.String-">POWER_LED_OFF</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i193" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#POWER_LED_ON-java.lang.String-">POWER_LED_ON</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i194" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PrinterFree-java.lang.String-">PrinterFree</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i195" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PrinterInit-java.lang.String-java.lang.String-int-int-int-">PrinterInit</a></span>(java.lang.String&nbsp;device,
           java.lang.String&nbsp;path,
           int&nbsp;baudrate,
           int&nbsp;module,
           int&nbsp;isUpgrade)</code>&nbsp;</td>
</tr>
<tr id="i196" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PrinterReceive-byte:A-int-">PrinterReceive</a></span>(byte[]&nbsp;joutData,
              int&nbsp;jsendLeng)</code>&nbsp;</td>
</tr>
<tr id="i197" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PrinterSend-byte:A-int-">PrinterSend</a></span>(byte[]&nbsp;jsend,
           int&nbsp;jsendLeng)</code>&nbsp;</td>
</tr>
<tr id="i198" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PrinterSendAndReceive-byte:A-int-byte:A-int-int-">PrinterSendAndReceive</a></span>(byte[]&nbsp;sendjdata,
                     int&nbsp;sendjlen,
                     byte[]&nbsp;outjdata,
                     int&nbsp;outjlen,
                     int&nbsp;delayS)</code>&nbsp;</td>
</tr>
<tr id="i199" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PrinterSerialPortOpen-java.lang.String-java.lang.String-int-">PrinterSerialPortOpen</a></span>(java.lang.String&nbsp;device,
                     java.lang.String&nbsp;path,
                     int&nbsp;baudrate)</code>&nbsp;</td>
</tr>
<tr id="i200" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Psam_Cmd-java.lang.String-char-char:A-int-">Psam_Cmd</a></span>(java.lang.String&nbsp;device,
        char&nbsp;cmd,
        char[]&nbsp;cmddata,
        int&nbsp;cmdlen)</code>&nbsp;</td>
</tr>
<tr id="i201" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Psam_Free-java.lang.String-">Psam_Free</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i202" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Psam_Init-java.lang.String-">Psam_Init</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i203" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Psam_InitXy-java.lang.String-">Psam_InitXy</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i204" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PSAM_UPDATE-int-int-int-byte:A-">PSAM_UPDATE</a></span>(int&nbsp;jpackage_sum_number,
           int&nbsp;jpackage_which_number,
           int&nbsp;pszData_len,
           byte[]&nbsp;pszData)</code>&nbsp;</td>
</tr>
<tr id="i205" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTCapture-char-">PTCapture</a></span>(char&nbsp;index)</code>&nbsp;</td>
</tr>
<tr id="i206" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTConvertTemplateEx-byte-byte-byte:A-int-">PTConvertTemplateEx</a></span>(byte&nbsp;iCounts,
                   byte&nbsp;jtarget_type,
                   byte[]&nbsp;srcbuf,
                   int&nbsp;jsrclen)</code>&nbsp;</td>
</tr>
<tr id="i207" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTDeleteAllFingers-char-">PTDeleteAllFingers</a></span>(char&nbsp;index)</code>&nbsp;</td>
</tr>
<tr id="i208" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTEnroll-char-">PTEnroll</a></span>(char&nbsp;index)</code>&nbsp;</td>
</tr>
<tr id="i209" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTExit-java.lang.String-">PTExit</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i210" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTGetAppData-char-">PTGetAppData</a></span>(char&nbsp;index)</code>&nbsp;</td>
</tr>
<tr id="i211" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTGrab-char-">PTGrab</a></span>(char&nbsp;index)</code>&nbsp;</td>
</tr>
<tr id="i212" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTGUICancel-char-">PTGUICancel</a></span>(char&nbsp;index)</code>&nbsp;</td>
</tr>
<tr id="i213" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTInfo-char-">PTInfo</a></span>(char&nbsp;index)</code>&nbsp;</td>
</tr>
<tr id="i214" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTInit-java.lang.String-java.lang.String-">PTInit</a></span>(java.lang.String&nbsp;device,
      java.lang.String&nbsp;uart)</code>&nbsp;</td>
</tr>
<tr id="i215" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTListAllFingers-char-">PTListAllFingers</a></span>(char&nbsp;index)</code>&nbsp;</td>
</tr>
<tr id="i216" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTResponseContinue-char-">PTResponseContinue</a></span>(char&nbsp;index)</code>&nbsp;</td>
</tr>
<tr id="i217" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTSetAppData-char-char:A-char-">PTSetAppData</a></span>(char&nbsp;index,
            char[]&nbsp;data,
            char&nbsp;len)</code>&nbsp;</td>
</tr>
<tr id="i218" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTStoreFinger-byte-byte:A-">PTStoreFinger</a></span>(byte&nbsp;iCounts,
             byte[]&nbsp;templateData)</code>&nbsp;</td>
</tr>
<tr id="i219" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTTLSFContinue-char-">PTTLSFContinue</a></span>(char&nbsp;index)</code>&nbsp;</td>
</tr>
<tr id="i220" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTVerify-char-char:A-int-">PTVerify</a></span>(char&nbsp;index,
        char[]&nbsp;data,
        int&nbsp;len)</code>&nbsp;</td>
</tr>
<tr id="i221" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#PTVerifyALL-char-">PTVerifyALL</a></span>(char&nbsp;index)</code>&nbsp;</td>
</tr>
<tr id="i222" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#R2000_FreHopSet-int-">R2000_FreHopSet</a></span>(int&nbsp;fre)</code>&nbsp;</td>
</tr>
<tr id="i223" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#R2000_OPENMODE--">R2000_OPENMODE</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i224" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Request_WriteSpecialPart-int-byte:A-">Request_WriteSpecialPart</a></span>(int&nbsp;len,
                        byte[]&nbsp;buf)</code>&nbsp;</td>
</tr>
<tr id="i225" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Requset_ReadPart-byte-byte-byte-byte-byte-byte-">Requset_ReadPart</a></span>(byte&nbsp;speed,
                byte&nbsp;des,
                byte&nbsp;flag,
                byte&nbsp;partnum,
                byte&nbsp;ptr,
                byte&nbsp;len)</code>&nbsp;</td>
</tr>
<tr id="i226" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Requset_WritePart-byte:A-byte-byte-byte-byte:A-">Requset_WritePart</a></span>(byte[]&nbsp;tid,
                 byte&nbsp;num,
                 byte&nbsp;startaddr,
                 byte&nbsp;len,
                 byte[]&nbsp;buf)</code>&nbsp;</td>
</tr>
<tr id="i227" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_AddApp-char:A-int-int-">RF_ISO14443A_DESFIRE_AddApp</a></span>(char[]&nbsp;AppId,
                           int&nbsp;KeySetting,
                           int&nbsp;FileNums)</code>&nbsp;</td>
</tr>
<tr id="i228" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_AddStdFile-int-int-char:A-int-">RF_ISO14443A_DESFIRE_AddStdFile</a></span>(int&nbsp;FileNo,
                               int&nbsp;CommSet,
                               char[]&nbsp;AccessRight,
                               int&nbsp;FileSize)</code>&nbsp;</td>
</tr>
<tr id="i229" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_AddValueFile-int-int-char:A-int-int-int-">RF_ISO14443A_DESFIRE_AddValueFile</a></span>(int&nbsp;FileNo,
                                 int&nbsp;CommSet,
                                 char[]&nbsp;AccessRights,
                                 int&nbsp;MinValue,
                                 int&nbsp;MaxValue,
                                 int&nbsp;InitValue)</code>&nbsp;</td>
</tr>
<tr id="i230" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_Auth-int-char:A-int-">RF_ISO14443A_DESFIRE_Auth</a></span>(int&nbsp;KeyNo,
                         char[]&nbsp;KeyBuf,
                         int&nbsp;KeyLen)</code>&nbsp;</td>
</tr>
<tr id="i231" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_ChangeFileSetting-int-int-char:A-">RF_ISO14443A_DESFIRE_ChangeFileSetting</a></span>(int&nbsp;FileNo,
                                      int&nbsp;CommSet,
                                      char[]&nbsp;AccessRights)</code>&nbsp;</td>
</tr>
<tr id="i232" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_ChangeKey-int-char:A-int-">RF_ISO14443A_DESFIRE_ChangeKey</a></span>(int&nbsp;KeyNo,
                              char[]&nbsp;KeyBuf,
                              int&nbsp;KeyLen)</code>&nbsp;</td>
</tr>
<tr id="i233" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_ChangeKeySetting-int-">RF_ISO14443A_DESFIRE_ChangeKeySetting</a></span>(int&nbsp;KeySetting)</code>&nbsp;</td>
</tr>
<tr id="i234" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_Cpysel-int-">RF_ISO14443A_DESFIRE_Cpysel</a></span>(int&nbsp;cpyType)</code>&nbsp;</td>
</tr>
<tr id="i235" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_CreditValueFile-int-int-">RF_ISO14443A_DESFIRE_CreditValueFile</a></span>(int&nbsp;FileNo,
                                    int&nbsp;CreValue)</code>&nbsp;</td>
</tr>
<tr id="i236" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_DebitValueFile-int-int-">RF_ISO14443A_DESFIRE_DebitValueFile</a></span>(int&nbsp;FileNo,
                                   int&nbsp;DeValue)</code>&nbsp;</td>
</tr>
<tr id="i237" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_DelApp-char:A-">RF_ISO14443A_DESFIRE_DelApp</a></span>(char[]&nbsp;AppId)</code>&nbsp;</td>
</tr>
<tr id="i238" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_DelFile-int-">RF_ISO14443A_DESFIRE_DelFile</a></span>(int&nbsp;FileNo)</code>&nbsp;</td>
</tr>
<tr id="i239" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_FormatCard--">RF_ISO14443A_DESFIRE_FormatCard</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i240" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_GetApps--">RF_ISO14443A_DESFIRE_GetApps</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i241" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_GetFileIds--">RF_ISO14443A_DESFIRE_GetFileIds</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i242" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_GetFileSetting-int-">RF_ISO14443A_DESFIRE_GetFileSetting</a></span>(int&nbsp;FileNo)</code>&nbsp;</td>
</tr>
<tr id="i243" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_GetKeySetting--">RF_ISO14443A_DESFIRE_GetKeySetting</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i244" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_GetPiccInfo--">RF_ISO14443A_DESFIRE_GetPiccInfo</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i245" class="rowColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_GetValueFile-int-">RF_ISO14443A_DESFIRE_GetValueFile</a></span>(int&nbsp;FileNo)</code>&nbsp;</td>
</tr>
<tr id="i246" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_RatPss--">RF_ISO14443A_DESFIRE_RatPss</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i247" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_ReadStdFile-int-int-int-">RF_ISO14443A_DESFIRE_ReadStdFile</a></span>(int&nbsp;FileNo,
                                int&nbsp;OffSet,
                                int&nbsp;DataSize)</code>&nbsp;</td>
</tr>
<tr id="i248" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_SelApp-byte:A-">RF_ISO14443A_DESFIRE_SelApp</a></span>(byte[]&nbsp;AppId)</code>&nbsp;</td>
</tr>
<tr id="i249" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RF_ISO14443A_DESFIRE_WriteStdFile-int-int-int-char:A-">RF_ISO14443A_DESFIRE_WriteStdFile</a></span>(int&nbsp;FileNo,
                                 int&nbsp;OffSet,
                                 int&nbsp;DataSize,
                                 char[]&nbsp;DataBuf)</code>&nbsp;</td>
</tr>
<tr id="i250" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RFID_free-java.lang.String-">RFID_free</a></span>(java.lang.String&nbsp;device)</code>
<div class="block">RFID_free 模块释放</div>
</td>
</tr>
<tr id="i251" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RFID_GetVer--">RFID_GetVer</a></span>()</code>
<div class="block">RFID_GetVer 获取RFID模块硬件版本</div>
</td>
</tr>
<tr id="i252" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RFID_init-java.lang.String-java.lang.String-int-int-">RFID_init</a></span>(java.lang.String&nbsp;device,
         java.lang.String&nbsp;uart,
         int&nbsp;baudrate,
         int&nbsp;isRfOff)</code>
<div class="block">RFID_init 模块初始化</div>
</td>
</tr>
<tr id="i253" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#RFID_UPDATE-int-int-int-byte:A-">RFID_UPDATE</a></span>(int&nbsp;jpackage_sum_number,
           int&nbsp;jpackage_which_number,
           int&nbsp;pszData_len,
           byte[]&nbsp;pszData)</code>&nbsp;</td>
</tr>
<tr id="i254" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ScanerLed_Free-java.lang.String-">ScanerLed_Free</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i255" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ScanerLed_Init-java.lang.String-">ScanerLed_Init</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i256" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ScanerLed_Off-java.lang.String-">ScanerLed_Off</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i257" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ScanerLed_On-java.lang.String-">ScanerLed_On</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i258" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#SerailClose-java.lang.String-">SerailClose</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i259" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#SerailOpen-java.lang.String-java.lang.String-int-int-int-">SerailOpen</a></span>(java.lang.String&nbsp;device,
          java.lang.String&nbsp;path,
          int&nbsp;baudrate,
          int&nbsp;module,
          int&nbsp;check)</code>&nbsp;</td>
</tr>
<tr id="i260" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#SetConfig-byte:A-">SetConfig</a></span>(byte[]&nbsp;jbuf)</code>&nbsp;</td>
</tr>
<tr id="i261" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#SetGen2-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">SetGen2</a></span>(char&nbsp;Target,
       char&nbsp;Action,
       char&nbsp;T,
       char&nbsp;Q,
       char&nbsp;StartQ,
       char&nbsp;MinQ,
       char&nbsp;MaxQ,
       char&nbsp;D,
       char&nbsp;C,
       char&nbsp;P,
       char&nbsp;Sel,
       char&nbsp;Session,
       char&nbsp;G,
       char&nbsp;LF)</code>&nbsp;</td>
</tr>
<tr id="i262" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#SetInventoryFixed-int-">SetInventoryFixed</a></span>(int&nbsp;freq)</code>&nbsp;</td>
</tr>
<tr id="i263" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#SetInventorySper--">SetInventorySper</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i264" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#SetInventorySper-int-">SetInventorySper</a></span>(int&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i265" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#SetQTPara-char-">SetQTPara</a></span>(char&nbsp;pszData)</code>&nbsp;</td>
</tr>
<tr id="i266" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#SetTestMode--">SetTestMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i267" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#spClose--">spClose</a></span>()</code>
<div class="block">关闭串口</div>
</td>
</tr>
<tr id="i268" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#SpiFree-java.lang.String-">SpiFree</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i269" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#SpiInit-java.lang.String-">SpiInit</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i270" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#SpiRead-byte:A-int-">SpiRead</a></span>(byte[]&nbsp;OutData,
       int&nbsp;len)</code>&nbsp;</td>
</tr>
<tr id="i271" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#SpiWrite-byte:A-int-">SpiWrite</a></span>(byte[]&nbsp;sendData,
        int&nbsp;len)</code>&nbsp;</td>
</tr>
<tr id="i272" class="altColor">
<td class="colFirst"><code>java.io.FileDescriptor</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#spOpen-java.lang.String-int-int-">spOpen</a></span>(java.lang.String&nbsp;path,
      int&nbsp;baudrate,
      int&nbsp;flags)</code>
<div class="block">打开串口</div>
</td>
</tr>
<tr id="i273" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#TransBuf-byte-int-byte:A-">TransBuf</a></span>(byte&nbsp;oper,
        int&nbsp;len,
        byte[]&nbsp;jbuf)</code>&nbsp;</td>
</tr>
<tr id="i274" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#TransData-byte:A-int-byte-">TransData</a></span>(byte[]&nbsp;tbuf,
         int&nbsp;tlen,
         byte&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i275" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UartSwitch-java.lang.String-int-">UartSwitch</a></span>(java.lang.String&nbsp;device,
          int&nbsp;module)</code>&nbsp;</td>
</tr>
<tr id="i276" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHF706_CloseAndDisconnect--">UHF706_CloseAndDisconnect</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i277" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHF706_OpenAndConnect-java.lang.String-">UHF706_OpenAndConnect</a></span>(java.lang.String&nbsp;path)</code>&nbsp;</td>
</tr>
<tr id="i278" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFAuthenticateCommon-int-int-int-byte:A-int-int-byte:A-">UHFAuthenticateCommon</a></span>(int&nbsp;password,
                     int&nbsp;bank,
                     int&nbsp;addr,
                     byte[]&nbsp;jmData,
                     int&nbsp;mDataBitsLen,
                     int&nbsp;jkeyId,
                     byte[]&nbsp;jtData)</code>&nbsp;</td>
</tr>
<tr id="i279" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockEraseDataRecvData-byte:A-int-">UHFBlockEraseDataRecvData</a></span>(byte[]&nbsp;inData,
                         int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i280" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockEraseDataSendData-byte:A-char-int-int-byte:A-char-int-char-">UHFBlockEraseDataSendData</a></span>(byte[]&nbsp;pszuAccessPwd,
                         char&nbsp;ufBank,
                         int&nbsp;ufPtr,
                         int&nbsp;ufCnt,
                         byte[]&nbsp;ufData,
                         char&nbsp;uBank,
                         int&nbsp;uPtr,
                         char&nbsp;uCnt)</code>&nbsp;</td>
</tr>
<tr id="i281" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockPermalock_Ex-char:A-char-int-int-char:A-char-char-int-char-byte:A-">UHFBlockPermalock_Ex</a></span>(char[]&nbsp;uAccessPwd,
                    char&nbsp;FilterBank,
                    int&nbsp;FilterStartaddr,
                    int&nbsp;FilterLen,
                    char[]&nbsp;FilterData,
                    char&nbsp;ReadLock,
                    char&nbsp;uBank,
                    int&nbsp;uPtr,
                    char&nbsp;uRange,
                    byte[]&nbsp;uMaskbuf)</code>&nbsp;</td>
</tr>
<tr id="i282" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockPermalock-char:A-char-char-char-char:A-char-char-char-char-char:A-">UHFBlockPermalock</a></span>(char[]&nbsp;jucppwd,
                 char&nbsp;bank,
                 char&nbsp;startaddr,
                 char&nbsp;datalen,
                 char[]&nbsp;jucpdatabuf,
                 char&nbsp;readlock,
                 char&nbsp;MB,
                 char&nbsp;blockptr,
                 char&nbsp;blockrange,
                 char[]&nbsp;jucpmask)</code>&nbsp;</td>
</tr>
<tr id="i283" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockWriteData-char:A-char-int-int-char:A-char-int-int-char:A-">UHFBlockWriteData</a></span>(char[]&nbsp;pszuAccessPwd,
                 char&nbsp;ufBank,
                 int&nbsp;ufPtr,
                 int&nbsp;ufCnt,
                 char[]&nbsp;pszuDat,
                 char&nbsp;uBank,
                 int&nbsp;uPtr,
                 int&nbsp;uCnt,
                 char[]&nbsp;pszuWriteData)</code>&nbsp;</td>
</tr>
<tr id="i284" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockWriteDataRecvData-byte:A-int-">UHFBlockWriteDataRecvData</a></span>(byte[]&nbsp;inData,
                         int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i285" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBlockWriteDataSendData-byte:A-char-int-int-byte:A-char-int-char-byte:A-">UHFBlockWriteDataSendData</a></span>(byte[]&nbsp;pszuAccessPwd,
                         char&nbsp;ufBank,
                         int&nbsp;ufPtr,
                         int&nbsp;ufCnt,
                         byte[]&nbsp;ufData,
                         char&nbsp;uBank,
                         int&nbsp;uPtr,
                         char&nbsp;uCnt,
                         byte[]&nbsp;writeDatabuf)</code>&nbsp;</td>
</tr>
<tr id="i286" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTAuthentication-char:A-char-int-int-char:A-char-int-int-char:A-char:A-">UHFBTAuthentication</a></span>(char[]&nbsp;pszuAccessPwd,
                   char&nbsp;ufBank,
                   int&nbsp;ufPtr,
                   int&nbsp;ufCnt,
                   char[]&nbsp;pszuDat,
                   char&nbsp;uBank,
                   int&nbsp;uPtr,
                   int&nbsp;uCnt,
                   char[]&nbsp;pszuWriteData,
                   char[]&nbsp;rev)</code>&nbsp;</td>
</tr>
<tr id="i287" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTDeleteAllTagToFlash--">UHFBTDeleteAllTagToFlash</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i288" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTDeleteAllTagToFlashRecvData-byte:A-int-">UHFBTDeleteAllTagToFlashRecvData</a></span>(byte[]&nbsp;inData,
                                int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i289" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTDeleteAllTagToFlashSendData--">UHFBTDeleteAllTagToFlashSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i290" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTEncReadUser-int-int-byte:A-">UHFBTEncReadUser</a></span>(int&nbsp;jaddr,
                int&nbsp;jlen,
                byte[]&nbsp;jdata)</code>&nbsp;</td>
</tr>
<tr id="i291" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTEncWriteUser-int-int-byte:A-">UHFBTEncWriteUser</a></span>(int&nbsp;jaddr,
                 int&nbsp;jlen,
                 byte[]&nbsp;jindata)</code>&nbsp;</td>
</tr>
<tr id="i292" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTEndUpdate--">UHFBTEndUpdate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i293" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTEraseData-char:A-char-int-char-char:A-char-int-int-">UHFBTEraseData</a></span>(char[]&nbsp;pszuAccessPwd,
              char&nbsp;filterBank,
              int&nbsp;filterPtr,
              char&nbsp;filterCnt,
              char[]&nbsp;filterData,
              char&nbsp;uBank,
              int&nbsp;uPtr,
              int&nbsp;uCnt)</code>&nbsp;</td>
</tr>
<tr id="i294" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTFreHopSet-int-">UHFBTFreHopSet</a></span>(int&nbsp;fre)</code>&nbsp;</td>
</tr>
<tr id="i295" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetAllTagNumFromFlash--">UHFBTGetAllTagNumFromFlash</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i296" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetAllTagNumFromFlashRecvData-byte:A-int-">UHFBTGetAllTagNumFromFlashRecvData</a></span>(byte[]&nbsp;inData,
                                  int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i297" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetAllTagNumFromFlashSendData--">UHFBTGetAllTagNumFromFlashSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i298" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetBarcode-byte:A-">UHFBTGetBarcode</a></span>(byte[]&nbsp;outdata)</code>&nbsp;</td>
</tr>
<tr id="i299" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetBattery--">UHFBTGetBattery</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i300" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetCW--">UHFBTGetCW</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i301" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetGen2-byte:A-">UHFBTGetGen2</a></span>(byte[]&nbsp;outData)</code>&nbsp;</td>
</tr>
<tr id="i302" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetPower--">UHFBTGetPower</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i303" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetPowerValueRecvData-byte:A-int-">UHFBTGetPowerValueRecvData</a></span>(byte[]&nbsp;inData,
                          int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i304" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetSendCmd-byte:A-">UHFBTGetSendCmd</a></span>(byte[]&nbsp;cmd)</code>&nbsp;</td>
</tr>
<tr id="i305" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetTag-byte:A-int-">UHFBTGetTag</a></span>(byte[]&nbsp;data,
           int&nbsp;timeOut)</code>&nbsp;</td>
</tr>
<tr id="i306" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetTagDataFromFlash-byte:A-">UHFBTGetTagDataFromFlash</a></span>(byte[]&nbsp;outData)</code>&nbsp;</td>
</tr>
<tr id="i307" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetTagDataFromFlashRecvData-byte:A-int-">UHFBTGetTagDataFromFlashRecvData</a></span>(byte[]&nbsp;inData,
                                int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i308" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTGetTagDataFromFlashSendData--">UHFBTGetTagDataFromFlashSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i309" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTInventorySingle-byte:A-">UHFBTInventorySingle</a></span>(byte[]&nbsp;outData)</code>&nbsp;</td>
</tr>
<tr id="i310" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTKeydataMac-byte:A-byte:A-">UHFBTKeydataMac</a></span>(byte[]&nbsp;jkeydata,
               byte[]&nbsp;jmac)</code>&nbsp;</td>
</tr>
<tr id="i311" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTKill-char:A-char-int-int-char:A-">UHFBTKill</a></span>(char[]&nbsp;pszuAccessPwd,
         char&nbsp;bank,
         int&nbsp;ptr,
         int&nbsp;cnt,
         char[]&nbsp;pszuData)</code>&nbsp;</td>
</tr>
<tr id="i312" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTLockMemEx-char:A-char-int-int-char:A-char:A-">UHFBTLockMemEx</a></span>(char[]&nbsp;pszuAccessPwd,
              char&nbsp;bank,
              int&nbsp;ptr,
              int&nbsp;cnt,
              char[]&nbsp;pszuData,
              char[]&nbsp;pszuLockData)</code>&nbsp;</td>
</tr>
<tr id="i313" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTOpen2DRecvData-byte:A-int-">UHFBTOpen2DRecvData</a></span>(byte[]&nbsp;inData,
                   int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i314" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTR2000Temperature-byte:A-">UHFBTR2000Temperature</a></span>(byte[]&nbsp;outTemper)</code>&nbsp;</td>
</tr>
<tr id="i315" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTR2000Version-byte:A-">UHFBTR2000Version</a></span>(byte[]&nbsp;outVersion)</code>&nbsp;</td>
</tr>
<tr id="i316" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTReadData-char:A-char-int-int-char:A-char-int-char-">UHFBTReadData</a></span>(char[]&nbsp;pszuAccessPwd,
             char&nbsp;ufBank,
             int&nbsp;ufPtr,
             int&nbsp;ufCnt,
             char[]&nbsp;ufData,
             char&nbsp;uBank,
             int&nbsp;uPtr,
             char&nbsp;uCnt)</code>&nbsp;</td>
</tr>
<tr id="i317" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTReadEpcTidUserMode-int-int-byte:A-">UHFBTReadEpcTidUserMode</a></span>(int&nbsp;rev1,
                       int&nbsp;rev2,
                       byte[]&nbsp;outData)</code>&nbsp;</td>
</tr>
<tr id="i318" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTReBootAPP-char-">UHFBTReBootAPP</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i319" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetBeep-byte-">UHFBTSetBeep</a></span>(byte&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i320" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetBeepRecvData-byte:A-int-">UHFBTSetBeepRecvData</a></span>(byte[]&nbsp;inData,
                    int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i321" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetCW-int-">UHFBTSetCW</a></span>(int&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i322" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetEpcTidUserMode-int-int-int-int-">UHFBTSetEpcTidUserMode</a></span>(int&nbsp;saveFlag,
                      int&nbsp;memory,
                      int&nbsp;address,
                      int&nbsp;length)</code>&nbsp;</td>
</tr>
<tr id="i323" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetGen2-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">UHFBTSetGen2</a></span>(char&nbsp;Target,
            char&nbsp;Action,
            char&nbsp;T,
            char&nbsp;Q,
            char&nbsp;StartQ,
            char&nbsp;MinQ,
            char&nbsp;MaxQ,
            char&nbsp;D,
            char&nbsp;C,
            char&nbsp;P,
            char&nbsp;Sel,
            char&nbsp;Session,
            char&nbsp;G,
            char&nbsp;LF)</code>&nbsp;</td>
</tr>
<tr id="i324" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetPower-byte-">UHFBTSetPower</a></span>(byte&nbsp;power)</code>&nbsp;</td>
</tr>
<tr id="i325" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetProtocolType-int-">UHFBTSetProtocolType</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i326" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSetR6Workmode-int-">UHFBTSetR6Workmode</a></span>(int&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i327" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTStartInventory--">UHFBTStartInventory</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i328" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTStartUpdate--">UHFBTStartUpdate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i329" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTSTM32Version-byte:A-">UHFBTSTM32Version</a></span>(byte[]&nbsp;outVersion)</code>&nbsp;</td>
</tr>
<tr id="i330" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTStopInventory--">UHFBTStopInventory</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i331" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTUHFGBTagLock-char:A-char-int-int-char:A-int-int-int-">UHFBTUHFGBTagLock</a></span>(char[]&nbsp;pszuAccessPwd,
                 char&nbsp;bank,
                 int&nbsp;ptr,
                 int&nbsp;cnt,
                 char[]&nbsp;pszuData,
                 int&nbsp;memory,
                 int&nbsp;config,
                 int&nbsp;action)</code>&nbsp;</td>
</tr>
<tr id="i332" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTUHFGetProtocolType--">UHFBTUHFGetProtocolType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i333" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTUpdateData-byte:A-">UHFBTUpdateData</a></span>(byte[]&nbsp;pszuDate)</code>&nbsp;</td>
</tr>
<tr id="i334" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFBTWriteData-char:A-char-int-int-char:A-char-int-int-char:A-">UHFBTWriteData</a></span>(char[]&nbsp;pszuAccessPwd,
              char&nbsp;ufBank,
              int&nbsp;ufPtr,
              int&nbsp;ufCnt,
              char[]&nbsp;pszuDat,
              char&nbsp;uBank,
              int&nbsp;uPtr,
              int&nbsp;uCnt,
              char[]&nbsp;pszuWriteData)</code>&nbsp;</td>
</tr>
<tr id="i335" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFCheckOpMode-int-int-int-byte:A-">UHFCheckOpMode</a></span>(int&nbsp;mask_bank,
              int&nbsp;mask_addr,
              int&nbsp;mask_len,
              byte[]&nbsp;mask_data)</code>&nbsp;</td>
</tr>
<tr id="i336" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFCloseAndDisconnect--">UHFCloseAndDisconnect</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i337" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFDeactivate-int-char:A-char-int-int-char:A-">UHFDeactivate</a></span>(int&nbsp;cmd,
             char[]&nbsp;pszuAccessPwd,
             char&nbsp;uBank,
             int&nbsp;uPtr,
             int&nbsp;cnt,
             char[]&nbsp;pszuUii)</code>&nbsp;</td>
</tr>
<tr id="i338" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFDecryptSM4-char-char:A-byte:A-">UHFDecryptSM4</a></span>(char&nbsp;datalen,
             char[]&nbsp;pszdata,
             byte[]&nbsp;outdata)</code>&nbsp;</td>
</tr>
<tr id="i339" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFDecSM4-byte:A-int-">UHFDecSM4</a></span>(byte[]&nbsp;jDatabuf,
         int&nbsp;jDatalen)</code>&nbsp;</td>
</tr>
<tr id="i340" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFDecUSER-int-int-">UHFDecUSER</a></span>(int&nbsp;jaddr,
          int&nbsp;jlen)</code>&nbsp;</td>
</tr>
<tr id="i341" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFDwell-int-int-">UHFDwell</a></span>(int&nbsp;dwell,
        int&nbsp;count)</code>&nbsp;</td>
</tr>
<tr id="i342" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFEncryptSM4-char-char:A-byte:A-">UHFEncryptSM4</a></span>(char&nbsp;datalen,
             char[]&nbsp;pszdata,
             byte[]&nbsp;outdata)</code>&nbsp;</td>
</tr>
<tr id="i343" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFEncSM4-byte:A-int-">UHFEncSM4</a></span>(byte[]&nbsp;jDatabuf,
         int&nbsp;jDatalen)</code>&nbsp;</td>
</tr>
<tr id="i344" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFEncUSER-int-int-byte:A-">UHFEncUSER</a></span>(int&nbsp;jaddr,
          int&nbsp;jlen,
          byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i345" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFEraseData-char:A-char-int-char-char:A-">UHFEraseData</a></span>(char[]&nbsp;uAccessPwd,
            char&nbsp;uBank,
            int&nbsp;uPtr,
            char&nbsp;uCnt,
            char[]&nbsp;uUii)</code>&nbsp;</td>
</tr>
<tr id="i346" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFEraseDataEx-char:A-char-int-char-char:A-char-int-int-">UHFEraseDataEx</a></span>(char[]&nbsp;pszuAccessPwd,
              char&nbsp;filterBank,
              int&nbsp;filterPtr,
              char&nbsp;filterCnt,
              char[]&nbsp;filterData,
              char&nbsp;uBank,
              int&nbsp;uPtr,
              int&nbsp;uCnt)</code>&nbsp;</td>
</tr>
<tr id="i347" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFEraseDataSingle-char:A-char-int-char-">UHFEraseDataSingle</a></span>(char[]&nbsp;uAccessPwd,
                  char&nbsp;uBank,
                  int&nbsp;uPtr,
                  char&nbsp;uCnt)</code>&nbsp;</td>
</tr>
<tr id="i348" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFFlafCrcOff--">UHFFlafCrcOff</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i349" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFFlagCrcOn--">UHFFlagCrcOn</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i350" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFFree-java.lang.String-">UHFFree</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i351" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGBTagLock-char:A-char-int-int-char:A-int-int-int-">UHFGBTagLock</a></span>(char[]&nbsp;pszuAccessPwd,
            char&nbsp;bank,
            int&nbsp;ptr,
            int&nbsp;cnt,
            char[]&nbsp;pszuData,
            int&nbsp;memory,
            int&nbsp;config,
            int&nbsp;action)</code>&nbsp;</td>
</tr>
<tr id="i352" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGBTagLockRecvData-byte:A-int-">UHFGBTagLockRecvData</a></span>(byte[]&nbsp;inData,
                    int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i353" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGBTagLockSendData-byte:A-char-int-int-byte:A-char-char-char-">UHFGBTagLockSendData</a></span>(byte[]&nbsp;pszuAccessPwd,
                    char&nbsp;ufBank,
                    int&nbsp;ufPtr,
                    int&nbsp;ufCnt,
                    byte[]&nbsp;ufData,
                    char&nbsp;jmemory,
                    char&nbsp;jconfig,
                    char&nbsp;jaction)</code>&nbsp;</td>
</tr>
<tr id="i354" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetANT--">UHFGetANT</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i355" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetANT-byte:A-">UHFGetANT</a></span>(byte[]&nbsp;buf)</code>
<div class="block">功能：获取天线设置
 输出：buf--2bytes, 共16bits,</div>
</td>
</tr>
<tr id="i356" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetANTRecvData-byte:A-int-">UHFGetANTRecvData</a></span>(byte[]&nbsp;inData,
                 int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i357" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetANTSendData--">UHFGetANTSendData</a></span>()</code>
<div class="block">功能：获取天线设置
 输出：buf--2bytes, 共16bits,</div>
</td>
</tr>
<tr id="i358" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetANTWorkTime-byte-int:A-">UHFGetANTWorkTime</a></span>(byte&nbsp;antnum,
                 int[]&nbsp;WorkTime)</code>
<div class="block">功能：获取天线工作时间
 输入：antnum -- 天线号
 输出：WorkTime -- 工作时间 ，单位ms, 范围 10-65535ms
 返回：0：获取成功     -1：获取失败</div>
</td>
</tr>
<tr id="i359" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetBeep--">UHFGetBeep</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i360" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetBID--">UHFGetBID</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i361" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetBTFrequency--">UHFGetBTFrequency</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i362" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetCalibrationData-char:A-char-char:A-">UHFGetCalibrationData</a></span>(char[]&nbsp;epc,
                     char&nbsp;antNum,
                     char[]&nbsp;powerValue)</code>&nbsp;</td>
</tr>
<tr id="i363" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetCW--">UHFGetCW</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i364" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetCWRecvData-byte:A-int-">UHFGetCWRecvData</a></span>(byte[]&nbsp;inData,
                int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i365" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetCWSendData--">UHFGetCWSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i366" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetDestIp-byte:A-int:A-">UHFGetDestIp</a></span>(byte[]&nbsp;ipbuf,
            int[]&nbsp;port)</code>&nbsp;</td>
</tr>
<tr id="i367" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetEPCTIDMode--">UHFGetEPCTIDMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i368" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetEPCTIDModeRecvData-byte:A-int-">UHFGetEPCTIDModeRecvData</a></span>(byte[]&nbsp;inData,
                        int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i369" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetEPCTIDModeSendData-char-char-">UHFGetEPCTIDModeSendData</a></span>(char&nbsp;rev1,
                        char&nbsp;rev2)</code>&nbsp;</td>
</tr>
<tr id="i370" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetEPCTIDUSERAddrLength--">UHFGetEPCTIDUSERAddrLength</a></span>()</code>
<div class="block">功能：设置EPC+TID+USER 区地址和长度

 输出：
     char[0] : 状态，0成功
     char[1] : 数据长度，5
     char[2]  盘点模式： 0x00为盘点EPC； 0x01为盘点EPC+TID； 0x02为EPC+TID+USER
     char[3]  TID地址：盘点TID的起始地址，单位word（2 Byte），模式为0x01时有效

     char[4]  TID长度：盘点TID的长度，单位word（2 Byte），模式为0x01时有效
     char[5]  USER地址：盘点USER区的起始地址，单位word（2 Byte），模式为0x02时有效
     char[6]  USER长度：盘点USER区的长度，单位word（2 Byte），模式为0x02时有效</div>
</td>
</tr>
<tr id="i371" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetEPCTIDUSERMode--">UHFGetEPCTIDUSERMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i372" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetFastInventoryMode-int-int-">UHFGetFastInventoryMode</a></span>(int&nbsp;rev1,
                       int&nbsp;rev2)</code>&nbsp;</td>
</tr>
<tr id="i373" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetFrequency_Ex--">UHFGetFrequency_Ex</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i374" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetFrequency--">UHFGetFrequency</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i375" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetGen2RecvData-byte:A-int-">UHFGetGen2RecvData</a></span>(byte[]&nbsp;inData,
                  int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i376" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetGen2SendData--">UHFGetGen2SendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i377" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetHardwareVersionType--">UHFGetHardwareVersionType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i378" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetHwType--">UHFGetHwType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i379" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetHwTypeM3--">UHFGetHwTypeM3</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i380" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetIoControl--">UHFGetIoControl</a></span>()</code>
<div class="block">功能：获取继电器和 IO 控制输出设置状态
     返回值：null -- 执行失败
     data：2字节，output1: 0:低电平   1：高电平 output2: 0:低电平   1：高电平</div>
</td>
</tr>
<tr id="i381" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetIp-byte:A-int:A-byte:A-byte:A-">UHFGetIp</a></span>(byte[]&nbsp;ipbuf,
        int[]&nbsp;port,
        byte[]&nbsp;mask,
        byte[]&nbsp;gate)</code>&nbsp;</td>
</tr>
<tr id="i382" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetIpRecvData-byte:A-int-">UHFGetIpRecvData</a></span>(byte[]&nbsp;inData,
                int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i383" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetIpSendData--">UHFGetIpSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i384" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetMode--">UHFGetMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i385" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetMultiDataReceived--">UHFGetMultiDataReceived</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i386" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetOnChipRSSI-char:A-char-char:A-">UHFGetOnChipRSSI</a></span>(char[]&nbsp;epc,
                char&nbsp;antNum,
                char[]&nbsp;powerValue)</code>&nbsp;</td>
</tr>
<tr id="i387" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetOnChipRSSIAndTempCode-char:A-char-char:A-">UHFGetOnChipRSSIAndTempCode</a></span>(char[]&nbsp;epc,
                           char&nbsp;antNum,
                           char[]&nbsp;powerValue)</code>&nbsp;</td>
</tr>
<tr id="i388" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetParam-char-char:A-">UHFGetParam</a></span>(char&nbsp;type,
           char[]&nbsp;id)</code>&nbsp;</td>
</tr>
<tr id="i389" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetPower--">UHFGetPower</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i390" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetPowerRecvData-byte:A-int-">UHFGetPowerRecvData</a></span>(byte[]&nbsp;inData,
                   int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i391" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetPowerSendData--">UHFGetPowerSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i392" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetProtocolType--">UHFGetProtocolType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i393" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetProtocolTypeRecvData-byte:A-int-">UHFGetProtocolTypeRecvData</a></span>(byte[]&nbsp;inData,
                          int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i394" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetProtocolTypeSendData--">UHFGetProtocolTypeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i395" class="rowColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetPwm--">UHFGetPwm</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i396" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetReaderBeepRecvData-byte:A-int-">UHFGetReaderBeepRecvData</a></span>(byte[]&nbsp;inData,
                        int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i397" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetReaderBeepSendData--">UHFGetReaderBeepSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i398" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetReceived_EX_R2000--">UHFGetReceived_EX_R2000</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i399" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetReceived_EX--">UHFGetReceived_EX</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i400" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetReceived_EX2-char:A-">UHFGetReceived_EX2</a></span>(char[]&nbsp;outData)</code>&nbsp;</td>
</tr>
<tr id="i401" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetReceived--">UHFGetReceived</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i402" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetRegionRecvData-byte:A-int-">UHFGetRegionRecvData</a></span>(byte[]&nbsp;inData,
                    int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i403" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetRegionSendData--">UHFGetRegionSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i404" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetRFLink--">UHFGetRFLink</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i405" class="rowColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetRSSI--">UHFGetRSSI</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i406" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSensorCode-char:A-char-char:A-">UHFGetSensorCode</a></span>(char[]&nbsp;epc,
                char&nbsp;antNum,
                char[]&nbsp;powerValue)</code>
<div class="block">zjx 20191127  温度标签增加通讯命令   -------- start --------</div>
</td>
</tr>
<tr id="i407" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSingelMode--">UHFGetSingelMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i408" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSleepTimeRecvData-byte:A-int-">UHFGetSleepTimeRecvData</a></span>(byte[]&nbsp;inData,
                       int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i409" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSleepTimeSendData--">UHFGetSleepTimeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i410" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSM4--">UHFGetSM4</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i411" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGETSM4-byte:A-byte:A-byte:A-">UHFGETSM4</a></span>(byte[]&nbsp;mode,
         byte[]&nbsp;keydata,
         byte[]&nbsp;lvdata)</code>&nbsp;</td>
</tr>
<tr id="i412" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSoftwareVersionRecvData-byte:A-int-">UHFGetSoftwareVersionRecvData</a></span>(byte[]&nbsp;inData,
                             int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i413" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSoftwareVersionSendData--">UHFGetSoftwareVersionSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i414" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSTM32VersionRecvData-byte:A-int-">UHFGetSTM32VersionRecvData</a></span>(byte[]&nbsp;inData,
                          int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i415" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetSTM32VersionSendData--">UHFGetSTM32VersionSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i416" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTagfocusRecvData-byte:A-int-">UHFGetTagfocusRecvData</a></span>(byte[]&nbsp;inData,
                      int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i417" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTagfocusSendData--">UHFGetTagfocusSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i418" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTagsDataRecvData-byte:A-int-">UHFGetTagsDataRecvData</a></span>(byte[]&nbsp;inData,
                      int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i419" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTagsDataSendData--">UHFGetTagsDataSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i420" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTemperatureRecvData-byte:A-int-">UHFGetTemperatureRecvData</a></span>(byte[]&nbsp;inData,
                         int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i421" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTemperatureSendData--">UHFGetTemperatureSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i422" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTempertureCode-char:A-char-char:A-">UHFGetTempertureCode</a></span>(char[]&nbsp;epc,
                    char&nbsp;antNum,
                    char[]&nbsp;powerValue)</code>&nbsp;</td>
</tr>
<tr id="i423" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTempProtectVal--">UHFGetTempProtectVal</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i424" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTempTagReceived-char:A-">UHFGetTempTagReceived</a></span>(char[]&nbsp;outData)</code>&nbsp;</td>
</tr>
<tr id="i425" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetTempTagReceived2-char:A-">UHFGetTempTagReceived2</a></span>(char[]&nbsp;outData)</code>&nbsp;</td>
</tr>
<tr id="i426" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetVersion--">UHFGetVersion</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i427" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFGetWorkMode--">UHFGetWorkMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i428" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInit-java.lang.String-">UHFInit</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i429" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInitRegFile-int-int-int-byte:A-">UHFInitRegFile</a></span>(int&nbsp;mask_bank,
              int&nbsp;mask_addr,
              int&nbsp;mask_len,
              byte[]&nbsp;mask_data)</code>&nbsp;</td>
</tr>
<tr id="i430" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventory_EX_BankPtrCnt-char-char-char-char-char-">UHFInventory_EX_BankPtrCnt</a></span>(char&nbsp;flagAnti,
                          char&nbsp;initQ,
                          char&nbsp;bank,
                          char&nbsp;ptr,
                          char&nbsp;cnt)</code>&nbsp;</td>
</tr>
<tr id="i431" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventory_EX_cnt-char-char-char-">UHFInventory_EX_cnt</a></span>(char&nbsp;flagAnti,
                   char&nbsp;initQ,
                   char&nbsp;cnt)</code>&nbsp;</td>
</tr>
<tr id="i432" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventory_EX-char-char-">UHFInventory_EX</a></span>(char&nbsp;flagAnti,
               char&nbsp;initQ)</code>&nbsp;</td>
</tr>
<tr id="i433" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventory-char-char-">UHFInventory</a></span>(char&nbsp;flagAnti,
            char&nbsp;initQ)</code>&nbsp;</td>
</tr>
<tr id="i434" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventoryBank-char:A-char-int-int-">UHFInventoryBank</a></span>(char[]&nbsp;pszData,
                char&nbsp;bank,
                int&nbsp;ptr,
                int&nbsp;len)</code>&nbsp;</td>
</tr>
<tr id="i435" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventoryBID-char-char-">UHFInventoryBID</a></span>(char&nbsp;flag0,
               char&nbsp;flag1)</code>&nbsp;</td>
</tr>
<tr id="i436" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventoryRecvData-byte:A-int-">UHFInventoryRecvData</a></span>(byte[]&nbsp;inData,
                    int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i437" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySendData--">UHFInventorySendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i438" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingle_EX--">UHFInventorySingle_EX</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i439" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingle_R2000--">UHFInventorySingle_R2000</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i440" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingle_sf--">UHFInventorySingle_sf</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i441" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingle_tc-char-">UHFInventorySingle_tc</a></span>(char&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i442" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingle--">UHFInventorySingle</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i443" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingleEPCTIDUSER-int-int-">UHFInventorySingleEPCTIDUSER</a></span>(int&nbsp;p1,
                            int&nbsp;p2)</code>&nbsp;</td>
</tr>
<tr id="i444" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingleRecvData-byte:A-int-">UHFInventorySingleRecvData</a></span>(byte[]&nbsp;inData,
                          int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i445" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventorySingleSendData--">UHFInventorySingleSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i446" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventoryTempTag-char-char:A-">UHFInventoryTempTag</a></span>(char&nbsp;antNum,
                   char[]&nbsp;powerValue)</code>&nbsp;</td>
</tr>
<tr id="i447" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventoryTempTag2-char-char:A-">UHFInventoryTempTag2</a></span>(char&nbsp;antNum,
                    char[]&nbsp;powerValue)</code>&nbsp;</td>
</tr>
<tr id="i448" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFInventoryTID-char-char-">UHFInventoryTID</a></span>(char&nbsp;flagAnti,
               char&nbsp;initQ)</code>&nbsp;</td>
</tr>
<tr id="i449" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFJump2Boot--">UHFJump2Boot</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i450" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFJump2BootRecvData-byte:A-int-">UHFJump2BootRecvData</a></span>(byte[]&nbsp;inData,
                    int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i451" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFJump2BootSendData-char-">UHFJump2BootSendData</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i452" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFJump2BootSTM32--">UHFJump2BootSTM32</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i453" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFKillTag-char:A-char:A-">UHFKillTag</a></span>(char[]&nbsp;uKillPwd,
          char[]&nbsp;uUii)</code>&nbsp;</td>
</tr>
<tr id="i454" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFKillTagEx-char:A-char-int-int-char:A-">UHFKillTagEx</a></span>(char[]&nbsp;pszuAccessPwd,
            char&nbsp;uBank,
            int&nbsp;uPtr,
            int&nbsp;uCnt,
            char[]&nbsp;pszuUii)</code>&nbsp;</td>
</tr>
<tr id="i455" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFKillTagRecvData-byte:A-int-">UHFKillTagRecvData</a></span>(byte[]&nbsp;inData,
                  int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i456" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFKillTagSendData-byte:A-char-int-int-byte:A-">UHFKillTagSendData</a></span>(byte[]&nbsp;pszuAccessPwd,
                  char&nbsp;ufBank,
                  int&nbsp;ufPtr,
                  int&nbsp;ufCnt,
                  byte[]&nbsp;ufData)</code>&nbsp;</td>
</tr>
<tr id="i457" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFKillTagSingle-char:A-">UHFKillTagSingle</a></span>(char[]&nbsp;uKillPwd)</code>&nbsp;</td>
</tr>
<tr id="i458" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFLedOnOff-java.lang.String-int-int-">UHFLedOnOff</a></span>(java.lang.String&nbsp;device,
           int&nbsp;Id,
           int&nbsp;on)</code>&nbsp;</td>
</tr>
<tr id="i459" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFLockMem-char:A-char:A-char:A-">UHFLockMem</a></span>(char[]&nbsp;uAccessPwd,
          char[]&nbsp;uLockData,
          char[]&nbsp;uUii)</code>&nbsp;</td>
</tr>
<tr id="i460" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFLockMemEx-char:A-char-int-int-char:A-char:A-">UHFLockMemEx</a></span>(char[]&nbsp;pszuAccessPwd,
            char&nbsp;bank,
            int&nbsp;ptr,
            int&nbsp;cnt,
            char[]&nbsp;pszuData,
            char[]&nbsp;pszuLockData)</code>&nbsp;</td>
</tr>
<tr id="i461" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFLockMemSingle-char:A-char:A-">UHFLockMemSingle</a></span>(char[]&nbsp;uAccessPwd,
                char[]&nbsp;uLockData)</code>&nbsp;</td>
</tr>
<tr id="i462" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFLockTagRecvData-byte:A-int-">UHFLockTagRecvData</a></span>(byte[]&nbsp;inData,
                  int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i463" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFLockTagSendData-byte:A-char-int-int-byte:A-byte:A-">UHFLockTagSendData</a></span>(byte[]&nbsp;pszuAccessPwd,
                  char&nbsp;ufBank,
                  int&nbsp;ufPtr,
                  int&nbsp;ufCnt,
                  byte[]&nbsp;ufData,
                  byte[]&nbsp;ulockbuf)</code>&nbsp;</td>
</tr>
<tr id="i464" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFOpenAndConnect_Ex-java.lang.String-">UHFOpenAndConnect_Ex</a></span>(java.lang.String&nbsp;uart)</code>&nbsp;</td>
</tr>
<tr id="i465" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFOpenAndConnect-java.lang.String-">UHFOpenAndConnect</a></span>(java.lang.String&nbsp;uart)</code>&nbsp;</td>
</tr>
<tr id="i466" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadData_EX-char:A-char-int-char-char:A-">UHFReadData_EX</a></span>(char[]&nbsp;uAccessPwd,
              char&nbsp;uBank,
              int&nbsp;uPtr,
              char&nbsp;uCnt,
              char[]&nbsp;uTID)</code>&nbsp;</td>
</tr>
<tr id="i467" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadData_Ex2-char:A-char-int-int-char:A-char-int-int-">UHFReadData_Ex2</a></span>(char[]&nbsp;pszuAccessPwd,
               char&nbsp;ufBank,
               int&nbsp;ufPtr,
               int&nbsp;ufCnt,
               char[]&nbsp;ufData,
               char&nbsp;uBank,
               int&nbsp;uPtr,
               int&nbsp;uCnt)</code>&nbsp;</td>
</tr>
<tr id="i468" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadData-char:A-char-int-int-char:A-">UHFReadData</a></span>(char[]&nbsp;uAccessPwd,
           char&nbsp;uBank,
           int&nbsp;uPtr,
           int&nbsp;uCnt,
           char[]&nbsp;uUii)</code>&nbsp;</td>
</tr>
<tr id="i469" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadDataRecvData-byte:A-int-">UHFReadDataRecvData</a></span>(byte[]&nbsp;inData,
                   int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i470" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadDataSendData-byte:A-char-int-int-byte:A-char-int-char-">UHFReadDataSendData</a></span>(byte[]&nbsp;pszuAccessPwd,
                   char&nbsp;ufBank,
                   int&nbsp;ufPtr,
                   int&nbsp;ufCnt,
                   byte[]&nbsp;ufData,
                   char&nbsp;uBank,
                   int&nbsp;uPtr,
                   char&nbsp;uCnt)</code>&nbsp;</td>
</tr>
<tr id="i471" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadDataSingle-char:A-char-int-char-">UHFReadDataSingle</a></span>(char[]&nbsp;uAccessPwd,
                 char&nbsp;uBank,
                 int&nbsp;uPtr,
                 char&nbsp;uCnt)</code>&nbsp;</td>
</tr>
<tr id="i472" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadMultiTemp-int-int-int-byte:A-int-int-int:A-int:A-float:A-">UHFReadMultiTemp</a></span>(int&nbsp;jmbank,
                int&nbsp;jmaddr,
                int&nbsp;jmlen,
                byte[]&nbsp;jmdata,
                int&nbsp;jstart,
                int&nbsp;jnum,
                int[]&nbsp;jtotalnum,
                int[]&nbsp;jreturned,
                float[]&nbsp;jtemp)</code>&nbsp;</td>
</tr>
<tr id="i473" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadQTData_Ex-char:A-char-int-int-char:A-char-int-char-">UHFReadQTData_Ex</a></span>(char[]&nbsp;pszuAccessPwd,
                char&nbsp;ufBank,
                int&nbsp;ufPtr,
                int&nbsp;ufCnt,
                char[]&nbsp;ufData,
                char&nbsp;uBank,
                int&nbsp;uPtr,
                char&nbsp;uCnt)</code>&nbsp;</td>
</tr>
<tr id="i474" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadQTData-char:A-char-int-char-char:A-">UHFReadQTData</a></span>(char[]&nbsp;uAccessPwd,
             char&nbsp;uBank,
             int&nbsp;uPtr,
             char&nbsp;uCnt,
             char[]&nbsp;uUii)</code>&nbsp;</td>
</tr>
<tr id="i475" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadQTDataSingle-char:A-char-int-char-">UHFReadQTDataSingle</a></span>(char[]&nbsp;uAccessPwd,
                   char&nbsp;uBank,
                   int&nbsp;uPtr,
                   char&nbsp;uCnt)</code>&nbsp;</td>
</tr>
<tr id="i476" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadTagTemp-int-int-int-byte:A-float:A-">UHFReadTagTemp</a></span>(int&nbsp;mask_bank,
              int&nbsp;mask_addr,
              int&nbsp;mask_len,
              byte[]&nbsp;mask_data,
              float[]&nbsp;readTemp)</code>&nbsp;</td>
</tr>
<tr id="i477" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFReadTagVoltage-int-int-int-byte:A-float:A-">UHFReadTagVoltage</a></span>(int&nbsp;jmbank,
                 int&nbsp;jmaddr,
                 int&nbsp;jmlen,
                 byte[]&nbsp;jmdata,
                 float[]&nbsp;jvoltage)</code>&nbsp;</td>
</tr>
<tr id="i478" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetANT-byte-byte:A-">UHFSetANT</a></span>(byte&nbsp;saveflag,
         byte[]&nbsp;buf)</code>
<div class="block">功能：天线设置
 输入：saveflag -- 1:掉电保存，  0：不保存
 输入：buf--2bytes, 共16bits, 每bit 置1选择对应天线</div>
</td>
</tr>
<tr id="i479" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetANT-int-char:A-">UHFSetANT</a></span>(int&nbsp;saveFlag,
         char[]&nbsp;temp)</code>&nbsp;</td>
</tr>
<tr id="i480" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetANTRecvData-byte:A-int-">UHFSetANTRecvData</a></span>(byte[]&nbsp;inData,
                 int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i481" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetANTSendData-char-byte:A-int-">UHFSetANTSendData</a></span>(char&nbsp;saveflag,
                 byte[]&nbsp;buf,
                 int&nbsp;bufLen)</code>
<div class="block">功能：天线设置
 输入：saveflag -- 1:掉电保存，  0：不保存
 输入：buf--2bytes, 共16bits, 每bit 置1选择对应天线</div>
</td>
</tr>
<tr id="i482" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetANTWorkTime-byte-byte-int-">UHFSetANTWorkTime</a></span>(byte&nbsp;antnum,
                 byte&nbsp;saveflag,
                 int&nbsp;WorkTime)</code>
<div class="block">功能：设置天线工作时间
 输入：antnum -- 天线号
 输入：saveflag -- 1:掉电保存， 0：不保存
 输入：WorkTime -- 工作时间 ，单位ms, 范围 10-65535ms
 返回：0：设置成功     -1：设置失败</div>
</td>
</tr>
<tr id="i483" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetBeep-int-">UHFSetBeep</a></span>(int&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i484" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetBTFrequency-byte-">UHFSetBTFrequency</a></span>(byte&nbsp;FreMode)</code>&nbsp;</td>
</tr>
<tr id="i485" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetCW-char-">UHFSetCW</a></span>(char&nbsp;pszData)</code>&nbsp;</td>
</tr>
<tr id="i486" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetCWRecvData-byte:A-int-">UHFSetCWRecvData</a></span>(byte[]&nbsp;inData,
                int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i487" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetCWSendData-char-">UHFSetCWSendData</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i488" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetDestIp-byte:A-int-">UHFSetDestIp</a></span>(byte[]&nbsp;ipbuf,
            int&nbsp;port)</code>&nbsp;</td>
</tr>
<tr id="i489" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetEPCTIDMode-char-">UHFSetEPCTIDMode</a></span>(char&nbsp;par)</code>&nbsp;</td>
</tr>
<tr id="i490" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetEPCTIDModeRecvData-byte:A-int-">UHFSetEPCTIDModeRecvData</a></span>(byte[]&nbsp;inData,
                        int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i491" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetEPCTIDModeSendData-char-char-char-char-">UHFSetEPCTIDModeSendData</a></span>(char&nbsp;saveFlag,
                        char&nbsp;memory,
                        char&nbsp;address,
                        char&nbsp;length)</code>&nbsp;</td>
</tr>
<tr id="i492" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetEPCTIDUSERAddrLength-char-char-char-char-char-">UHFSetEPCTIDUSERAddrLength</a></span>(char&nbsp;mode,
                          char&nbsp;TID_addr,
                          char&nbsp;TID_length,
                          char&nbsp;USER_addr,
                          char&nbsp;USER_length)</code>
<div class="block">功能：设置EPC+TID+USER 区地址和长度
 输入：mode       盘点模式： 0x00为盘点EPC； 0x01为盘点EPC+TID； 0x02为EPC+TID+USER
     TID_addr    TID地址：盘点TID的起始地址，单位word（2 Byte），模式为0x01时有效

     TID_length  TID长度：盘点TID的长度，单位word（2 Byte），模式为0x01时有效
     USER_addr   USER地址：盘点USER区的起始地址，单位word（2 Byte），模式为0x02时有效
     USER_length USER长度：盘点USER区的长度，单位word（2 Byte），模式为0x02时有效</div>
</td>
</tr>
<tr id="i493" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetEPCUserMode-char-int-int-int-">UHFSetEPCUserMode</a></span>(char&nbsp;par,
                 int&nbsp;ptr,
                 int&nbsp;len,
                 int&nbsp;save)</code>&nbsp;</td>
</tr>
<tr id="i494" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFastID-char-">UHFSetFastID</a></span>(char&nbsp;par)</code>&nbsp;</td>
</tr>
<tr id="i495" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFastInventoryMode-int-int-int-">UHFSetFastInventoryMode</a></span>(int&nbsp;on,
                       int&nbsp;save,
                       int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i496" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFilter_Ex-char-char-int-int-char:A-">UHFSetFilter_Ex</a></span>(char&nbsp;flagstore,
               char&nbsp;bank,
               int&nbsp;ptr,
               int&nbsp;len,
               char[]&nbsp;pszData)</code>&nbsp;</td>
</tr>
<tr id="i497" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFilter-char-char-int-int-char:A-">UHFSetFilter</a></span>(char&nbsp;flagstore,
            char&nbsp;uBank,
            int&nbsp;uPtr,
            int&nbsp;len,
            char[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i498" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFilterRecvData-byte:A-int-">UHFSetFilterRecvData</a></span>(byte[]&nbsp;inData,
                    int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i499" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFilterSendData-char-char-int-int-byte:A-">UHFSetFilterSendData</a></span>(char&nbsp;saveflag,
                    char&nbsp;ufBank,
                    int&nbsp;ufPtr,
                    int&nbsp;datalen,
                    byte[]&nbsp;databuf)</code>&nbsp;</td>
</tr>
<tr id="i500" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFrequency_EX-char-">UHFSetFrequency_EX</a></span>(char&nbsp;FreMode)</code>&nbsp;</td>
</tr>
<tr id="i501" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetFrequency-char-char-char:A-char-char-char-">UHFSetFrequency</a></span>(char&nbsp;uFreMode,
               char&nbsp;uFreBase,
               char[]&nbsp;uBaseFre,
               char&nbsp;uChannNum,
               char&nbsp;uChannSpc,
               char&nbsp;uFreHopUCHAR)</code>&nbsp;</td>
</tr>
<tr id="i502" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetGen2RecvData-byte:A-int-">UHFSetGen2RecvData</a></span>(byte[]&nbsp;inData,
                  int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i503" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetGen2SendData-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">UHFSetGen2SendData</a></span>(char&nbsp;Target,
                  char&nbsp;Action,
                  char&nbsp;T,
                  char&nbsp;Q_Q,
                  char&nbsp;StartQ,
                  char&nbsp;MinQ,
                  char&nbsp;MaxQ,
                  char&nbsp;D_D,
                  char&nbsp;C_C,
                  char&nbsp;P_P,
                  char&nbsp;Sel,
                  char&nbsp;Session,
                  char&nbsp;G_G,
                  char&nbsp;LF)</code>&nbsp;</td>
</tr>
<tr id="i504" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetInventoryMode-int-int-int-int-int-int-int-int-">UHFSetInventoryMode</a></span>(int&nbsp;save,
                   int&nbsp;jpw,
                   int&nbsp;sector,
                   int&nbsp;startAddr,
                   int&nbsp;length,
                   int&nbsp;sector2,
                   int&nbsp;startAddr2,
                   int&nbsp;length2)</code>&nbsp;</td>
</tr>
<tr id="i505" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetIOControl-byte-byte-byte-">UHFSetIOControl</a></span>(byte&nbsp;output1,
               byte&nbsp;output2,
               byte&nbsp;outStatus)</code>
<div class="block">功能：继电器和 IO 控制输出设置
 输入：output1:    0:低电平   1：高电平

     output2:    0:低电平   1：高电平

     outStatus： 0：断开    1：闭合

     返回值：0：设置成功     -1：设置失败</div>
</td>
</tr>
<tr id="i506" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetIp-byte:A-int-byte:A-byte:A-">UHFSetIp</a></span>(byte[]&nbsp;ipbuf,
        int&nbsp;port,
        byte[]&nbsp;mask,
        byte[]&nbsp;gate)</code>&nbsp;</td>
</tr>
<tr id="i507" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetIpRecvData-byte:A-int-">UHFSetIpRecvData</a></span>(byte[]&nbsp;inData,
                int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i508" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetIpSendData-byte:A-byte:A-">UHFSetIpSendData</a></span>(byte[]&nbsp;ipbuf,
                byte[]&nbsp;postbuf)</code>&nbsp;</td>
</tr>
<tr id="i509" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetJumpFrequencyRecvData-byte:A-int-">UHFSetJumpFrequencyRecvData</a></span>(byte[]&nbsp;inData,
                           int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i510" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetJumpFrequencySendData-char-int:A-">UHFSetJumpFrequencySendData</a></span>(char&nbsp;nums,
                           int[]&nbsp;Freqbuf)</code>&nbsp;</td>
</tr>
<tr id="i511" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetMode-char-">UHFSetMode</a></span>(char&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i512" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetParam-char-char:A-char:A-">UHFSetParam</a></span>(char&nbsp;type,
           char[]&nbsp;id,
           char[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i513" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetPower-char-">UHFSetPower</a></span>(char&nbsp;uPower)</code>&nbsp;</td>
</tr>
<tr id="i514" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetPowerOnDynamic-char-">UHFSetPowerOnDynamic</a></span>(char&nbsp;uPower)</code>&nbsp;</td>
</tr>
<tr id="i515" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetPowerRecvData-byte:A-int-">UHFSetPowerRecvData</a></span>(byte[]&nbsp;inData,
                   int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i516" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetPowerSendData-char-char-">UHFSetPowerSendData</a></span>(char&nbsp;saveflag,
                   char&nbsp;uPower)</code>&nbsp;</td>
</tr>
<tr id="i517" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetProtocolType-int-">UHFSetProtocolType</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i518" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetProtocolTypeRecvData-byte:A-int-">UHFSetProtocolTypeRecvData</a></span>(byte[]&nbsp;inData,
                          int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i519" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetProtocolTypeSendData-char-">UHFSetProtocolTypeSendData</a></span>(char&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i520" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetPwm-int-int-">UHFSetPwm</a></span>(int&nbsp;WorkTime,
         int&nbsp;WaitTime)</code>&nbsp;</td>
</tr>
<tr id="i521" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetReaderBeepRecvData-byte:A-int-">UHFSetReaderBeepRecvData</a></span>(byte[]&nbsp;inData,
                        int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i522" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetReaderBeepSendData-char-">UHFSetReaderBeepSendData</a></span>(char&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i523" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetRegionRecvData-byte:A-int-">UHFSetRegionRecvData</a></span>(byte[]&nbsp;inData,
                    int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i524" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetRegionSendData-char-char-">UHFSetRegionSendData</a></span>(char&nbsp;saveflag,
                    char&nbsp;region)</code>&nbsp;</td>
</tr>
<tr id="i525" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetRFLink-char-">UHFSetRFLink</a></span>(char&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i526" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSingelMode-char-">UHFSetSingelMode</a></span>(char&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i527" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSleepTimeRecvData-byte:A-int-">UHFSetSleepTimeRecvData</a></span>(byte[]&nbsp;inData,
                       int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i528" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSleepTimeSendData-char-">UHFSetSleepTimeSendData</a></span>(char&nbsp;uTime)</code>&nbsp;</td>
</tr>
<tr id="i529" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSM4-char-byte:A-byte:A-">UHFSetSM4</a></span>(char&nbsp;mode,
         byte[]&nbsp;jKeybuf,
         byte[]&nbsp;jIVbuf)</code>&nbsp;</td>
</tr>
<tr id="i530" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSETSM4-char-char:A-char:A-">UHFSETSM4</a></span>(char&nbsp;mode,
         char[]&nbsp;pszkeydata,
         char[]&nbsp;pszlvdata)</code>&nbsp;</td>
</tr>
<tr id="i531" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSoftReset--">UHFSetSoftReset</a></span>()</code>
<div class="block">功能：设置软件复位</div>
</td>
</tr>
<tr id="i532" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSoftResetRecvData-byte:A-int-">UHFSetSoftResetRecvData</a></span>(byte[]&nbsp;inData,
                       int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i533" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetSoftResetSendData--">UHFSetSoftResetSendData</a></span>()</code>
<div class="block">20200519 begin</div>
</td>
</tr>
<tr id="i534" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetTagFocus-char-">UHFSetTagFocus</a></span>(char&nbsp;par)</code>&nbsp;</td>
</tr>
<tr id="i535" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetTagfocusRecvData-byte:A-int-">UHFSetTagfocusRecvData</a></span>(byte[]&nbsp;inData,
                      int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i536" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetTagfocusSendData-char-">UHFSetTagfocusSendData</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i537" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetTempProtectVal-char-">UHFSetTempProtectVal</a></span>(char&nbsp;temp)</code>&nbsp;</td>
</tr>
<tr id="i538" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFSetWorkMode-byte-">UHFSetWorkMode</a></span>(byte&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i539" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStartLogging-int-int-int-byte:A-float-float-int-int-">UHFStartLogging</a></span>(int&nbsp;mask_bank,
               int&nbsp;mask_addr,
               int&nbsp;mask_len,
               byte[]&nbsp;mask_data,
               float&nbsp;min_temp,
               float&nbsp;max_temp,
               int&nbsp;work_delay,
               int&nbsp;work_interval)</code>&nbsp;</td>
</tr>
<tr id="i540" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStartReadDataFromMultiTag-char-char:A-char-char-char-">UHFStartReadDataFromMultiTag</a></span>(char&nbsp;flagAnti,
                            char[]&nbsp;apwd,
                            char&nbsp;bank,
                            char&nbsp;ptr,
                            char&nbsp;cnt)</code>&nbsp;</td>
</tr>
<tr id="i541" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStartUpdate--">UHFStartUpdate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i542" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStartUpdateRecvData-byte:A-int-">UHFStartUpdateRecvData</a></span>(byte[]&nbsp;inData,
                      int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i543" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStartUpdateSendData--">UHFStartUpdateSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i544" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopGet--">UHFStopGet</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i545" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopInventoryRecvData-byte:A-int-">UHFStopInventoryRecvData</a></span>(byte[]&nbsp;inData,
                        int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i546" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopInventorySendData--">UHFStopInventorySendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i547" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopLogging-int-int-int-byte:A-long-">UHFStopLogging</a></span>(int&nbsp;mask_bank,
              int&nbsp;mask_addr,
              int&nbsp;mask_len,
              byte[]&nbsp;mask_data,
              long&nbsp;password)</code>&nbsp;</td>
</tr>
<tr id="i548" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopUpdate--">UHFStopUpdate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i549" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopUpdateRecvData-byte:A-int-">UHFStopUpdateRecvData</a></span>(byte[]&nbsp;inData,
                     int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i550" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFStopUpdateSendData--">UHFStopUpdateSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i551" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFTCPTagsDataParseRecvData-byte:A-int-">UHFTCPTagsDataParseRecvData</a></span>(byte[]&nbsp;inData,
                           int&nbsp;len)</code>&nbsp;</td>
</tr>
<tr id="i552" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFToBoot-int-">UHFToBoot</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i553" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFUpdating-byte:A-int-">UHFUpdating</a></span>(byte[]&nbsp;buff,
           int&nbsp;len)</code>&nbsp;</td>
</tr>
<tr id="i554" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFUpdatingRecvData-byte:A-int-">UHFUpdatingRecvData</a></span>(byte[]&nbsp;inData,
                   int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i555" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFUpdatingSendData-byte:A-">UHFUpdatingSendData</a></span>(byte[]&nbsp;buf)</code>&nbsp;</td>
</tr>
<tr id="i556" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFUSBGetTagsDataRecvData-byte:A-int-">UHFUSBGetTagsDataRecvData</a></span>(byte[]&nbsp;inData,
                         int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i557" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteCalibrationData-char:A-char-char:A-char:A-">UHFWriteCalibrationData</a></span>(char[]&nbsp;epc,
                       char&nbsp;antNum,
                       char[]&nbsp;powerValue,
                       char[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i558" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteData_Ex2-char:A-char-int-int-char:A-char-int-int-char:A-">UHFWriteData_Ex2</a></span>(char[]&nbsp;pszuAccessPwd,
                char&nbsp;ufBank,
                int&nbsp;ufPtr,
                int&nbsp;ufCnt,
                char[]&nbsp;pszuDat,
                char&nbsp;uBank,
                int&nbsp;uPtr,
                int&nbsp;uCnt,
                char[]&nbsp;pszuWriteData)</code>&nbsp;</td>
</tr>
<tr id="i559" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteData-char:A-char-int-char-char:A-char:A-">UHFWriteData</a></span>(char[]&nbsp;uAccessPwd,
            char&nbsp;uBank,
            int&nbsp;uPtr,
            char&nbsp;uCnt,
            char[]&nbsp;uUii,
            char[]&nbsp;uWriteData)</code>&nbsp;</td>
</tr>
<tr id="i560" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteDataRecvData-byte:A-int-">UHFWriteDataRecvData</a></span>(byte[]&nbsp;inData,
                    int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i561" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteDataSendData-byte:A-char-int-int-byte:A-char-int-char-byte:A-">UHFWriteDataSendData</a></span>(byte[]&nbsp;pszuAccessPwd,
                    char&nbsp;ufBank,
                    int&nbsp;ufPtr,
                    int&nbsp;ufCnt,
                    byte[]&nbsp;ufData,
                    char&nbsp;uBank,
                    int&nbsp;uPtr,
                    char&nbsp;uCnt,
                    byte[]&nbsp;writeDatabuf)</code>&nbsp;</td>
</tr>
<tr id="i562" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteDataSingle-char:A-char-int-char-char:A-">UHFWriteDataSingle</a></span>(char[]&nbsp;uAccessPwd,
                  char&nbsp;uBank,
                  int&nbsp;uPtr,
                  char&nbsp;uCnt,
                  char[]&nbsp;uWriteData)</code>&nbsp;</td>
</tr>
<tr id="i563" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteQTData_Ex-char:A-char-int-int-char:A-char-int-int-char:A-">UHFWriteQTData_Ex</a></span>(char[]&nbsp;pszuAccessPwd,
                 char&nbsp;ufBank,
                 int&nbsp;ufPtr,
                 int&nbsp;ufCnt,
                 char[]&nbsp;pszuDat,
                 char&nbsp;uBank,
                 int&nbsp;uPtr,
                 int&nbsp;uCnt,
                 char[]&nbsp;pszuWriteData)</code>&nbsp;</td>
</tr>
<tr id="i564" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteQTData-char:A-char-int-char-char:A-char:A-">UHFWriteQTData</a></span>(char[]&nbsp;uAccessPwd,
              char&nbsp;uBank,
              int&nbsp;uPtr,
              char&nbsp;uCnt,
              char[]&nbsp;uUii,
              char[]&nbsp;uWriteData)</code>&nbsp;</td>
</tr>
<tr id="i565" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteQTDataSingle-char:A-char-int-char-char:A-">UHFWriteQTDataSingle</a></span>(char[]&nbsp;uAccessPwd,
                    char&nbsp;uBank,
                    int&nbsp;uPtr,
                    char&nbsp;uCnt,
                    char[]&nbsp;uWriteData)</code>&nbsp;</td>
</tr>
<tr id="i566" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHFWriteScreenBlock-byte:A-byte-int-int-byte:A-byte-int-int-byte:A-">UHFWriteScreenBlock</a></span>(byte[]&nbsp;passwd,
                   byte&nbsp;filterBank,
                   int&nbsp;filterPtr,
                   int&nbsp;filterCnt,
                   byte[]&nbsp;filterData,
                   byte&nbsp;type,
                   int&nbsp;ptr,
                   int&nbsp;cnt,
                   byte[]&nbsp;writeData)</code>&nbsp;</td>
</tr>
<tr id="i567" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHTSetR6WorkmodeRecvData-byte:A-int-">UHTSetR6WorkmodeRecvData</a></span>(byte[]&nbsp;inData,
                        int&nbsp;inLen)</code>&nbsp;</td>
</tr>
<tr id="i568" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UHTSetR6WorkmodeSendData-char-">UHTSetR6WorkmodeSendData</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i569" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Um7ProtocolConfig-char-">Um7ProtocolConfig</a></span>(char&nbsp;conf)</code>&nbsp;</td>
</tr>
<tr id="i570" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#Update_ReadInfo--">Update_ReadInfo</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i571" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UsbToFingerprint-java.lang.String-">UsbToFingerprint</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i572" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#UsbToHost-java.lang.String-">UsbToHost</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i573" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerDelChar-char:A-char:A-">ZAZfingerDelChar</a></span>(char[]&nbsp;templateIdStart,
                char[]&nbsp;templateIdEnd)</code>&nbsp;</td>
</tr>
<tr id="i574" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerDeviceInfo--">ZAZfingerDeviceInfo</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i575" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerDownChar-char:A-char:A-char:A-">ZAZfingerDownChar</a></span>(char[]&nbsp;templateSize,
                 char[]&nbsp;ramBufferId,
                 char[]&nbsp;templateData)</code>&nbsp;</td>
</tr>
<tr id="i576" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerDownImageData-char:A-char:A-">ZAZfingerDownImageData</a></span>(char[]&nbsp;imageBlockNumber,
                      char[]&nbsp;imageData)</code>&nbsp;</td>
</tr>
<tr id="i577" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerDownImageStart-char:A-char:A-">ZAZfingerDownImageStart</a></span>(char[]&nbsp;imageWidth,
                       char[]&nbsp;imageHeight)</code>&nbsp;</td>
</tr>
<tr id="i578" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZFingerFree-java.lang.String-">ZAZFingerFree</a></span>(java.lang.String&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i579" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerGenerate-char:A-">ZAZfingerGenerate</a></span>(char[]&nbsp;ramBufferId)</code>&nbsp;</td>
</tr>
<tr id="i580" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerGetBrokenId-char:A-char:A-">ZAZfingerGetBrokenId</a></span>(char[]&nbsp;templateIdStart,
                    char[]&nbsp;templateIdEnd)</code>&nbsp;</td>
</tr>
<tr id="i581" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerGetEmptyId-char:A-char:A-">ZAZfingerGetEmptyId</a></span>(char[]&nbsp;templateIdStart,
                   char[]&nbsp;templateIdEnd)</code>&nbsp;</td>
</tr>
<tr id="i582" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerGetEnrollCount-char:A-char:A-">ZAZfingerGetEnrollCount</a></span>(char[]&nbsp;templateIdStart,
                       char[]&nbsp;templateIdEnd)</code>&nbsp;</td>
</tr>
<tr id="i583" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerGetImage--">ZAZfingerGetImage</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i584" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerGetStatus-char:A-">ZAZfingerGetStatus</a></span>(char[]&nbsp;templateId)</code>&nbsp;</td>
</tr>
<tr id="i585" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZFingerInit-java.lang.String-java.lang.String-int-">ZAZFingerInit</a></span>(java.lang.String&nbsp;device,
             java.lang.String&nbsp;uart,
             int&nbsp;baudrate)</code>&nbsp;</td>
</tr>
<tr id="i586" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerLoadChar-char:A-char:A-">ZAZfingerLoadChar</a></span>(char[]&nbsp;templateId,
                 char[]&nbsp;ramBufferId)</code>&nbsp;</td>
</tr>
<tr id="i587" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerMatch-char:A-char:A-">ZAZfingerMatch</a></span>(char[]&nbsp;ramBufferId1,
              char[]&nbsp;ramBufferId2)</code>&nbsp;</td>
</tr>
<tr id="i588" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerMerge-char:A-int-">ZAZfingerMerge</a></span>(char[]&nbsp;ramBufferId,
              int&nbsp;number)</code>&nbsp;</td>
</tr>
<tr id="i589" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerReadParam-int-">ZAZfingerReadParam</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i590" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerSearch-char:A-char:A-char:A-">ZAZfingerSearch</a></span>(char[]&nbsp;ramBufferId,
               char[]&nbsp;templateIdStart,
               char[]&nbsp;templateIdEnd)</code>&nbsp;</td>
</tr>
<tr id="i591" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerSetParam-int-char:A-">ZAZfingerSetParam</a></span>(int&nbsp;type,
                 char[]&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i592" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerSledCtrl-int-">ZAZfingerSledCtrl</a></span>(int&nbsp;type)</code>&nbsp;</td>
</tr>
<tr id="i593" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerStoreChar-char:A-char:A-">ZAZfingerStoreChar</a></span>(char[]&nbsp;templateId,
                  char[]&nbsp;ramBufferId)</code>&nbsp;</td>
</tr>
<tr id="i594" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerTestConnection--">ZAZfingerTestConnection</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i595" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerUpChar-char:A-">ZAZfingerUpChar</a></span>(char[]&nbsp;ramBufferId)</code>&nbsp;</td>
</tr>
<tr id="i596" class="altColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerUpImage-int-java.lang.String-">ZAZfingerUpImage</a></span>(int&nbsp;imageMode,
                java.lang.String&nbsp;lpFileName)</code>&nbsp;</td>
</tr>
<tr id="i597" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html#ZAZfingerVerify-char:A-char:A-">ZAZfingerVerify</a></span>(char[]&nbsp;templateId,
               char[]&nbsp;ramBufferId)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="mFd">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>mFd</h4>
<pre>public&nbsp;java.io.FileDescriptor mFd</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi">DeviceAPI</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="UHFSetProtocolType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetProtocolType</h4>
<pre>public&nbsp;int&nbsp;UHFSetProtocolType(int&nbsp;type)</pre>
</li>
</ul>
<a name="UHFGetProtocolType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetProtocolType</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetProtocolType()</pre>
</li>
</ul>
<a name="UHFGBTagLock-char:A-char-int-int-char:A-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGBTagLock</h4>
<pre>public&nbsp;int&nbsp;UHFGBTagLock(char[]&nbsp;pszuAccessPwd,
                        char&nbsp;bank,
                        int&nbsp;ptr,
                        int&nbsp;cnt,
                        char[]&nbsp;pszuData,
                        int&nbsp;memory,
                        int&nbsp;config,
                        int&nbsp;action)</pre>
</li>
</ul>
<a name="ZAZFingerInit-java.lang.String-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZFingerInit</h4>
<pre>public&nbsp;int&nbsp;ZAZFingerInit(java.lang.String&nbsp;device,
                         java.lang.String&nbsp;uart,
                         int&nbsp;baudrate)</pre>
</li>
</ul>
<a name="ZAZFingerFree-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZFingerFree</h4>
<pre>public&nbsp;int&nbsp;ZAZFingerFree(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="ZAZfingerTestConnection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerTestConnection</h4>
<pre>public&nbsp;int&nbsp;ZAZfingerTestConnection()</pre>
</li>
</ul>
<a name="ZAZfingerSetParam-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerSetParam</h4>
<pre>public&nbsp;int&nbsp;ZAZfingerSetParam(int&nbsp;type,
                             char[]&nbsp;value)</pre>
</li>
</ul>
<a name="ZAZfingerReadParam-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerReadParam</h4>
<pre>public&nbsp;char[]&nbsp;ZAZfingerReadParam(int&nbsp;type)</pre>
</li>
</ul>
<a name="ZAZfingerDeviceInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerDeviceInfo</h4>
<pre>public&nbsp;char[]&nbsp;ZAZfingerDeviceInfo()</pre>
</li>
</ul>
<a name="ZAZfingerGetImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerGetImage</h4>
<pre>public&nbsp;char[]&nbsp;ZAZfingerGetImage()</pre>
</li>
</ul>
<a name="ZAZfingerUpImage-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerUpImage</h4>
<pre>public&nbsp;int[]&nbsp;ZAZfingerUpImage(int&nbsp;imageMode,
                              java.lang.String&nbsp;lpFileName)</pre>
</li>
</ul>
<a name="ZAZfingerDownImageStart-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerDownImageStart</h4>
<pre>public&nbsp;int&nbsp;ZAZfingerDownImageStart(char[]&nbsp;imageWidth,
                                   char[]&nbsp;imageHeight)</pre>
</li>
</ul>
<a name="ZAZfingerDownImageData-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerDownImageData</h4>
<pre>public&nbsp;int&nbsp;ZAZfingerDownImageData(char[]&nbsp;imageBlockNumber,
                                  char[]&nbsp;imageData)</pre>
</li>
</ul>
<a name="ZAZfingerSledCtrl-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerSledCtrl</h4>
<pre>public&nbsp;int&nbsp;ZAZfingerSledCtrl(int&nbsp;type)</pre>
</li>
</ul>
<a name="ZAZfingerStoreChar-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerStoreChar</h4>
<pre>public&nbsp;int&nbsp;ZAZfingerStoreChar(char[]&nbsp;templateId,
                              char[]&nbsp;ramBufferId)</pre>
</li>
</ul>
<a name="ZAZfingerLoadChar-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerLoadChar</h4>
<pre>public&nbsp;int&nbsp;ZAZfingerLoadChar(char[]&nbsp;templateId,
                             char[]&nbsp;ramBufferId)</pre>
</li>
</ul>
<a name="ZAZfingerUpChar-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerUpChar</h4>
<pre>public&nbsp;char[]&nbsp;ZAZfingerUpChar(char[]&nbsp;ramBufferId)</pre>
</li>
</ul>
<a name="ZAZfingerDownChar-char:A-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerDownChar</h4>
<pre>public&nbsp;int&nbsp;ZAZfingerDownChar(char[]&nbsp;templateSize,
                             char[]&nbsp;ramBufferId,
                             char[]&nbsp;templateData)</pre>
</li>
</ul>
<a name="ZAZfingerDelChar-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerDelChar</h4>
<pre>public&nbsp;int&nbsp;ZAZfingerDelChar(char[]&nbsp;templateIdStart,
                            char[]&nbsp;templateIdEnd)</pre>
</li>
</ul>
<a name="ZAZfingerGetEmptyId-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerGetEmptyId</h4>
<pre>public&nbsp;char[]&nbsp;ZAZfingerGetEmptyId(char[]&nbsp;templateIdStart,
                                  char[]&nbsp;templateIdEnd)</pre>
</li>
</ul>
<a name="ZAZfingerGetStatus-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerGetStatus</h4>
<pre>public&nbsp;char[]&nbsp;ZAZfingerGetStatus(char[]&nbsp;templateId)</pre>
</li>
</ul>
<a name="ZAZfingerGetBrokenId-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerGetBrokenId</h4>
<pre>public&nbsp;char[]&nbsp;ZAZfingerGetBrokenId(char[]&nbsp;templateIdStart,
                                   char[]&nbsp;templateIdEnd)</pre>
</li>
</ul>
<a name="ZAZfingerGetEnrollCount-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerGetEnrollCount</h4>
<pre>public&nbsp;char[]&nbsp;ZAZfingerGetEnrollCount(char[]&nbsp;templateIdStart,
                                      char[]&nbsp;templateIdEnd)</pre>
</li>
</ul>
<a name="ZAZfingerGenerate-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerGenerate</h4>
<pre>public&nbsp;int&nbsp;ZAZfingerGenerate(char[]&nbsp;ramBufferId)</pre>
</li>
</ul>
<a name="ZAZfingerMerge-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerMerge</h4>
<pre>public&nbsp;int&nbsp;ZAZfingerMerge(char[]&nbsp;ramBufferId,
                          int&nbsp;number)</pre>
</li>
</ul>
<a name="ZAZfingerMatch-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerMatch</h4>
<pre>public&nbsp;int&nbsp;ZAZfingerMatch(char[]&nbsp;ramBufferId1,
                          char[]&nbsp;ramBufferId2)</pre>
</li>
</ul>
<a name="ZAZfingerSearch-char:A-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerSearch</h4>
<pre>public&nbsp;char[]&nbsp;ZAZfingerSearch(char[]&nbsp;ramBufferId,
                              char[]&nbsp;templateIdStart,
                              char[]&nbsp;templateIdEnd)</pre>
</li>
</ul>
<a name="ZAZfingerVerify-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZAZfingerVerify</h4>
<pre>public&nbsp;char[]&nbsp;ZAZfingerVerify(char[]&nbsp;templateId,
                              char[]&nbsp;ramBufferId)</pre>
</li>
</ul>
<a name="ModulePowerOn-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ModulePowerOn</h4>
<pre>public&nbsp;int&nbsp;ModulePowerOn(java.lang.String&nbsp;device,
                         int&nbsp;module)</pre>
</li>
</ul>
<a name="ModulePowerOff-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ModulePowerOff</h4>
<pre>public&nbsp;int&nbsp;ModulePowerOff(java.lang.String&nbsp;device,
                          int&nbsp;module)</pre>
</li>
</ul>
<a name="ioctl_gpio-java.lang.String-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ioctl_gpio</h4>
<pre>public&nbsp;void&nbsp;ioctl_gpio(java.lang.String&nbsp;device,
                       int&nbsp;gpio,
                       int&nbsp;on)</pre>
</li>
</ul>
<a name="UartSwitch-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UartSwitch</h4>
<pre>public&nbsp;int&nbsp;UartSwitch(java.lang.String&nbsp;device,
                      int&nbsp;module)</pre>
</li>
</ul>
<a name="ModuleInit-java.lang.String-java.lang.String-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ModuleInit</h4>
<pre>public&nbsp;int&nbsp;ModuleInit(java.lang.String&nbsp;device,
                      java.lang.String&nbsp;path,
                      int&nbsp;baudrate,
                      int&nbsp;module)</pre>
</li>
</ul>
<a name="ModuleInitEX-java.lang.String-java.lang.String-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ModuleInitEX</h4>
<pre>public&nbsp;int&nbsp;ModuleInitEX(java.lang.String&nbsp;device,
                        java.lang.String&nbsp;path,
                        int&nbsp;baudrate,
                        int&nbsp;module,
                        int&nbsp;databits,
                        int&nbsp;stopbits,
                        int&nbsp;check)</pre>
</li>
</ul>
<a name="ModuleFree-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ModuleFree</h4>
<pre>public&nbsp;int&nbsp;ModuleFree(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="SerailOpen-java.lang.String-java.lang.String-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SerailOpen</h4>
<pre>public&nbsp;int&nbsp;SerailOpen(java.lang.String&nbsp;device,
                      java.lang.String&nbsp;path,
                      int&nbsp;baudrate,
                      int&nbsp;module,
                      int&nbsp;check)</pre>
</li>
</ul>
<a name="OpenSerail-java.lang.String-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OpenSerail</h4>
<pre>public&nbsp;int&nbsp;OpenSerail(java.lang.String&nbsp;path,
                      int&nbsp;baudrate,
                      int&nbsp;databits,
                      int&nbsp;stopbits,
                      int&nbsp;check)</pre>
</li>
</ul>
<a name="SerailClose-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SerailClose</h4>
<pre>public&nbsp;int&nbsp;SerailClose(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="ModuleSend-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ModuleSend</h4>
<pre>public&nbsp;int&nbsp;ModuleSend(byte[]&nbsp;data,
                      int&nbsp;len)</pre>
</li>
</ul>
<a name="ModuleReceive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ModuleReceive</h4>
<pre>public&nbsp;byte[]&nbsp;ModuleReceive()</pre>
</li>
</ul>
<a name="ModuleReceiveEx-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ModuleReceiveEx</h4>
<pre>public&nbsp;int&nbsp;ModuleReceiveEx(byte[]&nbsp;receive)</pre>
</li>
</ul>
<a name="bdOn-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bdOn</h4>
<pre>public&nbsp;int&nbsp;bdOn(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="bdOff-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bdOff</h4>
<pre>public&nbsp;int&nbsp;bdOff(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="spOpen-java.lang.String-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>spOpen</h4>
<pre>public&nbsp;java.io.FileDescriptor&nbsp;spOpen(java.lang.String&nbsp;path,
                                     int&nbsp;baudrate,
                                     int&nbsp;flags)</pre>
<div class="block">打开串口</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>path</code> - </dd>
<dd><code>baudrate</code> - </dd>
<dd><code>flags</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="spClose--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>spClose</h4>
<pre>public&nbsp;void&nbsp;spClose()</pre>
<div class="block">关闭串口</div>
</li>
</ul>
<a name="LedOn-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LedOn</h4>
<pre>public&nbsp;int&nbsp;LedOn(java.lang.String&nbsp;device,
                 int&nbsp;led)</pre>
</li>
</ul>
<a name="LedOff-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LedOff</h4>
<pre>public&nbsp;int&nbsp;LedOff(java.lang.String&nbsp;device,
                  int&nbsp;led)</pre>
</li>
</ul>
<a name="Barcode_1D_Open-java.lang.String-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Barcode_1D_Open</h4>
<pre>public&nbsp;int&nbsp;Barcode_1D_Open(java.lang.String&nbsp;device,
                           java.lang.String&nbsp;uart,
                           int&nbsp;baudrate)</pre>
</li>
</ul>
<a name="Barcode_1D_Close-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Barcode_1D_Close</h4>
<pre>public&nbsp;int&nbsp;Barcode_1D_Close(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="Barcode_1D_SetTimeOut-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Barcode_1D_SetTimeOut</h4>
<pre>public&nbsp;void&nbsp;Barcode_1D_SetTimeOut(int&nbsp;device)</pre>
</li>
</ul>
<a name="ScanerLed_Init-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ScanerLed_Init</h4>
<pre>public&nbsp;void&nbsp;ScanerLed_Init(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="ScanerLed_Free-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ScanerLed_Free</h4>
<pre>public&nbsp;void&nbsp;ScanerLed_Free(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="ScanerLed_On-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ScanerLed_On</h4>
<pre>public&nbsp;void&nbsp;ScanerLed_On(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="ScanerLed_Off-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ScanerLed_Off</h4>
<pre>public&nbsp;void&nbsp;ScanerLed_Off(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="Barcode_1D_Scan-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Barcode_1D_Scan</h4>
<pre>public&nbsp;byte[]&nbsp;Barcode_1D_Scan(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="Barcode_1D_StopScan-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Barcode_1D_StopScan</h4>
<pre>public&nbsp;int&nbsp;Barcode_1D_StopScan(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="Barcode_2D_Open-java.lang.String-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Barcode_2D_Open</h4>
<pre>public&nbsp;int&nbsp;Barcode_2D_Open(java.lang.String&nbsp;device,
                           java.lang.String&nbsp;uart,
                           int&nbsp;baudrate)</pre>
</li>
</ul>
<a name="Barcode_2D_Close-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Barcode_2D_Close</h4>
<pre>public&nbsp;int&nbsp;Barcode_2D_Close(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="Barcode_2D_Scan-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Barcode_2D_Scan</h4>
<pre>public&nbsp;byte[]&nbsp;Barcode_2D_Scan(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="Barcode_2D_StopScan-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Barcode_2D_StopScan</h4>
<pre>public&nbsp;int&nbsp;Barcode_2D_StopScan(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="Barcode_2D_SetTimeOut-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Barcode_2D_SetTimeOut</h4>
<pre>public&nbsp;void&nbsp;Barcode_2D_SetTimeOut(int&nbsp;time)</pre>
</li>
</ul>
<a name="RFID_init-java.lang.String-java.lang.String-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RFID_init</h4>
<pre>public&nbsp;int&nbsp;RFID_init(java.lang.String&nbsp;device,
                     java.lang.String&nbsp;uart,
                     int&nbsp;baudrate,
                     int&nbsp;isRfOff)</pre>
<div class="block">RFID_init 模块初始化</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>device</code> - device 设备类型 "C3000"或者"C4000"</dd>
<dd><code>uart</code> - 串口号,默认"/dev/ttysVK0"</dd>
<dd><code>baudrate</code> - 波特率,单协议设备是9600，双协议传入115200</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功返回0, 失败返回非0状态码</dd>
</dl>
</li>
</ul>
<a name="RFID_free-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RFID_free</h4>
<pre>public&nbsp;int&nbsp;RFID_free(java.lang.String&nbsp;device)</pre>
<div class="block">RFID_free 模块释放</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>device</code> - device 设备类型 "C3000"或者"C4000"</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功返回0, 失败返回非0状态码</dd>
</dl>
</li>
</ul>
<a name="RFID_GetVer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RFID_GetVer</h4>
<pre>public&nbsp;byte[]&nbsp;RFID_GetVer()</pre>
<div class="block">RFID_GetVer 获取RFID模块硬件版本</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>数组形式返回，1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据
 2）如果状态位表示成功，第1个元素表明后续还有的数据长度，这里表明版本号字符串的长度3）从第2个元素开始是版本号的内容</dd>
</dl>
</li>
</ul>
<a name="ISO14443A_request-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_request</h4>
<pre>public&nbsp;byte[]&nbsp;ISO14443A_request(java.lang.String&nbsp;device,
                                int&nbsp;iMode)</pre>
<div class="block">ISO14443A_request 获取A卡ID号 传入参数：1）device 设备类型 "C3000"或者"C4000"2）iMode
 0呼叫未进入休眠状态的标签，1呼叫所有的标签返回值：数组形式返回，1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据
 2）如果状态位表示成功，第1个元素表明后续还有的数据长度3）从第二个元素开始的数据格式为：
 ATQA(2Byte)+UID长度(1Byte)+UID[]+SAK 判断卡片类型通过</div>
</li>
</ul>
<a name="ISO14443A_authentication-int-int-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_authentication</h4>
<pre>public&nbsp;int&nbsp;ISO14443A_authentication(int&nbsp;iMode,
                                    int&nbsp;iBlock,
                                    char[]&nbsp;pszKey,
                                    int&nbsp;iLenKey)</pre>
<div class="block">ISO14443A_authentication 验证A卡秘钥 传入参数：1）iMode密钥验证模式 0 为验证A密钥；1为验证B密钥
 2）iBlock要验证密钥的绝对块号 块号范围 0~633）pszKey密钥内容4）iLenKey密钥长度 最大长度为6字节
 返回值：0为成功，非0为失败状态码</div>
</li>
</ul>
<a name="ISO14443A_read-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_read</h4>
<pre>public&nbsp;char[]&nbsp;ISO14443A_read(int&nbsp;iBlock)</pre>
<div class="block">ISO14443A_read 读取电子标签内容 传入参数：1）iBlock要读取数据的绝对块号 块号范围 0~63
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2）如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始是读到的的内容</div>
</li>
</ul>
<a name="ISO14443A_mifareone_alldata_read-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_mifareone_alldata_read</h4>
<pre>public&nbsp;char[]&nbsp;ISO14443A_mifareone_alldata_read(int&nbsp;iBlock,
                                               char[]&nbsp;pszKey)</pre>
</li>
</ul>
<a name="ISO14443A_write-int-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_write</h4>
<pre>public&nbsp;int&nbsp;ISO14443A_write(int&nbsp;iBlock,
                           char[]&nbsp;pszData,
                           int&nbsp;iLenData)</pre>
<div class="block">ISO14443A_write 将指定内容写入电子标签中 传入参数：1）iBlock要写入数据的绝对块号 块号范围 0~63
 2）pszData要写入的数据内容3）要写入的数据内容的长度返回值：0为成功，非0为失败状态码</div>
</li>
</ul>
<a name="ISO14443A_initval-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_initval</h4>
<pre>public&nbsp;int&nbsp;ISO14443A_initval(int&nbsp;iBlock,
                             int&nbsp;iValue)</pre>
<div class="block">ISO14443A_initval 电子钱包初始化 传入参数：1）iBlock要写入数据的绝对块号 块号范围 0~632）iValue 初始金额
 返回值：0为成功，非0为失败状态码</div>
</li>
</ul>
<a name="ISO14443A_readval-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_readval</h4>
<pre>public&nbsp;int[]&nbsp;ISO14443A_readval(int&nbsp;iBlock)</pre>
<div class="block">ISO14443A_readval 读取电子钱包余额 传入参数：1）iBlock要读取数据的绝对块号 块号范围 0~63
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2)第1个元素是读取到的余额</div>
</li>
</ul>
<a name="ISO14443A_decrement-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_decrement</h4>
<pre>public&nbsp;int&nbsp;ISO14443A_decrement(int&nbsp;iBlockValue,
                               int&nbsp;iBlockResult,
                               int&nbsp;iValue)</pre>
<div class="block">ISO14443A_decrement 电子钱包扣费 传入参数：1）iBlockValue当前金额所在块
 2）iBlockResult扣值后剩余金额保存的块3）iValue金额返回值：0为成功，非0为失败状态码</div>
</li>
</ul>
<a name="ISO14443A_increment-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_increment</h4>
<pre>public&nbsp;int&nbsp;ISO14443A_increment(int&nbsp;iBlockValue,
                               int&nbsp;iBlockResult,
                               int&nbsp;iValue)</pre>
<div class="block">ISO14443A_increment 电子钱包充值 传入参数：1）iBlockValue当前金额所在块2）充值后剩余金额保存的块
 3）iValue金额返回值：0为成功，非0为失败状态码</div>
</li>
</ul>
<a name="ISO14443A_restore-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_restore</h4>
<pre>public&nbsp;int&nbsp;ISO14443A_restore(int&nbsp;iBlock)</pre>
<div class="block">ISO14443A_restore 将EEPROM中的内容 传入卡的内部寄存器传入参数：1）iBlockValue当前金额所在块
 返回值：0为成功，非0为失败状态码</div>
</li>
</ul>
<a name="ISO14443A_transfer-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_transfer</h4>
<pre>public&nbsp;int&nbsp;ISO14443A_transfer(int&nbsp;iBlock)</pre>
<div class="block">ISO14443A_transfer 将寄存器的内容传送到EEPROM中； 传入参数：1）iBlockValue当前金额所在块
 返回值：0为成功，非0为失败状态码</div>
</li>
</ul>
<a name="ISO14443A_ul_read-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_ul_read</h4>
<pre>public&nbsp;char[]&nbsp;ISO14443A_ul_read(int&nbsp;iBlock)</pre>
<div class="block">ISO14443A_ul_read 读取电子标签内容 传入参数：1）iBlock要读取数据的绝对块号 块号范围 0~63
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据 2）如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始是读到的的内容</div>
</li>
</ul>
<a name="ISO14443A_ul_write-int-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_ul_write</h4>
<pre>public&nbsp;int&nbsp;ISO14443A_ul_write(int&nbsp;iBlock,
                              char[]&nbsp;pszData,
                              int&nbsp;iLenData)</pre>
<div class="block">ISO14443A_ul_write 将指定内容写入电子标签 传入参数：1）iBlock 写入数据的绝对块号 （0-3块不能写入数据）
 2)*pszData 写入的数据信息3)iLenData 写入数据信息的长度(4Byte)返回值：0为成功，非0为失败状态码</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>iBlock</code> - </dd>
<dd><code>pszData</code> - </dd>
<dd><code>iLenData</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="ISO14443A_cpu_rats--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_cpu_rats</h4>
<pre>public&nbsp;char[]&nbsp;ISO14443A_cpu_rats()</pre>
<div class="block">ISO14443A_cpu_rats CPU卡RATS操作指令（TYPE A类型） 传入参数：无
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2)如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始为CPU卡返回的rats操作数据</div>
</li>
</ul>
<a name="ISO14443A_cpu_reset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_cpu_reset</h4>
<pre>public&nbsp;char[]&nbsp;ISO14443A_cpu_reset()</pre>
<div class="block">ISO14443A_cpu_reset CPU卡复位操作指令（TYPE A类型） 传入参数：无
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2)如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始为CPU卡返回的reset操作数据</div>
</li>
</ul>
<a name="ISO14443A_cpu_command-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_cpu_command</h4>
<pre>public&nbsp;char[]&nbsp;ISO14443A_cpu_command(char[]&nbsp;pszCOS,
                                    int&nbsp;iLenCOS)</pre>
<div class="block">ISO14443A_cpu_command CPU卡 T=CL发送COS指令 传入参数：1）pszCOS传输的COS指令内容2）iLenCOS
 传输的COS指令内容长度返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据
 2)如果状态位表示成功，第1个元素表明后续还有的数据长度3）从第2个元素开始为CPU卡返回的COS指令内容</div>
</li>
</ul>
<a name="ISO14443B_cpu_reset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443B_cpu_reset</h4>
<pre>public&nbsp;char[]&nbsp;ISO14443B_cpu_reset()</pre>
<div class="block">ISO14443B_cpu_reset CPU卡复位操作指令（TYPE B类型） 传入参数：无
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2)如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始为CPU卡返回的reset操作数据</div>
</li>
</ul>
<a name="ISO14443B_UID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443B_UID</h4>
<pre>public&nbsp;char[]&nbsp;ISO14443B_UID()</pre>
</li>
</ul>
<a name="ISO14443B_cpu_command-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443B_cpu_command</h4>
<pre>public&nbsp;char[]&nbsp;ISO14443B_cpu_command(char[]&nbsp;pszCOS,
                                    int&nbsp;iLenCOS)</pre>
<div class="block">ISO14443B_cpu_command CPU卡 T=CL发送COS指令 传入参数：1）pszCOS传输的COS指令内容2）iLenCOS
 传输的COS指令内容长度返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据
 2)如果状态位表示成功，第1个元素表明后续还有的数据长度3）从第2个元素开始为CPU卡返回的COS指令内容</div>
</li>
</ul>
<a name="ISO15693_inventory-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_inventory</h4>
<pre>public&nbsp;char[]&nbsp;ISO15693_inventory(int&nbsp;iMode,
                                 int&nbsp;iAFI)</pre>
<div class="block">ISO15693_inventory 读取卡片UID 传入参数：1）iMode 0 多张卡呼叫 不带AFI，1 单张卡呼叫 不带AFI，2
 多张卡呼叫 带AFI ，3 单张卡呼叫 带AFI2）iAFI AFI值
 返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2)如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始为15693卡的ID号</div>
</li>
</ul>
<a name="ISO15693_read_sm-int-char:A-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_read_sm</h4>
<pre>public&nbsp;char[]&nbsp;ISO15693_read_sm(int&nbsp;iMode,
                               char[]&nbsp;pszUID,
                               int&nbsp;iLenUID,
                               int&nbsp;startblock,
                               int&nbsp;blocknum)</pre>
<div class="block">ISO15693_read_sm 读取卡片内部数据 传入参数：
 1）iMode 0   非SELECT状态，不传UID NXP I CODE SLI标签
 1 SELECT状态，不传UID NXP I CODE SLI 标签
 2 非SELECT状态，传UID NXP I CODE SLI 标签
 3 SELECT状态，传UID NXP I CODE SLI 标签
 4 非SELECT状态 不传UID TI 标签
 5 SELECT状态不传UID TI 标签
 6 非SELECT状态 传UID TI 标签
 7 SELECT状态 传UID TI 标签
 2）pszUID：UID
 3）iLenUID：UID长度
 4）Startblock：起始块号
 5）Blocknum：一共读取多少块（1-10）
 返回值：
 1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据
 2)如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始为读到的数据内容</div>
</li>
</ul>
<a name="ISO15693_read_sm_ex-int-char:A-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_read_sm_ex</h4>
<pre>public&nbsp;char[]&nbsp;ISO15693_read_sm_ex(int&nbsp;iMode,
                                  char[]&nbsp;pszUID,
                                  int&nbsp;iLenUID,
                                  int&nbsp;startblock,
                                  int&nbsp;blocknum)</pre>
</li>
</ul>
<a name="ISO15693_write_sm-int-char:A-int-int-int-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_write_sm</h4>
<pre>public&nbsp;int&nbsp;ISO15693_write_sm(int&nbsp;iMode,
                             char[]&nbsp;pszUID,
                             int&nbsp;iLenUID,
                             int&nbsp;startblock,
                             int&nbsp;blocknum,
                             char[]&nbsp;pszData,
                             int&nbsp;iWriteLen)</pre>
<div class="block">ISO15693_write_sm 向卡片内部写入数据 传入参数：1）iMode 0 非SELECT状态，不传UID NXP I CODE SLI
 标签 1 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I CODE SLI 标签
 3 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5 SELECT状态
 不传UID TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签2）pszUID：UID
 3）iLenUID：UID长度4）Startblock：起始块号5）blocknum：一共写入多少块6）pszData 写入的数据信息
 7）iWriteLen：写入的数据长度返回值：0成功，非0状态码</div>
</li>
</ul>
<a name="ISO15693_write_sm_ex-int-char:A-int-int-int-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_write_sm_ex</h4>
<pre>public&nbsp;int&nbsp;ISO15693_write_sm_ex(int&nbsp;iMode,
                                char[]&nbsp;pszUID,
                                int&nbsp;iLenUID,
                                int&nbsp;startblock,
                                int&nbsp;blocknum,
                                char[]&nbsp;pszData,
                                int&nbsp;iWriteLen)</pre>
</li>
</ul>
<a name="ISO15693_lockBlock-int-char:A-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_lockBlock</h4>
<pre>public&nbsp;int&nbsp;ISO15693_lockBlock(int&nbsp;iMode,
                              char[]&nbsp;pszUID,
                              int&nbsp;iLenUID,
                              int&nbsp;startblock,
                              int&nbsp;blocknum)</pre>
<div class="block">ISO15693_lockBlock 锁定数据块 传入参数：1）iMode 0 非SELECT状态，不传UID NXP I CODE SLI 标签
 1 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I CODE SLI 标签 3
 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5 SELECT状态 不传UID
 TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签2）pszUID：UID
 3）iLenUID：UID长度4）Startblock：起始块号5）blocknum：一共写入多少块返回值：0成功，非0状态码</div>
</li>
</ul>
<a name="ISO15693_writeAFI-int-char:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_writeAFI</h4>
<pre>public&nbsp;int&nbsp;ISO15693_writeAFI(int&nbsp;iMode,
                             char[]&nbsp;pszUID,
                             int&nbsp;iLenUID,
                             int&nbsp;iAFI)</pre>
<div class="block">ISO15693_writeAFI 写AFI 传入参数：1）iMode 0 非SELECT状态，不传UID NXP I CODE SLI 标签 1
 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I CODE SLI 标签 3
 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5 SELECT状态 不传UID
 TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签2）pszUID：UID
 3）iLenUID：UID长度4）iAFI：AFI内容返回值：0成功，非0状态码</div>
</li>
</ul>
<a name="ISO15693_lockAFI-int-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_lockAFI</h4>
<pre>public&nbsp;int&nbsp;ISO15693_lockAFI(int&nbsp;iMode,
                            char[]&nbsp;pszUID,
                            int&nbsp;iLenUID)</pre>
<div class="block">ISO15693_lockAFI 锁AFI 传入参数：1）iMode 0 非SELECT状态，不传UID NXP I CODE SLI 标签 1
 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I CODE SLI 标签 3
 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5 SELECT状态 不传UID
 TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签2）pszUID：UID
 3）iLenUID：UID长度返回值：0成功，非0状态码</div>
</li>
</ul>
<a name="ISO15693_writeDSFID-int-char:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_writeDSFID</h4>
<pre>public&nbsp;int&nbsp;ISO15693_writeDSFID(int&nbsp;iMode,
                               char[]&nbsp;pszUID,
                               int&nbsp;iLenUID,
                               int&nbsp;iDSFID)</pre>
<div class="block">ISO15693_writeDSFID 写DSFID 传入参数：1）iMode 0 非SELECT状态，不传UID NXP I CODE SLI
 标签 1 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I CODE SLI 标签
 3 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5 SELECT状态
 不传UID TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签2）pszUID：UID
 3）iLenUID：UID长度4）iDSFID：DSFID内容返回值：0成功，非0状态码</div>
</li>
</ul>
<a name="ISO15693_lockDSFID-int-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_lockDSFID</h4>
<pre>public&nbsp;int&nbsp;ISO15693_lockDSFID(int&nbsp;iMode,
                              char[]&nbsp;pszUID,
                              int&nbsp;iLenUID)</pre>
<div class="block">ISO15693_lockDSFID 锁DSFID 传入参数：1）iMode 0 非SELECT状态，不传UID NXP I CODE SLI
 标签 1 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I CODE SLI 标签
 3 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5 SELECT状态
 不传UID TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签2）pszUID：UID
 3）iLenUID：UID长度返回值：0成功，非0状态码</div>
</li>
</ul>
<a name="ISO15693_getSystemInformation-int-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_getSystemInformation</h4>
<pre>public&nbsp;char[]&nbsp;ISO15693_getSystemInformation(int&nbsp;iMode,
                                            char[]&nbsp;pszUID,
                                            int&nbsp;iLenUID)</pre>
<div class="block">ISO15693_getSystemInformation 获取电子标签信息 传入参数：1）iMode 0 非SELECT状态，不传UID NXP
 I CODE SLI 标签 1 SELECT状态，不传UID NXP I CODE SLI 标签 2 非SELECT状态，传UID NXP I
 CODE SLI 标签 3 SELECT状态，传UID NXP I CODE SLI 标签 4 非SELECT状态 不传UID TI 标签 5
 SELECT状态 不传UID TI 标签 6 非SELECT状态 传UID TI 标签 7 SELECT状态 传UID TI 标签
 2）pszUID：UID3）iLenUID：UID长度返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据
 2)如果状态位表示成功，第1个元素表明后续还有的数据长度3）从第2个元素开始为电子标签信息</div>
</li>
</ul>
<a name="ISO15693_GenericFunction-char-char:A-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_GenericFunction</h4>
<pre>public&nbsp;char[]&nbsp;ISO15693_GenericFunction(char&nbsp;Command,
                                       char[]&nbsp;jdatabuf,
                                       char&nbsp;jdatalen)</pre>
</li>
</ul>
<a name="ISO15693_getMultipleBlockSecurityStatus-int-char:A-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_getMultipleBlockSecurityStatus</h4>
<pre>public&nbsp;char[]&nbsp;ISO15693_getMultipleBlockSecurityStatus(int&nbsp;iMode,
                                                      char[]&nbsp;pszUID,
                                                      int&nbsp;iLenUID,
                                                      int&nbsp;startblock,
                                                      int&nbsp;blocknum)</pre>
<div class="block">ISO15693_getMultipleBlockSecurityStatus 获取多块安全状态 传入参数：1）iMode 0
 非SELECT状态，不传UID NXP I CODE SLI 标签 1 SELECT状态，不传UID NXP I CODE SLI 标签 2
 非SELECT状态，传UID NXP I CODE SLI 标签 3 SELECT状态，传UID NXP I CODE SLI 标签 4
 非SELECT状态 不传UID TI 标签 5 SELECT状态 不传UID TI 标签 6 非SELECT状态 传UID TI 标签 7
 SELECT状态 传UID TI 标签2）pszUID：UID3）iLenUID：UID长度4）Startblock：起始块号
 5）blocknum：一共读取块数返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据
 2)如果状态位表示成功，第1个元素表明后续还有的数据长度3）从第2个元素开始为多块安全状态信息</div>
</li>
</ul>
<a name="ISO15693_transferCommand-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_transferCommand</h4>
<pre>public&nbsp;char[]&nbsp;ISO15693_transferCommand(char[]&nbsp;pszCmd,
                                       int&nbsp;iComLen)</pre>
<div class="block">ISO15693_transferCommand 15693透传函数 传入参数：1）UINT8 *pszCmd传输的指令内容2）iComLen
 指令长度返回值：1）第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2)如果状态位表示成功，第1个元素表明后续还有的数据长度
 3）从第2个元素开始为卡片返回的响应数据</div>
</li>
</ul>
<a name="EMFingerInit-java.lang.String-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMFingerInit</h4>
<pre>public&nbsp;int&nbsp;EMFingerInit(java.lang.String&nbsp;device,
                        java.lang.String&nbsp;uart,
                        int&nbsp;baudrate)</pre>
</li>
</ul>
<a name="EMFingerFree-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMFingerFree</h4>
<pre>public&nbsp;int&nbsp;EMFingerFree(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="EMGetRandomData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMGetRandomData</h4>
<pre>public&nbsp;char[]&nbsp;EMGetRandomData()</pre>
</li>
</ul>
<a name="EMGetImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMGetImage</h4>
<pre>public&nbsp;int&nbsp;EMGetImage()</pre>
</li>
</ul>
<a name="EMGenChar-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMGenChar</h4>
<pre>public&nbsp;int&nbsp;EMGenChar(int&nbsp;GenID)</pre>
</li>
</ul>
<a name="EMMatch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMMatch</h4>
<pre>public&nbsp;int[]&nbsp;EMMatch()</pre>
</li>
</ul>
<a name="EMSearch-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMSearch</h4>
<pre>public&nbsp;int[]&nbsp;EMSearch(int&nbsp;BuffID,
                      int&nbsp;StartPage,
                      int&nbsp;PageNum)</pre>
</li>
</ul>
<a name="EMRegModel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMRegModel</h4>
<pre>public&nbsp;int&nbsp;EMRegModel()</pre>
</li>
</ul>
<a name="EMStorChar-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMStorChar</h4>
<pre>public&nbsp;int&nbsp;EMStorChar(int&nbsp;BuffID,
                      int&nbsp;PageID)</pre>
</li>
</ul>
<a name="EMLoadChar-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMLoadChar</h4>
<pre>public&nbsp;int&nbsp;EMLoadChar(int&nbsp;BuffID,
                      int&nbsp;PageID)</pre>
</li>
</ul>
<a name="EMUpChar-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMUpChar</h4>
<pre>public&nbsp;char[]&nbsp;EMUpChar(int&nbsp;BuffID)</pre>
</li>
</ul>
<a name="EMDownChar-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMDownChar</h4>
<pre>public&nbsp;int&nbsp;EMDownChar(int&nbsp;BuffID,
                      char[]&nbsp;pszData)</pre>
</li>
</ul>
<a name="EMDeletChar-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMDeletChar</h4>
<pre>public&nbsp;int&nbsp;EMDeletChar(int&nbsp;PageID,
                       int&nbsp;nNum)</pre>
</li>
</ul>
<a name="EMEmpty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMEmpty</h4>
<pre>public&nbsp;int&nbsp;EMEmpty()</pre>
</li>
</ul>
<a name="EMSetReg-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMSetReg</h4>
<pre>public&nbsp;int&nbsp;EMSetReg(int&nbsp;RegID,
                    int&nbsp;nValue)</pre>
</li>
</ul>
<a name="EMAutoEnroll-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMAutoEnroll</h4>
<pre>public&nbsp;int[]&nbsp;EMAutoEnroll(int&nbsp;nTime,
                          int&nbsp;UserID)</pre>
</li>
</ul>
<a name="EMAutoMatch-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMAutoMatch</h4>
<pre>public&nbsp;int[]&nbsp;EMAutoMatch(int&nbsp;nFlag,
                         int&nbsp;StartPage,
                         int&nbsp;PageNum)</pre>
</li>
</ul>
<a name="EMSetPSW-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMSetPSW</h4>
<pre>public&nbsp;int&nbsp;EMSetPSW(char[]&nbsp;PassWord)</pre>
</li>
</ul>
<a name="EMVfyPSW-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMVfyPSW</h4>
<pre>public&nbsp;int&nbsp;EMVfyPSW(char[]&nbsp;PassWord)</pre>
</li>
</ul>
<a name="EMValidTempleteNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMValidTempleteNum</h4>
<pre>public&nbsp;int[]&nbsp;EMValidTempleteNum()</pre>
</li>
</ul>
<a name="EMReadChipSN--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMReadChipSN</h4>
<pre>public&nbsp;char[]&nbsp;EMReadChipSN()</pre>
</li>
</ul>
<a name="EMSetManuFacture-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMSetManuFacture</h4>
<pre>public&nbsp;int&nbsp;EMSetManuFacture(char[]&nbsp;NameInfo)</pre>
</li>
</ul>
<a name="EMSetDeviceName-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMSetDeviceName</h4>
<pre>public&nbsp;int&nbsp;EMSetDeviceName(char[]&nbsp;DeviceName)</pre>
</li>
</ul>
<a name="EMReadSysPara--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMReadSysPara</h4>
<pre>public&nbsp;char[]&nbsp;EMReadSysPara()</pre>
</li>
</ul>
<a name="EMReadSysParaMore--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMReadSysParaMore</h4>
<pre>public&nbsp;char[]&nbsp;EMReadSysParaMore()</pre>
</li>
</ul>
<a name="EMUpImage-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMUpImage</h4>
<pre>public&nbsp;int[]&nbsp;EMUpImage(int&nbsp;Mode,
                       java.lang.String&nbsp;lpFileName)</pre>
</li>
</ul>
<a name="EMUpImageISO-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMUpImageISO</h4>
<pre>public&nbsp;int[]&nbsp;EMUpImageISO(int&nbsp;Mode,
                          java.lang.String&nbsp;lpFileName)</pre>
</li>
</ul>
<a name="EM125k_init-java.lang.String-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM125k_init</h4>
<pre>public&nbsp;int&nbsp;EM125k_init(java.lang.String&nbsp;device,
                       java.lang.String&nbsp;uart,
                       int&nbsp;baudrate)</pre>
</li>
</ul>
<a name="EM125k_free-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM125k_free</h4>
<pre>public&nbsp;int&nbsp;EM125k_free(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="HardwareVersion_125k--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HardwareVersion_125k</h4>
<pre>public&nbsp;char[]&nbsp;HardwareVersion_125k()</pre>
</li>
</ul>
<a name="EM125k_read-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM125k_read</h4>
<pre>public&nbsp;char[]&nbsp;EM125k_read(int&nbsp;iMode)</pre>
</li>
</ul>
<a name="EM125k_UID_REQ--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM125k_UID_REQ</h4>
<pre>public&nbsp;char[]&nbsp;EM125k_UID_REQ()</pre>
</li>
</ul>
<a name="EM125k_ReadHitag-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM125k_ReadHitag</h4>
<pre>public&nbsp;char[]&nbsp;EM125k_ReadHitag(int&nbsp;nPage)</pre>
</li>
</ul>
<a name="EM125k_WriteHitagPage-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM125k_WriteHitagPage</h4>
<pre>public&nbsp;int&nbsp;EM125k_WriteHitagPage(int&nbsp;nPage,
                                 char[]&nbsp;pszData)</pre>
</li>
</ul>
<a name="EM125k_Read4305-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM125k_Read4305</h4>
<pre>public&nbsp;char[]&nbsp;EM125k_Read4305(int&nbsp;nPage)</pre>
<div class="block">读EM4305卡 ******************************** 功能描述：读4305卡的某一页（0~31）数据
 输入参数：nPage -- 页地址(0~31) 第0、3、5~13页可读可写，可供用户存储数据使用 第1页为UID存储区，只读 第2页为密码区
 第4页为参数配置区域 第14、15页为保护区 输出参数：pszData-- 4字节数据（每页可存放4字节）</div>
</li>
</ul>
<a name="EM125k_Write4305-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM125k_Write4305</h4>
<pre>public&nbsp;int&nbsp;EM125k_Write4305(int&nbsp;nPage,
                            char[]&nbsp;pszData)</pre>
<div class="block">写EM4305卡 ******************************** 功能描述：写4305卡的某一页（0~31）数据
 输入参数：nPage -- 页地址(0~31) 第0、3、5~13页可读可写，可供用户存储数据使用 第1页为UID存储区，只读 第2页为密码区
 第4页为参数配置区域 第14、15页为保护区 pszData-- 4字节数据（每页可存放4字节）</div>
</li>
</ul>
<a name="EM125K_GetEm4450UID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM125K_GetEm4450UID</h4>
<pre>public&nbsp;char[]&nbsp;EM125K_GetEm4450UID()</pre>
</li>
</ul>
<a name="EM_25kread--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM_25kread</h4>
<pre>public&nbsp;char[]&nbsp;EM_25kread()</pre>
<div class="block">读半双工的动物标签 ************************ 输出参数：pszData-- 16字节动物标签数据</div>
</li>
</ul>
<a name="EM125k_init_Ex-java.lang.String-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM125k_init_Ex</h4>
<pre>public&nbsp;int&nbsp;EM125k_init_Ex(java.lang.String&nbsp;device,
                          java.lang.String&nbsp;uart,
                          int&nbsp;baudrate)</pre>
</li>
</ul>
<a name="EM125k_read_Ex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM125k_read_Ex</h4>
<pre>public&nbsp;char[]&nbsp;EM125k_read_Ex()</pre>
</li>
</ul>
<a name="ConfigFDXTag-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ConfigFDXTag</h4>
<pre>public&nbsp;int&nbsp;ConfigFDXTag(char[]&nbsp;cidbuf,
                        char[]&nbsp;nidbuf)</pre>
</li>
</ul>
<a name="HID_GetUid--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HID_GetUid</h4>
<pre>public&nbsp;int&nbsp;HID_GetUid()</pre>
</li>
</ul>
<a name="EM125k_ReadHitag1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM125k_ReadHitag1</h4>
<pre>public&nbsp;char[]&nbsp;EM125k_ReadHitag1()</pre>
</li>
</ul>
<a name="UHFInventory_EX_BankPtrCnt-char-char-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventory_EX_BankPtrCnt</h4>
<pre>public&nbsp;int&nbsp;UHFInventory_EX_BankPtrCnt(char&nbsp;flagAnti,
                                      char&nbsp;initQ,
                                      char&nbsp;bank,
                                      char&nbsp;ptr,
                                      char&nbsp;cnt)</pre>
</li>
</ul>
<a name="UHFBlockPermalock-char:A-char-char-char-char:A-char-char-char-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBlockPermalock</h4>
<pre>public&nbsp;char[]&nbsp;UHFBlockPermalock(char[]&nbsp;jucppwd,
                                char&nbsp;bank,
                                char&nbsp;startaddr,
                                char&nbsp;datalen,
                                char[]&nbsp;jucpdatabuf,
                                char&nbsp;readlock,
                                char&nbsp;MB,
                                char&nbsp;blockptr,
                                char&nbsp;blockrange,
                                char[]&nbsp;jucpmask)</pre>
</li>
</ul>
<a name="Um7ProtocolConfig-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Um7ProtocolConfig</h4>
<pre>public&nbsp;int&nbsp;Um7ProtocolConfig(char&nbsp;conf)</pre>
</li>
</ul>
<a name="UHFFlagCrcOn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFFlagCrcOn</h4>
<pre>public&nbsp;void&nbsp;UHFFlagCrcOn()</pre>
</li>
</ul>
<a name="R2000_OPENMODE--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>R2000_OPENMODE</h4>
<pre>public&nbsp;int&nbsp;R2000_OPENMODE()</pre>
</li>
</ul>
<a name="R2000_FreHopSet-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>R2000_FreHopSet</h4>
<pre>public&nbsp;int&nbsp;R2000_FreHopSet(int&nbsp;fre)</pre>
</li>
</ul>
<a name="UHFFlafCrcOff--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFFlafCrcOff</h4>
<pre>public&nbsp;void&nbsp;UHFFlafCrcOff()</pre>
</li>
</ul>
<a name="UHFInit-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInit</h4>
<pre>public&nbsp;int&nbsp;UHFInit(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="UHFFree-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFFree</h4>
<pre>public&nbsp;int&nbsp;UHFFree(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="UHFOpenAndConnect-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFOpenAndConnect</h4>
<pre>public&nbsp;int&nbsp;UHFOpenAndConnect(java.lang.String&nbsp;uart)</pre>
</li>
</ul>
<a name="UHFOpenAndConnect_Ex-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFOpenAndConnect_Ex</h4>
<pre>public&nbsp;int&nbsp;UHFOpenAndConnect_Ex(java.lang.String&nbsp;uart)</pre>
</li>
</ul>
<a name="UHFCloseAndDisconnect--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFCloseAndDisconnect</h4>
<pre>public&nbsp;void&nbsp;UHFCloseAndDisconnect()</pre>
</li>
</ul>
<a name="UHFGetPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetPower</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetPower()</pre>
</li>
</ul>
<a name="UHFSetPower-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetPower</h4>
<pre>public&nbsp;int&nbsp;UHFSetPower(char&nbsp;uPower)</pre>
</li>
</ul>
<a name="UHFSetPowerOnDynamic-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetPowerOnDynamic</h4>
<pre>public&nbsp;int&nbsp;UHFSetPowerOnDynamic(char&nbsp;uPower)</pre>
</li>
</ul>
<a name="UHFGetHwType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetHwType</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetHwType()</pre>
</li>
</ul>
<a name="UHFGetHardwareVersionType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetHardwareVersionType</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetHardwareVersionType()</pre>
</li>
</ul>
<a name="UHFGetFrequency--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetFrequency</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetFrequency()</pre>
</li>
</ul>
<a name="UHFGetFrequency_Ex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetFrequency_Ex</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetFrequency_Ex()</pre>
</li>
</ul>
<a name="UHFSetFrequency-char-char-char:A-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetFrequency</h4>
<pre>public&nbsp;int&nbsp;UHFSetFrequency(char&nbsp;uFreMode,
                           char&nbsp;uFreBase,
                           char[]&nbsp;uBaseFre,
                           char&nbsp;uChannNum,
                           char&nbsp;uChannSpc,
                           char&nbsp;uFreHopUCHAR)</pre>
</li>
</ul>
<a name="UHFSetFrequency_EX-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetFrequency_EX</h4>
<pre>public&nbsp;int&nbsp;UHFSetFrequency_EX(char&nbsp;FreMode)</pre>
</li>
</ul>
<a name="UHFInventory-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventory</h4>
<pre>public&nbsp;int&nbsp;UHFInventory(char&nbsp;flagAnti,
                        char&nbsp;initQ)</pre>
</li>
</ul>
<a name="UHFInventory_EX-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventory_EX</h4>
<pre>public&nbsp;int&nbsp;UHFInventory_EX(char&nbsp;flagAnti,
                           char&nbsp;initQ)</pre>
</li>
</ul>
<a name="UHFInventory_EX_cnt-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventory_EX_cnt</h4>
<pre>public&nbsp;int&nbsp;UHFInventory_EX_cnt(char&nbsp;flagAnti,
                               char&nbsp;initQ,
                               char&nbsp;cnt)</pre>
</li>
</ul>
<a name="UHFInventoryTID-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventoryTID</h4>
<pre>public&nbsp;int&nbsp;UHFInventoryTID(char&nbsp;flagAnti,
                           char&nbsp;initQ)</pre>
</li>
</ul>
<a name="UHFStartReadDataFromMultiTag-char-char:A-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStartReadDataFromMultiTag</h4>
<pre>public&nbsp;int&nbsp;UHFStartReadDataFromMultiTag(char&nbsp;flagAnti,
                                        char[]&nbsp;apwd,
                                        char&nbsp;bank,
                                        char&nbsp;ptr,
                                        char&nbsp;cnt)</pre>
</li>
</ul>
<a name="UHFGetMultiDataReceived--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetMultiDataReceived</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetMultiDataReceived()</pre>
</li>
</ul>
<a name="UHFGetReceived--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetReceived</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetReceived()</pre>
</li>
</ul>
<a name="UHFGetReceived_EX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetReceived_EX</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetReceived_EX()</pre>
</li>
</ul>
<a name="UHFGetReceived_EX2-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetReceived_EX2</h4>
<pre>public&nbsp;int&nbsp;UHFGetReceived_EX2(char[]&nbsp;outData)</pre>
</li>
</ul>
<a name="UHFGetReceived_EX_R2000--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetReceived_EX_R2000</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetReceived_EX_R2000()</pre>
</li>
</ul>
<a name="UHFGetRSSI--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetRSSI</h4>
<pre>public&nbsp;int[]&nbsp;UHFGetRSSI()</pre>
</li>
</ul>
<a name="UHFStopGet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStopGet</h4>
<pre>public&nbsp;int&nbsp;UHFStopGet()</pre>
</li>
</ul>
<a name="UHFSetPwm-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetPwm</h4>
<pre>public&nbsp;int&nbsp;UHFSetPwm(int&nbsp;WorkTime,
                     int&nbsp;WaitTime)</pre>
</li>
</ul>
<a name="UHFGetPwm--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetPwm</h4>
<pre>public&nbsp;int[]&nbsp;UHFGetPwm()</pre>
</li>
</ul>
<a name="UHFSetSingelMode-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetSingelMode</h4>
<pre>public&nbsp;int&nbsp;UHFSetSingelMode(char&nbsp;mode)</pre>
</li>
</ul>
<a name="UHFGetSingelMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetSingelMode</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetSingelMode()</pre>
</li>
</ul>
<a name="UHFReadData-char:A-char-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFReadData</h4>
<pre>public&nbsp;char[]&nbsp;UHFReadData(char[]&nbsp;uAccessPwd,
                          char&nbsp;uBank,
                          int&nbsp;uPtr,
                          int&nbsp;uCnt,
                          char[]&nbsp;uUii)</pre>
</li>
</ul>
<a name="UHFReadData_EX-char:A-char-int-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFReadData_EX</h4>
<pre>public&nbsp;char[]&nbsp;UHFReadData_EX(char[]&nbsp;uAccessPwd,
                             char&nbsp;uBank,
                             int&nbsp;uPtr,
                             char&nbsp;uCnt,
                             char[]&nbsp;uTID)</pre>
</li>
</ul>
<a name="UHFWriteData-char:A-char-int-char-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFWriteData</h4>
<pre>public&nbsp;int&nbsp;UHFWriteData(char[]&nbsp;uAccessPwd,
                        char&nbsp;uBank,
                        int&nbsp;uPtr,
                        char&nbsp;uCnt,
                        char[]&nbsp;uUii,
                        char[]&nbsp;uWriteData)</pre>
</li>
</ul>
<a name="UHFEraseData-char:A-char-int-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFEraseData</h4>
<pre>public&nbsp;int&nbsp;UHFEraseData(char[]&nbsp;uAccessPwd,
                        char&nbsp;uBank,
                        int&nbsp;uPtr,
                        char&nbsp;uCnt,
                        char[]&nbsp;uUii)</pre>
</li>
</ul>
<a name="UHFEraseDataEx-char:A-char-int-char-char:A-char-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFEraseDataEx</h4>
<pre>public&nbsp;int&nbsp;UHFEraseDataEx(char[]&nbsp;pszuAccessPwd,
                          char&nbsp;filterBank,
                          int&nbsp;filterPtr,
                          char&nbsp;filterCnt,
                          char[]&nbsp;filterData,
                          char&nbsp;uBank,
                          int&nbsp;uPtr,
                          int&nbsp;uCnt)</pre>
</li>
</ul>
<a name="UHFLockMem-char:A-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFLockMem</h4>
<pre>public&nbsp;int&nbsp;UHFLockMem(char[]&nbsp;uAccessPwd,
                      char[]&nbsp;uLockData,
                      char[]&nbsp;uUii)</pre>
</li>
</ul>
<a name="UHFKillTag-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFKillTag</h4>
<pre>public&nbsp;int&nbsp;UHFKillTag(char[]&nbsp;uKillPwd,
                      char[]&nbsp;uUii)</pre>
</li>
</ul>
<a name="UHFGetVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetVersion</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetVersion()</pre>
</li>
</ul>
<a name="UHFInventorySingle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventorySingle</h4>
<pre>public&nbsp;char[]&nbsp;UHFInventorySingle()</pre>
</li>
</ul>
<a name="UHFInventorySingleEPCTIDUSER-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventorySingleEPCTIDUSER</h4>
<pre>public&nbsp;char[]&nbsp;UHFInventorySingleEPCTIDUSER(int&nbsp;p1,
                                           int&nbsp;p2)</pre>
</li>
</ul>
<a name="UHFInventorySingle_sf--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventorySingle_sf</h4>
<pre>public&nbsp;char[]&nbsp;UHFInventorySingle_sf()</pre>
</li>
</ul>
<a name="UHFInventorySingle_EX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventorySingle_EX</h4>
<pre>public&nbsp;char[]&nbsp;UHFInventorySingle_EX()</pre>
</li>
</ul>
<a name="UHFInventorySingle_R2000--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventorySingle_R2000</h4>
<pre>public&nbsp;char[]&nbsp;UHFInventorySingle_R2000()</pre>
</li>
</ul>
<a name="UHFReadDataSingle-char:A-char-int-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFReadDataSingle</h4>
<pre>public&nbsp;char[]&nbsp;UHFReadDataSingle(char[]&nbsp;uAccessPwd,
                                char&nbsp;uBank,
                                int&nbsp;uPtr,
                                char&nbsp;uCnt)</pre>
</li>
</ul>
<a name="UHFWriteDataSingle-char:A-char-int-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFWriteDataSingle</h4>
<pre>public&nbsp;char[]&nbsp;UHFWriteDataSingle(char[]&nbsp;uAccessPwd,
                                 char&nbsp;uBank,
                                 int&nbsp;uPtr,
                                 char&nbsp;uCnt,
                                 char[]&nbsp;uWriteData)</pre>
</li>
</ul>
<a name="UHFEraseDataSingle-char:A-char-int-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFEraseDataSingle</h4>
<pre>public&nbsp;char[]&nbsp;UHFEraseDataSingle(char[]&nbsp;uAccessPwd,
                                 char&nbsp;uBank,
                                 int&nbsp;uPtr,
                                 char&nbsp;uCnt)</pre>
</li>
</ul>
<a name="UHFLockMemSingle-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFLockMemSingle</h4>
<pre>public&nbsp;char[]&nbsp;UHFLockMemSingle(char[]&nbsp;uAccessPwd,
                               char[]&nbsp;uLockData)</pre>
</li>
</ul>
<a name="UHFKillTagSingle-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFKillTagSingle</h4>
<pre>public&nbsp;char[]&nbsp;UHFKillTagSingle(char[]&nbsp;uKillPwd)</pre>
</li>
</ul>
<a name="UHFSetFilter-char-char-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetFilter</h4>
<pre>public&nbsp;int&nbsp;UHFSetFilter(char&nbsp;flagstore,
                        char&nbsp;uBank,
                        int&nbsp;uPtr,
                        int&nbsp;len,
                        char[]&nbsp;data)</pre>
</li>
</ul>
<a name="UHFSetParam-char-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetParam</h4>
<pre>public&nbsp;int&nbsp;UHFSetParam(char&nbsp;type,
                       char[]&nbsp;id,
                       char[]&nbsp;data)</pre>
</li>
</ul>
<a name="UHFGetParam-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetParam</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetParam(char&nbsp;type,
                          char[]&nbsp;id)</pre>
</li>
</ul>
<a name="UHFSetFastInventoryMode-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetFastInventoryMode</h4>
<pre>public&nbsp;int&nbsp;UHFSetFastInventoryMode(int&nbsp;on,
                                   int&nbsp;save,
                                   int&nbsp;type)</pre>
</li>
</ul>
<a name="UHFGetFastInventoryMode-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetFastInventoryMode</h4>
<pre>public&nbsp;int&nbsp;UHFGetFastInventoryMode(int&nbsp;rev1,
                                   int&nbsp;rev2)</pre>
</li>
</ul>
<a name="FactoryReset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FactoryReset</h4>
<pre>public&nbsp;int&nbsp;FactoryReset()</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_SelApp-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_SelApp</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_SelApp(byte[]&nbsp;AppId)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_GetApps--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_GetApps</h4>
<pre>public&nbsp;byte[]&nbsp;RF_ISO14443A_DESFIRE_GetApps()</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_DelApp-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_DelApp</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_DelApp(char[]&nbsp;AppId)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_AddApp-char:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_AddApp</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_AddApp(char[]&nbsp;AppId,
                                       int&nbsp;KeySetting,
                                       int&nbsp;FileNums)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_GetFileIds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_GetFileIds</h4>
<pre>public&nbsp;byte[]&nbsp;RF_ISO14443A_DESFIRE_GetFileIds()</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_GetPiccInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_GetPiccInfo</h4>
<pre>public&nbsp;char[]&nbsp;RF_ISO14443A_DESFIRE_GetPiccInfo()</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_AddStdFile-int-int-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_AddStdFile</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_AddStdFile(int&nbsp;FileNo,
                                           int&nbsp;CommSet,
                                           char[]&nbsp;AccessRight,
                                           int&nbsp;FileSize)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_DelFile-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_DelFile</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_DelFile(int&nbsp;FileNo)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_GetFileSetting-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_GetFileSetting</h4>
<pre>public&nbsp;byte[]&nbsp;RF_ISO14443A_DESFIRE_GetFileSetting(int&nbsp;FileNo)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_ChangeFileSetting-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_ChangeFileSetting</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_ChangeFileSetting(int&nbsp;FileNo,
                                                  int&nbsp;CommSet,
                                                  char[]&nbsp;AccessRights)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_Auth-int-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_Auth</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_Auth(int&nbsp;KeyNo,
                                     char[]&nbsp;KeyBuf,
                                     int&nbsp;KeyLen)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_Cpysel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_Cpysel</h4>
<pre>public&nbsp;void&nbsp;RF_ISO14443A_DESFIRE_Cpysel(int&nbsp;cpyType)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_RatPss--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_RatPss</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_RatPss()</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_ChangeKey-int-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_ChangeKey</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_ChangeKey(int&nbsp;KeyNo,
                                          char[]&nbsp;KeyBuf,
                                          int&nbsp;KeyLen)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_GetKeySetting--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_GetKeySetting</h4>
<pre>public&nbsp;char[]&nbsp;RF_ISO14443A_DESFIRE_GetKeySetting()</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_ChangeKeySetting-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_ChangeKeySetting</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_ChangeKeySetting(int&nbsp;KeySetting)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_WriteStdFile-int-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_WriteStdFile</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_WriteStdFile(int&nbsp;FileNo,
                                             int&nbsp;OffSet,
                                             int&nbsp;DataSize,
                                             char[]&nbsp;DataBuf)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_ReadStdFile-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_ReadStdFile</h4>
<pre>public&nbsp;char[]&nbsp;RF_ISO14443A_DESFIRE_ReadStdFile(int&nbsp;FileNo,
                                               int&nbsp;OffSet,
                                               int&nbsp;DataSize)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_AddValueFile-int-int-char:A-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_AddValueFile</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_AddValueFile(int&nbsp;FileNo,
                                             int&nbsp;CommSet,
                                             char[]&nbsp;AccessRights,
                                             int&nbsp;MinValue,
                                             int&nbsp;MaxValue,
                                             int&nbsp;InitValue)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_GetValueFile-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_GetValueFile</h4>
<pre>public&nbsp;int[]&nbsp;RF_ISO14443A_DESFIRE_GetValueFile(int&nbsp;FileNo)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_CreditValueFile-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_CreditValueFile</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_CreditValueFile(int&nbsp;FileNo,
                                                int&nbsp;CreValue)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_DebitValueFile-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_DebitValueFile</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_DebitValueFile(int&nbsp;FileNo,
                                               int&nbsp;DeValue)</pre>
</li>
</ul>
<a name="RF_ISO14443A_DESFIRE_FormatCard--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RF_ISO14443A_DESFIRE_FormatCard</h4>
<pre>public&nbsp;int&nbsp;RF_ISO14443A_DESFIRE_FormatCard()</pre>
</li>
</ul>
<a name="Psam_Init-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Psam_Init</h4>
<pre>public&nbsp;int&nbsp;Psam_Init(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="Psam_InitXy-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Psam_InitXy</h4>
<pre>public&nbsp;int&nbsp;Psam_InitXy(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="Psam_Free-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Psam_Free</h4>
<pre>public&nbsp;int&nbsp;Psam_Free(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="Psam_Cmd-java.lang.String-char-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Psam_Cmd</h4>
<pre>public&nbsp;byte[]&nbsp;Psam_Cmd(java.lang.String&nbsp;device,
                       char&nbsp;cmd,
                       char[]&nbsp;cmddata,
                       int&nbsp;cmdlen)</pre>
</li>
</ul>
<a name="RFID_UPDATE-int-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RFID_UPDATE</h4>
<pre>public&nbsp;int&nbsp;RFID_UPDATE(int&nbsp;jpackage_sum_number,
                       int&nbsp;jpackage_which_number,
                       int&nbsp;pszData_len,
                       byte[]&nbsp;pszData)</pre>
</li>
</ul>
<a name="PSAM_UPDATE-int-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PSAM_UPDATE</h4>
<pre>public&nbsp;int&nbsp;PSAM_UPDATE(int&nbsp;jpackage_sum_number,
                       int&nbsp;jpackage_which_number,
                       int&nbsp;pszData_len,
                       byte[]&nbsp;pszData)</pre>
</li>
</ul>
<a name="infrared_read--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>infrared_read</h4>
<pre>public&nbsp;byte[]&nbsp;infrared_read()</pre>
</li>
</ul>
<a name="infrared_write-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>infrared_write</h4>
<pre>public&nbsp;int&nbsp;infrared_write(byte[]&nbsp;data,
                          int&nbsp;len)</pre>
</li>
</ul>
<a name="Infrared_Open-java.lang.String-java.lang.String-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Infrared_Open</h4>
<pre>public&nbsp;int&nbsp;Infrared_Open(java.lang.String&nbsp;device,
                         java.lang.String&nbsp;uart,
                         int&nbsp;baudrate,
                         int&nbsp;databits,
                         int&nbsp;stopbits,
                         int&nbsp;check)</pre>
</li>
</ul>
<a name="Infrared_SwitchSerialPort-java.lang.String-java.lang.String-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Infrared_SwitchSerialPort</h4>
<pre>public&nbsp;int&nbsp;Infrared_SwitchSerialPort(java.lang.String&nbsp;device,
                                     java.lang.String&nbsp;uart,
                                     int&nbsp;baudrate,
                                     int&nbsp;databits,
                                     int&nbsp;stopbits,
                                     int&nbsp;check)</pre>
</li>
</ul>
<a name="Infrared_Close-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Infrared_Close</h4>
<pre>public&nbsp;int&nbsp;Infrared_Close(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="Infared_IDPOWER--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Infared_IDPOWER</h4>
<pre>public&nbsp;byte[]&nbsp;Infared_IDPOWER()</pre>
</li>
</ul>
<a name="Infared_IDPOWER07--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Infared_IDPOWER07</h4>
<pre>public&nbsp;byte[]&nbsp;Infared_IDPOWER07()</pre>
</li>
</ul>
<a name="UHFReadQTData-char:A-char-int-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFReadQTData</h4>
<pre>public&nbsp;char[]&nbsp;UHFReadQTData(char[]&nbsp;uAccessPwd,
                            char&nbsp;uBank,
                            int&nbsp;uPtr,
                            char&nbsp;uCnt,
                            char[]&nbsp;uUii)</pre>
</li>
</ul>
<a name="UHFReadQTDataSingle-char:A-char-int-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFReadQTDataSingle</h4>
<pre>public&nbsp;char[]&nbsp;UHFReadQTDataSingle(char[]&nbsp;uAccessPwd,
                                  char&nbsp;uBank,
                                  int&nbsp;uPtr,
                                  char&nbsp;uCnt)</pre>
</li>
</ul>
<a name="UHFWriteQTData-char:A-char-int-char-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFWriteQTData</h4>
<pre>public&nbsp;int&nbsp;UHFWriteQTData(char[]&nbsp;uAccessPwd,
                          char&nbsp;uBank,
                          int&nbsp;uPtr,
                          char&nbsp;uCnt,
                          char[]&nbsp;uUii,
                          char[]&nbsp;uWriteData)</pre>
</li>
</ul>
<a name="UHFWriteQTDataSingle-char:A-char-int-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFWriteQTDataSingle</h4>
<pre>public&nbsp;char[]&nbsp;UHFWriteQTDataSingle(char[]&nbsp;uAccessPwd,
                                   char&nbsp;uBank,
                                   int&nbsp;uPtr,
                                   char&nbsp;uCnt,
                                   char[]&nbsp;uWriteData)</pre>
</li>
</ul>
<a name="GetQTPara--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GetQTPara</h4>
<pre>public&nbsp;char[]&nbsp;GetQTPara()</pre>
</li>
</ul>
<a name="SetQTPara-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SetQTPara</h4>
<pre>public&nbsp;int&nbsp;SetQTPara(char&nbsp;pszData)</pre>
</li>
</ul>
<a name="UHF706_OpenAndConnect-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHF706_OpenAndConnect</h4>
<pre>public&nbsp;int&nbsp;UHF706_OpenAndConnect(java.lang.String&nbsp;path)</pre>
</li>
</ul>
<a name="UHF706_CloseAndDisconnect--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHF706_CloseAndDisconnect</h4>
<pre>public&nbsp;void&nbsp;UHF706_CloseAndDisconnect()</pre>
</li>
</ul>
<a name="Config_SetRTC-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Config_SetRTC</h4>
<pre>public&nbsp;int&nbsp;Config_SetRTC(byte[]&nbsp;RTC)</pre>
</li>
</ul>
<a name="Config_ReadRTC--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Config_ReadRTC</h4>
<pre>public&nbsp;byte[]&nbsp;Config_ReadRTC()</pre>
</li>
</ul>
<a name="Config_SetPara-int-byte-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Config_SetPara</h4>
<pre>public&nbsp;int&nbsp;Config_SetPara(int&nbsp;type,
                          byte&nbsp;len,
                          byte[]&nbsp;data)</pre>
</li>
</ul>
<a name="Config_GetPara-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Config_GetPara</h4>
<pre>public&nbsp;byte[]&nbsp;Config_GetPara(int&nbsp;type)</pre>
</li>
</ul>
<a name="SetTestMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SetTestMode</h4>
<pre>public&nbsp;int&nbsp;SetTestMode()</pre>
</li>
</ul>
<a name="Config_GetAccess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Config_GetAccess</h4>
<pre>public&nbsp;byte[]&nbsp;Config_GetAccess()</pre>
</li>
</ul>
<a name="Config_SetWorkMode-byte-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Config_SetWorkMode</h4>
<pre>public&nbsp;int&nbsp;Config_SetWorkMode(byte&nbsp;nums,
                              byte[]&nbsp;modebuf)</pre>
</li>
</ul>
<a name="Auth_RequestRandom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Auth_RequestRandom</h4>
<pre>public&nbsp;byte[]&nbsp;Auth_RequestRandom()</pre>
</li>
</ul>
<a name="Auth_SendData-byte:A-byte:A-byte:A-byte:A-byte:A-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Auth_SendData</h4>
<pre>public&nbsp;int&nbsp;Auth_SendData(byte[]&nbsp;hrandom,
                         byte[]&nbsp;srandom,
                         byte[]&nbsp;devid,
                         byte[]&nbsp;reserve,
                         byte[]&nbsp;signature,
                         int&nbsp;certlen,
                         byte[]&nbsp;certbuf)</pre>
</li>
</ul>
<a name="Auth_UserInfo-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Auth_UserInfo</h4>
<pre>public&nbsp;byte[]&nbsp;Auth_UserInfo(byte[]&nbsp;jprovince,
                            byte[]&nbsp;jcity,
                            byte[]&nbsp;jjw,
                            byte[]&nbsp;jusage,
                            byte[]&nbsp;jtype1,
                            byte[]&nbsp;jcom_name,
                            byte[]&nbsp;jpeople,
                            byte[]&nbsp;jtel1,
                            byte[]&nbsp;jtel2,
                            byte[]&nbsp;jemail,
                            byte[]&nbsp;jdata,
                            byte[]&nbsp;jps)</pre>
</li>
</ul>
<a name="Auth_ActiveFile-byte-byte:A-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Auth_ActiveFile</h4>
<pre>public&nbsp;int&nbsp;Auth_ActiveFile(byte&nbsp;transmode,
                           byte[]&nbsp;deviceid,
                           byte[]&nbsp;reverse,
                           byte[]&nbsp;transbuf)</pre>
</li>
</ul>
<a name="Auth_CertificationChain-byte-byte-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Auth_CertificationChain</h4>
<pre>public&nbsp;int&nbsp;Auth_CertificationChain(byte&nbsp;level,
                                   byte&nbsp;levels,
                                   int&nbsp;len,
                                   byte[]&nbsp;buf)</pre>
</li>
</ul>
<a name="Update_ReadInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Update_ReadInfo</h4>
<pre>public&nbsp;byte[]&nbsp;Update_ReadInfo()</pre>
</li>
</ul>
<a name="SetInventorySper-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SetInventorySper</h4>
<pre>public&nbsp;int&nbsp;SetInventorySper(int&nbsp;flag)</pre>
</li>
</ul>
<a name="SetInventoryFixed-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SetInventoryFixed</h4>
<pre>public&nbsp;int&nbsp;SetInventoryFixed(int&nbsp;freq)</pre>
</li>
</ul>
<a name="Requset_ReadPart-byte-byte-byte-byte-byte-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Requset_ReadPart</h4>
<pre>public&nbsp;int&nbsp;Requset_ReadPart(byte&nbsp;speed,
                            byte&nbsp;des,
                            byte&nbsp;flag,
                            byte&nbsp;partnum,
                            byte&nbsp;ptr,
                            byte&nbsp;len)</pre>
</li>
</ul>
<a name="TransData-byte:A-int-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TransData</h4>
<pre>public&nbsp;byte[]&nbsp;TransData(byte[]&nbsp;tbuf,
                        int&nbsp;tlen,
                        byte&nbsp;flag)</pre>
</li>
</ul>
<a name="Requset_WritePart-byte:A-byte-byte-byte-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Requset_WritePart</h4>
<pre>public&nbsp;int&nbsp;Requset_WritePart(byte[]&nbsp;tid,
                             byte&nbsp;num,
                             byte&nbsp;startaddr,
                             byte&nbsp;len,
                             byte[]&nbsp;buf)</pre>
</li>
</ul>
<a name="Request_WriteSpecialPart-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Request_WriteSpecialPart</h4>
<pre>public&nbsp;int&nbsp;Request_WriteSpecialPart(int&nbsp;len,
                                    byte[]&nbsp;buf)</pre>
</li>
</ul>
<a name="TransBuf-byte-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TransBuf</h4>
<pre>public&nbsp;byte[]&nbsp;TransBuf(byte&nbsp;oper,
                       int&nbsp;len,
                       byte[]&nbsp;jbuf)</pre>
</li>
</ul>
<a name="SetConfig-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SetConfig</h4>
<pre>public&nbsp;int&nbsp;SetConfig(byte[]&nbsp;jbuf)</pre>
</li>
</ul>
<a name="SetInventorySper--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SetInventorySper</h4>
<pre>public&nbsp;int&nbsp;SetInventorySper()</pre>
</li>
</ul>
<a name="EventReport--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EventReport</h4>
<pre>public&nbsp;int&nbsp;EventReport()</pre>
</li>
</ul>
<a name="POWER_LED_ON-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>POWER_LED_ON</h4>
<pre>public&nbsp;int&nbsp;POWER_LED_ON(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="POWER_LED_OFF-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>POWER_LED_OFF</h4>
<pre>public&nbsp;int&nbsp;POWER_LED_OFF(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="OTG_GPIO_OFF-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OTG_GPIO_OFF</h4>
<pre>public&nbsp;int&nbsp;OTG_GPIO_OFF(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="OTG_GPIO_ON-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OTG_GPIO_ON</h4>
<pre>public&nbsp;int&nbsp;OTG_GPIO_ON(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="PTInit-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTInit</h4>
<pre>public&nbsp;int&nbsp;PTInit(java.lang.String&nbsp;device,
                  java.lang.String&nbsp;uart)</pre>
</li>
</ul>
<a name="PTExit-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTExit</h4>
<pre>public&nbsp;int&nbsp;PTExit(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="PTEnroll-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTEnroll</h4>
<pre>public&nbsp;int&nbsp;PTEnroll(char&nbsp;index)</pre>
</li>
</ul>
<a name="PTVerifyALL-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTVerifyALL</h4>
<pre>public&nbsp;int&nbsp;PTVerifyALL(char&nbsp;index)</pre>
</li>
</ul>
<a name="PTVerify-char-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTVerify</h4>
<pre>public&nbsp;int&nbsp;PTVerify(char&nbsp;index,
                    char[]&nbsp;data,
                    int&nbsp;len)</pre>
</li>
</ul>
<a name="PTDeleteAllFingers-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTDeleteAllFingers</h4>
<pre>public&nbsp;int&nbsp;PTDeleteAllFingers(char&nbsp;index)</pre>
</li>
</ul>
<a name="PTStoreFinger-byte-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTStoreFinger</h4>
<pre>public&nbsp;int&nbsp;PTStoreFinger(byte&nbsp;iCounts,
                         byte[]&nbsp;templateData)</pre>
</li>
</ul>
<a name="PTGUICancel-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTGUICancel</h4>
<pre>public&nbsp;int&nbsp;PTGUICancel(char&nbsp;index)</pre>
</li>
</ul>
<a name="PTListAllFingers-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTListAllFingers</h4>
<pre>public&nbsp;byte[]&nbsp;PTListAllFingers(char&nbsp;index)</pre>
</li>
</ul>
<a name="PTResponseContinue-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTResponseContinue</h4>
<pre>public&nbsp;byte[]&nbsp;PTResponseContinue(char&nbsp;index)</pre>
</li>
</ul>
<a name="PTGrab-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTGrab</h4>
<pre>public&nbsp;int&nbsp;PTGrab(char&nbsp;index)</pre>
</li>
</ul>
<a name="PTTLSFContinue-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTTLSFContinue</h4>
<pre>public&nbsp;byte[]&nbsp;PTTLSFContinue(char&nbsp;index)</pre>
</li>
</ul>
<a name="PTInfo-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTInfo</h4>
<pre>public&nbsp;byte[]&nbsp;PTInfo(char&nbsp;index)</pre>
</li>
</ul>
<a name="PTCapture-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTCapture</h4>
<pre>public&nbsp;int&nbsp;PTCapture(char&nbsp;index)</pre>
</li>
</ul>
<a name="PTSetAppData-char-char:A-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTSetAppData</h4>
<pre>public&nbsp;int&nbsp;PTSetAppData(char&nbsp;index,
                        char[]&nbsp;data,
                        char&nbsp;len)</pre>
</li>
</ul>
<a name="PTGetAppData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTGetAppData</h4>
<pre>public&nbsp;byte[]&nbsp;PTGetAppData(char&nbsp;index)</pre>
</li>
</ul>
<a name="PTConvertTemplateEx-byte-byte-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PTConvertTemplateEx</h4>
<pre>public&nbsp;byte[]&nbsp;PTConvertTemplateEx(byte&nbsp;iCounts,
                                  byte&nbsp;jtarget_type,
                                  byte[]&nbsp;srcbuf,
                                  int&nbsp;jsrclen)</pre>
</li>
</ul>
<a name="UHFGetRFLink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetRFLink</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetRFLink()</pre>
</li>
</ul>
<a name="UHFSetRFLink-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetRFLink</h4>
<pre>public&nbsp;int&nbsp;UHFSetRFLink(char&nbsp;mode)</pre>
</li>
</ul>
<a name="UHFSetFastID-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetFastID</h4>
<pre>public&nbsp;int&nbsp;UHFSetFastID(char&nbsp;par)</pre>
</li>
</ul>
<a name="UHFSetTagFocus-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetTagFocus</h4>
<pre>public&nbsp;int&nbsp;UHFSetTagFocus(char&nbsp;par)</pre>
</li>
</ul>
<a name="UHFSetEPCTIDMode-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetEPCTIDMode</h4>
<pre>public&nbsp;int&nbsp;UHFSetEPCTIDMode(char&nbsp;par)</pre>
</li>
</ul>
<a name="UHFSetEPCUserMode-char-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetEPCUserMode</h4>
<pre>public&nbsp;int&nbsp;UHFSetEPCUserMode(char&nbsp;par,
                             int&nbsp;ptr,
                             int&nbsp;len,
                             int&nbsp;save)</pre>
</li>
</ul>
<a name="UHFGetEPCTIDUSERMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetEPCTIDUSERMode</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetEPCTIDUSERMode()</pre>
</li>
</ul>
<a name="UHFGetEPCTIDMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetEPCTIDMode</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetEPCTIDMode()</pre>
</li>
</ul>
<a name="UHFSetMode-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetMode</h4>
<pre>public&nbsp;int&nbsp;UHFSetMode(char&nbsp;mode)</pre>
</li>
</ul>
<a name="UHFGetMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetMode</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetMode()</pre>
</li>
</ul>
<a name="UHFInventoryBank-char:A-char-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventoryBank</h4>
<pre>public&nbsp;int&nbsp;UHFInventoryBank(char[]&nbsp;pszData,
                            char&nbsp;bank,
                            int&nbsp;ptr,
                            int&nbsp;len)</pre>
</li>
</ul>
<a name="GetTemperature--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GetTemperature</h4>
<pre>public&nbsp;char[]&nbsp;GetTemperature()</pre>
</li>
</ul>
<a name="CardBalance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CardBalance</h4>
<pre>public&nbsp;byte[]&nbsp;CardBalance()</pre>
</li>
</ul>
<a name="CardConsume-byte:A-char:A-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CardConsume</h4>
<pre>public&nbsp;byte[]&nbsp;CardConsume(byte[]&nbsp;time,
                          char[]&nbsp;money,
                          java.lang.String&nbsp;path,
                          int&nbsp;islog)</pre>
</li>
</ul>
<a name="getPsamId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPsamId</h4>
<pre>public&nbsp;byte[]&nbsp;getPsamId()</pre>
</li>
</ul>
<a name="CleanVar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CleanVar</h4>
<pre>public&nbsp;void&nbsp;CleanVar()</pre>
</li>
</ul>
<a name="CardConsumeConfirm-byte:A-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CardConsumeConfirm</h4>
<pre>public&nbsp;byte[]&nbsp;CardConsumeConfirm(byte[]&nbsp;timer,
                                 char[]&nbsp;carddealnum,
                                 char[]&nbsp;cardnum)</pre>
</li>
</ul>
<a name="ISO14443A_BUS_CMD-char:A-char:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_BUS_CMD</h4>
<pre>public&nbsp;char[]&nbsp;ISO14443A_BUS_CMD(char[]&nbsp;jtxtimebuf,
                                char[]&nbsp;jtxuidbuf,
                                int&nbsp;jtxuidnum,
                                int&nbsp;jtxflag)</pre>
</li>
</ul>
<a name="GetAccState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GetAccState</h4>
<pre>public&nbsp;void&nbsp;GetAccState()</pre>
</li>
</ul>
<a name="GetGen2--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GetGen2</h4>
<pre>public&nbsp;char[]&nbsp;GetGen2()</pre>
</li>
</ul>
<a name="SetGen2-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SetGen2</h4>
<pre>public&nbsp;int&nbsp;SetGen2(char&nbsp;Target,
                   char&nbsp;Action,
                   char&nbsp;T,
                   char&nbsp;Q,
                   char&nbsp;StartQ,
                   char&nbsp;MinQ,
                   char&nbsp;MaxQ,
                   char&nbsp;D,
                   char&nbsp;C,
                   char&nbsp;P,
                   char&nbsp;Sel,
                   char&nbsp;Session,
                   char&nbsp;G,
                   char&nbsp;LF)</pre>
</li>
</ul>
<a name="fips_encryption_decryption-byte:A-int-byte:A-int-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fips_encryption_decryption</h4>
<pre>public&nbsp;byte[]&nbsp;fips_encryption_decryption(byte[]&nbsp;jinbuf,
                                         int&nbsp;jdlen,
                                         byte[]&nbsp;jkeybuf,
                                         int&nbsp;keylen,
                                         byte&nbsp;jmode)</pre>
</li>
</ul>
<a name="fips_encryption_decryption_EX-byte:A-int-byte:A-int-byte-java.lang.Object-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fips_encryption_decryption_EX</h4>
<pre>public&nbsp;byte[]&nbsp;fips_encryption_decryption_EX(byte[]&nbsp;jinbuf,
                                            int&nbsp;jdlen,
                                            byte[]&nbsp;jkeybuf,
                                            int&nbsp;keylen,
                                            byte&nbsp;jmode,
                                            java.lang.Object&nbsp;mContext,
                                            int&nbsp;type)</pre>
</li>
</ul>
<a name="UHFJump2Boot--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFJump2Boot</h4>
<pre>public&nbsp;int&nbsp;UHFJump2Boot()</pre>
</li>
</ul>
<a name="UHFToBoot-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFToBoot</h4>
<pre>public&nbsp;int&nbsp;UHFToBoot(int&nbsp;type)</pre>
</li>
</ul>
<a name="UHFStartUpdate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStartUpdate</h4>
<pre>public&nbsp;int&nbsp;UHFStartUpdate()</pre>
</li>
</ul>
<a name="UHFUpdating-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFUpdating</h4>
<pre>public&nbsp;int&nbsp;UHFUpdating(byte[]&nbsp;buff,
                       int&nbsp;len)</pre>
</li>
</ul>
<a name="UHFStopUpdate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStopUpdate</h4>
<pre>public&nbsp;int&nbsp;UHFStopUpdate()</pre>
</li>
</ul>
<a name="MorphoCapturePKComp-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoCapturePKComp</h4>
<pre>public&nbsp;byte[]&nbsp;MorphoCapturePKComp(char&nbsp;processflag,
                                  char&nbsp;encryptflag)</pre>
</li>
</ul>
<a name="MorphoVerifyPKComp-char-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoVerifyPKComp</h4>
<pre>public&nbsp;byte[]&nbsp;MorphoVerifyPKComp(char&nbsp;processflag,
                                 byte[]&nbsp;jfgbuf,
                                 int&nbsp;jfglen)</pre>
</li>
</ul>
<a name="MorphoFingerInit-java.lang.String-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoFingerInit</h4>
<pre>public&nbsp;int&nbsp;MorphoFingerInit(java.lang.String&nbsp;device,
                            java.lang.String&nbsp;uart,
                            int&nbsp;baudrate)</pre>
</li>
</ul>
<a name="MorphoFingerFree-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoFingerFree</h4>
<pre>public&nbsp;int&nbsp;MorphoFingerFree(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="MorphoFingerMessage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoFingerMessage</h4>
<pre>public&nbsp;int&nbsp;MorphoFingerMessage()</pre>
</li>
</ul>
<a name="MorphoGrab-char-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoGrab</h4>
<pre>public&nbsp;int&nbsp;MorphoGrab(char&nbsp;var1,
                      java.lang.String&nbsp;imgName)</pre>
</li>
</ul>
<a name="MorphoEnroll-char-char:A-char:A-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoEnroll</h4>
<pre>public&nbsp;int&nbsp;MorphoEnroll(char&nbsp;flag,
                        char[]&nbsp;id,
                        char[]&nbsp;name,
                        java.lang.String&nbsp;lpFileName,
                        int&nbsp;getImage)</pre>
</li>
</ul>
<a name="MorphoLoadKs-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoLoadKs</h4>
<pre>public&nbsp;int&nbsp;MorphoLoadKs(byte[]&nbsp;keybuf)</pre>
</li>
</ul>
<a name="MorphoCapture-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoCapture</h4>
<pre>public&nbsp;byte[]&nbsp;MorphoCapture(char&nbsp;flag,
                            char&nbsp;encryptflag)</pre>
</li>
</ul>
<a name="MorphoIdentify-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoIdentify</h4>
<pre>public&nbsp;byte[]&nbsp;MorphoIdentify(char&nbsp;flag)</pre>
</li>
</ul>
<a name="MorphoCancel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoCancel</h4>
<pre>public&nbsp;int&nbsp;MorphoCancel()</pre>
</li>
</ul>
<a name="MorphoEraseAllBase--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoEraseAllBase</h4>
<pre>public&nbsp;int&nbsp;MorphoEraseAllBase()</pre>
</li>
</ul>
<a name="MorphoGetSecurityLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoGetSecurityLevel</h4>
<pre>public&nbsp;int&nbsp;MorphoGetSecurityLevel()</pre>
</li>
</ul>
<a name="MorphoSetSecurityLevel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoSetSecurityLevel</h4>
<pre>public&nbsp;int&nbsp;MorphoSetSecurityLevel(int&nbsp;level)</pre>
</li>
</ul>
<a name="MorphoDescriptor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoDescriptor</h4>
<pre>public&nbsp;byte[]&nbsp;MorphoDescriptor()</pre>
</li>
</ul>
<a name="MorphoPIDSN--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoPIDSN</h4>
<pre>public&nbsp;byte[]&nbsp;MorphoPIDSN()</pre>
</li>
</ul>
<a name="MorphoStop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MorphoStop</h4>
<pre>public&nbsp;int&nbsp;MorphoStop()</pre>
</li>
</ul>
<a name="UHFDeactivate-int-char:A-char-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFDeactivate</h4>
<pre>public&nbsp;int&nbsp;UHFDeactivate(int&nbsp;cmd,
                         char[]&nbsp;pszuAccessPwd,
                         char&nbsp;uBank,
                         int&nbsp;uPtr,
                         int&nbsp;cnt,
                         char[]&nbsp;pszuUii)</pre>
</li>
</ul>
<a name="UHFDwell-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFDwell</h4>
<pre>public&nbsp;int&nbsp;UHFDwell(int&nbsp;dwell,
                    int&nbsp;count)</pre>
</li>
</ul>
<a name="ModuleSendAndReceive-byte:A-int-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ModuleSendAndReceive</h4>
<pre>public&nbsp;int&nbsp;ModuleSendAndReceive(byte[]&nbsp;send,
                                int&nbsp;sendLen,
                                byte[]&nbsp;outData,
                                int&nbsp;outLen)</pre>
</li>
</ul>
<a name="UHFReadData_Ex2-char:A-char-int-int-char:A-char-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFReadData_Ex2</h4>
<pre>public&nbsp;char[]&nbsp;UHFReadData_Ex2(char[]&nbsp;pszuAccessPwd,
                              char&nbsp;ufBank,
                              int&nbsp;ufPtr,
                              int&nbsp;ufCnt,
                              char[]&nbsp;ufData,
                              char&nbsp;uBank,
                              int&nbsp;uPtr,
                              int&nbsp;uCnt)</pre>
</li>
</ul>
<a name="UHFWriteData_Ex2-char:A-char-int-int-char:A-char-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFWriteData_Ex2</h4>
<pre>public&nbsp;int&nbsp;UHFWriteData_Ex2(char[]&nbsp;pszuAccessPwd,
                            char&nbsp;ufBank,
                            int&nbsp;ufPtr,
                            int&nbsp;ufCnt,
                            char[]&nbsp;pszuDat,
                            char&nbsp;uBank,
                            int&nbsp;uPtr,
                            int&nbsp;uCnt,
                            char[]&nbsp;pszuWriteData)</pre>
</li>
</ul>
<a name="UHFBlockWriteData-char:A-char-int-int-char:A-char-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBlockWriteData</h4>
<pre>public&nbsp;int&nbsp;UHFBlockWriteData(char[]&nbsp;pszuAccessPwd,
                             char&nbsp;ufBank,
                             int&nbsp;ufPtr,
                             int&nbsp;ufCnt,
                             char[]&nbsp;pszuDat,
                             char&nbsp;uBank,
                             int&nbsp;uPtr,
                             int&nbsp;uCnt,
                             char[]&nbsp;pszuWriteData)</pre>
</li>
</ul>
<a name="UHFSetFilter_Ex-char-char-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetFilter_Ex</h4>
<pre>public&nbsp;int&nbsp;UHFSetFilter_Ex(char&nbsp;flagstore,
                           char&nbsp;bank,
                           int&nbsp;ptr,
                           int&nbsp;len,
                           char[]&nbsp;pszData)</pre>
</li>
</ul>
<a name="UHFLockMemEx-char:A-char-int-int-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFLockMemEx</h4>
<pre>public&nbsp;int&nbsp;UHFLockMemEx(char[]&nbsp;pszuAccessPwd,
                        char&nbsp;bank,
                        int&nbsp;ptr,
                        int&nbsp;cnt,
                        char[]&nbsp;pszuData,
                        char[]&nbsp;pszuLockData)</pre>
</li>
</ul>
<a name="PrinterInit-java.lang.String-java.lang.String-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PrinterInit</h4>
<pre>public&nbsp;int&nbsp;PrinterInit(java.lang.String&nbsp;device,
                       java.lang.String&nbsp;path,
                       int&nbsp;baudrate,
                       int&nbsp;module,
                       int&nbsp;isUpgrade)</pre>
</li>
</ul>
<a name="PrinterSerialPortOpen-java.lang.String-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PrinterSerialPortOpen</h4>
<pre>public&nbsp;int&nbsp;PrinterSerialPortOpen(java.lang.String&nbsp;device,
                                 java.lang.String&nbsp;path,
                                 int&nbsp;baudrate)</pre>
</li>
</ul>
<a name="PrinterFree-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PrinterFree</h4>
<pre>public&nbsp;int&nbsp;PrinterFree(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="PrinterSend-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PrinterSend</h4>
<pre>public&nbsp;int&nbsp;PrinterSend(byte[]&nbsp;jsend,
                       int&nbsp;jsendLeng)</pre>
</li>
</ul>
<a name="PrinterReceive-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PrinterReceive</h4>
<pre>public&nbsp;int&nbsp;PrinterReceive(byte[]&nbsp;joutData,
                          int&nbsp;jsendLeng)</pre>
</li>
</ul>
<a name="PrinterSendAndReceive-byte:A-int-byte:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PrinterSendAndReceive</h4>
<pre>public&nbsp;int&nbsp;PrinterSendAndReceive(byte[]&nbsp;sendjdata,
                                 int&nbsp;sendjlen,
                                 byte[]&nbsp;outjdata,
                                 int&nbsp;outjlen,
                                 int&nbsp;delayS)</pre>
</li>
</ul>
<a name="UHFInventoryBID-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventoryBID</h4>
<pre>public&nbsp;int&nbsp;UHFInventoryBID(char&nbsp;flag0,
                           char&nbsp;flag1)</pre>
</li>
</ul>
<a name="UHFInventorySingle_tc-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventorySingle_tc</h4>
<pre>public&nbsp;char[]&nbsp;UHFInventorySingle_tc(char&nbsp;mode)</pre>
</li>
</ul>
<a name="UHFGetBID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetBID</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetBID()</pre>
</li>
</ul>
<a name="UHFWriteQTData_Ex-char:A-char-int-int-char:A-char-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFWriteQTData_Ex</h4>
<pre>public&nbsp;int&nbsp;UHFWriteQTData_Ex(char[]&nbsp;pszuAccessPwd,
                             char&nbsp;ufBank,
                             int&nbsp;ufPtr,
                             int&nbsp;ufCnt,
                             char[]&nbsp;pszuDat,
                             char&nbsp;uBank,
                             int&nbsp;uPtr,
                             int&nbsp;uCnt,
                             char[]&nbsp;pszuWriteData)</pre>
</li>
</ul>
<a name="UHFReadQTData_Ex-char:A-char-int-int-char:A-char-int-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFReadQTData_Ex</h4>
<pre>public&nbsp;char[]&nbsp;UHFReadQTData_Ex(char[]&nbsp;pszuAccessPwd,
                               char&nbsp;ufBank,
                               int&nbsp;ufPtr,
                               int&nbsp;ufCnt,
                               char[]&nbsp;ufData,
                               char&nbsp;uBank,
                               int&nbsp;uPtr,
                               char&nbsp;uCnt)</pre>
</li>
</ul>
<a name="UHFSetCW-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetCW</h4>
<pre>public&nbsp;int&nbsp;UHFSetCW(char&nbsp;pszData)</pre>
</li>
</ul>
<a name="UHFGetCW--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetCW</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetCW()</pre>
</li>
</ul>
<a name="UHFBTSetProtocolType-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTSetProtocolType</h4>
<pre>public&nbsp;int&nbsp;UHFBTSetProtocolType(int&nbsp;type)</pre>
</li>
</ul>
<a name="UHFBTUHFGetProtocolType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTUHFGetProtocolType</h4>
<pre>public&nbsp;int&nbsp;UHFBTUHFGetProtocolType()</pre>
</li>
</ul>
<a name="UHFBTUHFGBTagLock-char:A-char-int-int-char:A-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTUHFGBTagLock</h4>
<pre>public&nbsp;int&nbsp;UHFBTUHFGBTagLock(char[]&nbsp;pszuAccessPwd,
                             char&nbsp;bank,
                             int&nbsp;ptr,
                             int&nbsp;cnt,
                             char[]&nbsp;pszuData,
                             int&nbsp;memory,
                             int&nbsp;config,
                             int&nbsp;action)</pre>
</li>
</ul>
<a name="UHFBTGetPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetPower</h4>
<pre>public&nbsp;int&nbsp;UHFBTGetPower()</pre>
</li>
</ul>
<a name="UHFBTSetPower-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTSetPower</h4>
<pre>public&nbsp;int&nbsp;UHFBTSetPower(byte&nbsp;power)</pre>
</li>
</ul>
<a name="UHFGetBTFrequency--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetBTFrequency</h4>
<pre>public&nbsp;int&nbsp;UHFGetBTFrequency()</pre>
</li>
</ul>
<a name="UHFBTFreHopSet-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTFreHopSet</h4>
<pre>public&nbsp;int&nbsp;UHFBTFreHopSet(int&nbsp;fre)</pre>
</li>
</ul>
<a name="UHFSetBTFrequency-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetBTFrequency</h4>
<pre>public&nbsp;int&nbsp;UHFSetBTFrequency(byte&nbsp;FreMode)</pre>
</li>
</ul>
<a name="UHFBTR2000Version-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTR2000Version</h4>
<pre>public&nbsp;int&nbsp;UHFBTR2000Version(byte[]&nbsp;outVersion)</pre>
</li>
</ul>
<a name="UHFBTSTM32Version-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTSTM32Version</h4>
<pre>public&nbsp;int&nbsp;UHFBTSTM32Version(byte[]&nbsp;outVersion)</pre>
</li>
</ul>
<a name="UHFBTStartInventory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTStartInventory</h4>
<pre>public&nbsp;int&nbsp;UHFBTStartInventory()</pre>
</li>
</ul>
<a name="UHFBTStopInventory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTStopInventory</h4>
<pre>public&nbsp;int&nbsp;UHFBTStopInventory()</pre>
</li>
</ul>
<a name="UHFBTGetTag-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetTag</h4>
<pre>public&nbsp;int&nbsp;UHFBTGetTag(byte[]&nbsp;data,
                       int&nbsp;timeOut)</pre>
</li>
</ul>
<a name="UHFBTGetSendCmd-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetSendCmd</h4>
<pre>public&nbsp;int&nbsp;UHFBTGetSendCmd(byte[]&nbsp;cmd)</pre>
</li>
</ul>
<a name="UHFBTReadData-char:A-char-int-int-char:A-char-int-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTReadData</h4>
<pre>public&nbsp;char[]&nbsp;UHFBTReadData(char[]&nbsp;pszuAccessPwd,
                            char&nbsp;ufBank,
                            int&nbsp;ufPtr,
                            int&nbsp;ufCnt,
                            char[]&nbsp;ufData,
                            char&nbsp;uBank,
                            int&nbsp;uPtr,
                            char&nbsp;uCnt)</pre>
</li>
</ul>
<a name="UHFBTWriteData-char:A-char-int-int-char:A-char-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTWriteData</h4>
<pre>public&nbsp;int&nbsp;UHFBTWriteData(char[]&nbsp;pszuAccessPwd,
                          char&nbsp;ufBank,
                          int&nbsp;ufPtr,
                          int&nbsp;ufCnt,
                          char[]&nbsp;pszuDat,
                          char&nbsp;uBank,
                          int&nbsp;uPtr,
                          int&nbsp;uCnt,
                          char[]&nbsp;pszuWriteData)</pre>
</li>
</ul>
<a name="UHFBTAuthentication-char:A-char-int-int-char:A-char-int-int-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTAuthentication</h4>
<pre>public&nbsp;int&nbsp;UHFBTAuthentication(char[]&nbsp;pszuAccessPwd,
                               char&nbsp;ufBank,
                               int&nbsp;ufPtr,
                               int&nbsp;ufCnt,
                               char[]&nbsp;pszuDat,
                               char&nbsp;uBank,
                               int&nbsp;uPtr,
                               int&nbsp;uCnt,
                               char[]&nbsp;pszuWriteData,
                               char[]&nbsp;rev)</pre>
</li>
</ul>
<a name="UHFBTLockMemEx-char:A-char-int-int-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTLockMemEx</h4>
<pre>public&nbsp;int&nbsp;UHFBTLockMemEx(char[]&nbsp;pszuAccessPwd,
                          char&nbsp;bank,
                          int&nbsp;ptr,
                          int&nbsp;cnt,
                          char[]&nbsp;pszuData,
                          char[]&nbsp;pszuLockData)</pre>
</li>
</ul>
<a name="UHFBTReBootAPP-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTReBootAPP</h4>
<pre>public&nbsp;int&nbsp;UHFBTReBootAPP(char&nbsp;flag)</pre>
</li>
</ul>
<a name="UHFBTStartUpdate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTStartUpdate</h4>
<pre>public&nbsp;int&nbsp;UHFBTStartUpdate()</pre>
</li>
</ul>
<a name="UHFBTUpdateData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTUpdateData</h4>
<pre>public&nbsp;int&nbsp;UHFBTUpdateData(byte[]&nbsp;pszuDate)</pre>
</li>
</ul>
<a name="UHFBTEndUpdate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTEndUpdate</h4>
<pre>public&nbsp;int&nbsp;UHFBTEndUpdate()</pre>
</li>
</ul>
<a name="UHFSETSM4-char-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSETSM4</h4>
<pre>public&nbsp;int&nbsp;UHFSETSM4(char&nbsp;mode,
                     char[]&nbsp;pszkeydata,
                     char[]&nbsp;pszlvdata)</pre>
</li>
</ul>
<a name="UHFGETSM4-byte:A-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGETSM4</h4>
<pre>public&nbsp;int&nbsp;UHFGETSM4(byte[]&nbsp;mode,
                     byte[]&nbsp;keydata,
                     byte[]&nbsp;lvdata)</pre>
</li>
</ul>
<a name="UHFDecryptSM4-char-char:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFDecryptSM4</h4>
<pre>public&nbsp;int&nbsp;UHFDecryptSM4(char&nbsp;datalen,
                         char[]&nbsp;pszdata,
                         byte[]&nbsp;outdata)</pre>
</li>
</ul>
<a name="UHFEncryptSM4-char-char:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFEncryptSM4</h4>
<pre>public&nbsp;int&nbsp;UHFEncryptSM4(char&nbsp;datalen,
                         char[]&nbsp;pszdata,
                         byte[]&nbsp;outdata)</pre>
</li>
</ul>
<a name="UHFBTKill-char:A-char-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTKill</h4>
<pre>public&nbsp;int&nbsp;UHFBTKill(char[]&nbsp;pszuAccessPwd,
                     char&nbsp;bank,
                     int&nbsp;ptr,
                     int&nbsp;cnt,
                     char[]&nbsp;pszuData)</pre>
</li>
</ul>
<a name="UHFBTGetBattery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetBattery</h4>
<pre>public&nbsp;int&nbsp;UHFBTGetBattery()</pre>
</li>
</ul>
<a name="UHFBTGetBarcode-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetBarcode</h4>
<pre>public&nbsp;int&nbsp;UHFBTGetBarcode(byte[]&nbsp;outdata)</pre>
</li>
</ul>
<a name="UHFBTEncWriteUser-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTEncWriteUser</h4>
<pre>public&nbsp;int&nbsp;UHFBTEncWriteUser(int&nbsp;jaddr,
                             int&nbsp;jlen,
                             byte[]&nbsp;jindata)</pre>
</li>
</ul>
<a name="UHFBTEncReadUser-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTEncReadUser</h4>
<pre>public&nbsp;int&nbsp;UHFBTEncReadUser(int&nbsp;jaddr,
                            int&nbsp;jlen,
                            byte[]&nbsp;jdata)</pre>
</li>
</ul>
<a name="ISO15693_GenericFunctionEx-char-char-char:A-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO15693_GenericFunctionEx</h4>
<pre>public&nbsp;char[]&nbsp;ISO15693_GenericFunctionEx(char&nbsp;jCommand,
                                         char&nbsp;jIcMfg,
                                         char[]&nbsp;jdatabuf,
                                         char&nbsp;jdatalen)</pre>
</li>
</ul>
<a name="EM4325SensorData-char-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EM4325SensorData</h4>
<pre>public&nbsp;byte[]&nbsp;EM4325SensorData(char&nbsp;uBank,
                               int&nbsp;uMSA,
                               int&nbsp;uMDL,
                               char[]&nbsp;pszuData)</pre>
</li>
</ul>
<a name="UHFBTKeydataMac-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTKeydataMac</h4>
<pre>public&nbsp;int&nbsp;UHFBTKeydataMac(byte[]&nbsp;jkeydata,
                           byte[]&nbsp;jmac)</pre>
</li>
</ul>
<a name="UHFSetSM4-char-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetSM4</h4>
<pre>public&nbsp;int&nbsp;UHFSetSM4(char&nbsp;mode,
                     byte[]&nbsp;jKeybuf,
                     byte[]&nbsp;jIVbuf)</pre>
</li>
</ul>
<a name="UHFGetSM4--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetSM4</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetSM4()</pre>
</li>
</ul>
<a name="UHFEncSM4-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFEncSM4</h4>
<pre>public&nbsp;byte[]&nbsp;UHFEncSM4(byte[]&nbsp;jDatabuf,
                        int&nbsp;jDatalen)</pre>
</li>
</ul>
<a name="UHFDecSM4-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFDecSM4</h4>
<pre>public&nbsp;byte[]&nbsp;UHFDecSM4(byte[]&nbsp;jDatabuf,
                        int&nbsp;jDatalen)</pre>
</li>
</ul>
<a name="UHFEncUSER-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFEncUSER</h4>
<pre>public&nbsp;int&nbsp;UHFEncUSER(int&nbsp;jaddr,
                      int&nbsp;jlen,
                      byte[]&nbsp;data)</pre>
</li>
</ul>
<a name="UHFDecUSER-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFDecUSER</h4>
<pre>public&nbsp;byte[]&nbsp;UHFDecUSER(int&nbsp;jaddr,
                         int&nbsp;jlen)</pre>
</li>
</ul>
<a name="UHFJump2BootSTM32--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFJump2BootSTM32</h4>
<pre>public&nbsp;int&nbsp;UHFJump2BootSTM32()</pre>
</li>
</ul>
<a name="UHFGetHwTypeM3--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetHwTypeM3</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetHwTypeM3()</pre>
</li>
</ul>
<a name="UHFKillTagEx-char:A-char-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFKillTagEx</h4>
<pre>public&nbsp;int&nbsp;UHFKillTagEx(char[]&nbsp;pszuAccessPwd,
                        char&nbsp;uBank,
                        int&nbsp;uPtr,
                        int&nbsp;uCnt,
                        char[]&nbsp;pszuUii)</pre>
</li>
</ul>
<a name="UHFGetTempProtectVal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetTempProtectVal</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetTempProtectVal()</pre>
</li>
</ul>
<a name="UHFSetTempProtectVal-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetTempProtectVal</h4>
<pre>public&nbsp;int&nbsp;UHFSetTempProtectVal(char&nbsp;temp)</pre>
</li>
</ul>
<a name="GetLastError--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GetLastError</h4>
<pre>public&nbsp;int&nbsp;GetLastError()</pre>
</li>
</ul>
<a name="UHFGetANT--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetANT</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetANT()</pre>
</li>
</ul>
<a name="UHFSetANT-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetANT</h4>
<pre>public&nbsp;int&nbsp;UHFSetANT(int&nbsp;saveFlag,
                     char[]&nbsp;temp)</pre>
</li>
</ul>
<a name="UHFLedOnOff-java.lang.String-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFLedOnOff</h4>
<pre>public&nbsp;int&nbsp;UHFLedOnOff(java.lang.String&nbsp;device,
                       int&nbsp;Id,
                       int&nbsp;on)</pre>
</li>
</ul>
<a name="UHFBlockPermalock_Ex-char:A-char-int-int-char:A-char-char-int-char-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBlockPermalock_Ex</h4>
<pre>public&nbsp;char[]&nbsp;UHFBlockPermalock_Ex(char[]&nbsp;uAccessPwd,
                                   char&nbsp;FilterBank,
                                   int&nbsp;FilterStartaddr,
                                   int&nbsp;FilterLen,
                                   char[]&nbsp;FilterData,
                                   char&nbsp;ReadLock,
                                   char&nbsp;uBank,
                                   int&nbsp;uPtr,
                                   char&nbsp;uRange,
                                   byte[]&nbsp;uMaskbuf)</pre>
</li>
</ul>
<a name="UHFBTR2000Temperature-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTR2000Temperature</h4>
<pre>public&nbsp;int&nbsp;UHFBTR2000Temperature(byte[]&nbsp;outTemper)</pre>
</li>
</ul>
<a name="UHFBTSetBeep-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTSetBeep</h4>
<pre>public&nbsp;int&nbsp;UHFBTSetBeep(byte&nbsp;flag)</pre>
</li>
</ul>
<a name="UHFBTInventorySingle-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTInventorySingle</h4>
<pre>public&nbsp;int&nbsp;UHFBTInventorySingle(byte[]&nbsp;outData)</pre>
</li>
</ul>
<a name="UHFBTSetEpcTidUserMode-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTSetEpcTidUserMode</h4>
<pre>public&nbsp;int&nbsp;UHFBTSetEpcTidUserMode(int&nbsp;saveFlag,
                                  int&nbsp;memory,
                                  int&nbsp;address,
                                  int&nbsp;length)</pre>
</li>
</ul>
<a name="UHFBTReadEpcTidUserMode-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTReadEpcTidUserMode</h4>
<pre>public&nbsp;int&nbsp;UHFBTReadEpcTidUserMode(int&nbsp;rev1,
                                   int&nbsp;rev2,
                                   byte[]&nbsp;outData)</pre>
</li>
</ul>
<a name="SpiInit-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SpiInit</h4>
<pre>public&nbsp;int&nbsp;SpiInit(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="SpiFree-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SpiFree</h4>
<pre>public&nbsp;int&nbsp;SpiFree(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="SpiWrite-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SpiWrite</h4>
<pre>public&nbsp;int&nbsp;SpiWrite(byte[]&nbsp;sendData,
                    int&nbsp;len)</pre>
</li>
</ul>
<a name="SpiRead-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SpiRead</h4>
<pre>public&nbsp;int&nbsp;SpiRead(byte[]&nbsp;OutData,
                   int&nbsp;len)</pre>
</li>
</ul>
<a name="UHFBTGetAllTagNumFromFlash--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetAllTagNumFromFlash</h4>
<pre>public&nbsp;int&nbsp;UHFBTGetAllTagNumFromFlash()</pre>
</li>
</ul>
<a name="UHFBTDeleteAllTagToFlash--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTDeleteAllTagToFlash</h4>
<pre>public&nbsp;int&nbsp;UHFBTDeleteAllTagToFlash()</pre>
</li>
</ul>
<a name="UHFBTGetTagDataFromFlash-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetTagDataFromFlash</h4>
<pre>public&nbsp;int&nbsp;UHFBTGetTagDataFromFlash(byte[]&nbsp;outData)</pre>
</li>
</ul>
<a name="UHFBTSetCW-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTSetCW</h4>
<pre>public&nbsp;int&nbsp;UHFBTSetCW(int&nbsp;flag)</pre>
</li>
</ul>
<a name="UHFBTGetCW--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetCW</h4>
<pre>public&nbsp;int&nbsp;UHFBTGetCW()</pre>
</li>
</ul>
<a name="EMFingerMoudleSet-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EMFingerMoudleSet</h4>
<pre>public&nbsp;int&nbsp;EMFingerMoudleSet(int&nbsp;type)</pre>
</li>
</ul>
<a name="UHFBTEraseData-char:A-char-int-char-char:A-char-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTEraseData</h4>
<pre>public&nbsp;int&nbsp;UHFBTEraseData(char[]&nbsp;pszuAccessPwd,
                          char&nbsp;filterBank,
                          int&nbsp;filterPtr,
                          char&nbsp;filterCnt,
                          char[]&nbsp;filterData,
                          char&nbsp;uBank,
                          int&nbsp;uPtr,
                          int&nbsp;uCnt)</pre>
</li>
</ul>
<a name="UHFBTSetR6Workmode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTSetR6Workmode</h4>
<pre>public&nbsp;int&nbsp;UHFBTSetR6Workmode(int&nbsp;mode)</pre>
</li>
</ul>
<a name="UHFBTSetBeepRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTSetBeepRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFBTSetBeepRecvData(byte[]&nbsp;inData,
                                int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFBTGetPowerValueRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetPowerValueRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFBTGetPowerValueRecvData(byte[]&nbsp;inData,
                                      int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFBTOpen2DRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTOpen2DRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFBTOpen2DRecvData(byte[]&nbsp;inData,
                                  int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFSetPowerSendData-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetPowerSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetPowerSendData(char&nbsp;saveflag,
                                  char&nbsp;uPower)</pre>
</li>
</ul>
<a name="UHFSetPowerRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetPowerRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetPowerRecvData(byte[]&nbsp;inData,
                               int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetPowerSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetPowerSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetPowerSendData()</pre>
</li>
</ul>
<a name="UHFGetPowerRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetPowerRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetPowerRecvData(byte[]&nbsp;inData,
                                  int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFInventorySingleSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventorySingleSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFInventorySingleSendData()</pre>
</li>
</ul>
<a name="UHFInventorySingleRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventorySingleRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFInventorySingleRecvData(byte[]&nbsp;inData,
                                         int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFInventorySendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventorySendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFInventorySendData()</pre>
</li>
</ul>
<a name="UHFInventoryRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventoryRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFInventoryRecvData(byte[]&nbsp;inData,
                                   int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFStopInventorySendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStopInventorySendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFStopInventorySendData()</pre>
</li>
</ul>
<a name="UHFStopInventoryRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStopInventoryRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFStopInventoryRecvData(byte[]&nbsp;inData,
                                    int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetTagsDataSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetTagsDataSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetTagsDataSendData()</pre>
</li>
</ul>
<a name="UHFGetTagsDataRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetTagsDataRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetTagsDataRecvData(byte[]&nbsp;inData,
                                     int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFReadDataSendData-byte:A-char-int-int-byte:A-char-int-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFReadDataSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFReadDataSendData(byte[]&nbsp;pszuAccessPwd,
                                  char&nbsp;ufBank,
                                  int&nbsp;ufPtr,
                                  int&nbsp;ufCnt,
                                  byte[]&nbsp;ufData,
                                  char&nbsp;uBank,
                                  int&nbsp;uPtr,
                                  char&nbsp;uCnt)</pre>
</li>
</ul>
<a name="UHFReadDataRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFReadDataRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFReadDataRecvData(byte[]&nbsp;inData,
                                  int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFWriteDataSendData-byte:A-char-int-int-byte:A-char-int-char-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFWriteDataSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFWriteDataSendData(byte[]&nbsp;pszuAccessPwd,
                                   char&nbsp;ufBank,
                                   int&nbsp;ufPtr,
                                   int&nbsp;ufCnt,
                                   byte[]&nbsp;ufData,
                                   char&nbsp;uBank,
                                   int&nbsp;uPtr,
                                   char&nbsp;uCnt,
                                   byte[]&nbsp;writeDatabuf)</pre>
</li>
</ul>
<a name="UHFWriteDataRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFWriteDataRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFWriteDataRecvData(byte[]&nbsp;inData,
                                int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFLockTagSendData-byte:A-char-int-int-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFLockTagSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFLockTagSendData(byte[]&nbsp;pszuAccessPwd,
                                 char&nbsp;ufBank,
                                 int&nbsp;ufPtr,
                                 int&nbsp;ufCnt,
                                 byte[]&nbsp;ufData,
                                 byte[]&nbsp;ulockbuf)</pre>
</li>
</ul>
<a name="UHFLockTagRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFLockTagRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFLockTagRecvData(byte[]&nbsp;inData,
                              int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFKillTagSendData-byte:A-char-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFKillTagSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFKillTagSendData(byte[]&nbsp;pszuAccessPwd,
                                 char&nbsp;ufBank,
                                 int&nbsp;ufPtr,
                                 int&nbsp;ufCnt,
                                 byte[]&nbsp;ufData)</pre>
</li>
</ul>
<a name="UHFKillTagRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFKillTagRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFKillTagRecvData(byte[]&nbsp;inData,
                              int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFSetRegionSendData-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetRegionSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetRegionSendData(char&nbsp;saveflag,
                                   char&nbsp;region)</pre>
</li>
</ul>
<a name="UHFSetRegionRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetRegionRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetRegionRecvData(byte[]&nbsp;inData,
                                int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetRegionSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetRegionSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetRegionSendData()</pre>
</li>
</ul>
<a name="UHFGetRegionRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetRegionRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetRegionRecvData(byte[]&nbsp;inData,
                                   int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetSoftwareVersionSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetSoftwareVersionSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetSoftwareVersionSendData()</pre>
</li>
</ul>
<a name="UHFGetSoftwareVersionRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetSoftwareVersionRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetSoftwareVersionRecvData(byte[]&nbsp;inData,
                                            int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetTemperatureSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetTemperatureSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetTemperatureSendData()</pre>
</li>
</ul>
<a name="UHFGetTemperatureRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetTemperatureRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetTemperatureRecvData(byte[]&nbsp;inData,
                                        int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFUSBGetTagsDataRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFUSBGetTagsDataRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFUSBGetTagsDataRecvData(byte[]&nbsp;inData,
                                        int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFSetJumpFrequencySendData-char-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetJumpFrequencySendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetJumpFrequencySendData(char&nbsp;nums,
                                          int[]&nbsp;Freqbuf)</pre>
</li>
</ul>
<a name="UHFSetJumpFrequencyRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetJumpFrequencyRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetJumpFrequencyRecvData(byte[]&nbsp;inData,
                                       int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFBTDeleteAllTagToFlashSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTDeleteAllTagToFlashSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFBTDeleteAllTagToFlashSendData()</pre>
</li>
</ul>
<a name="UHFBTDeleteAllTagToFlashRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTDeleteAllTagToFlashRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFBTDeleteAllTagToFlashRecvData(byte[]&nbsp;inData,
                                            int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFBTGetAllTagNumFromFlashSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetAllTagNumFromFlashSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFBTGetAllTagNumFromFlashSendData()</pre>
</li>
</ul>
<a name="UHFBTGetAllTagNumFromFlashRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetAllTagNumFromFlashRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFBTGetAllTagNumFromFlashRecvData(byte[]&nbsp;inData,
                                              int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFBTGetTagDataFromFlashSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetTagDataFromFlashSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFBTGetTagDataFromFlashSendData()</pre>
</li>
</ul>
<a name="UHFBTGetTagDataFromFlashRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetTagDataFromFlashRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFBTGetTagDataFromFlashRecvData(byte[]&nbsp;inData,
                                               int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFSetCWSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetCWSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetCWSendData(char&nbsp;flag)</pre>
</li>
</ul>
<a name="UHFSetCWRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetCWRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetCWRecvData(byte[]&nbsp;inData,
                            int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetCWSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetCWSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetCWSendData()</pre>
</li>
</ul>
<a name="UHFGetCWRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetCWRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetCWRecvData(byte[]&nbsp;inData,
                               int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFSetFilterSendData-char-char-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetFilterSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetFilterSendData(char&nbsp;saveflag,
                                   char&nbsp;ufBank,
                                   int&nbsp;ufPtr,
                                   int&nbsp;datalen,
                                   byte[]&nbsp;databuf)</pre>
</li>
</ul>
<a name="UHFSetFilterRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetFilterRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetFilterRecvData(byte[]&nbsp;inData,
                                int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGBTagLockSendData-byte:A-char-int-int-byte:A-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGBTagLockSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGBTagLockSendData(byte[]&nbsp;pszuAccessPwd,
                                   char&nbsp;ufBank,
                                   int&nbsp;ufPtr,
                                   int&nbsp;ufCnt,
                                   byte[]&nbsp;ufData,
                                   char&nbsp;jmemory,
                                   char&nbsp;jconfig,
                                   char&nbsp;jaction)</pre>
</li>
</ul>
<a name="UHFGBTagLockRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGBTagLockRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFGBTagLockRecvData(byte[]&nbsp;inData,
                                int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFBlockWriteDataSendData-byte:A-char-int-int-byte:A-char-int-char-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBlockWriteDataSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFBlockWriteDataSendData(byte[]&nbsp;pszuAccessPwd,
                                        char&nbsp;ufBank,
                                        int&nbsp;ufPtr,
                                        int&nbsp;ufCnt,
                                        byte[]&nbsp;ufData,
                                        char&nbsp;uBank,
                                        int&nbsp;uPtr,
                                        char&nbsp;uCnt,
                                        byte[]&nbsp;writeDatabuf)</pre>
</li>
</ul>
<a name="UHFBlockWriteDataRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBlockWriteDataRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFBlockWriteDataRecvData(byte[]&nbsp;inData,
                                     int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFBlockEraseDataSendData-byte:A-char-int-int-byte:A-char-int-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBlockEraseDataSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFBlockEraseDataSendData(byte[]&nbsp;pszuAccessPwd,
                                        char&nbsp;ufBank,
                                        int&nbsp;ufPtr,
                                        int&nbsp;ufCnt,
                                        byte[]&nbsp;ufData,
                                        char&nbsp;uBank,
                                        int&nbsp;uPtr,
                                        char&nbsp;uCnt)</pre>
</li>
</ul>
<a name="UHFBlockEraseDataRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBlockEraseDataRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFBlockEraseDataRecvData(byte[]&nbsp;inData,
                                     int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFSetProtocolTypeSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetProtocolTypeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetProtocolTypeSendData(char&nbsp;type)</pre>
</li>
</ul>
<a name="UHFSetProtocolTypeRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetProtocolTypeRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetProtocolTypeRecvData(byte[]&nbsp;inData,
                                      int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetProtocolTypeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetProtocolTypeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetProtocolTypeSendData()</pre>
</li>
</ul>
<a name="UHFGetProtocolTypeRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetProtocolTypeRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetProtocolTypeRecvData(byte[]&nbsp;inData,
                                         int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFSetGen2SendData-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetGen2SendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetGen2SendData(char&nbsp;Target,
                                 char&nbsp;Action,
                                 char&nbsp;T,
                                 char&nbsp;Q_Q,
                                 char&nbsp;StartQ,
                                 char&nbsp;MinQ,
                                 char&nbsp;MaxQ,
                                 char&nbsp;D_D,
                                 char&nbsp;C_C,
                                 char&nbsp;P_P,
                                 char&nbsp;Sel,
                                 char&nbsp;Session,
                                 char&nbsp;G_G,
                                 char&nbsp;LF)</pre>
</li>
</ul>
<a name="UHFSetGen2RecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetGen2RecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetGen2RecvData(byte[]&nbsp;inData,
                              int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetGen2SendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetGen2SendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetGen2SendData()</pre>
</li>
</ul>
<a name="UHFGetGen2RecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetGen2RecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetGen2RecvData(byte[]&nbsp;inData,
                                 int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFSetEPCTIDModeSendData-char-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetEPCTIDModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetEPCTIDModeSendData(char&nbsp;saveFlag,
                                       char&nbsp;memory,
                                       char&nbsp;address,
                                       char&nbsp;length)</pre>
</li>
</ul>
<a name="UHFSetEPCTIDModeRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetEPCTIDModeRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetEPCTIDModeRecvData(byte[]&nbsp;inData,
                                    int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHTSetR6WorkmodeSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHTSetR6WorkmodeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHTSetR6WorkmodeSendData(char&nbsp;flag)</pre>
</li>
</ul>
<a name="UHTSetR6WorkmodeRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHTSetR6WorkmodeRecvData</h4>
<pre>public&nbsp;int&nbsp;UHTSetR6WorkmodeRecvData(byte[]&nbsp;inData,
                                    int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFJump2BootSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFJump2BootSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFJump2BootSendData(char&nbsp;flag)</pre>
</li>
</ul>
<a name="UHFJump2BootRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFJump2BootRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFJump2BootRecvData(byte[]&nbsp;inData,
                                int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFStartUpdateSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStartUpdateSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFStartUpdateSendData()</pre>
</li>
</ul>
<a name="UHFStartUpdateRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStartUpdateRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFStartUpdateRecvData(byte[]&nbsp;inData,
                                  int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFUpdatingSendData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFUpdatingSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFUpdatingSendData(byte[]&nbsp;buf)</pre>
</li>
</ul>
<a name="UHFUpdatingRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFUpdatingRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFUpdatingRecvData(byte[]&nbsp;inData,
                               int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFStopUpdateSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStopUpdateSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFStopUpdateSendData()</pre>
</li>
</ul>
<a name="UHFStopUpdateRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStopUpdateRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFStopUpdateRecvData(byte[]&nbsp;inData,
                                 int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetEPCTIDModeSendData-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetEPCTIDModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetEPCTIDModeSendData(char&nbsp;rev1,
                                       char&nbsp;rev2)</pre>
</li>
</ul>
<a name="UHFGetEPCTIDModeRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetEPCTIDModeRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetEPCTIDModeRecvData(byte[]&nbsp;inData,
                                       int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetSTM32VersionSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetSTM32VersionSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetSTM32VersionSendData()</pre>
</li>
</ul>
<a name="UHFGetSTM32VersionRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetSTM32VersionRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetSTM32VersionRecvData(byte[]&nbsp;inData,
                                         int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFBTSetGen2-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTSetGen2</h4>
<pre>public&nbsp;int&nbsp;UHFBTSetGen2(char&nbsp;Target,
                        char&nbsp;Action,
                        char&nbsp;T,
                        char&nbsp;Q,
                        char&nbsp;StartQ,
                        char&nbsp;MinQ,
                        char&nbsp;MaxQ,
                        char&nbsp;D,
                        char&nbsp;C,
                        char&nbsp;P,
                        char&nbsp;Sel,
                        char&nbsp;Session,
                        char&nbsp;G,
                        char&nbsp;LF)</pre>
</li>
</ul>
<a name="UHFBTGetGen2-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFBTGetGen2</h4>
<pre>public&nbsp;int&nbsp;UHFBTGetGen2(byte[]&nbsp;outData)</pre>
</li>
</ul>
<a name="UsbToHost-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UsbToHost</h4>
<pre>public&nbsp;int&nbsp;UsbToHost(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="UsbToFingerprint-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UsbToFingerprint</h4>
<pre>public&nbsp;int&nbsp;UsbToFingerprint(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="FingerprintSwitchUsb-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FingerprintSwitchUsb</h4>
<pre>public&nbsp;int&nbsp;FingerprintSwitchUsb(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="FingerprintSwitchUart-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FingerprintSwitchUart</h4>
<pre>public&nbsp;int&nbsp;FingerprintSwitchUart(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="A8UhfOutput4Off-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A8UhfOutput4Off</h4>
<pre>public&nbsp;int&nbsp;A8UhfOutput4Off(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="A8UhfOutput4On-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A8UhfOutput4On</h4>
<pre>public&nbsp;int&nbsp;A8UhfOutput4On(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="A8UhfOutput3Off-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A8UhfOutput3Off</h4>
<pre>public&nbsp;int&nbsp;A8UhfOutput3Off(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="A8UhfOutput3On-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A8UhfOutput3On</h4>
<pre>public&nbsp;int&nbsp;A8UhfOutput3On(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="DAJFingerInit-java.lang.String-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJFingerInit</h4>
<pre>public&nbsp;int&nbsp;DAJFingerInit(java.lang.String&nbsp;device,
                         java.lang.String&nbsp;uart,
                         int&nbsp;baudrate)</pre>
</li>
</ul>
<a name="DAJFingerFree-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJFingerFree</h4>
<pre>public&nbsp;int&nbsp;DAJFingerFree(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="DAJfingerGETInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerGETInfo</h4>
<pre>public&nbsp;char[]&nbsp;DAJfingerGETInfo()</pre>
</li>
</ul>
<a name="DAJfingerGETImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerGETImage</h4>
<pre>public&nbsp;int&nbsp;DAJfingerGETImage()</pre>
</li>
</ul>
<a name="DAJfingerUPImageProgress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerUPImageProgress</h4>
<pre>public&nbsp;int&nbsp;DAJfingerUPImageProgress()</pre>
</li>
</ul>
<a name="DAJfingerCancelUPImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerCancelUPImage</h4>
<pre>public&nbsp;void&nbsp;DAJfingerCancelUPImage()</pre>
</li>
</ul>
<a name="DAJfingerUPImage-byte-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerUPImage</h4>
<pre>public&nbsp;int&nbsp;DAJfingerUPImage(byte&nbsp;part,
                            byte[]&nbsp;img_buf)</pre>
</li>
</ul>
<a name="DAJfingerStoreChar-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerStoreChar</h4>
<pre>public&nbsp;int&nbsp;DAJfingerStoreChar(char&nbsp;flag,
                              char[]&nbsp;pBufferIDFlashPageID)</pre>
</li>
</ul>
<a name="DAJfingerUPTemplate-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerUPTemplate</h4>
<pre>public&nbsp;char[]&nbsp;DAJfingerUPTemplate(char&nbsp;flag,
                                  char[]&nbsp;pBufferIDFlashPageID)</pre>
</li>
</ul>
<a name="DAJfingerDOWNTemplate-char-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerDOWNTemplate</h4>
<pre>public&nbsp;int&nbsp;DAJfingerDOWNTemplate(char&nbsp;flag,
                                 char[]&nbsp;pBufferIDFlashPageID,
                                 char[]&nbsp;templateData)</pre>
</li>
</ul>
<a name="DAJfingerPKTemplate-char-char:A-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerPKTemplate</h4>
<pre>public&nbsp;int&nbsp;DAJfingerPKTemplate(char&nbsp;flagA,
                               char[]&nbsp;pBufferIDFlashPageIDA,
                               char&nbsp;flagB,
                               char[]&nbsp;pBufferIDFlashPageIDB)</pre>
</li>
</ul>
<a name="DAJfingerSearchTemplate-char:A-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerSearchTemplate</h4>
<pre>public&nbsp;char[]&nbsp;DAJfingerSearchTemplate(char[]&nbsp;ramBufferId,
                                      char[]&nbsp;templateIdStart,
                                      char[]&nbsp;templateIdEnd)</pre>
</li>
</ul>
<a name="DAJfingerDELTemplate-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerDELTemplate</h4>
<pre>public&nbsp;int&nbsp;DAJfingerDELTemplate(char&nbsp;flag,
                                char[]&nbsp;pBufferIDFlashPageID)</pre>
</li>
</ul>
<a name="DAJfingerCLEARTemplate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerCLEARTemplate</h4>
<pre>public&nbsp;int&nbsp;DAJfingerCLEARTemplate()</pre>
</li>
</ul>
<a name="DAJfingerGETTemplateCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerGETTemplateCount</h4>
<pre>public&nbsp;int&nbsp;DAJfingerGETTemplateCount()</pre>
</li>
</ul>
<a name="DAJfingerCLEARTemplateBuffer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerCLEARTemplateBuffer</h4>
<pre>public&nbsp;int&nbsp;DAJfingerCLEARTemplateBuffer()</pre>
</li>
</ul>
<a name="DAJfingerGRABImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerGRABImage</h4>
<pre>public&nbsp;byte[]&nbsp;DAJfingerGRABImage()</pre>
</li>
</ul>
<a name="DAJfingerGRABHalfImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerGRABHalfImage</h4>
<pre>public&nbsp;byte[]&nbsp;DAJfingerGRABHalfImage()</pre>
</li>
</ul>
<a name="DAJfingerGRABHalfImageProgress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerGRABHalfImageProgress</h4>
<pre>public&nbsp;int&nbsp;DAJfingerGRABHalfImageProgress()</pre>
</li>
</ul>
<a name="DAJfingerStopGRABImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DAJfingerStopGRABImage</h4>
<pre>public&nbsp;int&nbsp;DAJfingerStopGRABImage()</pre>
</li>
</ul>
<a name="UHFSetSleepTimeSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetSleepTimeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetSleepTimeSendData(char&nbsp;uTime)</pre>
</li>
</ul>
<a name="UHFSetSleepTimeRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetSleepTimeRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetSleepTimeRecvData(byte[]&nbsp;inData,
                                   int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetSleepTimeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetSleepTimeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetSleepTimeSendData()</pre>
</li>
</ul>
<a name="UHFGetSleepTimeRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetSleepTimeRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetSleepTimeRecvData(byte[]&nbsp;inData,
                                      int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFSetInventoryMode-int-int-int-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetInventoryMode</h4>
<pre>public&nbsp;int&nbsp;UHFSetInventoryMode(int&nbsp;save,
                               int&nbsp;jpw,
                               int&nbsp;sector,
                               int&nbsp;startAddr,
                               int&nbsp;length,
                               int&nbsp;sector2,
                               int&nbsp;startAddr2,
                               int&nbsp;length2)</pre>
</li>
</ul>
<a name="UHFSetTagfocusSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetTagfocusSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetTagfocusSendData(char&nbsp;flag)</pre>
</li>
</ul>
<a name="UHFSetTagfocusRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetTagfocusRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetTagfocusRecvData(byte[]&nbsp;inData,
                                  int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetTagfocusSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetTagfocusSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetTagfocusSendData()</pre>
</li>
</ul>
<a name="UHFGetTagfocusRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetTagfocusRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetTagfocusRecvData(byte[]&nbsp;inData,
                                     int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetSensorCode-char:A-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetSensorCode</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetSensorCode(char[]&nbsp;epc,
                               char&nbsp;antNum,
                               char[]&nbsp;powerValue)</pre>
<div class="block">zjx 20191127  温度标签增加通讯命令   -------- start --------</div>
</li>
</ul>
<a name="UHFGetCalibrationData-char:A-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetCalibrationData</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetCalibrationData(char[]&nbsp;epc,
                                    char&nbsp;antNum,
                                    char[]&nbsp;powerValue)</pre>
</li>
</ul>
<a name="UHFGetOnChipRSSI-char:A-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetOnChipRSSI</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetOnChipRSSI(char[]&nbsp;epc,
                               char&nbsp;antNum,
                               char[]&nbsp;powerValue)</pre>
</li>
</ul>
<a name="UHFGetTempertureCode-char:A-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetTempertureCode</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetTempertureCode(char[]&nbsp;epc,
                                   char&nbsp;antNum,
                                   char[]&nbsp;powerValue)</pre>
</li>
</ul>
<a name="UHFGetOnChipRSSIAndTempCode-char:A-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetOnChipRSSIAndTempCode</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetOnChipRSSIAndTempCode(char[]&nbsp;epc,
                                          char&nbsp;antNum,
                                          char[]&nbsp;powerValue)</pre>
</li>
</ul>
<a name="UHFInventoryTempTag-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventoryTempTag</h4>
<pre>public&nbsp;int&nbsp;UHFInventoryTempTag(char&nbsp;antNum,
                               char[]&nbsp;powerValue)</pre>
</li>
</ul>
<a name="UHFGetTempTagReceived-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetTempTagReceived</h4>
<pre>public&nbsp;int&nbsp;UHFGetTempTagReceived(char[]&nbsp;outData)</pre>
</li>
</ul>
<a name="UHFWriteCalibrationData-char:A-char-char:A-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFWriteCalibrationData</h4>
<pre>public&nbsp;int&nbsp;UHFWriteCalibrationData(char[]&nbsp;epc,
                                   char&nbsp;antNum,
                                   char[]&nbsp;powerValue,
                                   char[]&nbsp;data)</pre>
</li>
</ul>
<a name="UHFInventoryTempTag2-char-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInventoryTempTag2</h4>
<pre>public&nbsp;int&nbsp;UHFInventoryTempTag2(char&nbsp;antNum,
                                char[]&nbsp;powerValue)</pre>
</li>
</ul>
<a name="UHFGetTempTagReceived2-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetTempTagReceived2</h4>
<pre>public&nbsp;int&nbsp;UHFGetTempTagReceived2(char[]&nbsp;outData)</pre>
</li>
</ul>
<a name="UHFSetEPCTIDUSERAddrLength-char-char-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetEPCTIDUSERAddrLength</h4>
<pre>public&nbsp;int&nbsp;UHFSetEPCTIDUSERAddrLength(char&nbsp;mode,
                                      char&nbsp;TID_addr,
                                      char&nbsp;TID_length,
                                      char&nbsp;USER_addr,
                                      char&nbsp;USER_length)</pre>
<div class="block">功能：设置EPC+TID+USER 区地址和长度
 输入：mode       盘点模式： 0x00为盘点EPC； 0x01为盘点EPC+TID； 0x02为EPC+TID+USER
     TID_addr    TID地址：盘点TID的起始地址，单位word（2 Byte），模式为0x01时有效

     TID_length  TID长度：盘点TID的长度，单位word（2 Byte），模式为0x01时有效
     USER_addr   USER地址：盘点USER区的起始地址，单位word（2 Byte），模式为0x02时有效
     USER_length USER长度：盘点USER区的长度，单位word（2 Byte），模式为0x02时有效</div>
</li>
</ul>
<a name="UHFGetEPCTIDUSERAddrLength--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetEPCTIDUSERAddrLength</h4>
<pre>public&nbsp;char[]&nbsp;UHFGetEPCTIDUSERAddrLength()</pre>
<div class="block">功能：设置EPC+TID+USER 区地址和长度

 输出：
     char[0] : 状态，0成功
     char[1] : 数据长度，5
     char[2]  盘点模式： 0x00为盘点EPC； 0x01为盘点EPC+TID； 0x02为EPC+TID+USER
     char[3]  TID地址：盘点TID的起始地址，单位word（2 Byte），模式为0x01时有效

     char[4]  TID长度：盘点TID的长度，单位word（2 Byte），模式为0x01时有效
     char[5]  USER地址：盘点USER区的起始地址，单位word（2 Byte），模式为0x02时有效
     char[6]  USER长度：盘点USER区的长度，单位word（2 Byte），模式为0x02时有效</div>
</li>
</ul>
<a name="UHFSetSoftResetSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetSoftResetSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetSoftResetSendData()</pre>
<div class="block">20200519 begin</div>
</li>
</ul>
<a name="UHFSetSoftResetRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetSoftResetRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetSoftResetRecvData(byte[]&nbsp;inData,
                                   int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFSetReaderBeepSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetReaderBeepSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetReaderBeepSendData(char&nbsp;mode)</pre>
</li>
</ul>
<a name="UHFSetReaderBeepRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetReaderBeepRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetReaderBeepRecvData(byte[]&nbsp;inData,
                                    int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetReaderBeepSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetReaderBeepSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetReaderBeepSendData()</pre>
</li>
</ul>
<a name="UHFGetReaderBeepRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetReaderBeepRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFGetReaderBeepRecvData(byte[]&nbsp;inData,
                                    int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFSetIpSendData-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetIpSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetIpSendData(byte[]&nbsp;ipbuf,
                               byte[]&nbsp;postbuf)</pre>
</li>
</ul>
<a name="UHFSetIpRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetIpRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetIpRecvData(byte[]&nbsp;inData,
                            int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetIpSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetIpSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetIpSendData()</pre>
</li>
</ul>
<a name="UHFGetIpRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetIpRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetIpRecvData(byte[]&nbsp;inData,
                               int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFTCPTagsDataParseRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFTCPTagsDataParseRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFTCPTagsDataParseRecvData(byte[]&nbsp;inData,
                                          int&nbsp;len)</pre>
</li>
</ul>
<a name="UHFSetANTSendData-char-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetANTSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFSetANTSendData(char&nbsp;saveflag,
                                byte[]&nbsp;buf,
                                int&nbsp;bufLen)</pre>
<div class="block">功能：天线设置
 输入：saveflag -- 1:掉电保存，  0：不保存
 输入：buf--2bytes, 共16bits, 每bit 置1选择对应天线</div>
</li>
</ul>
<a name="UHFSetANTRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetANTRecvData</h4>
<pre>public&nbsp;int&nbsp;UHFSetANTRecvData(byte[]&nbsp;inData,
                             int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFGetANTSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetANTSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetANTSendData()</pre>
<div class="block">功能：获取天线设置
 输出：buf--2bytes, 共16bits,</div>
</li>
</ul>
<a name="UHFGetANTRecvData-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetANTRecvData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetANTRecvData(byte[]&nbsp;inData,
                                int&nbsp;inLen)</pre>
</li>
</ul>
<a name="UHFSetIp-byte:A-int-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetIp</h4>
<pre>public&nbsp;int&nbsp;UHFSetIp(byte[]&nbsp;ipbuf,
                    int&nbsp;port,
                    byte[]&nbsp;mask,
                    byte[]&nbsp;gate)</pre>
</li>
</ul>
<a name="UHFGetIp-byte:A-int:A-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetIp</h4>
<pre>public&nbsp;int&nbsp;UHFGetIp(byte[]&nbsp;ipbuf,
                    int[]&nbsp;port,
                    byte[]&nbsp;mask,
                    byte[]&nbsp;gate)</pre>
</li>
</ul>
<a name="UHFSetDestIp-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetDestIp</h4>
<pre>public&nbsp;int&nbsp;UHFSetDestIp(byte[]&nbsp;ipbuf,
                        int&nbsp;port)</pre>
</li>
</ul>
<a name="UHFGetDestIp-byte:A-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetDestIp</h4>
<pre>public&nbsp;int&nbsp;UHFGetDestIp(byte[]&nbsp;ipbuf,
                        int[]&nbsp;port)</pre>
</li>
</ul>
<a name="UHFSetBeep-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetBeep</h4>
<pre>public&nbsp;int&nbsp;UHFSetBeep(int&nbsp;mode)</pre>
</li>
</ul>
<a name="UHFGetBeep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetBeep</h4>
<pre>public&nbsp;int&nbsp;UHFGetBeep()</pre>
</li>
</ul>
<a name="UHFSetSoftReset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetSoftReset</h4>
<pre>public&nbsp;int&nbsp;UHFSetSoftReset()</pre>
<div class="block">功能：设置软件复位</div>
</li>
</ul>
<a name="UHFSetANTWorkTime-byte-byte-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetANTWorkTime</h4>
<pre>public&nbsp;int&nbsp;UHFSetANTWorkTime(byte&nbsp;antnum,
                             byte&nbsp;saveflag,
                             int&nbsp;WorkTime)</pre>
<div class="block">功能：设置天线工作时间
 输入：antnum -- 天线号
 输入：saveflag -- 1:掉电保存， 0：不保存
 输入：WorkTime -- 工作时间 ，单位ms, 范围 10-65535ms
 返回：0：设置成功     -1：设置失败</div>
</li>
</ul>
<a name="UHFGetANTWorkTime-byte-int:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetANTWorkTime</h4>
<pre>public&nbsp;int&nbsp;UHFGetANTWorkTime(byte&nbsp;antnum,
                             int[]&nbsp;WorkTime)</pre>
<div class="block">功能：获取天线工作时间
 输入：antnum -- 天线号
 输出：WorkTime -- 工作时间 ，单位ms, 范围 10-65535ms
 返回：0：获取成功     -1：获取失败</div>
</li>
</ul>
<a name="UHFSetANT-byte-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetANT</h4>
<pre>public&nbsp;int&nbsp;UHFSetANT(byte&nbsp;saveflag,
                     byte[]&nbsp;buf)</pre>
<div class="block">功能：天线设置
 输入：saveflag -- 1:掉电保存，  0：不保存
 输入：buf--2bytes, 共16bits, 每bit 置1选择对应天线</div>
</li>
</ul>
<a name="UHFGetANT-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetANT</h4>
<pre>public&nbsp;int&nbsp;UHFGetANT(byte[]&nbsp;buf)</pre>
<div class="block">功能：获取天线设置
 输出：buf--2bytes, 共16bits,</div>
</li>
</ul>
<a name="UHFSetWorkMode-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetWorkMode</h4>
<pre>public&nbsp;int&nbsp;UHFSetWorkMode(byte&nbsp;mode)</pre>
</li>
</ul>
<a name="UHFGetWorkMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetWorkMode</h4>
<pre>public&nbsp;int&nbsp;UHFGetWorkMode()</pre>
</li>
</ul>
<a name="UHFSetIOControl-byte-byte-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFSetIOControl</h4>
<pre>public&nbsp;int&nbsp;UHFSetIOControl(byte&nbsp;output1,
                           byte&nbsp;output2,
                           byte&nbsp;outStatus)</pre>
<div class="block">功能：继电器和 IO 控制输出设置
 输入：output1:    0:低电平   1：高电平

     output2:    0:低电平   1：高电平

     outStatus： 0：断开    1：闭合

     返回值：0：设置成功     -1：设置失败</div>
</li>
</ul>
<a name="UHFGetIoControl--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFGetIoControl</h4>
<pre>public&nbsp;byte[]&nbsp;UHFGetIoControl()</pre>
<div class="block">功能：获取继电器和 IO 控制输出设置状态
     返回值：null -- 执行失败
     data：2字节，output1: 0:低电平   1：高电平 output2: 0:低电平   1：高电平</div>
</li>
</ul>
<a name="A4OptoCoupler3On-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A4OptoCoupler3On</h4>
<pre>public&nbsp;int&nbsp;A4OptoCoupler3On(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="A4OptoCoupler3Off-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A4OptoCoupler3Off</h4>
<pre>public&nbsp;int&nbsp;A4OptoCoupler3Off(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="A4OptoCoupler4On-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A4OptoCoupler4On</h4>
<pre>public&nbsp;int&nbsp;A4OptoCoupler4On(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="A4OptoCoupler4Off-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A4OptoCoupler4Off</h4>
<pre>public&nbsp;int&nbsp;A4OptoCoupler4Off(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="A4WgData0On-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A4WgData0On</h4>
<pre>public&nbsp;int&nbsp;A4WgData0On(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="A4WgData0Off-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A4WgData0Off</h4>
<pre>public&nbsp;int&nbsp;A4WgData0Off(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="A4WgData1Off-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A4WgData1Off</h4>
<pre>public&nbsp;int&nbsp;A4WgData1Off(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="A4WgData1On-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>A4WgData1On</h4>
<pre>public&nbsp;int&nbsp;A4WgData1On(java.lang.String&nbsp;device)</pre>
</li>
</ul>
<a name="UHFInitRegFile-int-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFInitRegFile</h4>
<pre>public&nbsp;int&nbsp;UHFInitRegFile(int&nbsp;mask_bank,
                          int&nbsp;mask_addr,
                          int&nbsp;mask_len,
                          byte[]&nbsp;mask_data)</pre>
</li>
</ul>
<a name="UHFReadTagTemp-int-int-int-byte:A-float:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFReadTagTemp</h4>
<pre>public&nbsp;int&nbsp;UHFReadTagTemp(int&nbsp;mask_bank,
                          int&nbsp;mask_addr,
                          int&nbsp;mask_len,
                          byte[]&nbsp;mask_data,
                          float[]&nbsp;readTemp)</pre>
</li>
</ul>
<a name="UHFStartLogging-int-int-int-byte:A-float-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStartLogging</h4>
<pre>public&nbsp;int&nbsp;UHFStartLogging(int&nbsp;mask_bank,
                           int&nbsp;mask_addr,
                           int&nbsp;mask_len,
                           byte[]&nbsp;mask_data,
                           float&nbsp;min_temp,
                           float&nbsp;max_temp,
                           int&nbsp;work_delay,
                           int&nbsp;work_interval)</pre>
</li>
</ul>
<a name="UHFStopLogging-int-int-int-byte:A-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStopLogging</h4>
<pre>public&nbsp;int&nbsp;UHFStopLogging(int&nbsp;mask_bank,
                          int&nbsp;mask_addr,
                          int&nbsp;mask_len,
                          byte[]&nbsp;mask_data,
                          long&nbsp;password)</pre>
</li>
</ul>
<a name="UHFCheckOpMode-int-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFCheckOpMode</h4>
<pre>public&nbsp;int&nbsp;UHFCheckOpMode(int&nbsp;mask_bank,
                          int&nbsp;mask_addr,
                          int&nbsp;mask_len,
                          byte[]&nbsp;mask_data)</pre>
</li>
</ul>
<a name="UHFReadMultiTemp-int-int-int-byte:A-int-int-int:A-int:A-float:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFReadMultiTemp</h4>
<pre>public&nbsp;int&nbsp;UHFReadMultiTemp(int&nbsp;jmbank,
                            int&nbsp;jmaddr,
                            int&nbsp;jmlen,
                            byte[]&nbsp;jmdata,
                            int&nbsp;jstart,
                            int&nbsp;jnum,
                            int[]&nbsp;jtotalnum,
                            int[]&nbsp;jreturned,
                            float[]&nbsp;jtemp)</pre>
</li>
</ul>
<a name="UHFReadTagVoltage-int-int-int-byte:A-float:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFReadTagVoltage</h4>
<pre>public&nbsp;int&nbsp;UHFReadTagVoltage(int&nbsp;jmbank,
                             int&nbsp;jmaddr,
                             int&nbsp;jmlen,
                             byte[]&nbsp;jmdata,
                             float[]&nbsp;jvoltage)</pre>
</li>
</ul>
<a name="UHFWriteScreenBlock-byte:A-byte-int-int-byte:A-byte-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFWriteScreenBlock</h4>
<pre>public&nbsp;int&nbsp;UHFWriteScreenBlock(byte[]&nbsp;passwd,
                               byte&nbsp;filterBank,
                               int&nbsp;filterPtr,
                               int&nbsp;filterCnt,
                               byte[]&nbsp;filterData,
                               byte&nbsp;type,
                               int&nbsp;ptr,
                               int&nbsp;cnt,
                               byte[]&nbsp;writeData)</pre>
</li>
</ul>
<a name="UHFAuthenticateCommon-int-int-int-byte:A-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFAuthenticateCommon</h4>
<pre>public&nbsp;byte[]&nbsp;UHFAuthenticateCommon(int&nbsp;password,
                                    int&nbsp;bank,
                                    int&nbsp;addr,
                                    byte[]&nbsp;jmData,
                                    int&nbsp;mDataBitsLen,
                                    int&nbsp;jkeyId,
                                    byte[]&nbsp;jtData)</pre>
</li>
</ul>
<a name="ISO14443A_unvarnished_transfer-byte:A-int-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ISO14443A_unvarnished_transfer</h4>
<pre>public&nbsp;int&nbsp;ISO14443A_unvarnished_transfer(byte[]&nbsp;sendData,
                                          int&nbsp;sendDataLen,
                                          byte[]&nbsp;receiveData)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DeviceAPI.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/deviceapi/DeviceAPI.html" target="_top">Frames</a></li>
<li><a href="DeviceAPI.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
