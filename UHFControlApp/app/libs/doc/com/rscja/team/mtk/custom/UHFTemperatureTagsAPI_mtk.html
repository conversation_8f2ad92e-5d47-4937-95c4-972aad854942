<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>UHFTemperatureTagsAPI_mtk</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UHFTemperatureTagsAPI_mtk";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":9,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFTemperatureTagsAPI_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/custom/M775Authenticate_mtk.html" title="class in com.rscja.team.mtk.custom"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html" title="class in com.rscja.team.mtk.custom"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html" target="_top">Frames</a></li>
<li><a href="UHFTemperatureTagsAPI_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.rscja.team.mtk.deviceapi.RFIDWithUHFUART_mtk">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.mtk.custom</div>
<h2 title="Class UHFTemperatureTagsAPI_mtk" class="title">Class UHFTemperatureTagsAPI_mtk</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">com.rscja.deviceapi.UhfBase</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">com.rscja.team.mtk.deviceapi.RFIDWithUHFUART_mtk</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.mtk.custom.UHFTemperatureTagsAPI_mtk</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">UHFTemperatureTagsAPI_mtk</span>
extends <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a>
implements <a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.rscja.deviceapi.<a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.team.mtk.deviceapi.RFIDWithUHFUART_mtk">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></h3>
<code><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#TidLength">TidLength</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC">Bank_EPC</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED">Bank_RESERVED</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID">Bank_TID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER">Bank_USER</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_ACCESS">LockBank_ACCESS</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_EPC">LockBank_EPC</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_KILL">LockBank_KILL</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_TID">LockBank_TID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_USER">LockBank_USER</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_LOCK">LockMode_LOCK</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_OPEN">LockMode_OPEN</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_PLOCK">LockMode_PLOCK</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_POPEN">LockMode_POPEN</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#UPDATE_UHF">UPDATE_UHF</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom">UHFTemperatureTagsAPI.TagState</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#checkTagState--">checkTagState</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom">UHFTemperatureTagsAPI.TagState</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#checkTagState-int-int-int-java.lang.String-">checkTagState</a></span>(int&nbsp;filterBank,
             int&nbsp;filterPtr,
             int&nbsp;filterCnt,
             java.lang.String&nbsp;filterData)</code>
<div class="block">检查测温状态</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html" title="class in com.rscja.team.mtk.custom">UHFTemperatureTagsAPI_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#initRegFile-int-int-int-java.lang.String-">initRegFile</a></span>(int&nbsp;filterBank,
           int&nbsp;filterPtr,
           int&nbsp;filterCnt,
           java.lang.String&nbsp;filterData)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#readMultipleTemperature-int-int-">readMultipleTemperature</a></span>(int&nbsp;jstart,
                       int&nbsp;jnum)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#readMultipleTemperature-int-int-int-java.lang.String-int-int-">readMultipleTemperature</a></span>(int&nbsp;filterBank,
                       int&nbsp;filterPtr,
                       int&nbsp;filterCnt,
                       java.lang.String&nbsp;filterData,
                       int&nbsp;jstart,
                       int&nbsp;jnum)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#readTagTemp-int-int-int-java.lang.String-float:A-">readTagTemp</a></span>(int&nbsp;filterBank,
           int&nbsp;filterPtr,
           int&nbsp;filterCnt,
           java.lang.String&nbsp;filterData,
           float[]&nbsp;readTemp)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#readTagVoltage-float:A-">readTagVoltage</a></span>(float[]&nbsp;outVoltage)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#readTagVoltage-int-int-int-java.lang.String-float:A-">readTagVoltage</a></span>(int&nbsp;filterBank,
              int&nbsp;filterPtr,
              int&nbsp;filterCnt,
              java.lang.String&nbsp;filterData,
              float[]&nbsp;outVoltage)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#setEPCAndTemperatureMode--">setEPCAndTemperatureMode</a></span>()</code>
<div class="block">设置盘点标签为EPC+温度模式<br></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#setInventoryCallback-com.rscja.custom.UHFTemperatureTagsAPI.IUHFInventoryTempCallback-">setInventoryCallback</a></span>(<a href="../../../../../com/rscja/custom/UHFTemperatureTagsAPI.IUHFInventoryTempCallback.html" title="interface in com.rscja.custom">UHFTemperatureTagsAPI.IUHFInventoryTempCallback</a>&nbsp;tempCallback)</code>
<div class="block">盘点温度标签回调</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#startLogging-float-float-int-int-">startLogging</a></span>(float&nbsp;min_temp,
            float&nbsp;max_temp,
            int&nbsp;work_delay,
            int&nbsp;work_interval)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#startLogging-int-int-int-java.lang.String-float-float-int-int-">startLogging</a></span>(int&nbsp;filterBank,
            int&nbsp;filterPtr,
            int&nbsp;filterCnt,
            java.lang.String&nbsp;filterData,
            float&nbsp;min_temp,
            float&nbsp;max_temp,
            int&nbsp;work_delay,
            int&nbsp;work_interval)</code>
<div class="block">开始测量温度<br></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#stopLogging-int-int-int-java.lang.String-java.lang.String-">stopLogging</a></span>(int&nbsp;filterBank,
           int&nbsp;filterPtr,
           int&nbsp;filterCnt,
           java.lang.String&nbsp;filterData,
           java.lang.String&nbsp;pwd)</code>
<div class="block">停止测量温度<br></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html#stopLogging-java.lang.String-">stopLogging</a></span>(java.lang.String&nbsp;pwd)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.mtk.deviceapi.RFIDWithUHFUART_mtk">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi">RFIDWithUHFUART_mtk</a></h3>
<code><a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#eraseData-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#factoryReset--">factoryReset</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#free--">free</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getConnectStatus--">getConnectStatus</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getCW--">getCW</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getErrCode--">getErrCode</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getFastInventoryMode--">getFastInventoryMode</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getFrequencyMode--">getFrequencyMode</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getGen2--">getGen2</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getHardwareVersion--">getHardwareVersion</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getInputStatus--">getInputStatus</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getLBTMode--">getLBTMode</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getPower--">getPower</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getProtocol--">getProtocol</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getRFLink--">getRFLink</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getTagLocate-android.content.Context-">getTagLocate</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getTemperature--">getTemperature</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#getVersion--">getVersion</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#init--">init</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#init-android.content.Context-">init</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#inventorySingleTag--">inventorySingleTag</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#inventorySingleTag-com.rscja.deviceapi.entity.InventoryParameter-">inventorySingleTag</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#isInventorying--">isInventorying</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#isPowerOn--">isPowerOn</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#killTag-java.lang.String-">killTag</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#lockMem-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#readData-java.lang.String-int-int-int-">readData</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">readData</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#readTagFromBuffer--">readTagFromBuffer</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setCW-int-">setCW</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setDynamicDistance-int-">setDynamicDistance</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setEPCAndTIDMode--">setEPCAndTIDMode</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setEPCAndTIDUserMode-int-int-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setEPCAndUserReservedModeEx-int-int-int-int-int-">setEPCAndUserReservedModeEx</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setEPCMode--">setEPCMode</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setFastID-boolean-">setFastID</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setFastInventoryMode-boolean-">setFastInventoryMode</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setFilter-int-int-int-java.lang.String-">setFilter</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setFreHop-float-">setFreHop</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setFrequencyMode-int-">setFrequencyMode</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">setGen2</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setLBTMode-boolean-">setLBTMode</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setOnLowBatteryListener-android.content.Context-com.rscja.deviceapi.entity.LowBatteryEntity-com.rscja.deviceapi.interfaces.OnLowBatteryListener-">setOnLowBatteryListener</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setPower-int-">setPower</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setPowerOnBySystem-android.content.Context-">setPowerOnBySystem</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setProtocol-int-">setProtocol</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setRFLink-int-">setRFLink</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setTagFocus-boolean-">setTagFocus</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#setUart-java.lang.String-">setUart</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#startInventoryTag--">startInventoryTag</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#startInventoryTag-com.rscja.deviceapi.entity.InventoryParameter-">startInventoryTag</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#startLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFLocationCallback-">startLocation</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#startRadarLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFRadarLocationCallback-">startRadarLocation</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#stopInventory--">stopInventory</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#stopLocation--">stopLocation</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#stopRadarLocation--">stopRadarLocation</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfJump2Boot--">uhfJump2Boot</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfJump2Boot-int-">uhfJump2Boot</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfPowerOff_11--">uhfPowerOff_11</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfPowerOn_11--">uhfPowerOn_11</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfStartUpdate--">uhfStartUpdate</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfStopUpdate--">uhfStopUpdate</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#uhfUpdating-byte:A-">uhfUpdating</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc</a>, <a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.UhfBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.deviceapi.<a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/UhfBase.html#readTcpServiceState--">readTcpServiceState</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html" title="class in com.rscja.team.mtk.custom">UHFTemperatureTagsAPI_mtk</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="initRegFile-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initRegFile</h4>
<pre>public&nbsp;boolean&nbsp;initRegFile(int&nbsp;filterBank,
                           int&nbsp;filterPtr,
                           int&nbsp;filterCnt,
                           java.lang.String&nbsp;filterData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#initRegFile-int-int-int-java.lang.String-">initRegFile</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
</dl>
</li>
</ul>
<a name="readTagTemp-int-int-int-java.lang.String-float:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagTemp</h4>
<pre>public&nbsp;boolean&nbsp;readTagTemp(int&nbsp;filterBank,
                           int&nbsp;filterPtr,
                           int&nbsp;filterCnt,
                           java.lang.String&nbsp;filterData,
                           float[]&nbsp;readTemp)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#readTagTemp-int-int-int-java.lang.String-float:A-">readTagTemp</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
</dl>
</li>
</ul>
<a name="startLogging-float-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startLogging</h4>
<pre>public&nbsp;boolean&nbsp;startLogging(float&nbsp;min_temp,
                            float&nbsp;max_temp,
                            int&nbsp;work_delay,
                            int&nbsp;work_interval)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#startLogging-float-float-int-int-">startLogging</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
</dl>
</li>
</ul>
<a name="startLogging-int-int-int-java.lang.String-float-float-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startLogging</h4>
<pre>public&nbsp;boolean&nbsp;startLogging(int&nbsp;filterBank,
                            int&nbsp;filterPtr,
                            int&nbsp;filterCnt,
                            java.lang.String&nbsp;filterData,
                            float&nbsp;min_temp,
                            float&nbsp;max_temp,
                            int&nbsp;work_delay,
                            int&nbsp;work_interval)</pre>
<div class="block">开始测量温度<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#startLogging-int-int-int-java.lang.String-float-float-int-int-">startLogging</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filterBank</code> - 过滤的存储区 :<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>filterPtr</code> - 过滤的起始地址</dd>
<dd><code>filterCnt</code> - 过滤数据长度(单位:bit),当过滤的数据长度为0时，表示无过滤</dd>
<dd><code>filterData</code> - 过滤的数据</dd>
<dd><code>min_temp</code> - 最低温度</dd>
<dd><code>max_temp</code> - 最高温度</dd>
<dd><code>work_delay</code> - 第一次延时多久获取</dd>
<dd><code>work_interval</code> - 间隔时间</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="stopLogging-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopLogging</h4>
<pre>public&nbsp;boolean&nbsp;stopLogging(java.lang.String&nbsp;pwd)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#stopLogging-java.lang.String-">stopLogging</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
</dl>
</li>
</ul>
<a name="stopLogging-int-int-int-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopLogging</h4>
<pre>public&nbsp;boolean&nbsp;stopLogging(int&nbsp;filterBank,
                           int&nbsp;filterPtr,
                           int&nbsp;filterCnt,
                           java.lang.String&nbsp;filterData,
                           java.lang.String&nbsp;pwd)</pre>
<div class="block">停止测量温度<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#stopLogging-int-int-int-java.lang.String-java.lang.String-">stopLogging</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filterBank</code> - 过滤的存储区 :<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>filterPtr</code> - 过滤的起始地址</dd>
<dd><code>filterCnt</code> - 过滤数据长度(单位:bit),当过滤的数据长度为0时，表示无过滤</dd>
<dd><code>filterData</code> - 过滤的数据</dd>
<dd><code>pwd</code> - 密码</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="checkTagState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkTagState</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom">UHFTemperatureTagsAPI.TagState</a>&gt;&nbsp;checkTagState()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#checkTagState--">checkTagState</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
</dl>
</li>
</ul>
<a name="checkTagState-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkTagState</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom">UHFTemperatureTagsAPI.TagState</a>&gt;&nbsp;checkTagState(int&nbsp;filterBank,
                                                                    int&nbsp;filterPtr,
                                                                    int&nbsp;filterCnt,
                                                                    java.lang.String&nbsp;filterData)</pre>
<div class="block">检查测温状态</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#checkTagState-int-int-int-java.lang.String-">checkTagState</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="setEPCAndTemperatureMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCAndTemperatureMode</h4>
<pre>public&nbsp;boolean&nbsp;setEPCAndTemperatureMode()</pre>
<div class="block">设置盘点标签为EPC+温度模式<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#setEPCAndTemperatureMode--">setEPCAndTemperatureMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="setInventoryCallback-com.rscja.custom.UHFTemperatureTagsAPI.IUHFInventoryTempCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInventoryCallback</h4>
<pre>public&nbsp;void&nbsp;setInventoryCallback(<a href="../../../../../com/rscja/custom/UHFTemperatureTagsAPI.IUHFInventoryTempCallback.html" title="interface in com.rscja.custom">UHFTemperatureTagsAPI.IUHFInventoryTempCallback</a>&nbsp;tempCallback)</pre>
<div class="block">盘点温度标签回调</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#setInventoryCallback-com.rscja.custom.UHFTemperatureTagsAPI.IUHFInventoryTempCallback-">setInventoryCallback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
</dl>
</li>
</ul>
<a name="readMultipleTemperature-int-int-int-java.lang.String-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readMultipleTemperature</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a>&nbsp;readMultipleTemperature(int&nbsp;filterBank,
                                                                             int&nbsp;filterPtr,
                                                                             int&nbsp;filterCnt,
                                                                             java.lang.String&nbsp;filterData,
                                                                             int&nbsp;jstart,
                                                                             int&nbsp;jnum)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#readMultipleTemperature-int-int-int-java.lang.String-int-int-">readMultipleTemperature</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
</dl>
</li>
</ul>
<a name="readMultipleTemperature-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readMultipleTemperature</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a>&nbsp;readMultipleTemperature(int&nbsp;jstart,
                                                                             int&nbsp;jnum)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#readMultipleTemperature-int-int-">readMultipleTemperature</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
</dl>
</li>
</ul>
<a name="readTagVoltage-int-int-int-java.lang.String-float:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagVoltage</h4>
<pre>public&nbsp;boolean&nbsp;readTagVoltage(int&nbsp;filterBank,
                              int&nbsp;filterPtr,
                              int&nbsp;filterCnt,
                              java.lang.String&nbsp;filterData,
                              float[]&nbsp;outVoltage)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#readTagVoltage-int-int-int-java.lang.String-float:A-">readTagVoltage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
</dl>
</li>
</ul>
<a name="readTagVoltage-float:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>readTagVoltage</h4>
<pre>public&nbsp;boolean&nbsp;readTagVoltage(float[]&nbsp;outVoltage)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html#readTagVoltage-float:A-">readTagVoltage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces">IUHFTemperatureTagsAPI</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFTemperatureTagsAPI_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/custom/M775Authenticate_mtk.html" title="class in com.rscja.team.mtk.custom"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html" title="class in com.rscja.team.mtk.custom"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html" target="_top">Frames</a></li>
<li><a href="UHFTemperatureTagsAPI_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.rscja.team.mtk.deviceapi.RFIDWithUHFUART_mtk">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
