<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>com.rscja.team.mtk.deviceapi Class Hierarchy</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.rscja.team.mtk.deviceapi Class Hierarchy";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/custom/package-tree.html">Prev</a></li>
<li><a href="../../../../../com/rscja/team/mtk/scanner/led/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/deviceapi/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package com.rscja.team.mtk.deviceapi</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/Barcode1D_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Barcode1D_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IBarcode1D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode1D</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Barcode2D_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces">IBarcode2D</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">DeviceAPI</span></a></li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">FingerprintWithFIPS_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithFIPS</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">FingerprintWithMorpho_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">FingerprintWithTLK1NC_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithTLK1NC</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/Infrared_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Infrared_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces">IInfrared</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/LedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">LedLight_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/ILedLight.html" title="interface in com.rscja.deviceapi.interfaces">ILedLight</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/Module_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Module_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Printer_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces">IPrinter</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/PSAM_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">PSAM_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces">IPSAM</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDBase_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDBase</a>)
<ul>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDWithISO14443A_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443A4CPU_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDWithISO14443A4CPU_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A4CPU.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A4CPU</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDWithISO14443B_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443B</a>)</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDWithISO15693_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">ScanerLedLight_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces">IScanerLedLight</a>)</li>
<li type="circle">com.rscja.deviceapi.<a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi"><span class="typeNameLink">UhfBase</span></a>
<ul>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDWithUHFUART_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>)</li>
</ul>
</li>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">UsbFingerprint_mtk</span></a> (implements com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUsbFingerprint.html" title="interface in com.rscja.deviceapi.interfaces">IUsbFingerprint</a>)</li>
</ul>
</li>
</ul>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li type="circle">com.rscja.team.mtk.deviceapi.<a href="../../../../../com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.TagType.html" title="enum in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">RFIDWithISO15693_mtk.TagType</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/custom/package-tree.html">Prev</a></li>
<li><a href="../../../../../com/rscja/team/mtk/scanner/led/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/deviceapi/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
