<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FingerprintWithMorpho_mtk</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FingerprintWithMorpho_mtk";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":42,"i24":42,"i25":42};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FingerprintWithMorpho_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" target="_top">Frames</a></li>
<li><a href="FingerprintWithMorpho_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.mtk.deviceapi</div>
<h2 title="Class FingerprintWithMorpho_mtk" class="title">Class FingerprintWithMorpho_mtk</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.mtk.deviceapi.FingerprintWithMorpho_mtk</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">FingerprintWithMorpho_mtk</span>
extends java.lang.Object
implements <a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#free--">free</a></span>()</code>
<div class="block">释放指纹模块<br>
 free fingerprint module<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithMorpho_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取指纹模块操作实例<br>
 acquire fingerprint module operation eample<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#getMorphoDescriptor--">getMorphoDescriptor</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#getMorphoPIDSN--">getMorphoPIDSN</a></span>()</code>
<div class="block">获取指纹版本<br>
 acquire fingerprint version<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#getMorphoSecurityLevel--">getMorphoSecurityLevel</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#init--">init</a></span>()</code>
<div class="block">初始化指纹模块 <br>
 Initialize fingerprint module<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#isPowerOn--">isPowerOn</a></span>()</code>
<div class="block">判断设备是否上电<br>
 Judge the device is powered on or not.<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#morphoEraseAllBase--">morphoEraseAllBase</a></span>()</code>
<div class="block">删除所有指纹信息<br>
 delete all fingerprint infor<br></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#setGrabCallBack-com.rscja.deviceapi.FingerprintWithMorpho.GrabCallBack-">setGrabCallBack</a></span>(<a href="../../../../../com/rscja/deviceapi/FingerprintWithMorpho.GrabCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.GrabCallBack</a>&nbsp;callBack)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#setIdentificationCallBack-com.rscja.deviceapi.FingerprintWithMorpho.IdentificationCallBack-">setIdentificationCallBack</a></span>(<a href="../../../../../com/rscja/deviceapi/FingerprintWithMorpho.IdentificationCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.IdentificationCallBack</a>&nbsp;callBack)</code>
<div class="block">设置指纹验证回调接口<br>
 setup fingerprint verification call-back contact<br></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#setMorphoLoadKs-byte:A-">setMorphoLoadKs</a></span>(byte[]&nbsp;keybuf)</code>
<div class="block">设置加密数据秘钥<br>
 Setup encypted data key<br></div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#setMorphoSecurityLevel-int-">setMorphoSecurityLevel</a></span>(int&nbsp;level)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#setPtCaptureCallBack-com.rscja.deviceapi.FingerprintWithMorpho.PtCaptureCallBack-">setPtCaptureCallBack</a></span>(<a href="../../../../../com/rscja/deviceapi/FingerprintWithMorpho.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.PtCaptureCallBack</a>&nbsp;callBack)</code>
<div class="block">设置获取指纹模版回调接口<br>
 Setup call-back contact for acquiring fingerprint template<br></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#setPtEnrollCallBack-com.rscja.deviceapi.FingerprintWithMorpho.EnrollCallBack-">setPtEnrollCallBack</a></span>(<a href="../../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a>&nbsp;callBack)</code>
<div class="block">设置采集指纹回调接口<br>
 setup fingerprint acquire call-back contact<br></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#setTemplateVerifyCallBack-com.rscja.deviceapi.FingerprintWithMorpho.TemplateVerifyCallBack-">setTemplateVerifyCallBack</a></span>(<a href="../../../../../com/rscja/deviceapi/FingerprintWithMorpho.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.TemplateVerifyCallBack</a>&nbsp;callBack)</code>
<div class="block">设置模版比对回调接口<br>
 Setup call-back contact for template comparison<br></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#setTimeOut-int-">setTimeOut</a></span>(int&nbsp;timeOut)</code>
<div class="block">设置指纹模块超时时间<br></div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#startEnroll-int-java.lang.String-">startEnroll</a></span>(int&nbsp;id,
           java.lang.String&nbsp;name)</code>
<div class="block">开始采集指纹,注意:请调用<br>
 Start acquire fingerprint, attention: call out<br>
 <b><code>#setPtEnrollCallBack(EnrollCallBack callBack)</code></b> 方法设置接收回调数据<br>
 <b><code>#setPtEnrollCallBack(EnrollCallBack callBack)</code></b> setup receive call-back data<br></div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#startEnroll-int-java.lang.String-java.lang.String-java.lang.String-">startEnroll</a></span>(int&nbsp;id,
           java.lang.String&nbsp;name,
           java.lang.String&nbsp;imgDirectory,
           java.lang.String&nbsp;imgName)</code>
<div class="block">开始采集指纹,注意:请调用<br>
 Start acquire fingerprint, attention: call out<br>
 <b><code>#setPtEnrollCallBack(EnrollCallBack callBack)</code></b> 方法设置接收回调数据<br>
 <b><code>#setPtEnrollCallBack(EnrollCallBack callBack)</code></b> setup receive call-back data<br></div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#startGrab-java.lang.String-java.lang.String-">startGrab</a></span>(java.lang.String&nbsp;imgDirectory,
         java.lang.String&nbsp;imgName)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#startIdentification--">startIdentification</a></span>()</code>
<div class="block">开始验证指纹,注意:请调用<br>
 Start verify fingerprint, attention: call-out<br>
 <b><code>#setIdentificationCallBack(IdentificationCallBack callBack)</code></b> 方法设置接收回调数据<br>
 <b><code>#setIdentificationCallBack(IdentificationCallBack callBack)</code></b> setup receive call-back data<br></div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#startPtCapture-boolean-">startPtCapture</a></span>(boolean&nbsp;encryptflag)</code>
<div class="block">开始获取指纹模版,注意:请调用<br>
 Start acquire fingerprint template, attention: call out<br>
 <b><code>#setPtCaptureCallBack(PtCaptureCallBack callBack)</code></b> 方法设置接收回调数据<br>
 <b><code>#setPtCaptureCallBack(PtCaptureCallBack callBack)</code></b> setup receive call-back data<br></div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#startPtCapturePKComp-boolean-">startPtCapturePKComp</a></span>(boolean&nbsp;encryptflag)</code>
<div class="block">开始获取指纹模版,注意:请调用<br>
 Start acquire fingerprint template, attention: call out<br>
 <b><code>#setPtCaptureCallBack(PtCaptureCallBack callBack)</code></b> 方法设置接收回调数据<br>
 <b><code>#setPtCaptureCallBack(PtCaptureCallBack callBack)</code></b> setup receive call-back data<br></div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#startTemplateVerify-byte:A-int-">startTemplateVerify</a></span>(byte[]&nbsp;template,
                   int&nbsp;type)</code>
<div class="block">开始模版验证,注意:请调用<br>
 start template verification, attention: call out<br>
 <b><code>#setTemplateVerifyCallBack(TemplateVerifyCallBack callBack)</code></b> 方法设置接收回调数据<br>
 <b><code>#setTemplateVerifyCallBack(TemplateVerifyCallBack callBack)</code></b>method to setup received call-back data.<br></div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#stopEnroll--">stopEnroll</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#stopIdentification--">stopIdentification</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#stopPtCapture--">stopPtCapture</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IFingerprintWithMorpho">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#isPowerOn--">isPowerOn</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setPtCaptureCallBack-com.rscja.deviceapi.FingerprintWithMorpho.PtCaptureCallBack-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPtCaptureCallBack</h4>
<pre>public&nbsp;void&nbsp;setPtCaptureCallBack(<a href="../../../../../com/rscja/deviceapi/FingerprintWithMorpho.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.PtCaptureCallBack</a>&nbsp;callBack)</pre>
<div class="block">设置获取指纹模版回调接口<br>
 Setup call-back contact for acquiring fingerprint template<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#setPtCaptureCallBack-com.rscja.deviceapi.FingerprintWithMorpho.PtCaptureCallBack-">setPtCaptureCallBack</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>callBack</code> - 回调方法<br>
                 callBack call-back method<br></dd>
</dl>
</li>
</ul>
<a name="setTemplateVerifyCallBack-com.rscja.deviceapi.FingerprintWithMorpho.TemplateVerifyCallBack-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTemplateVerifyCallBack</h4>
<pre>public&nbsp;void&nbsp;setTemplateVerifyCallBack(<a href="../../../../../com/rscja/deviceapi/FingerprintWithMorpho.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.TemplateVerifyCallBack</a>&nbsp;callBack)</pre>
<div class="block">设置模版比对回调接口<br>
 Setup call-back contact for template comparison<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#setTemplateVerifyCallBack-com.rscja.deviceapi.FingerprintWithMorpho.TemplateVerifyCallBack-">setTemplateVerifyCallBack</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>callBack</code> - 回调方法<br>
                 callBack call-back method<br></dd>
</dl>
</li>
</ul>
<a name="setGrabCallBack-com.rscja.deviceapi.FingerprintWithMorpho.GrabCallBack-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGrabCallBack</h4>
<pre>public&nbsp;void&nbsp;setGrabCallBack(<a href="../../../../../com/rscja/deviceapi/FingerprintWithMorpho.GrabCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.GrabCallBack</a>&nbsp;callBack)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#setGrabCallBack-com.rscja.deviceapi.FingerprintWithMorpho.GrabCallBack-">setGrabCallBack</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
</dl>
</li>
</ul>
<a name="setPtEnrollCallBack-com.rscja.deviceapi.FingerprintWithMorpho.EnrollCallBack-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPtEnrollCallBack</h4>
<pre>public&nbsp;void&nbsp;setPtEnrollCallBack(<a href="../../../../../com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.EnrollCallBack</a>&nbsp;callBack)</pre>
<div class="block">设置采集指纹回调接口<br>
 setup fingerprint acquire call-back contact<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#setPtEnrollCallBack-com.rscja.deviceapi.FingerprintWithMorpho.EnrollCallBack-">setPtEnrollCallBack</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>callBack</code> - 回调方法<br>
                 call-back method<br></dd>
</dl>
</li>
</ul>
<a name="setIdentificationCallBack-com.rscja.deviceapi.FingerprintWithMorpho.IdentificationCallBack-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIdentificationCallBack</h4>
<pre>public&nbsp;void&nbsp;setIdentificationCallBack(<a href="../../../../../com/rscja/deviceapi/FingerprintWithMorpho.IdentificationCallBack.html" title="interface in com.rscja.deviceapi">FingerprintWithMorpho.IdentificationCallBack</a>&nbsp;callBack)</pre>
<div class="block">设置指纹验证回调接口<br>
 setup fingerprint verification call-back contact<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#setIdentificationCallBack-com.rscja.deviceapi.FingerprintWithMorpho.IdentificationCallBack-">setIdentificationCallBack</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>callBack</code> - 回调方法<br>
                 call-back method<br></dd>
</dl>
</li>
</ul>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" title="class in com.rscja.team.mtk.deviceapi">FingerprintWithMorpho_mtk</a>&nbsp;getInstance()
                                             throws <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">获取指纹模块操作实例<br>
 acquire fingerprint module operation eample<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>指纹模块操作实例</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code> - 配置错误异常</dd>
</dl>
</li>
</ul>
<a name="init--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;boolean&nbsp;init()</pre>
<div class="block">初始化指纹模块 <br>
 Initialize fingerprint module<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#init--">init</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true means success, false means failed<br></dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;boolean&nbsp;free()</pre>
<div class="block">释放指纹模块<br>
 free fingerprint module<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#free--">free</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true means success,false means failed<br></dd>
</dl>
</li>
</ul>
<a name="stopPtCapture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopPtCapture</h4>
<pre>@Deprecated
public&nbsp;boolean&nbsp;stopPtCapture()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block"><b><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#setTimeOut-int-"><code>setTimeOut(int timeOut)</code></a><br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#stopPtCapture--">stopPtCapture</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
</dl>
</li>
</ul>
<a name="stopEnroll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopEnroll</h4>
<pre>@Deprecated
public&nbsp;boolean&nbsp;stopEnroll()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block"><b><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#setTimeOut-int-"><code>setTimeOut(int timeOut)</code></a><br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#stopEnroll--">stopEnroll</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
</dl>
</li>
</ul>
<a name="stopIdentification--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopIdentification</h4>
<pre>@Deprecated
public&nbsp;boolean&nbsp;stopIdentification()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block"><b><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html#setTimeOut-int-"><code>setTimeOut(int timeOut)</code></a><br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#stopIdentification--">stopIdentification</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
</dl>
</li>
</ul>
<a name="startGrab-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startGrab</h4>
<pre>public&nbsp;void&nbsp;startGrab(java.lang.String&nbsp;imgDirectory,
                      java.lang.String&nbsp;imgName)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#startGrab-java.lang.String-java.lang.String-">startGrab</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
</dl>
</li>
</ul>
<a name="startPtCapture-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startPtCapture</h4>
<pre>public&nbsp;void&nbsp;startPtCapture(boolean&nbsp;encryptflag)</pre>
<div class="block">开始获取指纹模版,注意:请调用<br>
 Start acquire fingerprint template, attention: call out<br>
 <b><code>#setPtCaptureCallBack(PtCaptureCallBack callBack)</code></b> 方法设置接收回调数据<br>
 <b><code>#setPtCaptureCallBack(PtCaptureCallBack callBack)</code></b> setup receive call-back data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#startPtCapture-boolean-">startPtCapture</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
</dl>
</li>
</ul>
<a name="startPtCapturePKComp-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startPtCapturePKComp</h4>
<pre>public&nbsp;void&nbsp;startPtCapturePKComp(boolean&nbsp;encryptflag)</pre>
<div class="block">开始获取指纹模版,注意:请调用<br>
 Start acquire fingerprint template, attention: call out<br>
 <b><code>#setPtCaptureCallBack(PtCaptureCallBack callBack)</code></b> 方法设置接收回调数据<br>
 <b><code>#setPtCaptureCallBack(PtCaptureCallBack callBack)</code></b> setup receive call-back data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#startPtCapturePKComp-boolean-">startPtCapturePKComp</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
</dl>
</li>
</ul>
<a name="startTemplateVerify-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startTemplateVerify</h4>
<pre>public&nbsp;void&nbsp;startTemplateVerify(byte[]&nbsp;template,
                                int&nbsp;type)</pre>
<div class="block">开始模版验证,注意:请调用<br>
 start template verification, attention: call out<br>
 <b><code>#setTemplateVerifyCallBack(TemplateVerifyCallBack callBack)</code></b> 方法设置接收回调数据<br>
 <b><code>#setTemplateVerifyCallBack(TemplateVerifyCallBack callBack)</code></b>method to setup received call-back data.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#startTemplateVerify-byte:A-int-">startTemplateVerify</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>template</code> - 模板数据</dd>
<dd><code>type</code> - 0:PKComp</dd>
</dl>
</li>
</ul>
<a name="startEnroll-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startEnroll</h4>
<pre>public&nbsp;void&nbsp;startEnroll(int&nbsp;id,
                        java.lang.String&nbsp;name)</pre>
<div class="block">开始采集指纹,注意:请调用<br>
 Start acquire fingerprint, attention: call out<br>
 <b><code>#setPtEnrollCallBack(EnrollCallBack callBack)</code></b> 方法设置接收回调数据<br>
 <b><code>#setPtEnrollCallBack(EnrollCallBack callBack)</code></b> setup receive call-back data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#startEnroll-int-java.lang.String-">startEnroll</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
</dl>
</li>
</ul>
<a name="startEnroll-int-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startEnroll</h4>
<pre>public&nbsp;void&nbsp;startEnroll(int&nbsp;id,
                        java.lang.String&nbsp;name,
                        java.lang.String&nbsp;imgDirectory,
                        java.lang.String&nbsp;imgName)
                 throws java.io.IOException</pre>
<div class="block">开始采集指纹,注意:请调用<br>
 Start acquire fingerprint, attention: call out<br>
 <b><code>#setPtEnrollCallBack(EnrollCallBack callBack)</code></b> 方法设置接收回调数据<br>
 <b><code>#setPtEnrollCallBack(EnrollCallBack callBack)</code></b> setup receive call-back data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#startEnroll-int-java.lang.String-java.lang.String-java.lang.String-">startEnroll</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - 指纹id</dd>
<dd><code>name</code> - 姓名</dd>
<dd><code>imgDirectory</code> - 图片目录</dd>
<dd><code>imgName</code> - 图片名称只支持.wsq</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="startIdentification--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startIdentification</h4>
<pre>public&nbsp;void&nbsp;startIdentification()</pre>
<div class="block">开始验证指纹,注意:请调用<br>
 Start verify fingerprint, attention: call-out<br>
 <b><code>#setIdentificationCallBack(IdentificationCallBack callBack)</code></b> 方法设置接收回调数据<br>
 <b><code>#setIdentificationCallBack(IdentificationCallBack callBack)</code></b> setup receive call-back data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#startIdentification--">startIdentification</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
</dl>
</li>
</ul>
<a name="getMorphoDescriptor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMorphoDescriptor</h4>
<pre>public&nbsp;java.lang.String&nbsp;getMorphoDescriptor()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#getMorphoDescriptor--">getMorphoDescriptor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
</dl>
</li>
</ul>
<a name="setMorphoLoadKs-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMorphoLoadKs</h4>
<pre>public&nbsp;boolean&nbsp;setMorphoLoadKs(byte[]&nbsp;keybuf)</pre>
<div class="block">设置加密数据秘钥<br>
 Setup encypted data key<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#setMorphoLoadKs-byte:A-">setMorphoLoadKs</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>keybuf</code> - 秘钥<br>
               key<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true 成功，false 失败<br>
 true means success, false means failed<br></dd>
</dl>
</li>
</ul>
<a name="getMorphoPIDSN--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMorphoPIDSN</h4>
<pre>public&nbsp;java.lang.String&nbsp;getMorphoPIDSN()</pre>
<div class="block">获取指纹版本<br>
 acquire fingerprint version<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#getMorphoPIDSN--">getMorphoPIDSN</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回指纹版本信息<br>
 return fingerprint version infor<br></dd>
</dl>
</li>
</ul>
<a name="morphoEraseAllBase--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>morphoEraseAllBase</h4>
<pre>public&nbsp;boolean&nbsp;morphoEraseAllBase()</pre>
<div class="block">删除所有指纹信息<br>
 delete all fingerprint infor<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#morphoEraseAllBase--">morphoEraseAllBase</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getMorphoSecurityLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMorphoSecurityLevel</h4>
<pre>public&nbsp;int&nbsp;getMorphoSecurityLevel()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#getMorphoSecurityLevel--">getMorphoSecurityLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
</dl>
</li>
</ul>
<a name="setMorphoSecurityLevel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMorphoSecurityLevel</h4>
<pre>public&nbsp;boolean&nbsp;setMorphoSecurityLevel(int&nbsp;level)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#setMorphoSecurityLevel-int-">setMorphoSecurityLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
</dl>
</li>
</ul>
<a name="setTimeOut-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimeOut</h4>
<pre>public&nbsp;boolean&nbsp;setTimeOut(int&nbsp;timeOut)</pre>
<div class="block">设置指纹模块超时时间<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html#setTimeOut-int-">setTimeOut</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces">IFingerprintWithMorpho</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:success  false:fail</dd>
</dl>
</li>
</ul>
<a name="isPowerOn--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPowerOn</h4>
<pre>public&nbsp;boolean&nbsp;isPowerOn()</pre>
<div class="block">判断设备是否上电<br>
 Judge the device is powered on or not.<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FingerprintWithMorpho_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" target="_top">Frames</a></li>
<li><a href="FingerprintWithMorpho_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
