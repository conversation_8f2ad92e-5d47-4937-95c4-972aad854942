<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>C90_6762_ScanLed_mtk</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="C90_6762_ScanLed_mtk";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/C90_6762_ScanLed_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/rscja/team/mtk/scanner/led/C7X_6765_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../../com/rscja/team/mtk/scanner/led/ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html" target="_top">Frames</a></li>
<li><a href="C90_6762_ScanLed_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.mtk.scanner.led</div>
<h2 title="Class C90_6762_ScanLed_mtk" class="title">Class C90_6762_ScanLed_mtk</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../../com/rscja/scanner/led/ScanLed.html" title="class in com.rscja.scanner.led">com.rscja.scanner.led.ScanLed</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.mtk.scanner.led.C90_6762_ScanLed_mtk</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">C90_6762_ScanLed_mtk</span>
extends <a href="../../../../../../com/rscja/scanner/led/ScanLed.html" title="class in com.rscja.scanner.led">ScanLed</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C90_6762_ScanLed_mtk</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html#getScanLed--">getScanLed</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html#off--">off</a></span>()</code>
<div class="block">关闭LED灯<br>
 Switch off LED light<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html#on--">on</a></span>()</code>
<div class="block">打开LED灯<br>
 Switch on LED light<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.scanner.led.ScanLed">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.scanner.led.<a href="../../../../../../com/rscja/scanner/led/ScanLed.html" title="class in com.rscja.scanner.led">ScanLed</a></h3>
<code><a href="../../../../../../com/rscja/scanner/led/ScanLed.html#blink--">blink</a>, <a href="../../../../../../com/rscja/scanner/led/ScanLed.html#blink-int-int-">blink</a>, <a href="../../../../../../com/rscja/scanner/led/ScanLed.html#free--">free</a>, <a href="../../../../../../com/rscja/scanner/led/ScanLed.html#init-android.content.Context-">init</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getScanLed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScanLed</h4>
<pre>public static&nbsp;<a href="../../../../../../com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led">C90_6762_ScanLed_mtk</a>&nbsp;getScanLed()</pre>
</li>
</ul>
<a name="on--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>on</h4>
<pre>public&nbsp;void&nbsp;on()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../../../com/rscja/scanner/led/ScanLed.html#on--">ScanLed</a></code></span></div>
<div class="block">打开LED灯<br>
 Switch on LED light<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/scanner/led/ScanLed.html#on--">on</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/scanner/led/ScanLed.html" title="class in com.rscja.scanner.led">ScanLed</a></code></dd>
</dl>
</li>
</ul>
<a name="off--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>off</h4>
<pre>public&nbsp;void&nbsp;off()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../../../com/rscja/scanner/led/ScanLed.html#off--">ScanLed</a></code></span></div>
<div class="block">关闭LED灯<br>
 Switch off LED light<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/scanner/led/ScanLed.html#off--">off</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/scanner/led/ScanLed.html" title="class in com.rscja.scanner.led">ScanLed</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/C90_6762_ScanLed_mtk.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/rscja/team/mtk/scanner/led/C7X_6765_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../../com/rscja/team/mtk/scanner/led/ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html" target="_top">Frames</a></li>
<li><a href="C90_6762_ScanLed_mtk.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
