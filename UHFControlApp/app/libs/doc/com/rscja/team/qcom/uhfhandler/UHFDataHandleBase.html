<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>UHFDataHandleBase</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UHFDataHandleBase";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":6,"i12":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFDataHandleBase.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/uhfhandler/HFR1UsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html" target="_top">Frames</a></li>
<li><a href="UHFDataHandleBase.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.uhfhandler</div>
<h2 title="Class UHFDataHandleBase" class="title">Class UHFDataHandleBase</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.uhfhandler.UHFDataHandleBase</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../../com/rscja/team/qcom/uhfhandler/HFR1UsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">HFR1UsbDataHandle</a>, <a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFRxBLEDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">UHFRxBLEDataHandle</a>, <a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFRxUsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">UHFRxUsbDataHandle</a>, <a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFUR4DataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">UHFUR4DataHandle</a>, <a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFUrAxDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler">UHFUrAxDataHandle</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">UHFDataHandleBase</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#isStop">isStop</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#UHFDataHandleBase--">UHFDataHandleBase</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">addCmdList</a></span>(<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;info)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#cleanAllData--">cleanAllData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#cleanCmd--">cleanCmd</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#cleanOldCmd--">cleanOldCmd</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#cleanTagCmd--">cleanTagCmd</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getCmdList-int-">getCmdList</a></span>(int&nbsp;cmd)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getCmdList-int-int-">getCmdList</a></span>(int&nbsp;cmd,
          int&nbsp;controlWord)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getLastBarcodeCmd-int-int-int-">getLastBarcodeCmd</a></span>(int&nbsp;cmd,
                 int&nbsp;controlWord,
                 int&nbsp;timeOut)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getLastCmd-int-int-">getLastCmd</a></span>(int&nbsp;cmd,
          int&nbsp;timeOut)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getLastCmd-int-int-int-">getLastCmd</a></span>(int&nbsp;cmd,
          int&nbsp;controlWord,
          int&nbsp;timeOut)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getTagCmd--">getTagCmd</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#isBuildFullData-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">isBuildFullData</a></span>(<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;cmdinfo)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#parseData-byte:A-">parseData</a></span>(byte[]&nbsp;inDataBuf)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="isStop">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isStop</h4>
<pre>public static&nbsp;boolean isStop</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="UHFDataHandleBase--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UHFDataHandleBase</h4>
<pre>public&nbsp;UHFDataHandleBase()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isBuildFullData-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBuildFullData</h4>
<pre>public abstract&nbsp;boolean&nbsp;isBuildFullData(<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;cmdinfo)</pre>
</li>
</ul>
<a name="addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCmdList</h4>
<pre>public abstract&nbsp;void&nbsp;addCmdList(<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;info)</pre>
</li>
</ul>
<a name="cleanAllData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cleanAllData</h4>
<pre>public&nbsp;void&nbsp;cleanAllData()</pre>
</li>
</ul>
<a name="getTagCmd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTagCmd</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;getTagCmd()</pre>
</li>
</ul>
<a name="cleanTagCmd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cleanTagCmd</h4>
<pre>public&nbsp;void&nbsp;cleanTagCmd()</pre>
</li>
</ul>
<a name="cleanCmd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cleanCmd</h4>
<pre>public&nbsp;void&nbsp;cleanCmd()</pre>
</li>
</ul>
<a name="getLastCmd-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastCmd</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;getLastCmd(int&nbsp;cmd,
                                            int&nbsp;controlWord,
                                            int&nbsp;timeOut)</pre>
</li>
</ul>
<a name="getLastBarcodeCmd-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastBarcodeCmd</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;getLastBarcodeCmd(int&nbsp;cmd,
                                                   int&nbsp;controlWord,
                                                   int&nbsp;timeOut)</pre>
</li>
</ul>
<a name="getLastCmd-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastCmd</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;getLastCmd(int&nbsp;cmd,
                                            int&nbsp;timeOut)</pre>
</li>
</ul>
<a name="getCmdList-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCmdList</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&gt;&nbsp;getCmdList(int&nbsp;cmd)</pre>
</li>
</ul>
<a name="getCmdList-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCmdList</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&gt;&nbsp;getCmdList(int&nbsp;cmd,
                                                            int&nbsp;controlWord)</pre>
</li>
</ul>
<a name="cleanOldCmd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cleanOldCmd</h4>
<pre>public&nbsp;void&nbsp;cleanOldCmd()</pre>
</li>
</ul>
<a name="parseData-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>parseData</h4>
<pre>public&nbsp;void&nbsp;parseData(byte[]&nbsp;inDataBuf)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFDataHandleBase.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/uhfhandler/HFR1UsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html" target="_top">Frames</a></li>
<li><a href="UHFDataHandleBase.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
