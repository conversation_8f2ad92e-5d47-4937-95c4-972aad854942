<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>UHFProtocolParseUrAxBase_qcom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UHFProtocolParseUrAxBase_qcom";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFProtocolParseUrAxBase_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA8_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" target="_top">Frames</a></li>
<li><a href="UHFProtocolParseUrAxBase_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.rs232utils</div>
<h2 title="Class UHFProtocolParseUrAxBase_qcom" class="title">Class UHFProtocolParseUrAxBase_qcom</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi.UHFProtocolParseByJava</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.rs232utils.UHFProtocolParseUrAxBase_qcom</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA4_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrA4_qcom</a>, <a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA8_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrA8_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">UHFProtocolParseUrAxBase_qcom</span>
extends <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#UHFProtocolParseUrAxBase_qcom--">UHFProtocolParseUrAxBase_qcom</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#closeUhfSendData--">closeUhfSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getAndroidDeviceHardwareVersionSendData--">getAndroidDeviceHardwareVersionSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getAndroidDeviceRebootSendData--">getAndroidDeviceRebootSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getAntSendData--">getAntSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getAntWorkTimeSendData-byte-">getAntWorkTimeSendData</a></span>(byte&nbsp;antnum)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getAxGPIOInputStatusSendData--">getAxGPIOInputStatusSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getBeepSendData-boolean-">getBeepSendData</a></span>(boolean&nbsp;isOpen)</code>
<div class="block">获取设置蜂鸣器的发送数据</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getEthernetIpAssignModeSendData--">getEthernetIpAssignModeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getEthernetIpConfigSendData--">getEthernetIpConfigSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getEthernetIpv6ConfigSendData--">getEthernetIpv6ConfigSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getReaderBeepStatusSendData--">getReaderBeepStatusSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getUHFCurrentIpConfigSendData--">getUHFCurrentIpConfigSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getUpgradeTcpServiceVersionSendData--">getUpgradeTcpServiceVersionSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getWifiInfoSendData--">getWifiInfoSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getWifiIpConfigSendData--">getWifiIpConfigSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#getWifiIpv6ConfigSendData--">getWifiIpv6ConfigSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#openUhfSendData--">openUhfSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#openWifiSendData-boolean-">openWifiSendData</a></span>(boolean&nbsp;open)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#outputOnAndOffSendData-byte:A-">outputOnAndOffSendData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseA4GPIOInputStatusData-byte:A-">parseA4GPIOInputStatusData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseAndroidDeviceHardwareVersionData-byte:A-">parseAndroidDeviceHardwareVersionData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseAndroidDeviceRebootData-byte:A-">parseAndroidDeviceRebootData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseBeepData-byte:A-">parseBeepData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析设置蜂鸣器返回的数据</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseCloseUhf-byte:A-">parseCloseUhf</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseErrorCodeData-byte:A-">parseErrorCodeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseEthernetConfigInfoData-byte:A-">parseEthernetConfigInfoData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseGetAntData-byte:A-">parseGetAntData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseGetAntWorkTimeReceiveData-byte:A-">parseGetAntWorkTimeReceiveData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseGetEthernetIpConfigData-byte:A-">parseGetEthernetIpConfigData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseGetEthernetIpv6ConfigResultData-byte:A-">parseGetEthernetIpv6ConfigResultData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseGetReaderBeepStatusData-byte:A-">parseGetReaderBeepStatusData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseGetUpgradeTcpServiceVersionData-byte:A-">parseGetUpgradeTcpServiceVersionData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseGetWifiIpConfigData-byte:A-">parseGetWifiIpConfigData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseGetWifiIpv6ConfigResultData-byte:A-">parseGetWifiIpv6ConfigResultData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseOpenUhf-byte:A-">parseOpenUhf</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseOpenWifiData-byte:A-">parseOpenWifiData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseOutputOnAndOffData-byte:A-">parseOutputOnAndOffData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parsePortData-byte:A-">parsePortData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseReadMacData-byte:A-">parseReadMacData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseSetAntBlinkData-byte:A-">parseSetAntBlinkData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseSetAntData-byte:A-">parseSetAntData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseSetAntWorkTimeReceiveData-byte:A-">parseSetAntWorkTimeReceiveData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseSetBuzzeroffOfAndroidData-byte:A-">parseSetBuzzeroffOfAndroidData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseSetBuzzerOnOfAndroidData-byte:A-">parseSetBuzzerOnOfAndroidData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseSetEthernetDynamicResultData-byte:A-">parseSetEthernetDynamicResultData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseStartUpgradeTcpServiceData-byte:A-">parseStartUpgradeTcpServiceData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseStartUpgradeUhfData-byte:A-">parseStartUpgradeUhfData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseUHFCurrentIpConfigResultData-byte:A-">parseUHFCurrentIpConfigResultData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseWifiConfigInfoData-byte:A-">parseWifiConfigInfoData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#parseWifiInfoData-byte:A-">parseWifiInfoData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#readMacSendData--">readMacSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#setAntBlinkSendData-byte:A-">setAntBlinkSendData</a></span>(byte[]&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#setAntSendData-char-byte:A-">setAntSendData</a></span>(char&nbsp;saveflag,
              byte[]&nbsp;antStatus)</code>&nbsp;</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#setAntWorkTimeSendData-byte-int-">setAntWorkTimeSendData</a></span>(byte&nbsp;antnum,
                      int&nbsp;WorkTime)</code>&nbsp;</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#setBuzzerOffOfAndroidSendData--">setBuzzerOffOfAndroidSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#setBuzzerOnOfAndroidSendData-int-">setBuzzerOnOfAndroidSendData</a></span>(int&nbsp;time)</code>&nbsp;</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#setEthernetConfigInfoSendData-com.rscja.deviceapi.entity.ReaderIPEntity-">setEthernetConfigInfoSendData</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#setEthernetDynamicSendData--">setEthernetDynamicSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#setPortSendData-int-">setPortSendData</a></span>(int&nbsp;port)</code>&nbsp;</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#setPowerSendData-int-int-">setPowerSendData</a></span>(int&nbsp;ant,
                int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#setWifiConfigInfoSendData-com.rscja.deviceapi.entity.WifiConfig-">setWifiConfigInfoSendData</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a>&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#startUpgradeTcpServiceSendData-long-byte:A-">startUpgradeTcpServiceSendData</a></span>(long&nbsp;fileLen,
                              byte[]&nbsp;md5)</code>&nbsp;</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#startUpgradeUhfSendData-long-byte:A-">startUpgradeUhfSendData</a></span>(long&nbsp;fileLen,
                       byte[]&nbsp;md5)</code>&nbsp;</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html#writeMacSendData-byte:A-">writeMacSendData</a></span>(byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.qcom.deviceapi.UHFProtocolParseByJava">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.qcom.deviceapi.<a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></h3>
<code><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#barcodeScanTimeOutSend--">barcodeScanTimeOutSend</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#blinkOfLedSendData-int-int-int-">blinkOfLedSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#blockEraseDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-">blockEraseDataSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#blockWriteDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-java.lang.String-">blockWriteDataSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#btDeleteAllTagToFlashSendData--">btDeleteAllTagToFlashSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#btGetAllTagNumFromFlashSendData--">btGetAllTagNumFromFlashSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#btGetNewTagNumFromFlashSendData--">btGetNewTagNumFromFlashSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#btGetTagDataFromFlashSendData--">btGetTagDataFromFlashSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#closeLedSendData--">closeLedSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#enableBarcodeACK-boolean-">enableBarcodeACK</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#GBTagLockSendData-java.lang.String-char-int-int-java.lang.String-char-char-char-">GBTagLockSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getBatterySendData--">getBatterySendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getCWSendData--">getCWSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getEPCTIDModeSendData-char-char-">getEPCTIDModeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getFastIDSendData--">getFastIDSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getFrequencyModeSendData--">getFrequencyModeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getGen2SendData--">getGen2SendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getInstance--">getInstance</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getInventorySingleTagSendData--">getInventorySingleTagSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getInventorySingleTagSendData-byte:A-">getInventorySingleTagSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getKillSendData-java.lang.String-int-int-int-java.lang.String-">getKillSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getLockSendData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">getLockSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getParameterSendData-byte:A-">getParameterSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getPowerSendData--">getPowerSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getProtocolSendData--">getProtocolSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getReaderAwaitSleepTimeSendData--">getReaderAwaitSleepTimeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getReadSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-">getReadSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getReadTagSendData--">getReadTagSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getRFLinkSendData--">getRFLinkSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getScanBarcodeInBlinkModeSendData--">getScanBarcodeInBlinkModeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getScanBarcodeInTriggleModeSendData--">getScanBarcodeInTriggleModeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getScanBarcodeSendData--">getScanBarcodeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getSetSoftResetSendData--">getSetSoftResetSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStartInventoryTagSendData--">getStartInventoryTagSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStartInventoryTagSendData-byte:A-">getStartInventoryTagSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getSTM32VersionSendData--">getSTM32VersionSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStopInventorySendData--">getStopInventorySendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStopScanBarcodeInBlinkModeSendData--">getStopScanBarcodeInBlinkModeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStopScanBarcodeInTriggleModeSendData--">getStopScanBarcodeInTriggleModeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getTagfocusSendData--">getTagfocusSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getTemperatureSendData--">getTemperatureSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getUserSettingSendData-byte-">getUserSettingSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getVersionSendData--">getVersionSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getWriteSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">getWriteSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#isBarcodeDataContainSSIIDSend--">isBarcodeDataContainSSIIDSend</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#isBarcodeDataContainTypeSend--">isBarcodeDataContainTypeSend</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#makeSendData-int-byte:A-">makeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#openLedSendData--">openLedSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBarcodeData-byte:A-">parseBarcodeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBatteryData-byte:A-">parseBatteryData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBlinkOfLedData-byte:A-">parseBlinkOfLedData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBlockEraseDataData-byte:A-">parseBlockEraseDataData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBlockWriteData-byte:A-">parseBlockWriteData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBtDeleteAllTagToFlashData-byte:A-">parseBtDeleteAllTagToFlashData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBtGetAllTagNumFromFlashData-byte:A-">parseBtGetAllTagNumFromFlashData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBtGetNewTagNumFromFlashData-byte:A-">parseBtGetNewTagNumFromFlashData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBtGetTagDataFromFlashData-byte:A-">parseBtGetTagDataFromFlashData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseCloseLedData-byte:A-">parseCloseLedData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseContinuousInventoryTagData-byte:A-">parseContinuousInventoryTagData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseContinuousInventoryTagData-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-com.rscja.deviceapi.entity.TagInfoRule-">parseContinuousInventoryTagData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseFastIdData-byte:A-">parseFastIdData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGBTagLockData-byte:A-">parseGBTagLockData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetCWData-byte:A-">parseGetCWData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetEPCTIDModeData-byte:A-">parseGetEPCTIDModeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetFastIdData-byte:A-">parseGetFastIdData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetFrequencyModeData-byte:A-">parseGetFrequencyModeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetGen2Data-byte:A-">parseGetGen2Data</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetParameterData-byte:A-byte:A-">parseGetParameterData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetPowerAllData-byte:A-">parseGetPowerAllData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetPowerData-byte:A-">parseGetPowerData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetProtocolData-byte:A-">parseGetProtocolData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetReaderAwaitSleepTimeData-byte:A-">parseGetReaderAwaitSleepTimeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetRFLinkData-byte:A-">parseGetRFLinkData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetTagfocusData-byte:A-">parseGetTagfocusData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetUserSettingData-byte:A-">parseGetUserSettingData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseInventorySingleTagData-byte:A-">parseInventorySingleTagData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseisBarcodeDataContainSSIIDData-byte:A-">parseisBarcodeDataContainSSIIDData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseisBarcodeDataContainTypeData-byte:A-">parseisBarcodeDataContainTypeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseisBarcodeScanTimeOutData-byte:A-">parseisBarcodeScanTimeOutData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseKillData-byte:A-">parseKillData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseLockData-byte:A-">parseLockData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseOpenLedData-byte:A-">parseOpenLedData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseReadData-byte:A-">parseReadData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseReadTagData_EPC-byte:A-">parseReadTagData_EPC</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseReadTagDataEPC_TID_USER-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-com.rscja.deviceapi.entity.TagInfoRule-">parseReadTagDataEPC_TID_USER</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parserUhfTagBuff_EPC_TID_USER-byte:A-boolean-">parserUhfTagBuff_EPC_TID_USER</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parserUhfTagBuff_EPC_TID_USER-byte:A-com.rscja.deviceapi.entity.TagInfoRule-">parserUhfTagBuff_EPC_TID_USER</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseScanBarcodeInBlinkModeData-byte:A-">parseScanBarcodeInBlinkModeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseScanBarcodeInTriggleModeData-byte:A-">parseScanBarcodeInTriggleModeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetBarcodeSSIDData-byte:A-">parseSetBarcodeSSIDData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetBarcodeTypeData-byte:A-">parseSetBarcodeTypeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetBeepTimeOfDuration-byte:A-">parseSetBeepTimeOfDuration</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetCWData-byte:A-">parseSetCWData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetEPCAndTIDModeData-byte:A-">parseSetEPCAndTIDModeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetEPCAndTIDUserModeData-byte:A-">parseSetEPCAndTIDUserModeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetEPCModeData-byte:A-">parseSetEPCModeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetFilterData-byte:A-">parseSetFilterData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetFrequencyModeData-byte:A-">parseSetFrequencyModeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetGen2Data-byte:A-">parseSetGen2Data</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetJumpFrequencyData-byte:A-">parseSetJumpFrequencyData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetParameterData-byte:A-byte:A-byte:A-">parseSetParameterData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetPowerData-byte:A-">parseSetPowerData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetPowerOnDynamicData-byte:A-">parseSetPowerOnDynamicData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetProtocolData-byte:A-">parseSetProtocolData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetR6WorkModeData-byte:A-">parseSetR6WorkModeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetReaderAwaitSleepTimeData-byte:A-">parseSetReaderAwaitSleepTimeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetRFLinkData-byte:A-">parseSetRFLinkData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetSoftResetData-byte:A-">parseSetSoftResetData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetTagfocusData-byte:A-">parseSetTagfocusData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetUserSettingData-byte:A-">parseSetUserSettingData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseStartInventoryTagData-byte:A-">parseStartInventoryTagData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSTM32VersionData-byte:A-">parseSTM32VersionData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseStopInventoryData-byte:A-">parseStopInventoryData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseStopScanBarcodeInBlinkModeData-byte:A-">parseStopScanBarcodeInBlinkModeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseStopScanBarcodeInTriggleModeData-byte:A-">parseStopScanBarcodeInTriggleModeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseTemperatureData-byte:A-">parseTemperatureData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseUHFJump2BootData-byte:A-">parseUHFJump2BootData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseUHFStartUpdateData-byte:A-">parseUHFStartUpdateData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseUHFStopUpdateData-byte:A-">parseUHFStopUpdateData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseUHFUpdatingData-byte:A-">parseUHFUpdatingData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseVersionData-byte:A-">parseVersionData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseWriteData-byte:A-">parseWriteData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#requestGetBeepSendData--">requestGetBeepSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#requestGetEx10SDKFirmware--">requestGetEx10SDKFirmware</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#requestGetFastInventoryMode--">requestGetFastInventoryMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#requestInventoryTempTag-byte:A-">requestInventoryTempTag</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#requestSetFastInventoryMode-boolean-">requestSetFastInventoryMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#responseGetEx10SDKFirmware-byte:A-">responseGetEx10SDKFirmware</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#responseGetFastInventoryMode-byte:A-">responseGetFastInventoryMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#responseSetFastInventoryMode-byte:A-">responseSetFastInventoryMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#respparseBeepData-byte:A-">respparseBeepData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setBarcodeSSIID-boolean-">setBarcodeSSIID</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setBarcodeType-boolean-">setBarcodeType</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setBeepTimeOfDurationSendData-int-">setBeepTimeOfDurationSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setCWSendData-char-">setCWSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setEPCAndTIDModeSendData--">setEPCAndTIDModeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setEPCAndTIDUserModeSendData-int-int-">setEPCAndTIDUserModeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setEPCAndTIDUserModeSendData-int-int-int-">setEPCAndTIDUserModeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setEPCModeSendData--">setEPCModeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setFastIdSendData-int-">setFastIdSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setFilterSendData-char-int-int-java.lang.String-">setFilterSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setFrequencyModeSendData-int-">setFrequencyModeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setGen2SendData-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">setGen2SendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setIndexData-byte:A-">setIndexData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setInventoryMessageModeSendData--">setInventoryMessageModeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setJumpFrequencySendData-int-">setJumpFrequencySendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setParameterSendData-byte:A-byte:A-">setParameterSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setPowerOnDynamicSendData-int-">setPowerOnDynamicSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setPowerSendData-int-">setPowerSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setProtocolSendData-int-">setProtocolSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setR6WorkmodeSendData-char-">setR6WorkmodeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setReaderAwaitSleepTimeSendData-char-">setReaderAwaitSleepTimeSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setRFLinkSendData-int-">setRFLinkSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setTagfocusSendData-char-">setTagfocusSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setUserSettingSendData-byte-byte:A-">setUserSettingSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#stop2DSendData--">stop2DSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#uhfJump2BootSendData-char-">uhfJump2BootSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#uhfStartUpdateSendData--">uhfStartUpdateSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#UHFStopUpdateSendData--">UHFStopUpdateSendData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#uhfUpdatingSendData-byte:A-">uhfUpdatingSendData</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="UHFProtocolParseUrAxBase_qcom--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UHFProtocolParseUrAxBase_qcom</h4>
<pre>public&nbsp;UHFProtocolParseUrAxBase_qcom()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="openUhfSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openUhfSendData</h4>
<pre>public&nbsp;byte[]&nbsp;openUhfSendData()</pre>
</li>
</ul>
<a name="parseOpenUhf-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseOpenUhf</h4>
<pre>public&nbsp;boolean&nbsp;parseOpenUhf(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="closeUhfSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeUhfSendData</h4>
<pre>public&nbsp;byte[]&nbsp;closeUhfSendData()</pre>
</li>
</ul>
<a name="parseCloseUhf-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseCloseUhf</h4>
<pre>public&nbsp;boolean&nbsp;parseCloseUhf(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setAntSendData-char-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAntSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setAntSendData(char&nbsp;saveflag,
                             byte[]&nbsp;antStatus)</pre>
</li>
</ul>
<a name="parseSetAntData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetAntData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetAntData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getAntSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAntSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getAntSendData()</pre>
</li>
</ul>
<a name="parseGetAntData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetAntData</h4>
<pre>public&nbsp;byte[]&nbsp;parseGetAntData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getReaderBeepStatusSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReaderBeepStatusSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getReaderBeepStatusSendData()</pre>
</li>
</ul>
<a name="parseGetReaderBeepStatusData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetReaderBeepStatusData</h4>
<pre>public&nbsp;int&nbsp;parseGetReaderBeepStatusData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getBeepSendData-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBeepSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getBeepSendData(boolean&nbsp;isOpen)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getBeepSendData-boolean-">IUHFProtocolParse</a></code></span></div>
<div class="block">获取设置蜂鸣器的发送数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getBeepSendData-boolean-">getBeepSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getBeepSendData-boolean-">getBeepSendData</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>isOpen</code> - true:表示打开蜂鸣器， false:表示关闭蜂鸣器</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseBeepData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBeepData</h4>
<pre>public&nbsp;boolean&nbsp;parseBeepData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBeepData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析设置蜂鸣器返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBeepData-byte:A-">parseBeepData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBeepData-byte:A-">parseBeepData</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 设置蜂鸣器返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true: 设置成功  ,flase:设置设备</dd>
</dl>
</li>
</ul>
<a name="getAndroidDeviceRebootSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAndroidDeviceRebootSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getAndroidDeviceRebootSendData()</pre>
</li>
</ul>
<a name="parseAndroidDeviceRebootData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseAndroidDeviceRebootData</h4>
<pre>public&nbsp;boolean&nbsp;parseAndroidDeviceRebootData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setBuzzerOffOfAndroidSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuzzerOffOfAndroidSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setBuzzerOffOfAndroidSendData()</pre>
</li>
</ul>
<a name="parseSetBuzzeroffOfAndroidData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetBuzzeroffOfAndroidData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetBuzzeroffOfAndroidData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setBuzzerOnOfAndroidSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuzzerOnOfAndroidSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setBuzzerOnOfAndroidSendData(int&nbsp;time)</pre>
</li>
</ul>
<a name="parseSetBuzzerOnOfAndroidData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetBuzzerOnOfAndroidData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetBuzzerOnOfAndroidData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getAxGPIOInputStatusSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAxGPIOInputStatusSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getAxGPIOInputStatusSendData()</pre>
</li>
</ul>
<a name="parseA4GPIOInputStatusData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseA4GPIOInputStatusData</h4>
<pre>public&nbsp;int&nbsp;parseA4GPIOInputStatusData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setAntWorkTimeSendData-byte-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAntWorkTimeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setAntWorkTimeSendData(byte&nbsp;antnum,
                                     int&nbsp;WorkTime)</pre>
</li>
</ul>
<a name="parseSetAntWorkTimeReceiveData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetAntWorkTimeReceiveData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetAntWorkTimeReceiveData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="parseGetAntWorkTimeReceiveData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetAntWorkTimeReceiveData</h4>
<pre>public&nbsp;int&nbsp;parseGetAntWorkTimeReceiveData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getAntWorkTimeSendData-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAntWorkTimeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getAntWorkTimeSendData(byte&nbsp;antnum)</pre>
</li>
</ul>
<a name="startUpgradeTcpServiceSendData-long-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startUpgradeTcpServiceSendData</h4>
<pre>public&nbsp;byte[]&nbsp;startUpgradeTcpServiceSendData(long&nbsp;fileLen,
                                             byte[]&nbsp;md5)</pre>
</li>
</ul>
<a name="parseStartUpgradeTcpServiceData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseStartUpgradeTcpServiceData</h4>
<pre>public&nbsp;boolean&nbsp;parseStartUpgradeTcpServiceData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getUpgradeTcpServiceVersionSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUpgradeTcpServiceVersionSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getUpgradeTcpServiceVersionSendData()</pre>
</li>
</ul>
<a name="parseGetUpgradeTcpServiceVersionData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetUpgradeTcpServiceVersionData</h4>
<pre>public&nbsp;java.lang.String&nbsp;parseGetUpgradeTcpServiceVersionData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="outputOnAndOffSendData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>outputOnAndOffSendData</h4>
<pre>public&nbsp;byte[]&nbsp;outputOnAndOffSendData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="parseOutputOnAndOffData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseOutputOnAndOffData</h4>
<pre>public&nbsp;boolean&nbsp;parseOutputOnAndOffData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setEthernetConfigInfoSendData-com.rscja.deviceapi.entity.ReaderIPEntity-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEthernetConfigInfoSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setEthernetConfigInfoSendData(<a href="../../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;inData)</pre>
</li>
</ul>
<a name="parseEthernetConfigInfoData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseEthernetConfigInfoData</h4>
<pre>public&nbsp;boolean&nbsp;parseEthernetConfigInfoData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setWifiConfigInfoSendData-com.rscja.deviceapi.entity.WifiConfig-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWifiConfigInfoSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setWifiConfigInfoSendData(<a href="../../../../../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a>&nbsp;inData)</pre>
</li>
</ul>
<a name="parseWifiConfigInfoData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseWifiConfigInfoData</h4>
<pre>public&nbsp;boolean&nbsp;parseWifiConfigInfoData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setPortSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPortSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setPortSendData(int&nbsp;port)</pre>
</li>
</ul>
<a name="parsePortData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parsePortData</h4>
<pre>public&nbsp;boolean&nbsp;parsePortData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="startUpgradeUhfSendData-long-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startUpgradeUhfSendData</h4>
<pre>public&nbsp;byte[]&nbsp;startUpgradeUhfSendData(long&nbsp;fileLen,
                                      byte[]&nbsp;md5)</pre>
</li>
</ul>
<a name="parseStartUpgradeUhfData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseStartUpgradeUhfData</h4>
<pre>public&nbsp;boolean&nbsp;parseStartUpgradeUhfData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getWifiInfoSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWifiInfoSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getWifiInfoSendData()</pre>
</li>
</ul>
<a name="parseWifiInfoData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseWifiInfoData</h4>
<pre>public&nbsp;byte[]&nbsp;parseWifiInfoData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="openWifiSendData-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openWifiSendData</h4>
<pre>public&nbsp;byte[]&nbsp;openWifiSendData(boolean&nbsp;open)</pre>
</li>
</ul>
<a name="parseOpenWifiData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseOpenWifiData</h4>
<pre>public&nbsp;boolean&nbsp;parseOpenWifiData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getAndroidDeviceHardwareVersionSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAndroidDeviceHardwareVersionSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getAndroidDeviceHardwareVersionSendData()</pre>
</li>
</ul>
<a name="parseAndroidDeviceHardwareVersionData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseAndroidDeviceHardwareVersionData</h4>
<pre>public&nbsp;java.lang.String&nbsp;parseAndroidDeviceHardwareVersionData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getEthernetIpConfigSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEthernetIpConfigSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getEthernetIpConfigSendData()</pre>
</li>
</ul>
<a name="parseGetEthernetIpConfigData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetEthernetIpConfigData</h4>
<pre>public&nbsp;java.lang.String&nbsp;parseGetEthernetIpConfigData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getWifiIpConfigSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWifiIpConfigSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getWifiIpConfigSendData()</pre>
</li>
</ul>
<a name="parseGetWifiIpConfigData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetWifiIpConfigData</h4>
<pre>public&nbsp;java.lang.String&nbsp;parseGetWifiIpConfigData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setAntBlinkSendData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAntBlinkSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setAntBlinkSendData(byte[]&nbsp;ant)</pre>
</li>
</ul>
<a name="parseSetAntBlinkData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetAntBlinkData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetAntBlinkData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="parseErrorCodeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseErrorCodeData</h4>
<pre>public&nbsp;int&nbsp;parseErrorCodeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setPowerSendData-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPowerSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setPowerSendData(int&nbsp;ant,
                               int&nbsp;power)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setPowerSendData-int-int-">setPowerSendData</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></code></dd>
</dl>
</li>
</ul>
<a name="writeMacSendData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeMacSendData</h4>
<pre>public&nbsp;byte[]&nbsp;writeMacSendData(byte[]&nbsp;data)</pre>
</li>
</ul>
<a name="setEthernetDynamicSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEthernetDynamicSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setEthernetDynamicSendData()</pre>
</li>
</ul>
<a name="parseSetEthernetDynamicResultData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetEthernetDynamicResultData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetEthernetDynamicResultData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getEthernetIpAssignModeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEthernetIpAssignModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getEthernetIpAssignModeSendData()</pre>
</li>
</ul>
<a name="getEthernetIpv6ConfigSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEthernetIpv6ConfigSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getEthernetIpv6ConfigSendData()</pre>
</li>
</ul>
<a name="parseGetEthernetIpv6ConfigResultData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetEthernetIpv6ConfigResultData</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;parseGetEthernetIpv6ConfigResultData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getWifiIpv6ConfigSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWifiIpv6ConfigSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getWifiIpv6ConfigSendData()</pre>
</li>
</ul>
<a name="parseGetWifiIpv6ConfigResultData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetWifiIpv6ConfigResultData</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a>&nbsp;parseGetWifiIpv6ConfigResultData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getUHFCurrentIpConfigSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUHFCurrentIpConfigSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getUHFCurrentIpConfigSendData()</pre>
</li>
</ul>
<a name="parseUHFCurrentIpConfigResultData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseUHFCurrentIpConfigResultData</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;parseUHFCurrentIpConfigResultData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="readMacSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readMacSendData</h4>
<pre>public&nbsp;byte[]&nbsp;readMacSendData()</pre>
</li>
</ul>
<a name="parseReadMacData-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>parseReadMacData</h4>
<pre>public&nbsp;java.lang.String&nbsp;parseReadMacData(byte[]&nbsp;inData)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFProtocolParseUrAxBase_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA8_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html" title="class in com.rscja.team.qcom.rs232utils"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" target="_top">Frames</a></li>
<li><a href="UHFProtocolParseUrAxBase_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
