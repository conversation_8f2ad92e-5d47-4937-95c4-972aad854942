<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>HFBase</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HFBase";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HFBase.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/qcom/r1/hf/HFBase.html" target="_top">Frames</a></li>
<li><a href="HFBase.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.r1.hf</div>
<h2 title="Class HFBase" class="title">Class HFBase</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.r1.hf.HFBase</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../../../com/rscja/team/qcom/r1/hf/HF14443A.html" title="class in com.rscja.team.qcom.r1.hf">HF14443A</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HF14443B.html" title="class in com.rscja.team.qcom.r1.hf">HF14443B</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf">HF15693</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HFBase</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_15693_SELECT">COMMAND_15693_SELECT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ACTIVATEIDLE">COMMAND_ACTIVATEIDLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ANTICOLL">COMMAND_ANTICOLL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ANTICOLLINVENTORY">COMMAND_ANTICOLLINVENTORY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_AUTHENTICATION">COMMAND_AUTHENTICATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_COS_COMMAND">COMMAND_COS_COMMAND</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_DECREMENT">COMMAND_DECREMENT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_FM1216RESET">COMMAND_FM1216RESET</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_GET_MUL_BLOCK">COMMAND_GET_MUL_BLOCK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_GET_SYSTEM_INFO">COMMAND_GET_SYSTEM_INFO</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_HALT">COMMAND_HALT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_HALT_TYPEB">COMMAND_HALT_TYPEB</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_INCREMENT">COMMAND_INCREMENT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_INITVAL">COMMAND_INITVAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_INVENTORY">COMMAND_INVENTORY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_LOCK_AFI">COMMAND_LOCK_AFI</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_LOCK_BLOCK">COMMAND_LOCK_BLOCK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_LOCK_DSFID">COMMAND_LOCK_DSFID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_READ">COMMAND_READ</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_READ_SM">COMMAND_READ_SM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_READVAL">COMMAND_READVAL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_REQUESTA">COMMAND_REQUESTA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_RESET_TO_READY">COMMAND_RESET_TO_READY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_RESTORE">COMMAND_RESTORE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_SELECT">COMMAND_SELECT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TRANSFER">COMMAND_TRANSFER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TRANSFERCOMMAND">COMMAND_TRANSFERCOMMAND</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TYPEARATS">COMMAND_TYPEARATS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TYPEAREST">COMMAND_TYPEAREST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TYPEB_GET_UID">COMMAND_TYPEB_GET_UID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TYPEBREST">COMMAND_TYPEBREST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ULANTICOLL">COMMAND_ULANTICOLL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ULWRITE">COMMAND_ULWRITE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_WRITE">COMMAND_WRITE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_WRITE_AFI">COMMAND_WRITE_AFI</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_WRITE_DSFID">COMMAND_WRITE_DSFID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_WRITE_SM">COMMAND_WRITE_SM</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#HFBase--">HFBase</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="COMMAND_REQUESTA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_REQUESTA</h4>
<pre>public final&nbsp;int COMMAND_REQUESTA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_REQUESTA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_ACTIVATEIDLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_ACTIVATEIDLE</h4>
<pre>public final&nbsp;int COMMAND_ACTIVATEIDLE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_ACTIVATEIDLE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_ANTICOLL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_ANTICOLL</h4>
<pre>public final&nbsp;int COMMAND_ANTICOLL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_ANTICOLL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_SELECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_SELECT</h4>
<pre>public final&nbsp;int COMMAND_SELECT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_SELECT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_HALT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_HALT</h4>
<pre>public final&nbsp;int COMMAND_HALT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_HALT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_AUTHENTICATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_AUTHENTICATION</h4>
<pre>public final&nbsp;int COMMAND_AUTHENTICATION</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_AUTHENTICATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_READ">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_READ</h4>
<pre>public final&nbsp;int COMMAND_READ</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_READ">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_WRITE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_WRITE</h4>
<pre>public final&nbsp;int COMMAND_WRITE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_WRITE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_INITVAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_INITVAL</h4>
<pre>public final&nbsp;int COMMAND_INITVAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_INITVAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_READVAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_READVAL</h4>
<pre>public final&nbsp;int COMMAND_READVAL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_READVAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_DECREMENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_DECREMENT</h4>
<pre>public final&nbsp;int COMMAND_DECREMENT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_DECREMENT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_INCREMENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_INCREMENT</h4>
<pre>public final&nbsp;int COMMAND_INCREMENT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_INCREMENT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_RESTORE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_RESTORE</h4>
<pre>public final&nbsp;int COMMAND_RESTORE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_RESTORE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_TRANSFER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_TRANSFER</h4>
<pre>public final&nbsp;int COMMAND_TRANSFER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_TRANSFER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_ULANTICOLL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_ULANTICOLL</h4>
<pre>public final&nbsp;int COMMAND_ULANTICOLL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_ULANTICOLL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_ULWRITE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_ULWRITE</h4>
<pre>public final&nbsp;int COMMAND_ULWRITE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_ULWRITE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_FM1216RESET">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_FM1216RESET</h4>
<pre>public final&nbsp;int COMMAND_FM1216RESET</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_FM1216RESET">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_TYPEARATS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_TYPEARATS</h4>
<pre>public final&nbsp;int COMMAND_TYPEARATS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_TYPEARATS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_TYPEAREST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_TYPEAREST</h4>
<pre>public final&nbsp;int COMMAND_TYPEAREST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_TYPEAREST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_HALT_TYPEB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_HALT_TYPEB</h4>
<pre>public final&nbsp;int COMMAND_HALT_TYPEB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_HALT_TYPEB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_TYPEBREST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_TYPEBREST</h4>
<pre>public final&nbsp;int COMMAND_TYPEBREST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_TYPEBREST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_TYPEB_GET_UID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_TYPEB_GET_UID</h4>
<pre>public final&nbsp;int COMMAND_TYPEB_GET_UID</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_TYPEB_GET_UID">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_COS_COMMAND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_COS_COMMAND</h4>
<pre>public final&nbsp;int COMMAND_COS_COMMAND</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_COS_COMMAND">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_INVENTORY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_INVENTORY</h4>
<pre>public final&nbsp;int COMMAND_INVENTORY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_INVENTORY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_GET_SYSTEM_INFO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_GET_SYSTEM_INFO</h4>
<pre>public final&nbsp;int COMMAND_GET_SYSTEM_INFO</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_GET_SYSTEM_INFO">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_READ_SM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_READ_SM</h4>
<pre>public final&nbsp;int COMMAND_READ_SM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_READ_SM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_WRITE_SM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_WRITE_SM</h4>
<pre>public final&nbsp;int COMMAND_WRITE_SM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_WRITE_SM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_LOCK_BLOCK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_LOCK_BLOCK</h4>
<pre>public final&nbsp;int COMMAND_LOCK_BLOCK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_LOCK_BLOCK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_15693_SELECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_15693_SELECT</h4>
<pre>public final&nbsp;int COMMAND_15693_SELECT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_15693_SELECT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_RESET_TO_READY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_RESET_TO_READY</h4>
<pre>public final&nbsp;int COMMAND_RESET_TO_READY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_RESET_TO_READY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_LOCK_AFI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_LOCK_AFI</h4>
<pre>public final&nbsp;int COMMAND_LOCK_AFI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_LOCK_AFI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_WRITE_DSFID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_WRITE_DSFID</h4>
<pre>public final&nbsp;int COMMAND_WRITE_DSFID</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_WRITE_DSFID">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_LOCK_DSFID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_LOCK_DSFID</h4>
<pre>public final&nbsp;int COMMAND_LOCK_DSFID</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_LOCK_DSFID">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_WRITE_AFI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_WRITE_AFI</h4>
<pre>public final&nbsp;int COMMAND_WRITE_AFI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_WRITE_AFI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_GET_MUL_BLOCK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_GET_MUL_BLOCK</h4>
<pre>public final&nbsp;int COMMAND_GET_MUL_BLOCK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_GET_MUL_BLOCK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_TRANSFERCOMMAND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMMAND_TRANSFERCOMMAND</h4>
<pre>public final&nbsp;int COMMAND_TRANSFERCOMMAND</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_TRANSFERCOMMAND">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="COMMAND_ANTICOLLINVENTORY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>COMMAND_ANTICOLLINVENTORY</h4>
<pre>public final&nbsp;int COMMAND_ANTICOLLINVENTORY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.r1.hf.HFBase.COMMAND_ANTICOLLINVENTORY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HFBase--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HFBase</h4>
<pre>public&nbsp;HFBase()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HFBase.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/qcom/r1/hf/HFBase.html" target="_top">Frames</a></li>
<li><a href="HFBase.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.lang.Object">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
