<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>com.rscja.team.qcom.urax Class Hierarchy</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.rscja.team.qcom.urax Class Hierarchy";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/uhfparse/package-tree.html">Prev</a></li>
<li><a href="../../../../../com/rscja/team/qcom/usb/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/urax/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package com.rscja.team.qcom.urax</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.rscja.team.qcom.urax.<a href="../../../../../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax"><span class="typeNameLink">QcomURA4Gpio</span></a> (implements com.rscja.team.qcom.urax.<a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a>)</li>
<li type="circle">com.rscja.team.qcom.urax.<a href="../../../../../com/rscja/team/qcom/urax/QcomURAxDevice.html" title="class in com.rscja.team.qcom.urax"><span class="typeNameLink">QcomURAxDevice</span></a> (implements com.rscja.team.qcom.urax.<a href="../../../../../com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax">IURAxDevice</a>)</li>
<li type="circle">com.rscja.team.qcom.urax.<a href="../../../../../com/rscja/team/qcom/urax/RKURA4C8Device.html" title="class in com.rscja.team.qcom.urax"><span class="typeNameLink">RKURA4C8Device</span></a> (implements com.rscja.team.qcom.urax.<a href="../../../../../com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax">IURAxDevice</a>)</li>
<li type="circle">com.rscja.team.qcom.urax.<a href="../../../../../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax"><span class="typeNameLink">RKURA4Gpio</span></a> (implements com.rscja.team.qcom.urax.<a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax">IURA4Gpio</a>)</li>
<li type="circle">com.rscja.team.qcom.urax.<a href="../../../../../com/rscja/team/qcom/urax/RKURAxDevice.html" title="class in com.rscja.team.qcom.urax"><span class="typeNameLink">RKURAxDevice</span></a> (implements com.rscja.team.qcom.urax.<a href="../../../../../com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax">IURAxDevice</a>)</li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li type="circle">com.rscja.team.qcom.urax.<a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax"><span class="typeNameLink">IURA4Gpio</span></a></li>
<li type="circle">com.rscja.team.qcom.urax.<a href="../../../../../com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax"><span class="typeNameLink">IURAxDevice</span></a></li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/uhfparse/package-tree.html">Prev</a></li>
<li><a href="../../../../../com/rscja/team/qcom/usb/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/urax/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
