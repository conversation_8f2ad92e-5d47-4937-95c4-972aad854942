<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Class com.rscja.team.qcom.deviceapi.HardwareInterface_qcom.FunctionEnum</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.rscja.team.qcom.deviceapi.HardwareInterface_qcom.FunctionEnum";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/qcom/deviceapi/class-use/HardwareInterface_qcom.FunctionEnum.html" target="_top">Frames</a></li>
<li><a href="HardwareInterface_qcom.FunctionEnum.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.rscja.team.qcom.deviceapi.HardwareInterface_qcom.FunctionEnum" class="title">Uses of Class<br>com.rscja.team.qcom.deviceapi.HardwareInterface_qcom.FunctionEnum</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a> in <a href="../../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> that return <a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a></code></td>
<td class="colLast"><span class="typeNameLabel">HardwareInterface_qcom.FunctionEnum.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">HardwareInterface_qcom.FunctionEnum.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> with parameters of type <a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">HardwareInterface_qcom.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html#free-com.rscja.team.qcom.deviceapi.HardwareInterface_qcom.FunctionEnum-">free</a></span>(<a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a>&nbsp;module)</code>
<div class="block">释放模块,模块断电同时关闭串口<br>
 Release module<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">HardwareInterface_qcom.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html#init-com.rscja.team.qcom.deviceapi.HardwareInterface_qcom.FunctionEnum-int-">init</a></span>(<a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a>&nbsp;module,
    int&nbsp;baudrate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">HardwareInterface_qcom.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html#init-com.rscja.team.qcom.deviceapi.HardwareInterface_qcom.FunctionEnum-int-int-int-int-">init</a></span>(<a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a>&nbsp;module,
    int&nbsp;baudrate,
    int&nbsp;databits,
    int&nbsp;stopbits,
    int&nbsp;check)</code>
<div class="block">初始化模块 ,模块上电同时打开串口<br/>
 Initialize the module</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><span class="typeNameLabel">HardwareInterface_qcom.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html#receive-com.rscja.team.qcom.deviceapi.HardwareInterface_qcom.FunctionEnum-">receive</a></span>(<a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a>&nbsp;module)</code>
<div class="block">接收数据,读取串口数据<br>
 Receive data<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">HardwareInterface_qcom.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html#send-com.rscja.team.qcom.deviceapi.HardwareInterface_qcom.FunctionEnum-byte:A-">send</a></span>(<a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a>&nbsp;module,
    byte[]&nbsp;data)</code>
<div class="block">发送数据<br>
 Send data<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><span class="typeNameLabel">HardwareInterface_qcom.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html#sendAndReceive-com.rscja.team.qcom.deviceapi.HardwareInterface_qcom.FunctionEnum-byte:A-">sendAndReceive</a></span>(<a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a>&nbsp;module,
              byte[]&nbsp;sendData)</code>
<div class="block">收发数据<br></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../../com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/qcom/deviceapi/class-use/HardwareInterface_qcom.FunctionEnum.html" target="_top">Frames</a></li>
<li><a href="HardwareInterface_qcom.FunctionEnum.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
