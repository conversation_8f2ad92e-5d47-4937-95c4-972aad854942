<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>SystemPropValues_qcom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SystemPropValues_qcom";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SystemPropValues_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/system/SystemInterfacesFactory_qcom.html" title="class in com.rscja.team.qcom.system"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/system/SystemPropValues_qcom.html" target="_top">Frames</a></li>
<li><a href="SystemPropValues_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.system</div>
<h2 title="Class SystemPropValues_qcom" class="title">Class SystemPropValues_qcom</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.system.SystemPropValues_qcom</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SystemPropValues_qcom</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#getBlackWhiteListState--">getBlackWhiteListState</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html" title="class in com.rscja.team.qcom.system">SystemPropValues_qcom</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#getSN--">getSN</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#isEnable4G--">isEnable4G</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#isEnableBlueth--">isEnableBlueth</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#isEnableCamera--">isEnableCamera</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#isEnableNavigationBar--">isEnableNavigationBar</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#isEnableOTG--">isEnableOTG</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#isEnableScreenshot--">isEnableScreenshot</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#isEnableSystemSettingButton--">isEnableSystemSettingButton</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#isEnableWifi--">isEnableWifi</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#setBlackWhiteListState-int-">setBlackWhiteListState</a></span>(int&nbsp;state)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#setCameraState-boolean-">setCameraState</a></span>(boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#setEnable4G-boolean-">setEnable4G</a></span>(boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#setEnableBlueth-boolean-">setEnableBlueth</a></span>(boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#setEnableNavigationBar-boolean-">setEnableNavigationBar</a></span>(boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#setEnableScreenshot-boolean-">setEnableScreenshot</a></span>(boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#setEnableSystemSettingButton-boolean-">setEnableSystemSettingButton</a></span>(boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#setEnableWifi-boolean-">setEnableWifi</a></span>(boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html#setOTGState-boolean-">setOTGState</a></span>(boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/qcom/system/SystemPropValues_qcom.html" title="class in com.rscja.team.qcom.system">SystemPropValues_qcom</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="setCameraState-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCameraState</h4>
<pre>public&nbsp;boolean&nbsp;setCameraState(boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnableCamera--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableCamera</h4>
<pre>public&nbsp;boolean&nbsp;isEnableCamera()</pre>
</li>
</ul>
<a name="setOTGState-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOTGState</h4>
<pre>public&nbsp;boolean&nbsp;setOTGState(boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnableOTG--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableOTG</h4>
<pre>public&nbsp;boolean&nbsp;isEnableOTG()</pre>
</li>
</ul>
<a name="setBlackWhiteListState-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBlackWhiteListState</h4>
<pre>public&nbsp;boolean&nbsp;setBlackWhiteListState(int&nbsp;state)</pre>
</li>
</ul>
<a name="getBlackWhiteListState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBlackWhiteListState</h4>
<pre>public&nbsp;int&nbsp;getBlackWhiteListState()</pre>
</li>
</ul>
<a name="setEnableWifi-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableWifi</h4>
<pre>public&nbsp;boolean&nbsp;setEnableWifi(boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnableWifi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableWifi</h4>
<pre>public&nbsp;boolean&nbsp;isEnableWifi()</pre>
</li>
</ul>
<a name="setEnableScreenshot-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableScreenshot</h4>
<pre>public&nbsp;boolean&nbsp;setEnableScreenshot(boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnableScreenshot--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableScreenshot</h4>
<pre>public&nbsp;boolean&nbsp;isEnableScreenshot()</pre>
</li>
</ul>
<a name="setEnableSystemSettingButton-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableSystemSettingButton</h4>
<pre>public&nbsp;boolean&nbsp;setEnableSystemSettingButton(boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnableSystemSettingButton--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableSystemSettingButton</h4>
<pre>public&nbsp;boolean&nbsp;isEnableSystemSettingButton()</pre>
</li>
</ul>
<a name="setEnableBlueth-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableBlueth</h4>
<pre>public&nbsp;boolean&nbsp;setEnableBlueth(boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnableBlueth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableBlueth</h4>
<pre>public&nbsp;boolean&nbsp;isEnableBlueth()</pre>
</li>
</ul>
<a name="isEnableNavigationBar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableNavigationBar</h4>
<pre>public&nbsp;boolean&nbsp;isEnableNavigationBar()</pre>
</li>
</ul>
<a name="setEnableNavigationBar-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableNavigationBar</h4>
<pre>public&nbsp;boolean&nbsp;setEnableNavigationBar(boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="isEnable4G--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnable4G</h4>
<pre>public&nbsp;boolean&nbsp;isEnable4G()</pre>
</li>
</ul>
<a name="setEnable4G-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnable4G</h4>
<pre>public&nbsp;boolean&nbsp;setEnable4G(boolean&nbsp;enable)</pre>
</li>
</ul>
<a name="getSN--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getSN</h4>
<pre>public&nbsp;java.lang.String&nbsp;getSN()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SystemPropValues_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/system/SystemInterfacesFactory_qcom.html" title="class in com.rscja.team.qcom.system"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/system/SystemPropValues_qcom.html" target="_top">Frames</a></li>
<li><a href="SystemPropValues_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
