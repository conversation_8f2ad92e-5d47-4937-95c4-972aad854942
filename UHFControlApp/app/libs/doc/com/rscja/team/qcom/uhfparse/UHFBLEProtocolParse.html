<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>UHFBLEProtocolParse</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UHFBLEProtocolParse";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFBLEProtocolParse.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html" target="_top">Frames</a></li>
<li><a href="UHFBLEProtocolParse.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.uhfparse</div>
<h2 title="Class UHFBLEProtocolParse" class="title">Class UHFBLEProtocolParse</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse">com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.uhfparse.UHFBLEProtocolParse</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">UHFBLEProtocolParse</span>
extends <a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.rscja.team.qcom.uhfparse.<a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase</a></h3>
<code><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase.CMDInfo</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html#UHFBLEProtocolParse--">UHFBLEProtocolParse</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html#addCmdList-com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase.CMDInfo-">addCmdList</a></span>(<a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase.CMDInfo</a>&nbsp;info)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html#isBuildFullData-com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase.CMDInfo-">isBuildFullData</a></span>(<a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase.CMDInfo</a>&nbsp;cmdinfo)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html#setkeyEventCallback-com.rscja.deviceapi.interfaces.KeyEventCallback-">setkeyEventCallback</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/KeyEventCallback.html" title="interface in com.rscja.deviceapi.interfaces">KeyEventCallback</a>&nbsp;keyEventCallback)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html#setSupportRssi-boolean-">setSupportRssi</a></span>(boolean&nbsp;isSupportRssi)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.qcom.uhfparse.<a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase</a></h3>
<code><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#addCmd-com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase.CMDInfo-">addCmd</a>, <a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#cleanOldCmd--">cleanOldCmd</a>, <a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#cleanTagCmd--">cleanTagCmd</a>, <a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#getCmdList--">getCmdList</a>, <a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#getCmdList-int-">getCmdList</a>, <a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#getCmdList-int-int-">getCmdList</a>, <a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#getTagCmd--">getTagCmd</a>, <a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#parseData-byte:A-">parseData</a>, <a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="UHFBLEProtocolParse--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UHFBLEProtocolParse</h4>
<pre>public&nbsp;UHFBLEProtocolParse()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setkeyEventCallback-com.rscja.deviceapi.interfaces.KeyEventCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setkeyEventCallback</h4>
<pre>public&nbsp;void&nbsp;setkeyEventCallback(<a href="../../../../../com/rscja/deviceapi/interfaces/KeyEventCallback.html" title="interface in com.rscja.deviceapi.interfaces">KeyEventCallback</a>&nbsp;keyEventCallback)</pre>
</li>
</ul>
<a name="isBuildFullData-com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase.CMDInfo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBuildFullData</h4>
<pre>public&nbsp;boolean&nbsp;isBuildFullData(<a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase.CMDInfo</a>&nbsp;cmdinfo)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#isBuildFullData-com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase.CMDInfo-">isBuildFullData</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase</a></code></dd>
</dl>
</li>
</ul>
<a name="setSupportRssi-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSupportRssi</h4>
<pre>public&nbsp;void&nbsp;setSupportRssi(boolean&nbsp;isSupportRssi)</pre>
</li>
</ul>
<a name="addCmdList-com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase.CMDInfo-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>addCmdList</h4>
<pre>public&nbsp;void&nbsp;addCmdList(<a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase.CMDInfo</a>&nbsp;info)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html#addCmdList-com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase.CMDInfo-">addCmdList</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse">UHFProtocolProtocolParseBase</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFBLEProtocolParse.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../../com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html" target="_top">Frames</a></li>
<li><a href="UHFBLEProtocolParse.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.team.qcom.uhfparse.UHFProtocolProtocolParseBase">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
