<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>RFIDWithISO14443A_qcom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RFIDWithISO14443A_qcom";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":9,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithISO14443A_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A4CPU_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" target="_top">Frames</a></li>
<li><a href="RFIDWithISO14443A_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.deviceapi</div>
<h2 title="Class RFIDWithISO14443A_qcom" class="title">Class RFIDWithISO14443A_qcom</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/Device_qcom.html" title="class in com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi.Device_qcom</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi.RFIDBase_qcom</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.deviceapi.RFIDWithISO14443A_qcom</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDBase</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">RFIDWithISO14443A_qcom</span>
extends <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDBase_qcom</a>
implements <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></pre>
<div class="block">RFID模块ISO14443A协议操作类<br>
 RFID module ISO 14443A protocol operation type<br>
 <p>
 注意： <br>
 Attention:<br>
 1、使用前请确认您的机器已安装此模块。 <br>
 1. Make sure this module is installed before using your device.<br>
 2、要正常使用模块需要在\libs\armeabi\目录放置libDeviceAPI.so文件 <br>
 2. Put libDeviceAPI.so file in directory \libs\armeabi\ then module can be used normally.<br>
 3、在操作设备前需要调用 <b><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#init--"><code>RFIDBase_qcom.init()</code></a></b> 打开设备，使用完后调用 <b><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#free--"><code>RFIDBase_qcom.free()</code></a></b> 关闭设备<br>
 3. call <b><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#init--"><code>RFIDBase_qcom.init()</code></a></b> to switch on device before operating device, call <b><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#free--"><code>RFIDBase_qcom.free()</code></a></b> to switch off device after using.<br></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>liuruifeng</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_AddApp-java.lang.String-int-int-">DESFire_AddApp</a></span>(java.lang.String&nbsp;hexAppId,
              int&nbsp;keySetting,
              int&nbsp;fileNums)</code>
<div class="block">Desfire卡 创建应用<br>
 Desfire card create application<br></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_AddStdFile-int-int-char:A-int-">DESFire_AddStdFile</a></span>(int&nbsp;fileNo,
                  int&nbsp;commSet,
                  char[]&nbsp;accessRight,
                  int&nbsp;fileSize)</code>
<div class="block">Desfire卡 创建标准数据文件<br>
 Desfire card create standard data file<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_AddValueFile-int-int-char:A-int-int-int-">DESFire_AddValueFile</a></span>(int&nbsp;fileNo,
                    int&nbsp;commSet,
                    char[]&nbsp;accessRights,
                    int&nbsp;minValue,
                    int&nbsp;maxValue,
                    int&nbsp;initValue)</code>
<div class="block">Desfire卡 创建值文件<br>
 Desfire card create values file<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_Auth-int-java.lang.String-">DESFire_Auth</a></span>(int&nbsp;keyNo,
            java.lang.String&nbsp;key)</code>
<div class="block">Desfire卡 验证密钥<br>
 Desfire card verify key<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_ChangeFileSetting-int-int-char:A-">DESFire_ChangeFileSetting</a></span>(int&nbsp;fileNo,
                         int&nbsp;commSet,
                         char[]&nbsp;accessRights)</code>
<div class="block">Desfire卡 获取文件设置<br>
 Desfire card acquire file setup<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_ChangeKey-int-java.lang.String-">DESFire_ChangeKey</a></span>(int&nbsp;keyNo,
                 java.lang.String&nbsp;newKey)</code>
<div class="block">Desfire卡 更改密钥<br>
 Desfire card change key<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_ChangeKeySetting-int-">DESFire_ChangeKeySetting</a></span>(int&nbsp;keySetting)</code>
<div class="block">Desfire卡 更改密钥设置<br>
 Desfire card change key setup<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_CreditValueFile-int-int-">DESFire_CreditValueFile</a></span>(int&nbsp;fileNo,
                       int&nbsp;value)</code>
<div class="block">Desfire卡 充值函数<br>
 Desfire card recharge formula</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_DebitValueFile-int-int-">DESFire_DebitValueFile</a></span>(int&nbsp;fileNo,
                      int&nbsp;value)</code>
<div class="block">Desfire卡 扣费函数<br>
 Desfire card deduction formula<br></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_DelApp-java.lang.String-">DESFire_DelApp</a></span>(java.lang.String&nbsp;hexAppId)</code>
<div class="block">Desfire卡 删除应用<br>
 Desfire card delete application<br></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_DelFile-int-">DESFire_DelFile</a></span>(int&nbsp;fileNo)</code>
<div class="block">Desfire卡 删除文件<br>
 Desfire card delete file<br></div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_FormatCard--">DESFire_FormatCard</a></span>()</code>
<div class="block">Desfire卡 格式化卡片<br>
 Desfire card format card<br></div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_GetApps--">DESFire_GetApps</a></span>()</code>
<div class="block">Desfire卡 获取所有应用<br>
 Desfire card acquire all application<br></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_GetFileIds--">DESFire_GetFileIds</a></span>()</code>
<div class="block">Desfire卡 获取应用所有文件ID<br>
 Desfire card acquire application all file ID<br></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity">DESFireFile</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_GetFiles--">DESFire_GetFiles</a></span>()</code>
<div class="block">Desfire卡 获取应用所有文件<br>
 Desfire card acquire all files of application<br></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_GetFileSetting-int-">DESFire_GetFileSetting</a></span>(int&nbsp;fileNo)</code>
<div class="block">Desfire卡 获取文件设置<br>
 Desfire card acquire file setup<br></div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_GetKeySetting--">DESFire_GetKeySetting</a></span>()</code>
<div class="block">Desfire卡 获取密钥设置信息<br>
 Desfire card acquire key setup infor<br></div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_GetPiccInfo--">DESFire_GetPiccInfo</a></span>()</code>
<div class="block">Desfire卡 获取卡片信息<br>
 Desfire card acquire card infor<br></div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_RatsAndPss--">DESFire_RatsAndPss</a></span>()</code>
<div class="block">Desfire卡 进入14443A协议的第4层，获取通讯参数<br>
 Desfire card enter 4th level of 14443A protocol, acquire communication parameter<br></div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_ReadStdFile-int-int-int-">DESFire_ReadStdFile</a></span>(int&nbsp;fileNo,
                   int&nbsp;offSet,
                   int&nbsp;dataSize)</code>
<div class="block">Desfire卡 读标准文件数据<br>
 Desfire card read standard file data<br></div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_ReadValueFile-int-">DESFire_ReadValueFile</a></span>(int&nbsp;fileNo)</code>
<div class="block">Desfire卡 获取值文件内容<br>
 Desfire card acquire values file content<br></div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_SelApp-java.lang.String-">DESFire_SelApp</a></span>(java.lang.String&nbsp;hexAppId)</code>
<div class="block">Desfire卡 选择应用<br>
 Desfire card select application<br></div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_selCpy-int-">DESFire_selCpy</a></span>(int&nbsp;cpyType)</code>
<div class="block">选择加密类型<br>
 select encryption type<br></div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#DESFire_WriteStdFile-int-int-int-char:A-">DESFire_WriteStdFile</a></span>(int&nbsp;fileNo,
                    int&nbsp;offSet,
                    int&nbsp;dataSize,
                    char[]&nbsp;dataBuf)</code>
<div class="block">Desfire卡 写标准文件数据<br>
 Desfire card write standard file data<br></div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443A_qcom</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO14443A协议操作实例<br>
 Acquire ISO14443A protocol operation Instance<br></div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#getIntegerSomeBit-int-int-">getIntegerSomeBit</a></span>(int&nbsp;resource,
                 int&nbsp;mask)</code>
<div class="block">取整数的某一位<br>
 get one place of integer<br></div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#ISO14443A_decrement-int-int-int-">ISO14443A_decrement</a></span>(int&nbsp;iBlockValue,
                   int&nbsp;iBlockResult,
                   int&nbsp;iValue)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#ISO14443A_increment-int-int-int-">ISO14443A_increment</a></span>(int&nbsp;iBlockValue,
                   int&nbsp;iBlockResult,
                   int&nbsp;iValue)</code>
<div class="block">电子钱包充值 <br>
 E-wallet charge</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#ISO14443A_initval-int-int-">ISO14443A_initval</a></span>(int&nbsp;iBlock,
                 int&nbsp;iValue)</code>
<div class="block">电子钱包初始化<br>
 E-wallet initialize</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>int[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#ISO14443A_readval-int-">ISO14443A_readval</a></span>(int&nbsp;iBlock)</code>
<div class="block">读取电子钱包余额 传入参数<br>
 read E-wallet balance</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#M1_ReadData-int-int-">M1_ReadData</a></span>(int&nbsp;sector,
           int&nbsp;block)</code>
<div class="block">读取指定扇区指定block的数据，适用于S50和S70标签。使用此函数的时候，要确保该扇区密钥验证已通过。<br>
 Read data in specified block of specified sector, used for S50 and S70 tag.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#M1_WriteData-int-int-java.lang.String-">M1_WriteData</a></span>(int&nbsp;sector,
            int&nbsp;block,
            java.lang.String&nbsp;hexData)</code>
<div class="block">向指定的扇区的Block 写入数据，适用于S50和S70标签。使用此函数的时候，要确保该扇区密钥验证已通过。<br>
 Write data in specified sector of block, used for S50 and S70 tag.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#read-int-">read</a></span>(int&nbsp;block)</code>
<div class="block">读卡，适用于Urltra light标签<br>
 card reading, used for Urltra light tag<br></div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#read-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-int-int-">read</a></span>(java.lang.String&nbsp;key,
    <a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443A.KeyType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.KeyType</a>&nbsp;keyType,
    int&nbsp;sector,
    int&nbsp;block)</code>
<div class="block">读卡,此函数包含寻卡和验证密钥步骤<br>
 Read card, this formula includes card search and key verfication procedure<br></div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#readAllData-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.TagType-">readAllData</a></span>(java.lang.String&nbsp;key,
           <a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443A.TagType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.TagType</a>&nbsp;tagType)</code>
<div class="block">读卡，读取卡片所有块中的数据<br>
 Card reading, read data of all blocks in the card<br></div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#request--">request</a></span>()</code>
<div class="block">寻卡<br>
 Search card<br></div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#sendBusCMD-char:A-char:A-int-int-">sendBusCMD</a></span>(char[]&nbsp;time,
          char[]&nbsp;uid,
          int&nbsp;uidnum,
          int&nbsp;flag)</code>
<div class="block">/**
 墨西哥公交命令<br>
 Mexico bus commande<br></div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#unvarnished_transfer-byte:A-">unvarnished_transfer</a></span>(byte[]&nbsp;sendData)</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#VerifySector-int-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-">VerifySector</a></span>(int&nbsp;sector,
            java.lang.String&nbsp;key,
            <a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443A.KeyType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.KeyType</a>&nbsp;keyType)</code>
<div class="block">验证扇区。S50和S70标签需要使用到此函数。密钥验证通过才可对该扇区进行读写操作。<br>
 verificate sector.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#write-int-java.lang.String-">write</a></span>(int&nbsp;block,
     java.lang.String&nbsp;hexData)</code>
<div class="block">写卡，适用于Urltra light标签<br>
 Card writing, used for Urltra light tag<br></div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html#write-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-int-int-java.lang.String-">write</a></span>(java.lang.String&nbsp;key,
     <a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443A.KeyType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.KeyType</a>&nbsp;keyType,
     int&nbsp;sector,
     int&nbsp;block,
     java.lang.String&nbsp;hexData)</code>
<div class="block">写卡，此函数包括寻卡和密钥验证异常<br>
 Card writing, this formula includes card searching and key verification err<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.qcom.deviceapi.RFIDBase_qcom">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.qcom.deviceapi.<a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDBase_qcom</a></h3>
<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#free--">free</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#getVersion--">getVersion</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#init--">init</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#init-boolean-">init</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#rfidUpgrade-int-int-int-byte:A-">rfidUpgrade</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.qcom.deviceapi.Device_qcom">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.qcom.deviceapi.<a href="../../../../../com/rscja/team/qcom/deviceapi/Device_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Device_qcom</a></h3>
<code><a href="../../../../../com/rscja/team/qcom/deviceapi/Device_qcom.html#isPowerOn--">isPowerOn</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IRFIDBase">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDBase</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#free--">free</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#getVersion--">getVersion</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#init--">init</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#init-boolean-">init</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#isPowerOn--">isPowerOn</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#rfidUpgrade-int-int-int-byte:A-">rfidUpgrade</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO14443A_qcom</a>&nbsp;getInstance()
                                          throws <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">获取ISO14443A协议操作实例<br>
 Acquire ISO14443A protocol operation Instance<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ISO14443A协议操作实例<br>
 ISO14443A protocol operation Instance<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code> - 配置错误异常<br>
                                Configuration err<br></dd>
</dl>
</li>
</ul>
<a name="request--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>request</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a>&nbsp;request()</pre>
<div class="block">寻卡<br>
 Search card<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#request--">request</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success will return SimpleRFIDEntity data, failure will return Null.<br></dd>
</dl>
</li>
</ul>
<a name="read-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a>&nbsp;read(java.lang.String&nbsp;key,
                             <a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443A.KeyType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.KeyType</a>&nbsp;keyType,
                             int&nbsp;sector,
                             int&nbsp;block)
                      throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDVerificationException.html" title="class in com.rscja.deviceapi.exception">RFIDVerificationException</a>,
                             <a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></pre>
<div class="block">读卡,此函数包含寻卡和验证密钥步骤<br>
 Read card, this formula includes card search and key verfication procedure<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#read-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-int-int-">read</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>key</code> - 密钥<br>
                key<br></dd>
<dd><code>keyType</code> - 密钥类型<br>
                key type<br></dd>
<dd><code>sector</code> - 扇区<br>
                sector<br></dd>
<dd><code>block</code> - 块区<br>
                block<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功则返回 SimpleRFIDEntity数据实体，寻卡失败则返回null<br>
 success will return SimpleRFIDEntity data, card searching failure will return Null.<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDVerificationException.html" title="class in com.rscja.deviceapi.exception">RFIDVerificationException</a></code> - 密钥验证失败异常<br>
                                   key verification failed<br></dd>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></code> - 读卡失败异常<br>
                                   card reading failed<br></dd>
</dl>
</li>
</ul>
<a name="readAllData-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.TagType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readAllData</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a>&nbsp;readAllData(java.lang.String&nbsp;key,
                                    <a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443A.TagType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.TagType</a>&nbsp;tagType)
                             throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDVerificationException.html" title="class in com.rscja.deviceapi.exception">RFIDVerificationException</a>,
                                    <a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></pre>
<div class="block">读卡，读取卡片所有块中的数据<br>
 Card reading, read data of all blocks in the card<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#readAllData-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.TagType-">readAllData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>key</code> - 密钥<br>
                key<br></dd>
<dd><code>tagType</code> - 卡片类型<br>
                card type<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功则返回 SimpleRFIDEntity数据实体，寻卡失败则返回null<br>
 success will return SimpleRFIDEntity data, card searching failure will return Null<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDVerificationException.html" title="class in com.rscja.deviceapi.exception">RFIDVerificationException</a></code> - 密钥验证失败异常<br>
                                   key verification failed<br></dd>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></code> - 读卡失败异常<br>
                                   card reading failed<br></dd>
</dl>
</li>
</ul>
<a name="read-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity">SimpleRFIDEntity</a>&nbsp;read(int&nbsp;block)
                      throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></pre>
<div class="block">读卡，适用于Urltra light标签<br>
 card reading, used for Urltra light tag<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#read-int-">read</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>block</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功则返回 SimpleRFIDEntity数据实体，寻卡失败则返回null<br>
 success will return SimpleRFIDEntity data, card searching failure will return Null<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></code> - 读卡失败异常<br>
                                  Card reading failed<br></dd>
</dl>
</li>
</ul>
<a name="write-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;boolean&nbsp;write(java.lang.String&nbsp;key,
                     <a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443A.KeyType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.KeyType</a>&nbsp;keyType,
                     int&nbsp;sector,
                     int&nbsp;block,
                     java.lang.String&nbsp;hexData)
              throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDVerificationException.html" title="class in com.rscja.deviceapi.exception">RFIDVerificationException</a>,
                     <a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></pre>
<div class="block">写卡，此函数包括寻卡和密钥验证异常<br>
 Card writing, this formula includes card searching and key verification err<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#write-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-int-int-java.lang.String-">write</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>key</code> - 密钥<br>
                key<br></dd>
<dd><code>keyType</code> - 密钥类型<br>
                key type<br></dd>
<dd><code>sector</code> - 扇区<br>
                sector<br></dd>
<dd><code>block</code> - 块区<br>
                block<br></dd>
<dd><code>hexData</code> - 十六进制数据<br>
                hexdecimal data<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true success, false failure<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDVerificationException.html" title="class in com.rscja.deviceapi.exception">RFIDVerificationException</a></code> - 密钥验证失败异常<br>
                                   key verification failed<br></dd>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></code> - 寻卡失败异常<br>
                                   card searching failed<br></dd>
</dl>
</li>
</ul>
<a name="write-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;boolean&nbsp;write(int&nbsp;block,
                     java.lang.String&nbsp;hexData)
              throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></pre>
<div class="block">写卡，适用于Urltra light标签<br>
 Card writing, used for Urltra light tag<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#write-int-java.lang.String-">write</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>block</code> - 块区<br>
                block<br></dd>
<dd><code>hexData</code> - 十六进制数据<br>
                hexdecimal data<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true success, false failure<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDVerificationException.html" title="class in com.rscja.deviceapi.exception">RFIDVerificationException</a></code> - 密钥验证失败异常<br>
                                   key verification failed<br></dd>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></code> - 寻卡失败异常<br>
                                   card searching failed<br></dd>
</dl>
</li>
</ul>
<a name="VerifySector-int-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VerifySector</h4>
<pre>public&nbsp;boolean&nbsp;VerifySector(int&nbsp;sector,
                            java.lang.String&nbsp;key,
                            <a href="../../../../../com/rscja/deviceapi/RFIDWithISO14443A.KeyType.html" title="enum in com.rscja.deviceapi">RFIDWithISO14443A.KeyType</a>&nbsp;keyType)</pre>
<div class="block">验证扇区。S50和S70标签需要使用到此函数。密钥验证通过才可对该扇区进行读写操作。<br>
 verificate sector. S50 and S70 tag need to use this formula. Key verification success then this sector can be used for read-write operation.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#VerifySector-int-java.lang.String-com.rscja.deviceapi.RFIDWithISO14443A.KeyType-">VerifySector</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sector</code> - S50标签扇区取值范围：0 ~ 15 ; S70标签扇区取值范围：0 ~ 39<br>
                S50 tag sector value range: 0 to 15; S70 tag sector value range: 0 to 39<br></dd>
<dd><code>key</code> - 密钥：12个十六进制数<br>
                key: 12 hexdecimal number<br></dd>
<dd><code>keyType</code> - 密钥类型<br>
                key type<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true表示验证通过，false表示验证失败<br>
 true means verfication success, false means verification failed<br></dd>
</dl>
</li>
</ul>
<a name="M1_ReadData-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>M1_ReadData</h4>
<pre>public&nbsp;char[]&nbsp;M1_ReadData(int&nbsp;sector,
                          int&nbsp;block)
                   throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></pre>
<div class="block">读取指定扇区指定block的数据，适用于S50和S70标签。使用此函数的时候，要确保该扇区密钥验证已通过。<br>
 Read data in specified block of specified sector, used for S50 and S70 tag. Make sure key verification of this sector is success when using this formula.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#M1_ReadData-int-int-">M1_ReadData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sector</code> - </dd>
<dd><code>block</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></code></dd>
</dl>
</li>
</ul>
<a name="M1_WriteData-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>M1_WriteData</h4>
<pre>public&nbsp;boolean&nbsp;M1_WriteData(int&nbsp;sector,
                            int&nbsp;block,
                            java.lang.String&nbsp;hexData)</pre>
<div class="block">向指定的扇区的Block 写入数据，适用于S50和S70标签。使用此函数的时候，要确保该扇区密钥验证已通过。<br>
 Write data in specified sector of block, used for S50 and S70 tag.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#M1_WriteData-int-int-java.lang.String-">M1_WriteData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sector</code> - </dd>
<dd><code>block</code> - </dd>
<dd><code>hexData</code> - 16进制字符串</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="DESFire_RatsAndPss--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_RatsAndPss</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_RatsAndPss()</pre>
<div class="block">Desfire卡 进入14443A协议的第4层，获取通讯参数<br>
 Desfire card enter 4th level of 14443A protocol, acquire communication parameter<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_RatsAndPss--">DESFire_RatsAndPss</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功 true，失败 false<br>
 success true, failure false<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_GetKeySetting--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_GetKeySetting</h4>
<pre>public&nbsp;int[]&nbsp;DESFire_GetKeySetting()</pre>
<div class="block">Desfire卡 获取密钥设置信息<br>
 Desfire card acquire key setup infor<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_GetKeySetting--">DESFire_GetKeySetting</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回null表示失败，[0]为密钥设置信息，[1]为密钥数量<br>
 return null means failure, [0] means key setup infor, [1] means key amount<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_ChangeKeySetting-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_ChangeKeySetting</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_ChangeKeySetting(int&nbsp;keySetting)</pre>
<div class="block">Desfire卡 更改密钥设置<br>
 Desfire card change key setup<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_ChangeKeySetting-int-">DESFire_ChangeKeySetting</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>keySetting</code> - 密钥设置信息<br>
                   key setup infor<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功 true，失败 false<br>
 success ture, failure false<br></dd>
</dl>
</li>
</ul>
<a name="getIntegerSomeBit-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIntegerSomeBit</h4>
<pre>public&nbsp;int&nbsp;getIntegerSomeBit(int&nbsp;resource,
                             int&nbsp;mask)</pre>
<div class="block">取整数的某一位<br>
 get one place of integer<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#getIntegerSomeBit-int-int-">getIntegerSomeBit</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resource</code> - 要取某一位的整数<br>
                 get one place of integer<br></dd>
<dd><code>mask</code> - 要取的位置索引，自右至左为0-7<br>
                 get position index, from right to left 0-7<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回某一位的值（0或者1）<br>
 return the value of one place (0 or 1)<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_SelApp-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_SelApp</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_SelApp(java.lang.String&nbsp;hexAppId)</pre>
<div class="block">Desfire卡 选择应用<br>
 Desfire card select application<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_SelApp-java.lang.String-">DESFire_SelApp</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>hexAppId</code> - 应用ID，十六进制字符串<br>
              Apply ID, hexdecimal char string<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功 true，失败 false<br>
 success true, failure false<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_Auth-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_Auth</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_Auth(int&nbsp;keyNo,
                            java.lang.String&nbsp;key)</pre>
<div class="block">Desfire卡 验证密钥<br>
 Desfire card verify key<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_Auth-int-java.lang.String-">DESFire_Auth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>keyNo</code> - 密钥号<br>
              key number<br></dd>
<dd><code>key</code> - 密钥<br>
              key<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功 true，失败 false<br>
 success true, failure false<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_selCpy-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_selCpy</h4>
<pre>public&nbsp;void&nbsp;DESFire_selCpy(int&nbsp;cpyType)</pre>
<div class="block">选择加密类型<br>
 select encryption type<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_selCpy-int-">DESFire_selCpy</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cpyType</code> - 0x01(AES),0x02(DES)</dd>
</dl>
</li>
</ul>
<a name="DESFire_ChangeKey-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_ChangeKey</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_ChangeKey(int&nbsp;keyNo,
                                 java.lang.String&nbsp;newKey)</pre>
<div class="block">Desfire卡 更改密钥<br>
 Desfire card change key<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_ChangeKey-int-java.lang.String-">DESFire_ChangeKey</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>keyNo</code> - 密钥号<br>
              key number<br></dd>
<dd><code>newKey</code> - 密钥<br>
              key<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功 true，失败 false<br>
 success true, failure false<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_GetApps--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_GetApps</h4>
<pre>public&nbsp;java.lang.String[]&nbsp;DESFire_GetApps()</pre>
<div class="block">Desfire卡 获取所有应用<br>
 Desfire card acquire all application<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_GetApps--">DESFire_GetApps</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回null表示失败，获取到的应用ID<br>
 return null means failed, acquire application ID<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_DelApp-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_DelApp</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_DelApp(java.lang.String&nbsp;hexAppId)</pre>
<div class="block">Desfire卡 删除应用<br>
 Desfire card delete application<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_DelApp-java.lang.String-">DESFire_DelApp</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>hexAppId</code> - 应用ID，十六进制字符串<br>
              Application ID, hexdecimal character string<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功 true，失败 false<br>
 success true, failure false<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_FormatCard--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_FormatCard</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_FormatCard()</pre>
<div class="block">Desfire卡 格式化卡片<br>
 Desfire card format card<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_FormatCard--">DESFire_FormatCard</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功 true，失败 false<br>
 success true, failure false<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_GetPiccInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_GetPiccInfo</h4>
<pre>public&nbsp;java.lang.String[]&nbsp;DESFire_GetPiccInfo()</pre>
<div class="block">Desfire卡 获取卡片信息<br>
 Desfire card acquire card infor<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_GetPiccInfo--">DESFire_GetPiccInfo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回null表示失败，获取Picc信息<br>
 return null means failed, acquire Picc infor<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_AddApp-java.lang.String-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_AddApp</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_AddApp(java.lang.String&nbsp;hexAppId,
                              int&nbsp;keySetting,
                              int&nbsp;fileNums)</pre>
<div class="block">Desfire卡 创建应用<br>
 Desfire card create application<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_AddApp-java.lang.String-int-int-">DESFire_AddApp</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>hexAppId</code> - 应用ID，十六进制字符串<br>
                   Apply ID, hexdecimal charactor string<br></dd>
<dd><code>keySetting</code> - 密钥设置<br>
                   key setup<br></dd>
<dd><code>fileNums</code> - 所包含的文件数<br>
                   File amount included<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功 true，失败 false<br>
 success true, failure false<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_GetFileIds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_GetFileIds</h4>
<pre>public&nbsp;int[]&nbsp;DESFire_GetFileIds()</pre>
<div class="block">Desfire卡 获取应用所有文件ID<br>
 Desfire card acquire application all file ID<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_GetFileIds--">DESFire_GetFileIds</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回null表示失败，获取到的文件ID<br>
 return null means failure, acquire file ID<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_GetFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_GetFiles</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity">DESFireFile</a>&gt;&nbsp;DESFire_GetFiles()</pre>
<div class="block">Desfire卡 获取应用所有文件<br>
 Desfire card acquire all files of application<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_GetFiles--">DESFire_GetFiles</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="DESFire_GetFileSetting-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_GetFileSetting</h4>
<pre>public&nbsp;byte[]&nbsp;DESFire_GetFileSetting(int&nbsp;fileNo)</pre>
<div class="block">Desfire卡 获取文件设置<br>
 Desfire card acquire file setup<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_GetFileSetting-int-">DESFire_GetFileSetting</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileNo</code> - 文件号<br>
               File number<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回null表示失败<br>
 return null means failure<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_ChangeFileSetting-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_ChangeFileSetting</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_ChangeFileSetting(int&nbsp;fileNo,
                                         int&nbsp;commSet,
                                         char[]&nbsp;accessRights)</pre>
<div class="block">Desfire卡 获取文件设置<br>
 Desfire card acquire file setup<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_ChangeFileSetting-int-int-char:A-">DESFire_ChangeFileSetting</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileNo</code> - 文件号<br>
                     file number<br></dd>
<dd><code>commSet</code> - 通讯设置<br>
                     communication setup<br></dd>
<dd><code>accessRights</code> - 存取权限<br>
                     save permission<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功 true，失败 false<br>
 success true, failure false<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_DelFile-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_DelFile</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_DelFile(int&nbsp;fileNo)</pre>
<div class="block">Desfire卡 删除文件<br>
 Desfire card delete file<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_DelFile-int-">DESFire_DelFile</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileNo</code> - 文件号<br>
               File number<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success true, failure false<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_AddStdFile-int-int-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_AddStdFile</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_AddStdFile(int&nbsp;fileNo,
                                  int&nbsp;commSet,
                                  char[]&nbsp;accessRight,
                                  int&nbsp;fileSize)</pre>
<div class="block">Desfire卡 创建标准数据文件<br>
 Desfire card create standard data file<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_AddStdFile-int-int-char:A-int-">DESFire_AddStdFile</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileNo</code> - 文件号<br>
                    File number<br></dd>
<dd><code>commSet</code> - 通讯设置<br>
                    communication setup<br></dd>
<dd><code>accessRight</code> - 存取权限<br>
                    save permission<br></dd>
<dd><code>fileSize</code> - 文件大小<br>
                    file size<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功 true，失败 false<br>
 success true, failure false<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_WriteStdFile-int-int-int-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_WriteStdFile</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_WriteStdFile(int&nbsp;fileNo,
                                    int&nbsp;offSet,
                                    int&nbsp;dataSize,
                                    char[]&nbsp;dataBuf)</pre>
<div class="block">Desfire卡 写标准文件数据<br>
 Desfire card write standard file data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_WriteStdFile-int-int-int-char:A-">DESFire_WriteStdFile</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileNo</code> - 文件号<br>
                 File number<br></dd>
<dd><code>offSet</code> - 起始位置偏移量<br>
                 Initial position shift<br></dd>
<dd><code>dataSize</code> - 数据大小<br>
                 Data size<br></dd>
<dd><code>dataBuf</code> - 数据，1~200个字节<br>
                 Data, 1 to 200 byte<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success true, failure false<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_ReadStdFile-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_ReadStdFile</h4>
<pre>public&nbsp;char[]&nbsp;DESFire_ReadStdFile(int&nbsp;fileNo,
                                  int&nbsp;offSet,
                                  int&nbsp;dataSize)</pre>
<div class="block">Desfire卡 读标准文件数据<br>
 Desfire card read standard file data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_ReadStdFile-int-int-int-">DESFire_ReadStdFile</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileNo</code> - 文件号<br>
                 file number<br></dd>
<dd><code>offSet</code> - 起始位置偏移量<br>
                 Initial position shift<br></dd>
<dd><code>dataSize</code> - 数据大小<br>
                 Data size<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>return null means failed<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_AddValueFile-int-int-char:A-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_AddValueFile</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_AddValueFile(int&nbsp;fileNo,
                                    int&nbsp;commSet,
                                    char[]&nbsp;accessRights,
                                    int&nbsp;minValue,
                                    int&nbsp;maxValue,
                                    int&nbsp;initValue)</pre>
<div class="block">Desfire卡 创建值文件<br>
 Desfire card create values file<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_AddValueFile-int-int-char:A-int-int-int-">DESFire_AddValueFile</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileNo</code> - 文件号<br>
                     File number<br></dd>
<dd><code>commSet</code> - 通讯设置<br>
                     Communication setup<br></dd>
<dd><code>accessRights</code> - 存取权限<br>
                     Save permission<br></dd>
<dd><code>minValue</code> - 最小值<br>
                     minValue<br></dd>
<dd><code>maxValue</code> - 最大值<br>
                     maxValue<br></dd>
<dd><code>initValue</code> - 初始值<br>
                     Initial value<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功 true，失败 false<br>
 success true, failure false<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_ReadValueFile-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_ReadValueFile</h4>
<pre>public&nbsp;int[]&nbsp;DESFire_ReadValueFile(int&nbsp;fileNo)</pre>
<div class="block">Desfire卡 获取值文件内容<br>
 Desfire card acquire values file content<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_ReadValueFile-int-">DESFire_ReadValueFile</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileNo</code> - 文件号<br>
               File number<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回null表示失败<br>
 return null means failure<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_CreditValueFile-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_CreditValueFile</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_CreditValueFile(int&nbsp;fileNo,
                                       int&nbsp;value)</pre>
<div class="block">Desfire卡 充值函数<br>
 Desfire card recharge formula</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_CreditValueFile-int-int-">DESFire_CreditValueFile</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileNo</code> - 文件号<br>
               file number<br></dd>
<dd><code>value</code> - 充入的值<br>
               value charged<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功 true，失败 false<br>
 success true, failed false<br></dd>
</dl>
</li>
</ul>
<a name="DESFire_DebitValueFile-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESFire_DebitValueFile</h4>
<pre>public&nbsp;boolean&nbsp;DESFire_DebitValueFile(int&nbsp;fileNo,
                                      int&nbsp;value)</pre>
<div class="block">Desfire卡 扣费函数<br>
 Desfire card deduction formula<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#DESFire_DebitValueFile-int-int-">DESFire_DebitValueFile</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileNo</code> - 文件号<br>
               file number<br></dd>
<dd><code>value</code> - 扣除的值<br>
               value deducted<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功 true，失败 false<br>
 success true, failure false<br></dd>
</dl>
</li>
</ul>
<a name="ISO14443A_increment-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_increment</h4>
<pre>public&nbsp;boolean&nbsp;ISO14443A_increment(int&nbsp;iBlockValue,
                                   int&nbsp;iBlockResult,
                                   int&nbsp;iValue)</pre>
<div class="block">电子钱包充值 <br>
 E-wallet charge</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#ISO14443A_increment-int-int-int-">ISO14443A_increment</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>iBlockValue</code> - 当前金额所在块<br>
                     iBlockValue current value block</dd>
<dd><code>iBlockResult</code> - 充值后剩余金额保存的块<br>
                     saved block of balance after recharge</dd>
<dd><code>iValue</code> - 金额返回值：0为成功，非0为失败状态码<br>
                     figure returned value: 0 means success, NZ means failure status code</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="ISO14443A_decrement-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_decrement</h4>
<pre>public&nbsp;boolean&nbsp;ISO14443A_decrement(int&nbsp;iBlockValue,
                                   int&nbsp;iBlockResult,
                                   int&nbsp;iValue)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#ISO14443A_decrement-int-int-int-">ISO14443A_decrement</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
</dl>
</li>
</ul>
<a name="ISO14443A_initval-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_initval</h4>
<pre>public&nbsp;boolean&nbsp;ISO14443A_initval(int&nbsp;iBlock,
                                 int&nbsp;iValue)</pre>
<div class="block">电子钱包初始化<br>
 E-wallet initialize</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#ISO14443A_initval-int-int-">ISO14443A_initval</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>iBlock</code> - 要写入数据的绝对块号 块号范围 0~63<br>
               absolute block numbers need to write data block number range 0 to 63</dd>
<dd><code>iValue</code> - 初始金额<br>
               iValue initial value</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="ISO14443A_readval-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ISO14443A_readval</h4>
<pre>public&nbsp;int[]&nbsp;ISO14443A_readval(int&nbsp;iBlock)</pre>
<div class="block">读取电子钱包余额 传入参数<br>
 read E-wallet balance</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#ISO14443A_readval-int-">ISO14443A_readval</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>iBlock</code> - iBlock要读取数据的绝对块号 块号范围 0~63<br>
               Block read absolute block number of data block number range: 0 to 63</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>第0个元素是状态位，0表示成功，非零为错误码，如果为错误码，后面无数据2)第1个元素是读取到的余额<br>
 number 0 is status bit, 0 means success, NZ means error code, if it is error code, there is no data after.</dd>
</dl>
</li>
</ul>
<a name="sendBusCMD-char:A-char:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sendBusCMD</h4>
<pre>public&nbsp;char[]&nbsp;sendBusCMD(char[]&nbsp;time,
                         char[]&nbsp;uid,
                         int&nbsp;uidnum,
                         int&nbsp;flag)</pre>
<div class="block">/**
 墨西哥公交命令<br>
 Mexico bus commande<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#sendBusCMD-char:A-char:A-int-int-">sendBusCMD</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>time</code> - 时分秒,3字节<br></dd>
<dd><code>time</code> - hrs. 3 bytes<br></dd>
<dd><code>uid</code> - 卡号缓存<br></dd>
<dd><code>uid</code> - card number cache<br></dd>
<dd><code>uidnum</code> - 卡号 数量<br></dd>
<dd><code>uidnum</code> - card number amount<br></dd>
<dd><code>flag</code> - 0：无上下车信息， 1： 上车 ，2 ：下车<br></dd>
<dd><code>flag</code> - 0: no data of get on and off, 1: get on, 2: get off<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>第一字节： 0：成功， 其他失败;<br>
 1st byte: 0: success, others: failure<br>
 第二字节：长度， 后续数据长度;<br>
 2nd byte: length, follow-up data length<br>
 第三字节： 0：无上下车信息， 1： 上车 ，2 ：下车;<br>
 3rd byte: 0: no data of get on and off, 1: get on, 2: get off;<br>
 第四字节---：为卡金信息（4字节卡号+2字节金额）的数据集 ，长度为  第二字节减一<br>
 4th byte--- : data set of amount in card (4 bytes card number+ 2 bytes amount), length is 2nd byte minus one<br></dd>
</dl>
</li>
</ul>
<a name="unvarnished_transfer-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>unvarnished_transfer</h4>
<pre>public&nbsp;byte[]&nbsp;unvarnished_transfer(byte[]&nbsp;sendData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html#unvarnished_transfer-byte:A-">unvarnished_transfer</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO14443A</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithISO14443A_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443A4CPU_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" target="_top">Frames</a></li>
<li><a href="RFIDWithISO14443A_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
