<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>UsbPL2302</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UsbPL2302";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UsbPL2302.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/qcom/usb/pl2302/UsbPL2302.html" target="_top">Frames</a></li>
<li><a href="UsbPL2302.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.team.qcom.usb.UsbBase_qcom">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.rscja.team.qcom.usb.UsbBase_qcom">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.usb.pl2302</div>
<h2 title="Class UsbPL2302" class="title">Class UsbPL2302</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">com.rscja.team.qcom.usb.UsbBase_qcom</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.usb.pl2302.UsbPL2302</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../../../com/rscja/team/qcom/usb/R1HFUSB.html" title="class in com.rscja.team.qcom.usb">R1HFUSB</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">UsbPL2302</span>
extends <a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></pre>
<div class="block">Created by xxj on 01/15.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.rscja.team.qcom.usb.UsbBase_qcom">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.rscja.team.qcom.usb.<a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></h3>
<code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.DataCallback.html" title="interface in com.rscja.team.qcom.usb">UsbBase_qcom.DataCallback</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.team.qcom.usb.UsbBase_qcom">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.rscja.team.qcom.usb.<a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></h3>
<code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html#dataCallback">dataCallback</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html#UsbPL2302--">UsbPL2302</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>android.hardware.usb.UsbDevice</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html#closeport--">closeport</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html#getConnectionStatus--">getConnectionStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>android.hardware.usb.UsbDeviceConnection</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html#getUsbDeviceConnection--">getUsbDeviceConnection</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html#init-android.content.Context-">init</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html#openPort-android.hardware.usb.UsbDevice-">openPort</a></span>(android.hardware.usb.UsbDevice&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html#sendData-byte:A-">sendData</a></span>(byte[]&nbsp;bytes)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html#setIsAutoReceive-boolean-">setIsAutoReceive</a></span>(boolean&nbsp;isAutoReceive)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.qcom.usb.UsbBase_qcom">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.qcom.usb.<a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></h3>
<code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html#getDeviceList-android.content.Context-">getDeviceList</a>, <a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html#getUsbDevice-android.content.Context-int-int-">getUsbDevice</a>, <a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a>, <a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html#setDataCallback-com.rscja.team.qcom.usb.UsbBase_qcom.DataCallback-">setDataCallback</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="UsbPL2302--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UsbPL2302</h4>
<pre>public&nbsp;UsbPL2302()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setIsAutoReceive-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsAutoReceive</h4>
<pre>public&nbsp;void&nbsp;setIsAutoReceive(boolean&nbsp;isAutoReceive)</pre>
</li>
</ul>
<a name="openPort-android.hardware.usb.UsbDevice-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openPort</h4>
<pre>public&nbsp;int&nbsp;openPort(android.hardware.usb.UsbDevice&nbsp;device)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html#openPort-android.hardware.usb.UsbDevice-">openPort</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></code></dd>
</dl>
</li>
</ul>
<a name="closeport--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeport</h4>
<pre>public&nbsp;android.hardware.usb.UsbDevice&nbsp;closeport()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html#closeport--">closeport</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></code></dd>
</dl>
</li>
</ul>
<a name="sendData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sendData</h4>
<pre>public&nbsp;int&nbsp;sendData(byte[]&nbsp;bytes)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html#sendData-byte:A-">sendData</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></code></dd>
</dl>
</li>
</ul>
<a name="getUsbDeviceConnection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUsbDeviceConnection</h4>
<pre>public&nbsp;android.hardware.usb.UsbDeviceConnection&nbsp;getUsbDeviceConnection()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html#getUsbDeviceConnection--">getUsbDeviceConnection</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></code></dd>
</dl>
</li>
</ul>
<a name="getConnectionStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConnectionStatus</h4>
<pre>public&nbsp;<a href="../../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;getConnectionStatus()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html#getConnectionStatus--">getConnectionStatus</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></code></dd>
</dl>
</li>
</ul>
<a name="init-android.content.Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;void&nbsp;init(android.content.Context&nbsp;context)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html#init-android.content.Context-">init</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../../com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb">UsbBase_qcom</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UsbPL2302.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/qcom/usb/pl2302/UsbPL2302.html" target="_top">Frames</a></li>
<li><a href="UsbPL2302.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.team.qcom.usb.UsbBase_qcom">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.rscja.team.qcom.usb.UsbBase_qcom">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
