<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>UHFProtocolParseByJava</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UHFProtocolParseByJava";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":9,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":9,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10,"i142":10,"i143":10,"i144":10,"i145":10,"i146":10,"i147":10,"i148":10,"i149":10,"i150":10,"i151":10,"i152":10,"i153":10,"i154":10,"i155":10,"i156":10,"i157":10,"i158":10,"i159":10,"i160":10,"i161":10,"i162":10,"i163":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFProtocolParseByJava.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseBleByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" target="_top">Frames</a></li>
<li><a href="UHFProtocolParseByJava.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.deviceapi</div>
<h2 title="Class UHFProtocolParseByJava" class="title">Class UHFProtocolParseByJava</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.deviceapi.UHFProtocolParseByJava</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseBleByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseBleByJava_qcom</a>, <a href="../../../../../com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils">UHFProtocolParseUrAxBase_qcom</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseUSBByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseUSBByJava_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">UHFProtocolParseByJava</span>
extends java.lang.Object
implements <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></pre>
<div class="block">java 解析R2\R5\R6 uhf协议</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>zhoupin</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#UHFProtocolParseByJava--">UHFProtocolParseByJava</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#barcodeScanTimeOutSend--">barcodeScanTimeOutSend</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#blinkOfLedSendData-int-int-int-">blinkOfLedSendData</a></span>(int&nbsp;duration,
                  int&nbsp;interval,
                  int&nbsp;count)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#blockEraseDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-">blockEraseDataSendData</a></span>(java.lang.String&nbsp;accessPwd,
                      char&nbsp;filterBank,
                      int&nbsp;filterPtr,
                      int&nbsp;filterCnt,
                      java.lang.String&nbsp;filterData,
                      char&nbsp;bank,
                      int&nbsp;ptr,
                      char&nbsp;cnt)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#blockWriteDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-java.lang.String-">blockWriteDataSendData</a></span>(java.lang.String&nbsp;accessPwd,
                      char&nbsp;filterBank,
                      int&nbsp;filterPtr,
                      int&nbsp;filterCnt,
                      java.lang.String&nbsp;filterData,
                      char&nbsp;bank,
                      int&nbsp;ptr,
                      char&nbsp;cnt,
                      java.lang.String&nbsp;writeData)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#btDeleteAllTagToFlashSendData--">btDeleteAllTagToFlashSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#btGetAllTagNumFromFlashSendData--">btGetAllTagNumFromFlashSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#btGetNewTagNumFromFlashSendData--">btGetNewTagNumFromFlashSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#btGetTagDataFromFlashSendData--">btGetTagDataFromFlashSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#closeLedSendData--">closeLedSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#enableBarcodeACK-boolean-">enableBarcodeACK</a></span>(boolean&nbsp;enalbe)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#GBTagLockSendData-java.lang.String-char-int-int-java.lang.String-char-char-char-">GBTagLockSendData</a></span>(java.lang.String&nbsp;pszuAccessPwd,
                 char&nbsp;ufBank,
                 int&nbsp;ufPtr,
                 int&nbsp;ufCnt,
                 java.lang.String&nbsp;ufData,
                 char&nbsp;jmemory,
                 char&nbsp;jconfig,
                 char&nbsp;jaction)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a></span>(java.util.ArrayList&lt;java.lang.Integer&gt;&nbsp;lockBank,
                int&nbsp;lockMode)</code>
<div class="block">获取锁标签的锁定码</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getBatterySendData--">getBatterySendData</a></span>()</code>
<div class="block">获取电量的发送数据</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getBeepSendData-boolean-">getBeepSendData</a></span>(boolean&nbsp;isOpen)</code>
<div class="block">获取设置蜂鸣器的发送数据</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getCWSendData--">getCWSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getEPCTIDModeSendData-char-char-">getEPCTIDModeSendData</a></span>(char&nbsp;rev1,
                     char&nbsp;rev2)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getFastIDSendData--">getFastIDSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getFrequencyModeSendData--">getFrequencyModeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getGen2SendData--">getGen2SendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getInventorySingleTagSendData--">getInventorySingleTagSendData</a></span>()</code>
<div class="block">获取开启单次盘点标签的发送数据</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getInventorySingleTagSendData-byte:A-">getInventorySingleTagSendData</a></span>(byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getKillSendData-java.lang.String-int-int-int-java.lang.String-">getKillSendData</a></span>(java.lang.String&nbsp;accessPwd,
               int&nbsp;filterBank,
               int&nbsp;filterPtr,
               int&nbsp;filterCnt,
               java.lang.String&nbsp;filterData)</code>
<div class="block">获取销毁标签的发送数据</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getLockSendData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">getLockSendData</a></span>(java.lang.String&nbsp;accessPwd,
               int&nbsp;filterBank,
               int&nbsp;filterPtr,
               int&nbsp;filterCnt,
               java.lang.String&nbsp;filterData,
               java.lang.String&nbsp;lockCode)</code>
<div class="block">获取锁标签需要发送的数据 <br></div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getParameterSendData-byte:A-">getParameterSendData</a></span>(byte[]&nbsp;id)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getPowerSendData--">getPowerSendData</a></span>()</code>
<div class="block">获取功率的发送数据<br></div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getProtocolSendData--">getProtocolSendData</a></span>()</code>
<div class="block">获取协议需要发送数据<br></div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getReaderAwaitSleepTimeSendData--">getReaderAwaitSleepTimeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getReadSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-">getReadSendData</a></span>(java.lang.String&nbsp;accessPwd,
               int&nbsp;filterBank,
               int&nbsp;filterPtr,
               int&nbsp;filterCnt,
               java.lang.String&nbsp;filterData,
               int&nbsp;bank,
               int&nbsp;ptr,
               int&nbsp;cnt)</code>
<div class="block">获取读标签的发送数据</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getReadTagSendData--">getReadTagSendData</a></span>()</code>
<div class="block">获取在循环盘点标签的模式中,读取缓存标签的发送数据</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getRFLinkSendData--">getRFLinkSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getScanBarcodeInBlinkModeSendData--">getScanBarcodeInBlinkModeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getScanBarcodeInTriggleModeSendData--">getScanBarcodeInTriggleModeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getScanBarcodeSendData--">getScanBarcodeSendData</a></span>()</code>
<div class="block">获取扫描条码的发送数据</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getSetSoftResetSendData--">getSetSoftResetSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStartInventoryTagSendData--">getStartInventoryTagSendData</a></span>()</code>
<div class="block">获取循环盘点标签的发送数据</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStartInventoryTagSendData-byte:A-">getStartInventoryTagSendData</a></span>(byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getSTM32VersionSendData--">getSTM32VersionSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStopInventorySendData--">getStopInventorySendData</a></span>()</code>
<div class="block">获取停止循环盘点标签的发送数据</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStopScanBarcodeInBlinkModeSendData--">getStopScanBarcodeInBlinkModeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getStopScanBarcodeInTriggleModeSendData--">getStopScanBarcodeInTriggleModeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getTagfocusSendData--">getTagfocusSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getTemperatureSendData--">getTemperatureSendData</a></span>()</code>
<div class="block">获取模块温度</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getUserSettingSendData-byte-">getUserSettingSendData</a></span>(byte&nbsp;key)</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getVersionSendData--">getVersionSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#getWriteSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">getWriteSendData</a></span>(java.lang.String&nbsp;accessPwd,
                int&nbsp;filterBank,
                int&nbsp;filterPtr,
                int&nbsp;filterCnt,
                java.lang.String&nbsp;filterData,
                int&nbsp;bank,
                int&nbsp;ptr,
                int&nbsp;cnt,
                java.lang.String&nbsp;writeData)</code>
<div class="block">获取写标签的发送数据<br></div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#isBarcodeDataContainSSIIDSend--">isBarcodeDataContainSSIIDSend</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#isBarcodeDataContainTypeSend--">isBarcodeDataContainTypeSend</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#makeSendData-int-byte:A-">makeSendData</a></span>(int&nbsp;cmd,
            byte[]&nbsp;databuf)</code>&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#openLedSendData--">openLedSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBarcodeData-byte:A-">parseBarcodeData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析扫描条码返回的数据</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBatteryData-byte:A-">parseBatteryData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析获取电量返回的数据</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBeepData-byte:A-">parseBeepData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析设置蜂鸣器返回的数据</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBlinkOfLedData-byte:A-">parseBlinkOfLedData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBlockEraseDataData-byte:A-">parseBlockEraseDataData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBlockWriteData-byte:A-">parseBlockWriteData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBtDeleteAllTagToFlashData-byte:A-">parseBtDeleteAllTagToFlashData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBtGetAllTagNumFromFlashData-byte:A-">parseBtGetAllTagNumFromFlashData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBtGetNewTagNumFromFlashData-byte:A-">parseBtGetNewTagNumFromFlashData</a></span>(byte[]&nbsp;indata)</code>&nbsp;</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseBtGetTagDataFromFlashData-byte:A-">parseBtGetTagDataFromFlashData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseCloseLedData-byte:A-">parseCloseLedData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseContinuousInventoryTagData-byte:A-">parseContinuousInventoryTagData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">循环盘点标签返回的数据</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseContinuousInventoryTagData-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-com.rscja.deviceapi.entity.TagInfoRule-">parseContinuousInventoryTagData</a></span>(<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;inData,
                               <a href="../../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a>&nbsp;tagInfoRule)</code>&nbsp;</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseFastIdData-byte:A-">parseFastIdData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGBTagLockData-byte:A-">parseGBTagLockData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetCWData-byte:A-">parseGetCWData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetEPCTIDModeData-byte:A-">parseGetEPCTIDModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetFastIdData-byte:A-">parseGetFastIdData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetFrequencyModeData-byte:A-">parseGetFrequencyModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetGen2Data-byte:A-">parseGetGen2Data</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetParameterData-byte:A-byte:A-">parseGetParameterData</a></span>(byte[]&nbsp;inData,
                     byte[]&nbsp;id)</code>&nbsp;</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity">AntennaPowerEntity</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetPowerAllData-byte:A-">parseGetPowerAllData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetPowerData-byte:A-">parseGetPowerData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析获取功率返回的数据</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetProtocolData-byte:A-">parseGetProtocolData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析获取协议返回的数据</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetReaderAwaitSleepTimeData-byte:A-">parseGetReaderAwaitSleepTimeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetRFLinkData-byte:A-">parseGetRFLinkData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetTagfocusData-byte:A-">parseGetTagfocusData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseGetUserSettingData-byte:A-">parseGetUserSettingData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseInventorySingleTagData-byte:A-">parseInventorySingleTagData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析单次盘点标签返回的数据</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseisBarcodeDataContainSSIIDData-byte:A-">parseisBarcodeDataContainSSIIDData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseisBarcodeDataContainTypeData-byte:A-">parseisBarcodeDataContainTypeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseisBarcodeScanTimeOutData-byte:A-">parseisBarcodeScanTimeOutData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseKillData-byte:A-">parseKillData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析销毁标签返回的数据</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseLockData-byte:A-">parseLockData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析锁标签返回的数据</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseOpenLedData-byte:A-">parseOpenLedData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseReadData-byte:A-">parseReadData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析读标签返回的数据</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseReadTagData_EPC-byte:A-">parseReadTagData_EPC</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析循环盘点标签返回的标签数据</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseReadTagDataEPC_TID_USER-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-com.rscja.deviceapi.entity.TagInfoRule-">parseReadTagDataEPC_TID_USER</a></span>(<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;inData,
                            <a href="../../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a>&nbsp;tagInfoRule)</code>&nbsp;</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parserUhfTagBuff_EPC_TID_USER-byte:A-boolean-">parserUhfTagBuff_EPC_TID_USER</a></span>(byte[]&nbsp;tagsBuff,
                             boolean&nbsp;isContainAnt)</code>&nbsp;</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parserUhfTagBuff_EPC_TID_USER-byte:A-com.rscja.deviceapi.entity.TagInfoRule-">parserUhfTagBuff_EPC_TID_USER</a></span>(byte[]&nbsp;tagsBuff,
                             <a href="../../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a>&nbsp;tagInfoRule)</code>&nbsp;</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseScanBarcodeInBlinkModeData-byte:A-">parseScanBarcodeInBlinkModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseScanBarcodeInTriggleModeData-byte:A-">parseScanBarcodeInTriggleModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetBarcodeSSIDData-byte:A-">parseSetBarcodeSSIDData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetBarcodeTypeData-byte:A-">parseSetBarcodeTypeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetBeepTimeOfDuration-byte:A-">parseSetBeepTimeOfDuration</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetCWData-byte:A-">parseSetCWData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetEPCAndTIDModeData-byte:A-">parseSetEPCAndTIDModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetEPCAndTIDUserModeData-byte:A-">parseSetEPCAndTIDUserModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetEPCModeData-byte:A-">parseSetEPCModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetFilterData-byte:A-">parseSetFilterData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetFrequencyModeData-byte:A-">parseSetFrequencyModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetGen2Data-byte:A-">parseSetGen2Data</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetJumpFrequencyData-byte:A-">parseSetJumpFrequencyData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetParameterData-byte:A-byte:A-byte:A-">parseSetParameterData</a></span>(byte[]&nbsp;inData,
                     byte[]&nbsp;id,
                     byte[]&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetPowerData-byte:A-">parseSetPowerData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析设置功率返回的数据</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetPowerOnDynamicData-byte:A-">parseSetPowerOnDynamicData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetProtocolData-byte:A-">parseSetProtocolData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析设置协议返回的数据</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetR6WorkModeData-byte:A-">parseSetR6WorkModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetReaderAwaitSleepTimeData-byte:A-">parseSetReaderAwaitSleepTimeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetRFLinkData-byte:A-">parseSetRFLinkData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetSoftResetData-byte:A-">parseSetSoftResetData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetTagfocusData-byte:A-">parseSetTagfocusData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSetUserSettingData-byte:A-">parseSetUserSettingData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseStartInventoryTagData-byte:A-">parseStartInventoryTagData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析开始盘点标签返回的数据</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseSTM32VersionData-byte:A-">parseSTM32VersionData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseStopInventoryData-byte:A-">parseStopInventoryData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析停止盘点标签返回的数据</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseStopScanBarcodeInBlinkModeData-byte:A-">parseStopScanBarcodeInBlinkModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseStopScanBarcodeInTriggleModeData-byte:A-">parseStopScanBarcodeInTriggleModeData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseTemperatureData-byte:A-">parseTemperatureData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析模块温度返回的数据</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseUHFJump2BootData-byte:A-">parseUHFJump2BootData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseUHFStartUpdateData-byte:A-">parseUHFStartUpdateData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseUHFStopUpdateData-byte:A-">parseUHFStopUpdateData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseUHFUpdatingData-byte:A-">parseUHFUpdatingData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseVersionData-byte:A-">parseVersionData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseWriteData-byte:A-">parseWriteData</a></span>(byte[]&nbsp;inData)</code>
<div class="block">解析写标签返回的数据</div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#requestGetBeepSendData--">requestGetBeepSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#requestGetEx10SDKFirmware--">requestGetEx10SDKFirmware</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#requestGetFastInventoryMode--">requestGetFastInventoryMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#requestInventoryTempTag-byte:A-">requestInventoryTempTag</a></span>(byte[]&nbsp;powerValue)</code>&nbsp;</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#requestSetFastInventoryMode-boolean-">requestSetFastInventoryMode</a></span>(boolean&nbsp;enable)</code>&nbsp;</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#responseGetEx10SDKFirmware-byte:A-">responseGetEx10SDKFirmware</a></span>(byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#responseGetFastInventoryMode-byte:A-">responseGetFastInventoryMode</a></span>(byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#responseSetFastInventoryMode-byte:A-">responseSetFastInventoryMode</a></span>(byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#respparseBeepData-byte:A-">respparseBeepData</a></span>(byte[]&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setBarcodeSSIID-boolean-">setBarcodeSSIID</a></span>(boolean&nbsp;barcodeType)</code>&nbsp;</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setBarcodeType-boolean-">setBarcodeType</a></span>(boolean&nbsp;barcodeType)</code>&nbsp;</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setBeepTimeOfDurationSendData-int-">setBeepTimeOfDurationSendData</a></span>(int&nbsp;time)</code>&nbsp;</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setCWSendData-char-">setCWSendData</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setEPCAndTIDModeSendData--">setEPCAndTIDModeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setEPCAndTIDUserModeSendData-int-int-">setEPCAndTIDUserModeSendData</a></span>(int&nbsp;user_prt,
                            int&nbsp;user_len)</code>&nbsp;</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setEPCAndTIDUserModeSendData-int-int-int-">setEPCAndTIDUserModeSendData</a></span>(int&nbsp;mode,
                            int&nbsp;user_prt,
                            int&nbsp;user_len)</code>&nbsp;</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setEPCModeSendData--">setEPCModeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setFastIdSendData-int-">setFastIdSendData</a></span>(int&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setFilterSendData-char-int-int-java.lang.String-">setFilterSendData</a></span>(char&nbsp;ufBank,
                 int&nbsp;ufPtr,
                 int&nbsp;datalen,
                 java.lang.String&nbsp;databuf)</code>&nbsp;</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setFrequencyModeSendData-int-">setFrequencyModeSendData</a></span>(int&nbsp;freMode)</code>&nbsp;</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setGen2SendData-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">setGen2SendData</a></span>(char&nbsp;Target,
               char&nbsp;Action,
               char&nbsp;T,
               char&nbsp;Q_Q,
               char&nbsp;StartQ,
               char&nbsp;MinQ,
               char&nbsp;MaxQ,
               char&nbsp;D_D,
               char&nbsp;C_C,
               char&nbsp;P_P,
               char&nbsp;Sel,
               char&nbsp;Session,
               char&nbsp;G_G,
               char&nbsp;LF)</code>&nbsp;</td>
</tr>
<tr id="i146" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setIndexData-byte:A-">setIndexData</a></span>(byte[]&nbsp;index)</code>&nbsp;</td>
</tr>
<tr id="i147" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setInventoryMessageModeSendData--">setInventoryMessageModeSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i148" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setJumpFrequencySendData-int-">setJumpFrequencySendData</a></span>(int&nbsp;Freqbuf)</code>&nbsp;</td>
</tr>
<tr id="i149" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setParameterSendData-byte:A-byte:A-">setParameterSendData</a></span>(byte[]&nbsp;id,
                    byte[]&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i150" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setPowerOnDynamicSendData-int-">setPowerOnDynamicSendData</a></span>(int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr id="i151" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setPowerSendData-int-">setPowerSendData</a></span>(int&nbsp;power)</code>
<div class="block">获取设置功率的发送数据<br></div>
</td>
</tr>
<tr id="i152" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setPowerSendData-int-int-">setPowerSendData</a></span>(int&nbsp;ant,
                int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr id="i153" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setProtocolSendData-int-">setProtocolSendData</a></span>(int&nbsp;protocol)</code>
<div class="block">获取设置协议的发送数据<br></div>
</td>
</tr>
<tr id="i154" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setR6WorkmodeSendData-char-">setR6WorkmodeSendData</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i155" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setReaderAwaitSleepTimeSendData-char-">setReaderAwaitSleepTimeSendData</a></span>(char&nbsp;time)</code>&nbsp;</td>
</tr>
<tr id="i156" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setRFLinkSendData-int-">setRFLinkSendData</a></span>(int&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i157" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setTagfocusSendData-char-">setTagfocusSendData</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i158" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#setUserSettingSendData-byte-byte:A-">setUserSettingSendData</a></span>(byte&nbsp;key,
                      byte[]&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i159" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#stop2DSendData--">stop2DSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i160" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#uhfJump2BootSendData-char-">uhfJump2BootSendData</a></span>(char&nbsp;flag)</code>&nbsp;</td>
</tr>
<tr id="i161" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#uhfStartUpdateSendData--">uhfStartUpdateSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i162" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#UHFStopUpdateSendData--">UHFStopUpdateSendData</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i163" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#uhfUpdatingSendData-byte:A-">uhfUpdatingSendData</a></span>(byte[]&nbsp;buf)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="UHFProtocolParseByJava--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UHFProtocolParseByJava</h4>
<pre>public&nbsp;UHFProtocolParseByJava()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="requestGetBeepSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestGetBeepSendData</h4>
<pre>public&nbsp;byte[]&nbsp;requestGetBeepSendData()</pre>
</li>
</ul>
<a name="respparseBeepData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>respparseBeepData</h4>
<pre>public&nbsp;int&nbsp;respparseBeepData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getBeepSendData-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBeepSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getBeepSendData(boolean&nbsp;isOpen)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getBeepSendData-boolean-">IUHFProtocolParse</a></code></span></div>
<div class="block">获取设置蜂鸣器的发送数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getBeepSendData-boolean-">getBeepSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>isOpen</code> - true:表示打开蜂鸣器， false:表示关闭蜂鸣器</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="setBeepTimeOfDurationSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBeepTimeOfDurationSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setBeepTimeOfDurationSendData(int&nbsp;time)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setBeepTimeOfDurationSendData-int-">setBeepTimeOfDurationSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseBeepData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBeepData</h4>
<pre>public&nbsp;boolean&nbsp;parseBeepData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBeepData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析设置蜂鸣器返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBeepData-byte:A-">parseBeepData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 设置蜂鸣器返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true: 设置成功  ,flase:设置设备</dd>
</dl>
</li>
</ul>
<a name="parseSetBeepTimeOfDuration-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetBeepTimeOfDuration</h4>
<pre>public&nbsp;boolean&nbsp;parseSetBeepTimeOfDuration(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetBeepTimeOfDuration-byte:A-">parseSetBeepTimeOfDuration</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="getScanBarcodeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScanBarcodeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getScanBarcodeSendData()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getScanBarcodeSendData--">IUHFProtocolParse</a></code></span></div>
<div class="block">获取扫描条码的发送数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getScanBarcodeSendData--">getScanBarcodeSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="stop2DSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stop2DSendData</h4>
<pre>public&nbsp;byte[]&nbsp;stop2DSendData()</pre>
</li>
</ul>
<a name="getScanBarcodeInBlinkModeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScanBarcodeInBlinkModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getScanBarcodeInBlinkModeSendData()</pre>
</li>
</ul>
<a name="parseScanBarcodeInBlinkModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseScanBarcodeInBlinkModeData</h4>
<pre>public&nbsp;boolean&nbsp;parseScanBarcodeInBlinkModeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getScanBarcodeInTriggleModeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScanBarcodeInTriggleModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getScanBarcodeInTriggleModeSendData()</pre>
</li>
</ul>
<a name="parseScanBarcodeInTriggleModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseScanBarcodeInTriggleModeData</h4>
<pre>public&nbsp;boolean&nbsp;parseScanBarcodeInTriggleModeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getStopScanBarcodeInBlinkModeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStopScanBarcodeInBlinkModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getStopScanBarcodeInBlinkModeSendData()</pre>
</li>
</ul>
<a name="parseStopScanBarcodeInBlinkModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseStopScanBarcodeInBlinkModeData</h4>
<pre>public&nbsp;boolean&nbsp;parseStopScanBarcodeInBlinkModeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getStopScanBarcodeInTriggleModeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStopScanBarcodeInTriggleModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getStopScanBarcodeInTriggleModeSendData()</pre>
</li>
</ul>
<a name="parseStopScanBarcodeInTriggleModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseStopScanBarcodeInTriggleModeData</h4>
<pre>public&nbsp;boolean&nbsp;parseStopScanBarcodeInTriggleModeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="parseBarcodeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBarcodeData</h4>
<pre>public&nbsp;byte[]&nbsp;parseBarcodeData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBarcodeData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析扫描条码返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBarcodeData-byte:A-">parseBarcodeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的条码原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回解析后的条码数据</dd>
</dl>
</li>
</ul>
<a name="setBarcodeType-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeType</h4>
<pre>public&nbsp;byte[]&nbsp;setBarcodeType(boolean&nbsp;barcodeType)</pre>
</li>
</ul>
<a name="parseSetBarcodeTypeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetBarcodeTypeData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetBarcodeTypeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setBarcodeSSIID-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeSSIID</h4>
<pre>public&nbsp;byte[]&nbsp;setBarcodeSSIID(boolean&nbsp;barcodeType)</pre>
</li>
</ul>
<a name="parseSetBarcodeSSIDData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetBarcodeSSIDData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetBarcodeSSIDData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="barcodeScanTimeOutSend--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>barcodeScanTimeOutSend</h4>
<pre>public&nbsp;byte[]&nbsp;barcodeScanTimeOutSend()</pre>
</li>
</ul>
<a name="parseisBarcodeScanTimeOutData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseisBarcodeScanTimeOutData</h4>
<pre>public&nbsp;int&nbsp;parseisBarcodeScanTimeOutData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="isBarcodeDataContainTypeSend--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBarcodeDataContainTypeSend</h4>
<pre>public&nbsp;byte[]&nbsp;isBarcodeDataContainTypeSend()</pre>
</li>
</ul>
<a name="parseisBarcodeDataContainTypeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseisBarcodeDataContainTypeData</h4>
<pre>public&nbsp;int&nbsp;parseisBarcodeDataContainTypeData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="isBarcodeDataContainSSIIDSend--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBarcodeDataContainSSIIDSend</h4>
<pre>public&nbsp;byte[]&nbsp;isBarcodeDataContainSSIIDSend()</pre>
</li>
</ul>
<a name="enableBarcodeACK-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableBarcodeACK</h4>
<pre>public&nbsp;byte[]&nbsp;enableBarcodeACK(boolean&nbsp;enalbe)</pre>
</li>
</ul>
<a name="parseisBarcodeDataContainSSIIDData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseisBarcodeDataContainSSIIDData</h4>
<pre>public&nbsp;int&nbsp;parseisBarcodeDataContainSSIIDData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setParameterSendData-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParameterSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setParameterSendData(byte[]&nbsp;id,
                                   byte[]&nbsp;value)</pre>
</li>
</ul>
<a name="parseSetParameterData-byte:A-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetParameterData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetParameterData(byte[]&nbsp;inData,
                                     byte[]&nbsp;id,
                                     byte[]&nbsp;value)</pre>
</li>
</ul>
<a name="getParameterSendData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParameterSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getParameterSendData(byte[]&nbsp;id)</pre>
</li>
</ul>
<a name="parseGetParameterData-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetParameterData</h4>
<pre>public&nbsp;byte[]&nbsp;parseGetParameterData(byte[]&nbsp;inData,
                                    byte[]&nbsp;id)</pre>
</li>
</ul>
<a name="getBatterySendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBatterySendData</h4>
<pre>public&nbsp;byte[]&nbsp;getBatterySendData()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getBatterySendData--">IUHFProtocolParse</a></code></span></div>
<div class="block">获取电量的发送数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getBatterySendData--">getBatterySendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseBatteryData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBatteryData</h4>
<pre>public&nbsp;int&nbsp;parseBatteryData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBatteryData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析获取电量返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBatteryData-byte:A-">parseBatteryData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回解析后的电量</dd>
</dl>
</li>
</ul>
<a name="getWriteSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWriteSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getWriteSendData(java.lang.String&nbsp;accessPwd,
                               int&nbsp;filterBank,
                               int&nbsp;filterPtr,
                               int&nbsp;filterCnt,
                               java.lang.String&nbsp;filterData,
                               int&nbsp;bank,
                               int&nbsp;ptr,
                               int&nbsp;cnt,
                               java.lang.String&nbsp;writeData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getWriteSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">IUHFProtocolParse</a></code></span></div>
<div class="block">获取写标签的发送数据<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getWriteSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">getWriteSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - 标签的ACCESS PASSWORD（4字 节）<br></dd>
<dd><code>filterBank</code> - 过滤的数据块<br></dd>
<dd><code>filterPtr</code> - 过滤的起始地址(单位:bit)<br></dd>
<dd><code>filterCnt</code> - 过滤的数据长度(单位:bit)<br></dd>
<dd><code>filterData</code> - 过滤的数据<br></dd>
<dd><code>bank</code> - 写入的数据块<br></dd>
<dd><code>ptr</code> - 写入的起始地址(单位:字)<br></dd>
<dd><code>cnt</code> - 写入的数据长度(单位:字)<br></dd>
<dd><code>writeData</code> - 写入的数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseWriteData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseWriteData</h4>
<pre>public&nbsp;boolean&nbsp;parseWriteData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseWriteData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析写标签返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseWriteData-byte:A-">parseWriteData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:写标签成功    false:写标签失败</dd>
</dl>
</li>
</ul>
<a name="getReadSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReadSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getReadSendData(java.lang.String&nbsp;accessPwd,
                              int&nbsp;filterBank,
                              int&nbsp;filterPtr,
                              int&nbsp;filterCnt,
                              java.lang.String&nbsp;filterData,
                              int&nbsp;bank,
                              int&nbsp;ptr,
                              int&nbsp;cnt)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReadSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-">IUHFProtocolParse</a></code></span></div>
<div class="block">获取读标签的发送数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReadSendData-java.lang.String-int-int-int-java.lang.String-int-int-int-">getReadSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - 访问密码<br></dd>
<dd><code>filterBank</code> - 过滤的数据块<br></dd>
<dd><code>filterPtr</code> - 过滤的起始地址(单位:bit)<br></dd>
<dd><code>filterCnt</code> - 过滤的数据长度(单位:bit)<br></dd>
<dd><code>filterData</code> - 过滤的数据<br></dd>
<dd><code>bank</code> - 读取的数据块<br></dd>
<dd><code>ptr</code> - 读取的起始地址(单位:字)<br></dd>
<dd><code>cnt</code> - 读取的数据长度(单位:字)<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据<br></dd>
</dl>
</li>
</ul>
<a name="parseReadData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseReadData</h4>
<pre>public&nbsp;java.lang.String&nbsp;parseReadData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseReadData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析读标签返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseReadData-byte:A-">parseReadData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回解析后的标签数据</dd>
</dl>
</li>
</ul>
<a name="getLockSendData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLockSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getLockSendData(java.lang.String&nbsp;accessPwd,
                              int&nbsp;filterBank,
                              int&nbsp;filterPtr,
                              int&nbsp;filterCnt,
                              java.lang.String&nbsp;filterData,
                              java.lang.String&nbsp;lockCode)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getLockSendData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">IUHFProtocolParse</a></code></span></div>
<div class="block">获取锁标签需要发送的数据 <br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getLockSendData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">getLockSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - 标签的ACCESS PASSWORD（4字 节）<br></dd>
<dd><code>filterBank</code> - 标签的存储区<br></dd>
<dd><code>filterPtr</code> - 过滤起始地址(单位:bit)<br></dd>
<dd><code>filterCnt</code> - 过滤数据长度(单位:bit)<br></dd>
<dd><code>filterData</code> - 过滤数据<br></dd>
<dd><code>lockCode</code> - 锁定码<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据<br></dd>
</dl>
</li>
</ul>
<a name="parseLockData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseLockData</h4>
<pre>public&nbsp;boolean&nbsp;parseLockData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseLockData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析锁标签返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseLockData-byte:A-">parseLockData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:锁成功， false:锁失败</dd>
</dl>
</li>
</ul>
<a name="generateLockCode-java.util.ArrayList-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateLockCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;generateLockCode(java.util.ArrayList&lt;java.lang.Integer&gt;&nbsp;lockBank,
                                         int&nbsp;lockMode)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#generateLockCode-java.util.ArrayList-int-">IUHFProtocolParse</a></code></span></div>
<div class="block">获取锁标签的锁定码</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>lockBank</code> - 要锁定的区域</dd>
<dd><code>lockMode</code> - 锁定的模式</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回null 表示失败</dd>
</dl>
</li>
</ul>
<a name="getKillSendData-java.lang.String-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKillSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getKillSendData(java.lang.String&nbsp;accessPwd,
                              int&nbsp;filterBank,
                              int&nbsp;filterPtr,
                              int&nbsp;filterCnt,
                              java.lang.String&nbsp;filterData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getKillSendData-java.lang.String-int-int-int-java.lang.String-">IUHFProtocolParse</a></code></span></div>
<div class="block">获取销毁标签的发送数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getKillSendData-java.lang.String-int-int-int-java.lang.String-">getKillSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - 标签的ACCESS PASSWORD（4字 节）<br></dd>
<dd><code>filterBank</code> - 标签的存储区<br></dd>
<dd><code>filterPtr</code> - 过滤起始地址(单位:bit)<br></dd>
<dd><code>filterCnt</code> - 过滤数据长度(单位:bit)<br></dd>
<dd><code>filterData</code> - 过滤数据<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据<br></dd>
</dl>
</li>
</ul>
<a name="parseKillData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseKillData</h4>
<pre>public&nbsp;boolean&nbsp;parseKillData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseKillData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析销毁标签返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseKillData-byte:A-">parseKillData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:销毁标签成功， false:销毁标签失败</dd>
</dl>
</li>
</ul>
<a name="getInventorySingleTagSendData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInventorySingleTagSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getInventorySingleTagSendData(byte[]&nbsp;data)</pre>
</li>
</ul>
<a name="getInventorySingleTagSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInventorySingleTagSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getInventorySingleTagSendData()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getInventorySingleTagSendData--">IUHFProtocolParse</a></code></span></div>
<div class="block">获取开启单次盘点标签的发送数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getInventorySingleTagSendData--">getInventorySingleTagSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseInventorySingleTagData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseInventorySingleTagData</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;parseInventorySingleTagData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseInventorySingleTagData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析单次盘点标签返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseInventorySingleTagData-byte:A-">parseInventorySingleTagData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回单次盘点标签的数据</dd>
</dl>
</li>
</ul>
<a name="parseContinuousInventoryTagData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseContinuousInventoryTagData</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;parseContinuousInventoryTagData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseContinuousInventoryTagData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">循环盘点标签返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseContinuousInventoryTagData-byte:A-">parseContinuousInventoryTagData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回循环盘点标签的数据</dd>
</dl>
</li>
</ul>
<a name="parseContinuousInventoryTagData-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-com.rscja.deviceapi.entity.TagInfoRule-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseContinuousInventoryTagData</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;parseContinuousInventoryTagData(<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;inData,
                                                  <a href="../../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a>&nbsp;tagInfoRule)</pre>
</li>
</ul>
<a name="getStartInventoryTagSendData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartInventoryTagSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getStartInventoryTagSendData(byte[]&nbsp;data)</pre>
</li>
</ul>
<a name="getStartInventoryTagSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartInventoryTagSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getStartInventoryTagSendData()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getStartInventoryTagSendData--">IUHFProtocolParse</a></code></span></div>
<div class="block">获取循环盘点标签的发送数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getStartInventoryTagSendData--">getStartInventoryTagSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseStartInventoryTagData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseStartInventoryTagData</h4>
<pre>public&nbsp;boolean&nbsp;parseStartInventoryTagData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseStartInventoryTagData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析开始盘点标签返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseStartInventoryTagData-byte:A-">parseStartInventoryTagData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:开始盘点成功，false:开始盘点失败</dd>
</dl>
</li>
</ul>
<a name="getStopInventorySendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStopInventorySendData</h4>
<pre>public&nbsp;byte[]&nbsp;getStopInventorySendData()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getStopInventorySendData--">IUHFProtocolParse</a></code></span></div>
<div class="block">获取停止循环盘点标签的发送数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getStopInventorySendData--">getStopInventorySendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseStopInventoryData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseStopInventoryData</h4>
<pre>public&nbsp;boolean&nbsp;parseStopInventoryData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseStopInventoryData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析停止盘点标签返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseStopInventoryData-byte:A-">parseStopInventoryData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:停止盘点成功，false:停止盘点失败</dd>
</dl>
</li>
</ul>
<a name="getReadTagSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReadTagSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getReadTagSendData()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReadTagSendData--">IUHFProtocolParse</a></code></span></div>
<div class="block">获取在循环盘点标签的模式中,读取缓存标签的发送数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReadTagSendData--">getReadTagSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseReadTagData_EPC-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseReadTagData_EPC</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;&nbsp;parseReadTagData_EPC(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseReadTagData_EPC-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析循环盘点标签返回的标签数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseReadTagData_EPC-byte:A-">parseReadTagData_EPC</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回解析后的标签数据</dd>
</dl>
</li>
</ul>
<a name="parseReadTagDataEPC_TID_USER-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-com.rscja.deviceapi.entity.TagInfoRule-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseReadTagDataEPC_TID_USER</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;&nbsp;parseReadTagDataEPC_TID_USER(<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;inData,
                                                               <a href="../../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a>&nbsp;tagInfoRule)</pre>
</li>
</ul>
<a name="setPowerSendData-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPowerSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setPowerSendData(int&nbsp;ant,
                               int&nbsp;power)</pre>
</li>
</ul>
<a name="setPowerSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPowerSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setPowerSendData(int&nbsp;power)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setPowerSendData-int-">IUHFProtocolParse</a></code></span></div>
<div class="block">获取设置功率的发送数据<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setPowerSendData-int-">setPowerSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>power</code> - 功率</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseSetPowerData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetPowerData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetPowerData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetPowerData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析设置功率返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetPowerData-byte:A-">parseSetPowerData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:设置功率成功, flase:设置功率失败</dd>
</dl>
</li>
</ul>
<a name="getPowerSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPowerSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getPowerSendData()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getPowerSendData--">IUHFProtocolParse</a></code></span></div>
<div class="block">获取功率的发送数据<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getPowerSendData--">getPowerSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseGetPowerData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetPowerData</h4>
<pre>public&nbsp;int&nbsp;parseGetPowerData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetPowerData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析获取功率返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetPowerData-byte:A-">parseGetPowerData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回功率</dd>
</dl>
</li>
</ul>
<a name="getVersionSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersionSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getVersionSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getVersionSendData--">getVersionSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="parseVersionData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseVersionData</h4>
<pre>public&nbsp;java.lang.String&nbsp;parseVersionData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseVersionData-byte:A-">parseVersionData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getTemperatureSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTemperatureSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getTemperatureSendData()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getTemperatureSendData--">IUHFProtocolParse</a></code></span></div>
<div class="block">获取模块温度</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getTemperatureSendData--">getTemperatureSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="parseTemperatureData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseTemperatureData</h4>
<pre>public&nbsp;int&nbsp;parseTemperatureData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseTemperatureData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析模块温度返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseTemperatureData-byte:A-">parseTemperatureData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setEPCModeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setEPCModeSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setEPCModeSendData--">setEPCModeSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setInventoryMessageModeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInventoryMessageModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setInventoryMessageModeSendData()</pre>
</li>
</ul>
<a name="parseSetEPCModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetEPCModeData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetEPCModeData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetEPCModeData-byte:A-">parseSetEPCModeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setEPCAndTIDModeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCAndTIDModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setEPCAndTIDModeSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setEPCAndTIDModeSendData--">setEPCAndTIDModeSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseSetEPCAndTIDModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetEPCAndTIDModeData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetEPCAndTIDModeData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetEPCAndTIDModeData-byte:A-">parseSetEPCAndTIDModeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setEPCAndTIDUserModeSendData-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCAndTIDUserModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setEPCAndTIDUserModeSendData(int&nbsp;user_prt,
                                           int&nbsp;user_len)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setEPCAndTIDUserModeSendData-int-int-">setEPCAndTIDUserModeSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setEPCAndTIDUserModeSendData-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCAndTIDUserModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setEPCAndTIDUserModeSendData(int&nbsp;mode,
                                           int&nbsp;user_prt,
                                           int&nbsp;user_len)</pre>
</li>
</ul>
<a name="parseSetEPCAndTIDUserModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetEPCAndTIDUserModeData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetEPCAndTIDUserModeData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetEPCAndTIDUserModeData-byte:A-">parseSetEPCAndTIDUserModeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="getEPCTIDModeSendData-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEPCTIDModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getEPCTIDModeSendData(char&nbsp;rev1,
                                    char&nbsp;rev2)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getEPCTIDModeSendData-char-char-">getEPCTIDModeSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseGetEPCTIDModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetEPCTIDModeData</h4>
<pre>public&nbsp;byte[]&nbsp;parseGetEPCTIDModeData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetEPCTIDModeData-byte:A-">parseGetEPCTIDModeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setFilterSendData-char-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFilterSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setFilterSendData(char&nbsp;ufBank,
                                int&nbsp;ufPtr,
                                int&nbsp;datalen,
                                java.lang.String&nbsp;databuf)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setFilterSendData-char-int-int-java.lang.String-">setFilterSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseSetFilterData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetFilterData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetFilterData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetFilterData-byte:A-">parseSetFilterData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="getSTM32VersionSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSTM32VersionSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getSTM32VersionSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getSTM32VersionSendData--">getSTM32VersionSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseSTM32VersionData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSTM32VersionData</h4>
<pre>public&nbsp;java.lang.String&nbsp;parseSTM32VersionData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSTM32VersionData-byte:A-">parseSTM32VersionData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setProtocolSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProtocolSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setProtocolSendData(int&nbsp;protocol)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setProtocolSendData-int-">IUHFProtocolParse</a></code></span></div>
<div class="block">获取设置协议的发送数据<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setProtocolSendData-int-">setProtocolSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>protocol</code> - 0x00 表示 ISO18000-6C 协议,  0x01 表示 GB/T 29768 国标协议,  0x02 表示 GJB 7377.1 国军标协议</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseSetProtocolData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetProtocolData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetProtocolData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetProtocolData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析设置协议返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetProtocolData-byte:A-">parseSetProtocolData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:设置协议成功， false:设置协议失败</dd>
</dl>
</li>
</ul>
<a name="getProtocolSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProtocolSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getProtocolSendData()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getProtocolSendData--">IUHFProtocolParse</a></code></span></div>
<div class="block">获取协议需要发送数据<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getProtocolSendData--">getProtocolSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>发送的数据</dd>
</dl>
</li>
</ul>
<a name="parseGetProtocolData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetProtocolData</h4>
<pre>public&nbsp;int&nbsp;parseGetProtocolData(byte[]&nbsp;inData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetProtocolData-byte:A-">IUHFProtocolParse</a></code></span></div>
<div class="block">解析获取协议返回的数据</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetProtocolData-byte:A-">parseGetProtocolData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inData</code> - 蓝牙返回的原始数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:获取协议成功， false:获取协议失败</dd>
</dl>
</li>
</ul>
<a name="getSetSoftResetSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSetSoftResetSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getSetSoftResetSendData()</pre>
</li>
</ul>
<a name="parseSetSoftResetData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetSoftResetData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetSoftResetData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getFrequencyModeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrequencyModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getFrequencyModeSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getFrequencyModeSendData--">getFrequencyModeSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseGetFrequencyModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetFrequencyModeData</h4>
<pre>public&nbsp;byte&nbsp;parseGetFrequencyModeData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetFrequencyModeData-byte:A-">parseGetFrequencyModeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setFrequencyModeSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFrequencyModeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setFrequencyModeSendData(int&nbsp;freMode)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setFrequencyModeSendData-int-">setFrequencyModeSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseSetFrequencyModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetFrequencyModeData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetFrequencyModeData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetFrequencyModeData-byte:A-">parseSetFrequencyModeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setGen2SendData-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGen2SendData</h4>
<pre>public&nbsp;byte[]&nbsp;setGen2SendData(char&nbsp;Target,
                              char&nbsp;Action,
                              char&nbsp;T,
                              char&nbsp;Q_Q,
                              char&nbsp;StartQ,
                              char&nbsp;MinQ,
                              char&nbsp;MaxQ,
                              char&nbsp;D_D,
                              char&nbsp;C_C,
                              char&nbsp;P_P,
                              char&nbsp;Sel,
                              char&nbsp;Session,
                              char&nbsp;G_G,
                              char&nbsp;LF)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setGen2SendData-char-char-char-char-char-char-char-char-char-char-char-char-char-char-">setGen2SendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseSetGen2Data-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetGen2Data</h4>
<pre>public&nbsp;boolean&nbsp;parseSetGen2Data(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetGen2Data-byte:A-">parseSetGen2Data</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="getGen2SendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGen2SendData</h4>
<pre>public&nbsp;byte[]&nbsp;getGen2SendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getGen2SendData--">getGen2SendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseGetGen2Data-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetGen2Data</h4>
<pre>public&nbsp;byte[]&nbsp;parseGetGen2Data(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetGen2Data-byte:A-">parseGetGen2Data</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setRFLinkSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRFLinkSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setRFLinkSendData(int&nbsp;mode)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setRFLinkSendData-int-">setRFLinkSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseSetRFLinkData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetRFLinkData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetRFLinkData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetRFLinkData-byte:A-">parseSetRFLinkData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="getRFLinkSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRFLinkSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getRFLinkSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getRFLinkSendData--">getRFLinkSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseGetRFLinkData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetRFLinkData</h4>
<pre>public&nbsp;int&nbsp;parseGetRFLinkData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetRFLinkData-byte:A-">parseGetRFLinkData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setFastIdSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFastIdSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setFastIdSendData(int&nbsp;flag)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setFastIdSendData-int-">setFastIdSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseFastIdData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseFastIdData</h4>
<pre>public&nbsp;boolean&nbsp;parseFastIdData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseFastIdData-byte:A-">parseFastIdData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="getFastIDSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFastIDSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getFastIDSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getFastIDSendData--">getFastIDSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseGetFastIdData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetFastIdData</h4>
<pre>public&nbsp;int&nbsp;parseGetFastIdData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetFastIdData-byte:A-">parseGetFastIdData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="openLedSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openLedSendData</h4>
<pre>public&nbsp;byte[]&nbsp;openLedSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#openLedSendData--">openLedSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="closeLedSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeLedSendData</h4>
<pre>public&nbsp;byte[]&nbsp;closeLedSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#closeLedSendData--">closeLedSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="blinkOfLedSendData-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blinkOfLedSendData</h4>
<pre>public&nbsp;byte[]&nbsp;blinkOfLedSendData(int&nbsp;duration,
                                 int&nbsp;interval,
                                 int&nbsp;count)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#blinkOfLedSendData-int-int-int-">blinkOfLedSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseOpenLedData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseOpenLedData</h4>
<pre>public&nbsp;boolean&nbsp;parseOpenLedData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseOpenLedData-byte:A-">parseOpenLedData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseCloseLedData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseCloseLedData</h4>
<pre>public&nbsp;boolean&nbsp;parseCloseLedData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseCloseLedData-byte:A-">parseCloseLedData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseBlinkOfLedData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBlinkOfLedData</h4>
<pre>public&nbsp;boolean&nbsp;parseBlinkOfLedData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBlinkOfLedData-byte:A-">parseBlinkOfLedData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="blockWriteDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blockWriteDataSendData</h4>
<pre>public&nbsp;byte[]&nbsp;blockWriteDataSendData(java.lang.String&nbsp;accessPwd,
                                     char&nbsp;filterBank,
                                     int&nbsp;filterPtr,
                                     int&nbsp;filterCnt,
                                     java.lang.String&nbsp;filterData,
                                     char&nbsp;bank,
                                     int&nbsp;ptr,
                                     char&nbsp;cnt,
                                     java.lang.String&nbsp;writeData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#blockWriteDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-java.lang.String-">blockWriteDataSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseBlockWriteData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBlockWriteData</h4>
<pre>public&nbsp;boolean&nbsp;parseBlockWriteData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBlockWriteData-byte:A-">parseBlockWriteData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="blockEraseDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blockEraseDataSendData</h4>
<pre>public&nbsp;byte[]&nbsp;blockEraseDataSendData(java.lang.String&nbsp;accessPwd,
                                     char&nbsp;filterBank,
                                     int&nbsp;filterPtr,
                                     int&nbsp;filterCnt,
                                     java.lang.String&nbsp;filterData,
                                     char&nbsp;bank,
                                     int&nbsp;ptr,
                                     char&nbsp;cnt)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#blockEraseDataSendData-java.lang.String-char-int-int-java.lang.String-char-int-char-">blockEraseDataSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseBlockEraseDataData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBlockEraseDataData</h4>
<pre>public&nbsp;boolean&nbsp;parseBlockEraseDataData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBlockEraseDataData-byte:A-">parseBlockEraseDataData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="GBTagLockSendData-java.lang.String-char-int-int-java.lang.String-char-char-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GBTagLockSendData</h4>
<pre>public&nbsp;byte[]&nbsp;GBTagLockSendData(java.lang.String&nbsp;pszuAccessPwd,
                                char&nbsp;ufBank,
                                int&nbsp;ufPtr,
                                int&nbsp;ufCnt,
                                java.lang.String&nbsp;ufData,
                                char&nbsp;jmemory,
                                char&nbsp;jconfig,
                                char&nbsp;jaction)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#GBTagLockSendData-java.lang.String-char-int-int-java.lang.String-char-char-char-">GBTagLockSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseGBTagLockData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGBTagLockData</h4>
<pre>public&nbsp;boolean&nbsp;parseGBTagLockData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGBTagLockData-byte:A-">parseGBTagLockData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setCWSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCWSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setCWSendData(char&nbsp;flag)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setCWSendData-char-">setCWSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseSetCWData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetCWData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetCWData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetCWData-byte:A-">parseSetCWData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="getCWSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCWSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getCWSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getCWSendData--">getCWSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseGetCWData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetCWData</h4>
<pre>public&nbsp;int&nbsp;parseGetCWData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetCWData-byte:A-">parseGetCWData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setJumpFrequencySendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJumpFrequencySendData</h4>
<pre>public&nbsp;byte[]&nbsp;setJumpFrequencySendData(int&nbsp;Freqbuf)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setJumpFrequencySendData-int-">setJumpFrequencySendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseSetJumpFrequencyData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetJumpFrequencyData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetJumpFrequencyData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetJumpFrequencyData-byte:A-">parseSetJumpFrequencyData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="btDeleteAllTagToFlashSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>btDeleteAllTagToFlashSendData</h4>
<pre>public&nbsp;byte[]&nbsp;btDeleteAllTagToFlashSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btDeleteAllTagToFlashSendData--">btDeleteAllTagToFlashSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseBtDeleteAllTagToFlashData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBtDeleteAllTagToFlashData</h4>
<pre>public&nbsp;boolean&nbsp;parseBtDeleteAllTagToFlashData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBtDeleteAllTagToFlashData-byte:A-">parseBtDeleteAllTagToFlashData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="btGetAllTagNumFromFlashSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>btGetAllTagNumFromFlashSendData</h4>
<pre>public&nbsp;byte[]&nbsp;btGetAllTagNumFromFlashSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btGetAllTagNumFromFlashSendData--">btGetAllTagNumFromFlashSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="btGetNewTagNumFromFlashSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>btGetNewTagNumFromFlashSendData</h4>
<pre>public&nbsp;byte[]&nbsp;btGetNewTagNumFromFlashSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btGetNewTagNumFromFlashSendData--">btGetNewTagNumFromFlashSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseBtGetAllTagNumFromFlashData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBtGetAllTagNumFromFlashData</h4>
<pre>public&nbsp;int&nbsp;parseBtGetAllTagNumFromFlashData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBtGetAllTagNumFromFlashData-byte:A-">parseBtGetAllTagNumFromFlashData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseBtGetNewTagNumFromFlashData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBtGetNewTagNumFromFlashData</h4>
<pre>public&nbsp;int&nbsp;parseBtGetNewTagNumFromFlashData(byte[]&nbsp;indata)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBtGetNewTagNumFromFlashData-byte:A-">parseBtGetNewTagNumFromFlashData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="btGetTagDataFromFlashSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>btGetTagDataFromFlashSendData</h4>
<pre>public&nbsp;byte[]&nbsp;btGetTagDataFromFlashSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#btGetTagDataFromFlashSendData--">btGetTagDataFromFlashSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseBtGetTagDataFromFlashData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBtGetTagDataFromFlashData</h4>
<pre>public&nbsp;byte[]&nbsp;parseBtGetTagDataFromFlashData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseBtGetTagDataFromFlashData-byte:A-">parseBtGetTagDataFromFlashData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setR6WorkmodeSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setR6WorkmodeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setR6WorkmodeSendData(char&nbsp;flag)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setR6WorkmodeSendData-char-">setR6WorkmodeSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseSetR6WorkModeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetR6WorkModeData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetR6WorkModeData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetR6WorkModeData-byte:A-">parseSetR6WorkModeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="uhfJump2BootSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uhfJump2BootSendData</h4>
<pre>public&nbsp;byte[]&nbsp;uhfJump2BootSendData(char&nbsp;flag)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#uhfJump2BootSendData-char-">uhfJump2BootSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseUHFJump2BootData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseUHFJump2BootData</h4>
<pre>public&nbsp;boolean&nbsp;parseUHFJump2BootData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseUHFJump2BootData-byte:A-">parseUHFJump2BootData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="uhfStartUpdateSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uhfStartUpdateSendData</h4>
<pre>public&nbsp;byte[]&nbsp;uhfStartUpdateSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#uhfStartUpdateSendData--">uhfStartUpdateSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseUHFStartUpdateData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseUHFStartUpdateData</h4>
<pre>public&nbsp;boolean&nbsp;parseUHFStartUpdateData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseUHFStartUpdateData-byte:A-">parseUHFStartUpdateData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="uhfUpdatingSendData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uhfUpdatingSendData</h4>
<pre>public&nbsp;byte[]&nbsp;uhfUpdatingSendData(byte[]&nbsp;buf)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#uhfUpdatingSendData-byte:A-">uhfUpdatingSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseUHFUpdatingData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseUHFUpdatingData</h4>
<pre>public&nbsp;boolean&nbsp;parseUHFUpdatingData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseUHFUpdatingData-byte:A-">parseUHFUpdatingData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="UHFStopUpdateSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UHFStopUpdateSendData</h4>
<pre>public&nbsp;byte[]&nbsp;UHFStopUpdateSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#UHFStopUpdateSendData--">UHFStopUpdateSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseUHFStopUpdateData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseUHFStopUpdateData</h4>
<pre>public&nbsp;boolean&nbsp;parseUHFStopUpdateData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseUHFStopUpdateData-byte:A-">parseUHFStopUpdateData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setReaderAwaitSleepTimeSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReaderAwaitSleepTimeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setReaderAwaitSleepTimeSendData(char&nbsp;time)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setReaderAwaitSleepTimeSendData-char-">setReaderAwaitSleepTimeSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseSetReaderAwaitSleepTimeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetReaderAwaitSleepTimeData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetReaderAwaitSleepTimeData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetReaderAwaitSleepTimeData-byte:A-">parseSetReaderAwaitSleepTimeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="getReaderAwaitSleepTimeSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReaderAwaitSleepTimeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getReaderAwaitSleepTimeSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getReaderAwaitSleepTimeSendData--">getReaderAwaitSleepTimeSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseGetReaderAwaitSleepTimeData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetReaderAwaitSleepTimeData</h4>
<pre>public&nbsp;int&nbsp;parseGetReaderAwaitSleepTimeData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetReaderAwaitSleepTimeData-byte:A-">parseGetReaderAwaitSleepTimeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setTagfocusSendData-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTagfocusSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setTagfocusSendData(char&nbsp;flag)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setTagfocusSendData-char-">setTagfocusSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseSetTagfocusData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetTagfocusData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetTagfocusData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetTagfocusData-byte:A-">parseSetTagfocusData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="getTagfocusSendData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTagfocusSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getTagfocusSendData()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#getTagfocusSendData--">getTagfocusSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseGetTagfocusData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetTagfocusData</h4>
<pre>public&nbsp;int&nbsp;parseGetTagfocusData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseGetTagfocusData-byte:A-">parseGetTagfocusData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="setPowerOnDynamicSendData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPowerOnDynamicSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setPowerOnDynamicSendData(int&nbsp;power)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#setPowerOnDynamicSendData-int-">setPowerOnDynamicSendData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="parseSetPowerOnDynamicData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetPowerOnDynamicData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetPowerOnDynamicData(byte[]&nbsp;inData)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#parseSetPowerOnDynamicData-byte:A-">parseSetPowerOnDynamicData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="requestSetFastInventoryMode-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestSetFastInventoryMode</h4>
<pre>public&nbsp;byte[]&nbsp;requestSetFastInventoryMode(boolean&nbsp;enable)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#requestSetFastInventoryMode-boolean-">requestSetFastInventoryMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="responseSetFastInventoryMode-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>responseSetFastInventoryMode</h4>
<pre>public&nbsp;boolean&nbsp;responseSetFastInventoryMode(byte[]&nbsp;data)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#responseSetFastInventoryMode-byte:A-">responseSetFastInventoryMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="requestGetFastInventoryMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestGetFastInventoryMode</h4>
<pre>public&nbsp;byte[]&nbsp;requestGetFastInventoryMode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#requestGetFastInventoryMode--">requestGetFastInventoryMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="responseGetFastInventoryMode-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>responseGetFastInventoryMode</h4>
<pre>public&nbsp;int&nbsp;responseGetFastInventoryMode(byte[]&nbsp;data)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#responseGetFastInventoryMode-byte:A-">responseGetFastInventoryMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="requestGetEx10SDKFirmware--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestGetEx10SDKFirmware</h4>
<pre>public&nbsp;byte[]&nbsp;requestGetEx10SDKFirmware()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#requestGetEx10SDKFirmware--">requestGetEx10SDKFirmware</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="responseGetEx10SDKFirmware-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>responseGetEx10SDKFirmware</h4>
<pre>public&nbsp;java.lang.String&nbsp;responseGetEx10SDKFirmware(byte[]&nbsp;data)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html#responseGetEx10SDKFirmware-byte:A-">responseGetEx10SDKFirmware</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces">IUHFProtocolParse</a></code></dd>
</dl>
</li>
</ul>
<a name="requestInventoryTempTag-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestInventoryTempTag</h4>
<pre>public&nbsp;byte[]&nbsp;requestInventoryTempTag(byte[]&nbsp;powerValue)</pre>
</li>
</ul>
<a name="makeSendData-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makeSendData</h4>
<pre>public&nbsp;byte[]&nbsp;makeSendData(int&nbsp;cmd,
                           byte[]&nbsp;databuf)</pre>
</li>
</ul>
<a name="parserUhfTagBuff_EPC_TID_USER-byte:A-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parserUhfTagBuff_EPC_TID_USER</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;parserUhfTagBuff_EPC_TID_USER(byte[]&nbsp;tagsBuff,
                                                boolean&nbsp;isContainAnt)</pre>
</li>
</ul>
<a name="parserUhfTagBuff_EPC_TID_USER-byte:A-com.rscja.deviceapi.entity.TagInfoRule-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parserUhfTagBuff_EPC_TID_USER</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;parserUhfTagBuff_EPC_TID_USER(byte[]&nbsp;tagsBuff,
                                                       <a href="../../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a>&nbsp;tagInfoRule)</pre>
</li>
</ul>
<a name="parseGetPowerAllData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseGetPowerAllData</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity">AntennaPowerEntity</a>&gt;&nbsp;parseGetPowerAllData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="setIndexData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIndexData</h4>
<pre>public&nbsp;byte[]&nbsp;setIndexData(byte[]&nbsp;index)</pre>
</li>
</ul>
<a name="setUserSettingSendData-byte-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUserSettingSendData</h4>
<pre>public&nbsp;byte[]&nbsp;setUserSettingSendData(byte&nbsp;key,
                                     byte[]&nbsp;value)</pre>
</li>
</ul>
<a name="parseSetUserSettingData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseSetUserSettingData</h4>
<pre>public&nbsp;boolean&nbsp;parseSetUserSettingData(byte[]&nbsp;inData)</pre>
</li>
</ul>
<a name="getUserSettingSendData-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUserSettingSendData</h4>
<pre>public&nbsp;byte[]&nbsp;getUserSettingSendData(byte&nbsp;key)</pre>
</li>
</ul>
<a name="parseGetUserSettingData-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>parseGetUserSettingData</h4>
<pre>public&nbsp;byte[]&nbsp;parseGetUserSettingData(byte[]&nbsp;inData)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UHFProtocolParseByJava.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseBleByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" target="_top">Frames</a></li>
<li><a href="UHFProtocolParseByJava.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
