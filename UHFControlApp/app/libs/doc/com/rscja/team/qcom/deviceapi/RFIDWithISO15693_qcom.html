<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>RFIDWithISO15693_qcom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RFIDWithISO15693_qcom";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":9,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithISO15693_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443B_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" target="_top">Frames</a></li>
<li><a href="RFIDWithISO15693_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.deviceapi</div>
<h2 title="Class RFIDWithISO15693_qcom" class="title">Class RFIDWithISO15693_qcom</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/Device_qcom.html" title="class in com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi.Device_qcom</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi.RFIDBase_qcom</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.deviceapi.RFIDWithISO15693_qcom</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDBase</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">RFIDWithISO15693_qcom</span>
extends <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDBase_qcom</a>
implements <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></pre>
<div class="block">RFID模块ISO15693协议操作类,<br>
 RFID module ISO15639 protocol operation type<br>
 <p>
 注意： <br>
 Attention: <br>
 1、使用前请确认您的机器已安装此模块。 <br>
 1. Make sure this module is installed before using your device.<br>
 2、要正常使用模块需要在\libs\armeabi\目录放置libDeviceAPI.so文件 <br>
 2. Put libDeviceAPI.so file in directory \libs\armeabi\ then module can be used normally.<br>
 3、在操作设备前需要调用 <b><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#init--"><code>RFIDBase_qcom.init()</code></a></b> 打开设备，使用完后调用 <b><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#free--"><code>RFIDBase_qcom.free()</code></a></b> 关闭设备<br>
 3. Call <b><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#init--"><code>RFIDBase_qcom.init()</code></a></b> to switch on device before using device, call <b><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#free--"><code>RFIDBase_qcom.free()</code></a></b> to switch off the device after using.<br></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>liuruifeng</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#genericFunction_ex-char-char-char:A-int-">genericFunction_ex</a></span>(char&nbsp;command,
                  char&nbsp;IcMfg,
                  char[]&nbsp;databuf,
                  int&nbsp;datalen)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#genericFunction-char-char:A-char-">genericFunction</a></span>(char&nbsp;command,
               char[]&nbsp;databuf,
               char&nbsp;datalen)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取ISO15693协议操作实例<br>
 Acquire ISO15693 protocol operation Instance<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity">ISO15693Entity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#inventory--">inventory</a></span>()</code>
<div class="block">读卡<br>
 read card<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#lockAFI--">lockAFI</a></span>()</code>
<div class="block">锁定AFI<br>
 lock AFI<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#lockDSFID--">lockDSFID</a></span>()</code>
<div class="block">锁定DSFID<br>
 lock DSFID<br></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity">ISO15693Entity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#read-int-">read</a></span>(int&nbsp;block)</code>
<div class="block">读取块数据<br>
 read block data<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity">ISO15693Entity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#read-int-int-">read</a></span>(int&nbsp;startBlock,
    int&nbsp;Blocklen)</code>
<div class="block">读取块数据<br>
 block data<br></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#read-com.rscja.deviceapi.entity.ISO15693Entity-int-">read</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity">ISO15693Entity</a>&nbsp;entity,
    int&nbsp;block)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#write-int-int-java.lang.String-">write</a></span>(int&nbsp;startBlock,
     int&nbsp;Blocklen,
     java.lang.String&nbsp;hexData)</code>
<div class="block">写卡<br>
 card writing<br></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#write-int-java.lang.String-">write</a></span>(int&nbsp;block,
     java.lang.String&nbsp;hexData)</code>
<div class="block">写卡<br>
 card writing<br></div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#write-com.rscja.deviceapi.entity.ISO15693Entity-int-java.lang.String-">write</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity">ISO15693Entity</a>&nbsp;entity,
     int&nbsp;block,
     java.lang.String&nbsp;hexData)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#writeAFI-int-">writeAFI</a></span>(int&nbsp;iAFI)</code>
<div class="block">写入AFI<br>
 Write AFi<br></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html#writeDSFID-int-">writeDSFID</a></span>(int&nbsp;iDSFID)</code>
<div class="block">写入DSFID<br>
 Write DSFID<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.qcom.deviceapi.RFIDBase_qcom">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.qcom.deviceapi.<a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDBase_qcom</a></h3>
<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#free--">free</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#getVersion--">getVersion</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#init--">init</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#init-boolean-">init</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html#rfidUpgrade-int-int-int-byte:A-">rfidUpgrade</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.qcom.deviceapi.Device_qcom">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.qcom.deviceapi.<a href="../../../../../com/rscja/team/qcom/deviceapi/Device_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Device_qcom</a></h3>
<code><a href="../../../../../com/rscja/team/qcom/deviceapi/Device_qcom.html#isPowerOn--">isPowerOn</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IRFIDBase">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDBase</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#free--">free</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#getVersion--">getVersion</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#init--">init</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#init-boolean-">init</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#isPowerOn--">isPowerOn</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDBase.html#rfidUpgrade-int-int-int-byte:A-">rfidUpgrade</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a>&nbsp;getInstance()
                                         throws <a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></pre>
<div class="block">获取ISO15693协议操作实例<br>
 Acquire ISO15693 protocol operation Instance<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ISO15693协议操作实例<br>
 ISO15693 protocol operation Instance<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception">ConfigurationException</a></code> - 配置错误异常<br>
                                Configuration err<br></dd>
</dl>
</li>
</ul>
<a name="inventory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inventory</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity">ISO15693Entity</a>&nbsp;inventory()</pre>
<div class="block">读卡<br>
 read card<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#inventory--">inventory</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回ISO15693Entity实体，寻卡失败返回null<br>
 return ISO15693Entity body, card searching failed return null<br></dd>
</dl>
</li>
</ul>
<a name="read-com.rscja.deviceapi.entity.ISO15693Entity-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;java.lang.String&nbsp;read(<a href="../../../../../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity">ISO15693Entity</a>&nbsp;entity,
                             int&nbsp;block)
                      throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#read-com.rscja.deviceapi.entity.ISO15693Entity-int-">read</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></code></dd>
</dl>
</li>
</ul>
<a name="read-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity">ISO15693Entity</a>&nbsp;read(int&nbsp;block)
                    throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></pre>
<div class="block">读取块数据<br>
 read block data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#read-int-">read</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>block</code> - 块区<br>
              block<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回ISO15693Entity实体，寻卡失败返回null<br>
 return ISO1593Entity body, card searching failure return null<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></code> - 读卡失败异常<br>
                                  card reading err<br></dd>
</dl>
</li>
</ul>
<a name="read-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity">ISO15693Entity</a>&nbsp;read(int&nbsp;startBlock,
                           int&nbsp;Blocklen)
                    throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></pre>
<div class="block">读取块数据<br>
 block data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#read-int-int-">read</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>startBlock</code> - 块区<br>
              block<br></dd>
<dd><code>Blocklen</code> - 块区<br>
              block<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回ISO15693Entity实体，寻卡失败返回null<br>
 return ISO15693Entity body, card searching failure return null<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception">RFIDReadFailureException</a></code> - 读卡失败异常<br>
                                  Card reading err<br></dd>
</dl>
</li>
</ul>
<a name="write-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;boolean&nbsp;write(int&nbsp;block,
                     java.lang.String&nbsp;hexData)
              throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></pre>
<div class="block">写卡<br>
 card writing<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#write-int-java.lang.String-">write</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>block</code> - 块区<br>
                block<br></dd>
<dd><code>hexData</code> - 十六进制数据<br>
                hexdecimal data<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true success, false failure<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></code> - 寻卡失败异常<br>
                               card searching err<br></dd>
</dl>
</li>
</ul>
<a name="write-com.rscja.deviceapi.entity.ISO15693Entity-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;boolean&nbsp;write(<a href="../../../../../com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity">ISO15693Entity</a>&nbsp;entity,
                     int&nbsp;block,
                     java.lang.String&nbsp;hexData)
              throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#write-com.rscja.deviceapi.entity.ISO15693Entity-int-java.lang.String-">write</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></code></dd>
</dl>
</li>
</ul>
<a name="write-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;boolean&nbsp;write(int&nbsp;startBlock,
                     int&nbsp;Blocklen,
                     java.lang.String&nbsp;hexData)
              throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></pre>
<div class="block">写卡<br>
 card writing<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#write-int-int-java.lang.String-">write</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>Blocklen</code> - 块区<br>
                block<br></dd>
<dd><code>hexData</code> - 十六进制数据<br>
                hexdecimal data<br></dd>
<dd><code>startBlock</code> - 块区<br>
                block<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true success, false failed<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></code> - 寻卡失败异常<br>
                               card searching err<br></dd>
</dl>
</li>
</ul>
<a name="writeAFI-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeAFI</h4>
<pre>public&nbsp;boolean&nbsp;writeAFI(int&nbsp;iAFI)
                 throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></pre>
<div class="block">写入AFI<br>
 Write AFi<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#writeAFI-int-">writeAFI</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>iAFI</code> - AFI值<br>
             AFI value<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true success, false failed<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></code> - 寻卡失败异常<br>
                               card searching err<br></dd>
</dl>
</li>
</ul>
<a name="lockAFI--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lockAFI</h4>
<pre>public&nbsp;boolean&nbsp;lockAFI()
                throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></pre>
<div class="block">锁定AFI<br>
 lock AFI<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#lockAFI--">lockAFI</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true success, false failed<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></code></dd>
</dl>
</li>
</ul>
<a name="writeDSFID-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeDSFID</h4>
<pre>public&nbsp;boolean&nbsp;writeDSFID(int&nbsp;iDSFID)
                   throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></pre>
<div class="block">写入DSFID<br>
 Write DSFID<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#writeDSFID-int-">writeDSFID</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>iDSFID</code> - DSFID值<br>
               DSFID value<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true success, false failed<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></code> - 寻卡失败异常<br>
                               card searching err<br></dd>
</dl>
</li>
</ul>
<a name="lockDSFID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lockDSFID</h4>
<pre>public&nbsp;boolean&nbsp;lockDSFID()
                  throws <a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></pre>
<div class="block">锁定DSFID<br>
 lock DSFID<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#lockDSFID--">lockDSFID</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 true success, false failed<br></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception">RFIDNotFoundException</a></code> - 寻卡失败异常<br>
                               card searching err<br></dd>
</dl>
</li>
</ul>
<a name="genericFunction-char-char:A-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>genericFunction</h4>
<pre>public&nbsp;char[]&nbsp;genericFunction(char&nbsp;command,
                              char[]&nbsp;databuf,
                              char&nbsp;datalen)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#genericFunction-char-char:A-char-">genericFunction</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>command</code> - 命令<br>
                command<br></dd>
<dd><code>databuf</code> - 数据<br>
                data<br></dd>
<dd><code>datalen</code> - 数据长度<br>
                data length<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>索引1表示状态，非零 失败， 0x00 成功，数据 64字节<br>
 index 1 means status, NZ failed, 0x00 success, data 64 byte<br></dd>
</dl>
</li>
</ul>
<a name="genericFunction_ex-char-char-char:A-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>genericFunction_ex</h4>
<pre>public&nbsp;char[]&nbsp;genericFunction_ex(char&nbsp;command,
                                 char&nbsp;IcMfg,
                                 char[]&nbsp;databuf,
                                 int&nbsp;datalen)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html#genericFunction_ex-char-char-char:A-int-">genericFunction_ex</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithISO15693</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>command</code> - 命令<br>
                command<br></dd>
<dd><code>IcMfg</code> - 厂商信息<br></dd>
<dd><code>databuf</code> - 数据<br>
                data<br></dd>
<dd><code>datalen</code> - 数据长度<br>
                data length<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>索引1表示状态，非零 失败， 0x00 成功，数据 64字节<br>
 index 1 means status, NZ failed, 0x00 success, data 64 byte<br></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithISO15693_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithISO14443B_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" target="_top">Frames</a></li>
<li><a href="RFIDWithISO15693_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
