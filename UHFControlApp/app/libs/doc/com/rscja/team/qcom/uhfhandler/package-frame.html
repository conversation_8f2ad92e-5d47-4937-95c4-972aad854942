<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>com.rscja.team.qcom.uhfhandler</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../../com/rscja/team/qcom/uhfhandler/package-summary.html" target="classFrame">com.rscja.team.qcom.uhfhandler</a></h1>
<div class="indexContainer">
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="HFR1UsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler" target="classFrame">HFR1UsbDataHandle</a></li>
<li><a href="UHFDataHandleBase.html" title="class in com.rscja.team.qcom.uhfhandler" target="classFrame">UHFDataHandleBase</a></li>
<li><a href="UHFRxBLEDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler" target="classFrame">UHFRxBLEDataHandle</a></li>
<li><a href="UHFRxUsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler" target="classFrame">UHFRxUsbDataHandle</a></li>
<li><a href="UHFUR4DataHandle.html" title="class in com.rscja.team.qcom.uhfhandler" target="classFrame">UHFUR4DataHandle</a></li>
<li><a href="UHFUrAxDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler" target="classFrame">UHFUrAxDataHandle</a></li>
</ul>
</div>
</body>
</html>
