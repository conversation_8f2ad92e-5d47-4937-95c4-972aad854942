<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Module_qcom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Module_qcom";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":9,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Module_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/LedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/PSAM_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/Module_qcom.html" target="_top">Frames</a></li>
<li><a href="Module_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.deviceapi</div>
<h2 title="Class Module_qcom" class="title">Class Module_qcom</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.deviceapi.Module_qcom</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Module_qcom</span>
extends java.lang.Object
implements <a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></pre>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Administrator</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#closeSerail--">closeSerail</a></span>()</code>
<div class="block">关闭串口</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#free--">free</a></span>()</code>
<div class="block">释放模块,模块断电同时关闭串口<br>
 Release module<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Module_qcom</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取操作实例<br>
 Acquire operation Instance<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#init-int-">init</a></span>(int&nbsp;module)</code>
<div class="block">初始化模块,模块上电同时打开串口<br>
 Initialize module<br></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#init-int-int-">init</a></span>(int&nbsp;module,
    int&nbsp;baudrate)</code>
<div class="block">初始化模块,模块上电同时打开串口</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#init-int-int-int-int-int-">init</a></span>(int&nbsp;module,
    int&nbsp;baudrate,
    int&nbsp;databits,
    int&nbsp;stopbits,
    int&nbsp;check)</code>
<div class="block">初始化模块 ,模块上电同时打开串口<br/>
 Initialize the module</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#ioctl_gpio-int-boolean-">ioctl_gpio</a></span>(int&nbsp;gpio,
          boolean&nbsp;isHigh)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#openSerail-java.lang.String-int-int-int-int-">openSerail</a></span>(java.lang.String&nbsp;path,
          int&nbsp;baudrate,
          int&nbsp;databits,
          int&nbsp;stopbits,
          int&nbsp;check)</code>
<div class="block">打开串口</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#powerOff-android.content.Context-int-">powerOff</a></span>(android.content.Context&nbsp;context,
        int&nbsp;module)</code>
<div class="block">模块下电，仅下电不关串口<br>
 module power off, power off only<br></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#powerOff-int-">powerOff</a></span>(int&nbsp;module)</code>
<div class="block">模块下电，仅下电不关串口<br>
 module power off, power off only<br></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#powerOn-android.content.Context-int-">powerOn</a></span>(android.content.Context&nbsp;context,
       int&nbsp;module)</code>
<div class="block">模块上电，仅上电不开串口,通过系统app上电,不经过DeviceAPI, 配合<a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#powerOff-android.content.Context-int-"><code>powerOff(Context context,int module)</code></a>  使用<br>
 Module power on, power on only<br></div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#powerOn-int-">powerOn</a></span>(int&nbsp;module)</code>
<div class="block">模块上电，仅上电不开串口,通过DeviceAPI代码上电,配合<a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#powerOff-int-"><code>powerOff(int module)</code></a>  使用<br>
 Module power on, power on only<br></div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#receive--">receive</a></span>()</code>
<div class="block">接收数据,读取串口数据会等待1秒钟<br>
 Receive data<br></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#receiveEx--">receiveEx</a></span>()</code>
<div class="block">接收数据,读取串口数据无等待时间<br>
 Receive data<br></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#send-byte:A-">send</a></span>(byte[]&nbsp;data)</code>
<div class="block">发送数据<br>
 Send data<br></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#sendAndReceive-byte:A-byte:A-">sendAndReceive</a></span>(byte[]&nbsp;sendData,
              byte[]&nbsp;outData)</code>
<div class="block">收发数据<br></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html" title="class in com.rscja.team.qcom.deviceapi">Module_qcom</a>&nbsp;getInstance()</pre>
<div class="block">获取操作实例<br>
 Acquire operation Instance<br></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="powerOn-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>powerOn</h4>
<pre>public&nbsp;boolean&nbsp;powerOn(int&nbsp;module)</pre>
<div class="block">模块上电，仅上电不开串口,通过DeviceAPI代码上电,配合<a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#powerOff-int-"><code>powerOff(int module)</code></a>  使用<br>
 Module power on, power on only<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#powerOn-int-">powerOn</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>module</code> - 模块编号 1、1D（9600） 2、RFID（115200） 3、UHF（115200） 4、Finger（57600）<br>
              module module serial number 1, 1D(9600) 2, RFID(115200) 3, UHF(115200) 4, Finger(57600)<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功true，失败false</dd>
</dl>
</li>
</ul>
<a name="powerOn-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>powerOn</h4>
<pre>public&nbsp;boolean&nbsp;powerOn(android.content.Context&nbsp;context,
                       int&nbsp;module)</pre>
<div class="block">模块上电，仅上电不开串口,通过系统app上电,不经过DeviceAPI, 配合<a href="../../../../../com/rscja/team/qcom/deviceapi/Module_qcom.html#powerOff-android.content.Context-int-"><code>powerOff(Context context,int module)</code></a>  使用<br>
 Module power on, power on only<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#powerOn-android.content.Context-int-">powerOn</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>module</code> - 模块编号 3、UHF <br>
       module module serial number  3, UHF</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="ioctl_gpio-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ioctl_gpio</h4>
<pre>public&nbsp;void&nbsp;ioctl_gpio(int&nbsp;gpio,
                       boolean&nbsp;isHigh)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#ioctl_gpio-int-boolean-">ioctl_gpio</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
</dl>
</li>
</ul>
<a name="powerOff-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>powerOff</h4>
<pre>public&nbsp;boolean&nbsp;powerOff(int&nbsp;module)</pre>
<div class="block">模块下电，仅下电不关串口<br>
 module power off, power off only<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#powerOff-int-">powerOff</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>module</code> - 模块编号 1、1D（9600） 2、RFID（115200） 3、UHF（115200） 4、Finger（57600）<br></dd>
<dd><code>module</code> - module serial number 1, 1D(9600) 2, RFID(115200) 3, UHF(115200) 4, Finger(57600)<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功true，失败false</dd>
</dl>
</li>
</ul>
<a name="powerOff-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>powerOff</h4>
<pre>public&nbsp;boolean&nbsp;powerOff(android.content.Context&nbsp;context,
                        int&nbsp;module)</pre>
<div class="block">模块下电，仅下电不关串口<br>
 module power off, power off only<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#powerOff-android.content.Context-int-">powerOff</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>module</code> - 模块编号 3、UHF（115200） <br></dd>
<dd><code>module</code> - module serial number 3, UHF(115200)<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功true，失败false</dd>
</dl>
</li>
</ul>
<a name="init-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;boolean&nbsp;init(int&nbsp;module)</pre>
<div class="block">初始化模块,模块上电同时打开串口<br>
 Initialize module<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#init-int-">init</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>module</code> - 模块编号 模块编号 1、1D（9600） 2、RFID（115200） 3、UHF（115200） 4、Finger（57600）,10、A8外接串口<br></dd>
<dd><code>module</code> - module serial number 1, 1D(9600) 2, RFID(115200) 3, UHF(115200) 4, Finger(57600),10、A8外接串口(115200) <br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功true，失败false</dd>
</dl>
</li>
</ul>
<a name="init-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;boolean&nbsp;init(int&nbsp;module,
                    int&nbsp;baudrate)</pre>
<div class="block">初始化模块,模块上电同时打开串口</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#init-int-int-">init</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>module</code> - 模块编号 模块编号 1、1D（9600） 2、RFID（115200） 3、UHF（115200） 4、Finger（57600）,10、A8扩展串口</dd>
<dd><code>baudrate</code> - 自定义波特率</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功true，失败false</dd>
</dl>
</li>
</ul>
<a name="init-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;boolean&nbsp;init(int&nbsp;module,
                    int&nbsp;baudrate,
                    int&nbsp;databits,
                    int&nbsp;stopbits,
                    int&nbsp;check)</pre>
<div class="block">初始化模块 ,模块上电同时打开串口<br/>
 Initialize the module</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#init-int-int-int-int-int-">init</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>module</code> - 模块编号 模块编号 1、1D（9600） 2、RFID（115200） 3、UHF（115200） 4、Finger（57600）  10、A8扩展串口</dd>
<dd><code>baudrate</code> - 自定义波特率 <br/></dd>
<dd><code>check</code> - 是否校验 <br/></dd>
<dd><code>databits</code> - 数据位   取值为  7 或者8</dd>
<dd><code>stopbits</code> - 停止位   取值为  1 或者2</dd>
<dd><code>check</code> - 效验类型 取值为  0无, 1奇, 2偶</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功true，失败false <br/>
 If sucess, return true, if failure, return false.</dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;boolean&nbsp;free()</pre>
<div class="block">释放模块,模块断电同时关闭串口<br>
 Release module<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#free--">free</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>成功true，失败false</dd>
</dl>
</li>
</ul>
<a name="send-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>send</h4>
<pre>public&nbsp;boolean&nbsp;send(byte[]&nbsp;data)</pre>
<div class="block">发送数据<br>
 Send data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#send-byte:A-">send</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - 数据<br>
             data <br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败</dd>
</dl>
</li>
</ul>
<a name="receive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>receive</h4>
<pre>public&nbsp;byte[]&nbsp;receive()</pre>
<div class="block">接收数据,读取串口数据会等待1秒钟<br>
 Receive data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#receive--">receive</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>数据<br>
 data<br></dd>
</dl>
</li>
</ul>
<a name="receiveEx--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>receiveEx</h4>
<pre>public&nbsp;byte[]&nbsp;receiveEx()</pre>
<div class="block">接收数据,读取串口数据无等待时间<br>
 Receive data<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#receiveEx--">receiveEx</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>数据<br>
 data<br></dd>
</dl>
</li>
</ul>
<a name="sendAndReceive-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sendAndReceive</h4>
<pre>public&nbsp;int&nbsp;sendAndReceive(byte[]&nbsp;sendData,
                          byte[]&nbsp;outData)</pre>
<div class="block">收发数据<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#sendAndReceive-byte:A-byte:A-">sendAndReceive</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sendData</code> - 发送的数据<br></dd>
<dd><code>outData</code> - 接收的数据<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回接收到的数据长度    (返回-1表示发送失败)</dd>
</dl>
</li>
</ul>
<a name="openSerail-java.lang.String-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openSerail</h4>
<pre>public&nbsp;boolean&nbsp;openSerail(java.lang.String&nbsp;path,
                          int&nbsp;baudrate,
                          int&nbsp;databits,
                          int&nbsp;stopbits,
                          int&nbsp;check)</pre>
<div class="block">打开串口</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#openSerail-java.lang.String-int-int-int-int-">openSerail</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baudrate</code> - 波特率</dd>
<dd><code>databits</code> - 数据位   取值为  7 或者8</dd>
<dd><code>stopbits</code> - 停止位   取值为  1 或者2</dd>
<dd><code>check</code> - 效验类型 取值为  0无, 1奇, 2偶</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="closeSerail--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>closeSerail</h4>
<pre>public&nbsp;boolean&nbsp;closeSerail()</pre>
<div class="block">关闭串口</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html#closeSerail--">closeSerail</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces">IModule</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Module_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/LedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/PSAM_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/Module_qcom.html" target="_top">Frames</a></li>
<li><a href="Module_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
