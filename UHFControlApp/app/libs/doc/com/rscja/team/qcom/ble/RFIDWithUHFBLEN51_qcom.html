<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>RFIDWithUHFBLEN51_qcom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RFIDWithUHFBLEN51_qcom";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithUHFBLEN51_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html" title="class in com.rscja.team.qcom.ble"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html" target="_top">Frames</a></li>
<li><a href="RFIDWithUHFBLEN51_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.ble</div>
<h2 title="Class RFIDWithUHFBLEN51_qcom" class="title">Class RFIDWithUHFBLEN51_qcom</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">com.rscja.deviceapi.UhfBase</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">com.rscja.team.qcom.ble.EmptyUhfBle</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.ble.RFIDWithUHFBLEN51_qcom</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../../com/rscja/custom/UHFTemperatureTag.RFIDWithUHFBLEN51.html" title="class in com.rscja.custom">UHFTemperatureTag.RFIDWithUHFBLEN51</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">RFIDWithUHFBLEN51_qcom</span>
extends <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a>
implements <a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.rscja.deviceapi.<a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC">Bank_EPC</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED">Bank_RESERVED</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID">Bank_TID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER">Bank_USER</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_ACCESS">LockBank_ACCESS</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_EPC">LockBank_EPC</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_KILL">LockBank_KILL</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_TID">LockBank_TID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_USER">LockBank_USER</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_LOCK">LockMode_LOCK</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_OPEN">LockMode_OPEN</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_PLOCK">LockMode_PLOCK</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_POPEN">LockMode_POPEN</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#UPDATE_UHF">UPDATE_UHF</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IBluetoothReader">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_FIRMWARE">VERSION_BT_FIRMWARE</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_HARDWARE">VERSION_BT_HARDWARE</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#VERSION_BT_SOFTWARE">VERSION_BT_SOFTWARE</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IReader">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#UPDATE_STM32">UPDATE_STM32</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#RFIDWithUHFBLEN51_qcom--">RFIDWithUHFBLEN51_qcom</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#getStatus-com.rscja.deviceapi.interfaces.ConnectionStatus-java.lang.Object-">getStatus</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;status,
         java.lang.Object&nbsp;device)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#isInventorying--">isInventorying</a></span>()</code>
<div class="block">uhf 是否正在盘点</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#readTagFromBuffer--">readTagFromBuffer</a></span>()</code>
<div class="block">Deprecated.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#readTagFromBufferList_EpcTidUser--">readTagFromBufferList_EpcTidUser</a></span>()</code>
<div class="block">Deprecated.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#readTagFromBufferList--">readTagFromBufferList</a></span>()</code>
<div class="block">Deprecated.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#setBluetoothReader-com.rscja.deviceapi.interfaces.IBluetoothData-">setBluetoothReader</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothData.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothData</a>&nbsp;bluetoothReader1)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/IUHFInventoryCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFInventoryCallback</a>&nbsp;inventoryCallback)</code>
<div class="block">设置盘点回调接口，接收循环盘点到的标签数据<br>
 Set the inventory callback interface to receive the label data from the cyclic inventory<br>
 备注：需要在开始循环盘点<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--"><code>IUHF.startInventoryTag()</code></a>之前调用此方法。<br>
 Note: This method needs to be called before starting the loop inventory <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--"><code>IUHF.startInventoryTag()</code></a>.<br></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#startInventoryTag--">startInventoryTag</a></span>()</code>
<div class="block">开始循环识别标签。</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#startInventoryTag-com.rscja.deviceapi.entity.InventoryParameter-">startInventoryTag</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/InventoryParameter.html" title="class in com.rscja.deviceapi.entity">InventoryParameter</a>&nbsp;inventoryParameter)</code>
<div class="block">开始循环识别标签。</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#startLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFLocationCallback-">startLocation</a></span>(android.content.Context&nbsp;context,
             java.lang.String&nbsp;label,
             int&nbsp;bank,
             int&nbsp;ptr,
             <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFLocationCallback</a>&nbsp;locationCallback)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#startRadarLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFRadarLocationCallback-">startRadarLocation</a></span>(android.content.Context&nbsp;context,
                  java.lang.String&nbsp;tag,
                  int&nbsp;bank,
                  int&nbsp;ptr,
                  <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFRadarLocationCallback</a>&nbsp;locationCallback)</code>
<div class="block">开始定位 <br>
 Start location</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html#stopInventory--">stopInventory</a></span>()</code>
<div class="block">停止循环识别，在调用此函数之后应当退出循环获取缓冲区的标签信息的子线程<br>
 Stop auto reading, after call this function to exit sub threads of tag data of buffer.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.qcom.ble.EmptyUhfBle">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.qcom.ble.<a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></h3>
<code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#blinkOfLed-int-int-int-">blinkOfLed</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#closeLed--">closeLed</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#connect-java.lang.String-">connect</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#connect-java.lang.String-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">connect</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#deleteAllTagToFlash--">deleteAllTagToFlash</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#disconnect--">disconnect</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#eraseData-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#free--">free</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getAllTagTotalFromFlash--">getAllTagTotalFromFlash</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getBattery--">getBattery</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getBeep--">getBeep</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getBleHardwareVersion--">getBleHardwareVersion</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getBluetoothVersion--">getBluetoothVersion</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getConnectStatus--">getConnectStatus</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getCW--">getCW</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getEx10SDKFirmware--">getEx10SDKFirmware</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getFastID--">getFastID</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getFrequencyMode--">getFrequencyMode</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getGen2--">getGen2</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getNewTagTotalFromFlash--">getNewTagTotalFromFlash</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getParameter-byte:A-">getParameter</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getPower--">getPower</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getProtocol--">getProtocol</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getReaderAwaitSleepTime--">getReaderAwaitSleepTime</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getRFLink--">getRFLink</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getSTM32Version--">getSTM32Version</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getTagDataFromFlash--">getTagDataFromFlash</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getTagfocus--">getTagfocus</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getTagLocate-android.content.Context-">getTagLocate</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getTemperature--">getTemperature</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#getVersion--">getVersion</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#init-android.content.Context-">init</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#inventorySingleTag--">inventorySingleTag</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#killTag-java.lang.String-">killTag</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#lockMem-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#openLed--">openLed</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#readData-java.lang.String-int-int-int-">readData</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">readData</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#scanBarcode--">scanBarcode</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#scanBarcodeToBytes--">scanBarcodeToBytes</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#sendData-byte:A-">sendData</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setBarcodeTypeInSSIID-boolean-">setBarcodeTypeInSSIID</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setBeep-boolean-">setBeep</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setCW-int-">setCW</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setDynamicDistance-int-">setDynamicDistance</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setEPCAndTIDMode--">setEPCAndTIDMode</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setEPCAndTIDUserMode-int-int-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setEPCMode--">setEPCMode</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setFastID-boolean-">setFastID</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setFilter-int-int-int-java.lang.String-">setFilter</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setFreHop-float-">setFreHop</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setFrequencyMode-int-">setFrequencyMode</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">setGen2</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setKeyEventCallback-com.rscja.deviceapi.interfaces.KeyEventCallback-">setKeyEventCallback</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setOnDataChangeListener-com.rscja.deviceapi.BluetoothReader.OnDataChangeListener-">setOnDataChangeListener</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setParameter-byte:A-byte:A-">setParameter</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setPower-int-">setPower</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setProtocol-int-">setProtocol</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setReaderAwaitSleepTime-int-">setReaderAwaitSleepTime</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setRemoteBluetoothName-java.lang.String-">setRemoteBluetoothName</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setRFLink-int-">setRFLink</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setSupportRssi-boolean-">setSupportRssi</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setTagFocus-boolean-">setTagFocus</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#startScanBarcode--">startScanBarcode</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#startScanBarcodeInBlinkMode-com.rscja.deviceapi.BluetoothReader.DecodeCallback-">startScanBarcodeInBlinkMode</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#startScanBarcodeInTriggleMode-com.rscja.deviceapi.BluetoothReader.DecodeCallback-">startScanBarcodeInTriggleMode</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-">startScanBTDevices</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#stopLocation--">stopLocation</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#stopRadarLocation--">stopRadarLocation</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#stopScanBarcode--">stopScanBarcode</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#stopScanBTDevices--">stopScanBTDevices</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#triggerBeep-int-">triggerBeep</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfJump2Boot--">uhfJump2Boot</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfJump2Boot-int-">uhfJump2Boot</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfJump2BootSTM32--">uhfJump2BootSTM32</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfStartUpdate--">uhfStartUpdate</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfStopUpdate--">uhfStopUpdate</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#uhfUpdating-byte:A-">uhfUpdating</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc</a>, <a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.UhfBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.deviceapi.<a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/UhfBase.html#factoryReset--">factoryReset</a>, <a href="../../../../../com/rscja/deviceapi/UhfBase.html#getFastInventoryMode--">getFastInventoryMode</a>, <a href="../../../../../com/rscja/deviceapi/UhfBase.html#readTcpServiceState--">readTcpServiceState</a>, <a href="../../../../../com/rscja/deviceapi/UhfBase.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/deviceapi/UhfBase.html#setFastInventoryMode-boolean-">setFastInventoryMode</a>, <a href="../../../../../com/rscja/deviceapi/UhfBase.html#setOnLowBatteryListener-android.content.Context-com.rscja.deviceapi.entity.LowBatteryEntity-com.rscja.deviceapi.interfaces.OnLowBatteryListener-">setOnLowBatteryListener</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUhfBle">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html#getEx10SDKFirmware--">getEx10SDKFirmware</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html#setDynamicDistance-int-">setDynamicDistance</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html#stopRadarLocation--">stopRadarLocation</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html#uhfJump2BootSTM32--">uhfJump2BootSTM32</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#factoryReset--">factoryReset</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#free--">free</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getConnectStatus--">getConnectStatus</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getCW--">getCW</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getFastInventoryMode--">getFastInventoryMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getFrequencyMode--">getFrequencyMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getGen2--">getGen2</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getProtocol--">getProtocol</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getRFLink--">getRFLink</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getTemperature--">getTemperature</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getVersion--">getVersion</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#init-android.content.Context-">init</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#inventorySingleTag--">inventorySingleTag</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-">killTag</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-">readData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">readData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setCW-int-">setCW</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDMode--">setEPCAndTIDMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-int-int-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCMode--">setEPCMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFastID-boolean-">setFastID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFastInventoryMode-boolean-">setFastInventoryMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFilter-int-int-int-java.lang.String-">setFilter</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFreHop-float-">setFreHop</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFrequencyMode-int-">setFrequencyMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">setGen2</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setProtocol-int-">setProtocol</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setRFLink-int-">setRFLink</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setTagFocus-boolean-">setTagFocus</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot--">uhfJump2Boot</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot-int-">uhfJump2Boot</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStartUpdate--">uhfStartUpdate</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStopUpdate--">uhfStopUpdate</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfUpdating-byte:A-">uhfUpdating</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUhfReader">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#deleteAllTagToFlash--">deleteAllTagToFlash</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#getAllTagTotalFromFlash--">getAllTagTotalFromFlash</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#getFastID--">getFastID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#getNewTagTotalFromFlash--">getNewTagTotalFromFlash</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#getReaderAwaitSleepTime--">getReaderAwaitSleepTime</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#getTagDataFromFlash--">getTagDataFromFlash</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#getTagfocus--">getTagfocus</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#setReaderAwaitSleepTime-int-">setReaderAwaitSleepTime</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IBluetoothReader">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothReader</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#connect-java.lang.String-">connect</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#connect-java.lang.String-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">connect</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#disconnect--">disconnect</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#free--">free</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBeep--">getBeep</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBleHardwareVersion--">getBleHardwareVersion</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getBluetoothVersion--">getBluetoothVersion</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#getConnectStatus--">getConnectStatus</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#init-android.content.Context-">init</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#sendData-byte:A-">sendData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#setOnDataChangeListener-com.rscja.deviceapi.BluetoothReader.OnDataChangeListener-">setOnDataChangeListener</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#setRemoteBluetoothName-java.lang.String-">setRemoteBluetoothName</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#startScanBTDevices-com.rscja.deviceapi.interfaces.ScanBTCallback-">startScanBTDevices</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#stopScanBarcode--">stopScanBarcode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothReader.html#stopScanBTDevices--">stopScanBTDevices</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IReader">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces">IReader</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#blinkOfLed-int-int-int-">blinkOfLed</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#closeLed--">closeLed</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#getBarcodeTypeInSSIID--">getBarcodeTypeInSSIID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#getBattery--">getBattery</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#getParameter-byte:A-">getParameter</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#getSTM32Version--">getSTM32Version</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#openLed--">openLed</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#scanBarcode--">scanBarcode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#scanBarcodeToBytes--">scanBarcodeToBytes</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#setBarcodeTypeInSSIID-boolean-">setBarcodeTypeInSSIID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#setBeep-boolean-">setBeep</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#setKeyEventCallback-com.rscja.deviceapi.interfaces.KeyEventCallback-">setKeyEventCallback</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#setParameter-byte:A-byte:A-">setParameter</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#startScanBarcode--">startScanBarcode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IReader.html#triggerBeep-int-">triggerBeep</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.ISingleAntenna">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html#getPower--">getPower</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html#setPower-int-">setPower</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IHandheldRFID">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html#getTagLocate-android.content.Context-">getTagLocate</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html#stopLocation--">stopLocation</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="RFIDWithUHFBLEN51_qcom--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RFIDWithUHFBLEN51_qcom</h4>
<pre>public&nbsp;RFIDWithUHFBLEN51_qcom()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setBluetoothReader-com.rscja.deviceapi.interfaces.IBluetoothData-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBluetoothReader</h4>
<pre>public&nbsp;void&nbsp;setBluetoothReader(<a href="../../../../../com/rscja/deviceapi/interfaces/IBluetoothData.html" title="interface in com.rscja.deviceapi.interfaces">IBluetoothData</a>&nbsp;bluetoothReader1)</pre>
</li>
</ul>
<a name="readTagFromBufferList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagFromBufferList</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;&nbsp;readTagFromBufferList()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#readTagFromBufferList--">IUhfReader</a></code></span></div>
<div class="block">Deprecated. Use <code>#setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code>  instead .</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfReader.html#readTagFromBufferList--">readTagFromBufferList</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces">IUhfReader</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#readTagFromBufferList--">readTagFromBufferList</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></code></dd>
</dl>
</li>
</ul>
<a name="readTagFromBufferList_EpcTidUser--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagFromBufferList_EpcTidUser</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;&nbsp;readTagFromBufferList_EpcTidUser()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html#readTagFromBufferList_EpcTidUser--">IUhfBle</a></code></span></div>
<div class="block">Deprecated. Use <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>IUHF.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>  instead .</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html#readTagFromBufferList_EpcTidUser--">readTagFromBufferList_EpcTidUser</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#readTagFromBufferList_EpcTidUser--">readTagFromBufferList_EpcTidUser</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></code></dd>
</dl>
</li>
</ul>
<a name="startInventoryTag-com.rscja.deviceapi.entity.InventoryParameter-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startInventoryTag</h4>
<pre>public&nbsp;boolean&nbsp;startInventoryTag(<a href="../../../../../com/rscja/deviceapi/entity/InventoryParameter.html" title="class in com.rscja.deviceapi.entity">InventoryParameter</a>&nbsp;inventoryParameter)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html#startInventoryTag-com.rscja.deviceapi.entity.InventoryParameter-">IUhfBle</a></code></span></div>
<div class="block"><p>开始循环识别标签。</p>
 <p>Begin looping through the identification labels.</p>
 <p>通过 <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>IUHF.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a> 这个回调接口获取标签数据，需要在开始盘点之前调用setInventoryCallback方法。</p>
 <p>Get the label data through the callback interface <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>IUHF.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>, you need to call the setInventoryCallback method before starting the inventory.</p>
 <p>备注：开启循环识别标签后模块只能响应<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--"><code>IUHF.stopInventory()</code></a>函数 。</p>
 <p>Note: The module can only respond to the <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--"><code>IUHF.stopInventory()</code></a> function after the loop identification tag is turned on.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html#startInventoryTag-com.rscja.deviceapi.entity.InventoryParameter-">startInventoryTag</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#startInventoryTag-com.rscja.deviceapi.entity.InventoryParameter-">startInventoryTag</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success)  false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="startInventoryTag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startInventoryTag</h4>
<pre>public&nbsp;boolean&nbsp;startInventoryTag()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--">IUHF</a></code></span></div>
<div class="block"><p>开始循环识别标签。</p>
 <p>Begin looping through the identification labels.</p>
 <p>通过 <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>IUHF.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a> 这个回调接口获取标签数据，需要在开始盘点之前调用setInventoryCallback方法。</p>
 <p>Get the label data through the callback interface <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>IUHF.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>, you need to call the setInventoryCallback method before starting the inventory.</p>
 <p>备注：开启循环识别标签后模块只能响应<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--"><code>IUHF.stopInventory()</code></a>函数 。</p>
 <p>Note: The module can only respond to the <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--"><code>IUHF.stopInventory()</code></a> function after the loop identification tag is turned on.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--">startInventoryTag</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#startInventoryTag--">startInventoryTag</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success)  false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="stopInventory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopInventory</h4>
<pre>public&nbsp;boolean&nbsp;stopInventory()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--">IUHF</a></code></span></div>
<div class="block">停止循环识别，在调用此函数之后应当退出循环获取缓冲区的标签信息的子线程<br>
 Stop auto reading, after call this function to exit sub threads of tag data of buffer. <br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--">stopInventory</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#stopInventory--">stopInventory</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="readTagFromBuffer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagFromBuffer</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;readTagFromBuffer()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readTagFromBuffer--">IUHF</a></code></span></div>
<div class="block">Deprecated. Use <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>IUHF.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>  instead .</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readTagFromBuffer--">readTagFromBuffer</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#readTagFromBuffer--">readTagFromBuffer</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></code></dd>
</dl>
</li>
</ul>
<a name="isInventorying--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isInventorying</h4>
<pre>public&nbsp;boolean&nbsp;isInventorying()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#isInventorying--">IUHF</a></code></span></div>
<div class="block">uhf 是否正在盘点</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#isInventorying--">isInventorying</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#isInventorying--">isInventorying</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getStatus-com.rscja.deviceapi.interfaces.ConnectionStatus-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatus</h4>
<pre>public&nbsp;void&nbsp;getStatus(<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;status,
                      java.lang.Object&nbsp;device)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html#getStatus-com.rscja.deviceapi.interfaces.ConnectionStatus-T-">getStatus</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>status</code> - 状态(status)</dd>
<dd><code>device</code> - 目标设备(device)</dd>
</dl>
</li>
</ul>
<a name="setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInventoryCallback</h4>
<pre>public&nbsp;void&nbsp;setInventoryCallback(<a href="../../../../../com/rscja/deviceapi/interfaces/IUHFInventoryCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFInventoryCallback</a>&nbsp;inventoryCallback)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">IUHF</a></code></span></div>
<div class="block">设置盘点回调接口，接收循环盘点到的标签数据<br>
 Set the inventory callback interface to receive the label data from the cyclic inventory<br>
 备注：需要在开始循环盘点<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--"><code>IUHF.startInventoryTag()</code></a>之前调用此方法。<br>
 Note: This method needs to be called before starting the loop inventory <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--"><code>IUHF.startInventoryTag()</code></a>.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inventoryCallback</code> - 盘点回调接口(inventory callback interface)</dd>
</dl>
</li>
</ul>
<a name="startLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFLocationCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startLocation</h4>
<pre>public&nbsp;boolean&nbsp;startLocation(android.content.Context&nbsp;context,
                             java.lang.String&nbsp;label,
                             int&nbsp;bank,
                             int&nbsp;ptr,
                             <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFLocationCallback</a>&nbsp;locationCallback)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html#startLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFLocationCallback-">startLocation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#startLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFLocationCallback-">startLocation</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></code></dd>
</dl>
</li>
</ul>
<a name="startRadarLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFRadarLocationCallback-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>startRadarLocation</h4>
<pre>public&nbsp;boolean&nbsp;startRadarLocation(android.content.Context&nbsp;context,
                                  java.lang.String&nbsp;tag,
                                  int&nbsp;bank,
                                  int&nbsp;ptr,
                                  <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFRadarLocationCallback</a>&nbsp;locationCallback)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html#startRadarLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFRadarLocationCallback-">IUhfBle</a></code></span></div>
<div class="block">开始定位 <br>
 Start location</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html#startRadarLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFRadarLocationCallback-">startRadarLocation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html#startRadarLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFRadarLocationCallback-">startRadarLocation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces">IUhfBle</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html#startRadarLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFRadarLocationCallback-">startRadarLocation</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble">EmptyUhfBle</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - context</dd>
<dd><code>tag</code> - 要定位的标签(locate to the label)</dd>
<dd><code>bank</code> - 标签的存储区(Storage area):<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED"><code>IUHF.Bank_RESERVED</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a></dd>
<dd><code>ptr</code> - 起始地址的偏移量(start address)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithUHFBLEN51_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html" title="class in com.rscja.team.qcom.ble"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html" target="_top">Frames</a></li>
<li><a href="RFIDWithUHFBLEN51_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
