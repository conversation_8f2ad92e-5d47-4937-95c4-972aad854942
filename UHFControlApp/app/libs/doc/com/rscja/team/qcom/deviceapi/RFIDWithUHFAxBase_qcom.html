<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>RFIDWithUHFAxBase_qcom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RFIDWithUHFAxBase_qcom";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithUHFAxBase_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" target="_top">Frames</a></li>
<li><a href="RFIDWithUHFAxBase_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.deviceapi</div>
<h2 title="Class RFIDWithUHFAxBase_qcom" class="title">Class RFIDWithUHFAxBase_qcom</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">com.rscja.deviceapi.UhfBase</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi.RFIDWithUHFUART_qcom</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.deviceapi.RFIDWithUHFAxBase_qcom</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi">RFIDWithUHFA4</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a>, <a href="../../../../../com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi">RFIDWithUHFA8</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">RFIDWithUHFAxBase_qcom</span>
extends <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a>
implements <a href="../../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.rscja.deviceapi.<a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC">Bank_EPC</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED">Bank_RESERVED</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID">Bank_TID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER">Bank_USER</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_ACCESS">LockBank_ACCESS</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_EPC">LockBank_EPC</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_KILL">LockBank_KILL</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_TID">LockBank_TID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_USER">LockBank_USER</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_LOCK">LockMode_LOCK</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_OPEN">LockMode_OPEN</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_PLOCK">LockMode_PLOCK</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_POPEN">LockMode_POPEN</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#UPDATE_UHF">UPDATE_UHF</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#buzzer--">buzzer</a></span>()</code>
<div class="block">成功的通知提示(发出提示音)<br>
 Success Notification Sound (Play Sound)</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#buzzerOn-int-int-int-">buzzerOn</a></span>(int&nbsp;playTime,
        int&nbsp;beepTime,
        int&nbsp;interval)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#free--">free</a></span>()</code>
<div class="block">关闭UHF模块<br>
 Switch off UHF module<br></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity">AntennaState</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#getANT--">getANT</a></span>()</code>
<div class="block">获取当前设置的天线</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaConnectState.html" title="class in com.rscja.deviceapi.entity">AntennaConnectState</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#getAntennaConnectState--">getAntennaConnectState</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity">AntennaPowerEntity</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#getAntennaPower--">getAntennaPower</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#led--">led</a></span>()</code>
<div class="block">成功的通知提示(指示灯闪烁)<br>
 Success Notification Sound (flashing light)</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#readTagFromBuffer--">readTagFromBuffer</a></span>()</code>
<div class="block">* Deprecated.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#setANT-java.util.List-">setANT</a></span>(java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity">AntennaState</a>&gt;&nbsp;antStatus)</code>
<div class="block">设置天线启用状态(Set the antenna enable state)</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#startInventoryTag--">startInventoryTag</a></span>()</code>
<div class="block">开始循环识别标签。</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html#successNotify--">successNotify</a></span>()</code>
<div class="block">成功的通知提示(指示灯闪烁同时发出提示音)<br>
 Success Notification Sound (Play notification tune when light flashing)</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.qcom.deviceapi.RFIDWithUHFUART_qcom">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.qcom.deviceapi.<a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></h3>
<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#eraseData-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#factoryReset--">factoryReset</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getConnectStatus--">getConnectStatus</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getCW--">getCW</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getEPCAndTIDUserModeEx-int:A-int:A-int:A-int:A-">getEPCAndTIDUserModeEx</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getErrCode--">getErrCode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getFastInventoryMode--">getFastInventoryMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getFrequencyMode--">getFrequencyMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getGen2--">getGen2</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getHardwareVersion--">getHardwareVersion</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getInstance--">getInstance</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getLBTMode--">getLBTMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getPower--">getPower</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getProtocol--">getProtocol</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getRFLink--">getRFLink</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getTagLocate-android.content.Context-">getTagLocate</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getTemperature--">getTemperature</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getVersion--">getVersion</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#init-android.content.Context-">init</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#inventorySingleTag--">inventorySingleTag</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#inventorySingleTag-com.rscja.deviceapi.entity.InventoryParameter-">inventorySingleTag</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#isInventorying--">isInventorying</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#isPowerOn--">isPowerOn</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#killTag-java.lang.String-">killTag</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#readData-java.lang.String-int-int-int-">readData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">readData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setCW-int-">setCW</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setDynamicDistance-int-">setDynamicDistance</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setEPCAndTIDMode--">setEPCAndTIDMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setEPCAndTIDUserMode-int-int-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setEPCAndTIDUserModeEx-int-int-int-int-int-">setEPCAndTIDUserModeEx</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setEPCAndUserReservedModeEx-int-int-int-int-int-">setEPCAndUserReservedModeEx</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setEPCMode--">setEPCMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setFastID-boolean-">setFastID</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setFastInventoryMode-boolean-">setFastInventoryMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setFilter-int-int-int-java.lang.String-">setFilter</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setFreHop-float-">setFreHop</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setFrequencyMode-int-">setFrequencyMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">setGen2</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setGen2-int-int-int-int-int-int-int-int-int-int-int-int-int-int-">setGen2</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setLBTMode-boolean-">setLBTMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setPower-int-">setPower</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setPowerOnBySystem-android.content.Context-">setPowerOnBySystem</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setProtocol-int-">setProtocol</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setRFLink-int-">setRFLink</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setTagFocus-boolean-">setTagFocus</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setUart-java.lang.String-">setUart</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#startInventoryTag-com.rscja.deviceapi.entity.InventoryParameter-">startInventoryTag</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#startLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFLocationCallback-">startLocation</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#startRadarLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFRadarLocationCallback-">startRadarLocation</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#stopInventory--">stopInventory</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#stopLocation--">stopLocation</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#stopRadarLocation--">stopRadarLocation</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfJump2Boot--">uhfJump2Boot</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfJump2Boot-int-">uhfJump2Boot</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfStartUpdate--">uhfStartUpdate</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfStopUpdate--">uhfStopUpdate</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfUpdating-byte:A-">uhfUpdating</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#UHFVerifyVoltage--">UHFVerifyVoltage</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.UhfBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.deviceapi.<a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/UhfBase.html#readTcpServiceState--">readTcpServiceState</a>, <a href="../../../../../com/rscja/deviceapi/UhfBase.html#setOnLowBatteryListener-android.content.Context-com.rscja.deviceapi.entity.LowBatteryEntity-com.rscja.deviceapi.interfaces.OnLowBatteryListener-">setOnLowBatteryListener</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUHFOfAndroidUart">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#setOnLowBatteryListener-android.content.Context-com.rscja.deviceapi.entity.LowBatteryEntity-com.rscja.deviceapi.interfaces.OnLowBatteryListener-">setOnLowBatteryListener</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;boolean&nbsp;free()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#free--">RFIDWithUHFUART_qcom</a></code></span></div>
<div class="block">关闭UHF模块<br>
 Switch off UHF module<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#free--">free</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#free--">free</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success)   false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="getANT--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getANT</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity">AntennaState</a>&gt;&nbsp;getANT()</pre>
<div class="block">获取当前设置的天线</div>
</li>
</ul>
<a name="setANT-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setANT</h4>
<pre>public&nbsp;boolean&nbsp;setANT(java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity">AntennaState</a>&gt;&nbsp;antStatus)</pre>
<div class="block">设置天线启用状态(Set the antenna enable state)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>antStatus</code> - 天线参数(Antenna parameter)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="readTagFromBuffer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagFromBuffer</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;readTagFromBuffer()</pre>
<div class="block">* Deprecated. Use <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>RFIDWithUHFUART_qcom.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>  instead .</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readTagFromBuffer--">readTagFromBuffer</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#readTagFromBuffer--">readTagFromBuffer</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="successNotify--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>successNotify</h4>
<pre>public&nbsp;void&nbsp;successNotify()</pre>
<div class="block">成功的通知提示(指示灯闪烁同时发出提示音)<br>
 Success Notification Sound (Play notification tune when light flashing)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html#successNotify--">successNotify</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a></code></dd>
</dl>
</li>
</ul>
<a name="led--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>led</h4>
<pre>public&nbsp;void&nbsp;led()</pre>
<div class="block">成功的通知提示(指示灯闪烁)<br>
 Success Notification Sound (flashing light)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html#led--">led</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a></code></dd>
</dl>
</li>
</ul>
<a name="buzzer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buzzer</h4>
<pre>public&nbsp;void&nbsp;buzzer()</pre>
<div class="block">成功的通知提示(发出提示音)<br>
 Success Notification Sound (Play Sound)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html#buzzer--">buzzer</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IURAxOfAndroidUart</a></code></dd>
</dl>
</li>
</ul>
<a name="buzzerOn-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buzzerOn</h4>
<pre>public&nbsp;void&nbsp;buzzerOn(int&nbsp;playTime,
                     int&nbsp;beepTime,
                     int&nbsp;interval)</pre>
</li>
</ul>
<a name="setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAntennaPower</h4>
<pre>public&nbsp;boolean&nbsp;setAntennaPower(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
                               int&nbsp;power)</pre>
</li>
</ul>
<a name="getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAntennaPower</h4>
<pre>public&nbsp;int&nbsp;getAntennaPower(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</pre>
</li>
</ul>
<a name="getAntennaPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAntennaPower</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity">AntennaPowerEntity</a>&gt;&nbsp;getAntennaPower()</pre>
</li>
</ul>
<a name="startInventoryTag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startInventoryTag</h4>
<pre>public&nbsp;boolean&nbsp;startInventoryTag()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--">IUHF</a></code></span></div>
<div class="block"><p>开始循环识别标签。</p>
 <p>Begin looping through the identification labels.</p>
 <p>通过 <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>IUHF.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a> 这个回调接口获取标签数据，需要在开始盘点之前调用setInventoryCallback方法。</p>
 <p>Get the label data through the callback interface <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>IUHF.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>, you need to call the setInventoryCallback method before starting the inventory.</p>
 <p>备注：开启循环识别标签后模块只能响应<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--"><code>IUHF.stopInventory()</code></a>函数 。</p>
 <p>Note: The module can only respond to the <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--"><code>IUHF.stopInventory()</code></a> function after the loop identification tag is turned on.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--">startInventoryTag</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#startInventoryTag--">startInventoryTag</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success)  false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="getAntennaConnectState--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getAntennaConnectState</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaConnectState.html" title="class in com.rscja.deviceapi.entity">AntennaConnectState</a>&gt;&nbsp;getAntennaConnectState()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithUHFAxBase_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" target="_top">Frames</a></li>
<li><a href="RFIDWithUHFAxBase_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
