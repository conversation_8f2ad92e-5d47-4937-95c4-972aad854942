<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Class com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/qcom/uhfhandler/class-use/UHFDataHandleBase.CMDInfo.html" target="_top">Frames</a></li>
<li><a href="UHFDataHandleBase.CMDInfo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo" class="title">Uses of Class<br>com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.interfaces">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.socket">com.rscja.team.qcom.socket</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.uhfhandler">com.rscja.team.qcom.uhfhandler</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.usb">com.rscja.team.qcom.usb</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a> in <a href="../../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a> with parameters of type <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static java.util.ArrayList&lt;<a href="../../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">UHFProtocolParseBase.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/deviceapi/UHFProtocolParseBase.html#parseReadTagDataEPC_TID_USER-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-com.rscja.deviceapi.entity.TagInfoRule-">parseReadTagDataEPC_TID_USER</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;data,
                            <a href="../../../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a>&nbsp;tagInfoRule)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.interfaces">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a> in <a href="../../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a> that return <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">IBluetoothData.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/deviceapi/interfaces/IBluetoothData.html#getTagCmd--">getTagCmd</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">IBluetoothData.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/deviceapi/interfaces/IBluetoothData.html#sendAndReceive-byte:A-int-">sendAndReceive</a></span>(byte[]&nbsp;sendData,
              int&nbsp;timeOut)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a> in <a href="../../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> with parameters of type <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">UHFProtocolParseByJava.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseContinuousInventoryTagData-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-com.rscja.deviceapi.entity.TagInfoRule-">parseContinuousInventoryTagData</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;inData,
                               <a href="../../../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a>&nbsp;tagInfoRule)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.ArrayList&lt;<a href="../../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">UHFProtocolParseUSBByJava_qcom.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseUSBByJava_qcom.html#parseReadTagDataEPC_TID_USER-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">parseReadTagDataEPC_TID_USER</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;inData)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">UHFProtocolParseByJava.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html#parseReadTagDataEPC_TID_USER-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-com.rscja.deviceapi.entity.TagInfoRule-">parseReadTagDataEPC_TID_USER</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;inData,
                            <a href="../../../../../../com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity">TagInfoRule</a>&nbsp;tagInfoRule)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.socket">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a> in <a href="../../../../../../com/rscja/team/qcom/socket/package-summary.html">com.rscja.team.qcom.socket</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/rscja/team/qcom/socket/package-summary.html">com.rscja.team.qcom.socket</a> that return <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">SocketManageUR4.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/socket/SocketManageUR4.html#getTagInfo--">getTagInfo</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">SocketManageA4.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/socket/SocketManageA4.html#receiveGPIOData--">receiveGPIOData</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">SocketManageA4.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/socket/SocketManageA4.html#receiveTagData--">receiveTagData</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">SocketManageUR4.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/socket/SocketManageUR4.html#sendAndReceive-byte:A-">sendAndReceive</a></span>(byte[]&nbsp;senddata)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">SocketManageA4.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/socket/SocketManageA4.html#sendAndReceive-byte:A-">sendAndReceive</a></span>(byte[]&nbsp;sendData)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">SocketManageUR4.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/socket/SocketManageUR4.html#sendAndReceive-byte:A-int-">sendAndReceive</a></span>(byte[]&nbsp;senddata,
              int&nbsp;timeOut)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">SocketManageA4.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/socket/SocketManageA4.html#sendAndReceive-byte:A-int-">sendAndReceive</a></span>(byte[]&nbsp;sendData,
              int&nbsp;controlWord)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">SocketManageA4.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/socket/SocketManageA4.html#sendAndReceive-byte:A-int-int-">sendAndReceive</a></span>(byte[]&nbsp;sendData,
              int&nbsp;controlWord,
              int&nbsp;timeOut)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.uhfhandler">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a> in <a href="../../../../../../com/rscja/team/qcom/uhfhandler/package-summary.html">com.rscja.team.qcom.uhfhandler</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/rscja/team/qcom/uhfhandler/package-summary.html">com.rscja.team.qcom.uhfhandler</a> that return <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">UHFUrAxDataHandle.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFUrAxDataHandle.html#getGPIOCmd--">getGPIOCmd</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">UHFDataHandleBase.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getLastBarcodeCmd-int-int-int-">getLastBarcodeCmd</a></span>(int&nbsp;cmd,
                 int&nbsp;controlWord,
                 int&nbsp;timeOut)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">UHFDataHandleBase.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getLastCmd-int-int-">getLastCmd</a></span>(int&nbsp;cmd,
          int&nbsp;timeOut)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">UHFDataHandleBase.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getLastCmd-int-int-int-">getLastCmd</a></span>(int&nbsp;cmd,
          int&nbsp;controlWord,
          int&nbsp;timeOut)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">UHFDataHandleBase.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getTagCmd--">getTagCmd</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/rscja/team/qcom/uhfhandler/package-summary.html">com.rscja.team.qcom.uhfhandler</a> that return types with arguments of type <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">UHFDataHandleBase.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getCmdList-int-">getCmdList</a></span>(int&nbsp;cmd)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">UHFDataHandleBase.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#getCmdList-int-int-">getCmdList</a></span>(int&nbsp;cmd,
          int&nbsp;controlWord)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/rscja/team/qcom/uhfhandler/package-summary.html">com.rscja.team.qcom.uhfhandler</a> with parameters of type <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UHFUrAxDataHandle.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFUrAxDataHandle.html#addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">addCmdList</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;info)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UHFUR4DataHandle.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFUR4DataHandle.html#addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">addCmdList</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;info)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UHFRxUsbDataHandle.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFRxUsbDataHandle.html#addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">addCmdList</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;info)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UHFRxBLEDataHandle.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFRxBLEDataHandle.html#addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">addCmdList</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;info)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><span class="typeNameLabel">UHFDataHandleBase.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">addCmdList</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;info)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HFR1UsbDataHandle.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/HFR1UsbDataHandle.html#addCmdList-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">addCmdList</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;info)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">UHFUrAxDataHandle.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFUrAxDataHandle.html#isBuildFullData-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">isBuildFullData</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;cmdInfo)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">UHFUR4DataHandle.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFUR4DataHandle.html#isBuildFullData-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">isBuildFullData</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;cmdInfo)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">UHFRxUsbDataHandle.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFRxUsbDataHandle.html#isBuildFullData-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">isBuildFullData</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;cmdinfo)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">UHFRxBLEDataHandle.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFRxBLEDataHandle.html#isBuildFullData-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">isBuildFullData</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;cmdinfo)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><span class="typeNameLabel">UHFDataHandleBase.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html#isBuildFullData-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">isBuildFullData</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;cmdinfo)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">HFR1UsbDataHandle.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/uhfhandler/HFR1UsbDataHandle.html#isBuildFullData-com.rscja.team.qcom.uhfhandler.UHFDataHandleBase.CMDInfo-">isBuildFullData</a></span>(<a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;cmdinfo)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.usb">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a> in <a href="../../../../../../com/rscja/team/qcom/usb/package-summary.html">com.rscja.team.qcom.usb</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/rscja/team/qcom/usb/package-summary.html">com.rscja.team.qcom.usb</a> that return <a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">UrxUsb_qcom.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/UrxUsb_qcom.html#getUHFTAGInfo--">getUHFTAGInfo</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">UrxUsb_qcom.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/UrxUsb_qcom.html#sendAndReceive-byte:A-">sendAndReceive</a></span>(byte[]&nbsp;bytes)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">RxUsb_qcom.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/RxUsb_qcom.html#sendAndReceive-byte:A-boolean-">sendAndReceive</a></span>(byte[]&nbsp;bytes,
              boolean&nbsp;clean)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">RxUsb_qcom.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/RxUsb_qcom.html#sendAndReceive-byte:A-boolean-int-">sendAndReceive</a></span>(byte[]&nbsp;bytes,
              boolean&nbsp;clean,
              int&nbsp;timeOut)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><span class="typeNameLabel">UrxUsb_qcom.</span><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/UrxUsb_qcom.html#sendAndReceive-byte:A-int-">sendAndReceive</a></span>(byte[]&nbsp;bytes,
              int&nbsp;timeOut)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/qcom/uhfhandler/class-use/UHFDataHandleBase.CMDInfo.html" target="_top">Frames</a></li>
<li><a href="UHFDataHandleBase.CMDInfo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
