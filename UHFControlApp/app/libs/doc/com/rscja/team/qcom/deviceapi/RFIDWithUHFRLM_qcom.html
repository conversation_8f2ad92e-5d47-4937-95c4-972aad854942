<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>RFIDWithUHFRLM_qcom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RFIDWithUHFRLM_qcom";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithUHFRLM_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" target="_top">Frames</a></li>
<li><a href="RFIDWithUHFRLM_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.deviceapi</div>
<h2 title="Class RFIDWithUHFRLM_qcom" class="title">Class RFIDWithUHFRLM_qcom</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">com.rscja.deviceapi.UhfBase</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi.RFIDWithUHFUART_qcom</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.deviceapi.RFIDWithUHFRLM_qcom</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces">IHandheldRFID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">ISingleAntenna</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">RFIDWithUHFRLM_qcom</span>
extends <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a>
implements <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.rscja.deviceapi.<a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC">Bank_EPC</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED">Bank_RESERVED</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID">Bank_TID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER">Bank_USER</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_ACCESS">LockBank_ACCESS</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_EPC">LockBank_EPC</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_KILL">LockBank_KILL</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_TID">LockBank_TID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_USER">LockBank_USER</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_LOCK">LockMode_LOCK</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_OPEN">LockMode_OPEN</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_PLOCK">LockMode_PLOCK</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_POPEN">LockMode_POPEN</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#UPDATE_UHF">UPDATE_UHF</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#getFrequencyMode--">getFrequencyMode</a></span>()</code>
<div class="block">读取模块的工作模式<br>
 Read work mode of module</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#inventorySingleTag--">inventorySingleTag</a></span>()</code>
<div class="block">单步识别标签<br>
 Identify tag in single mode</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#inventorySingleTagUii--">inventorySingleTagUii</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#killTag-java.lang.String-">killTag</a></span>(java.lang.String&nbsp;killPwd)</code>
<div class="block">销毁指定标签（不指定UII）,默认密码不能执行销毁<br>
 destroy specified tag (non-specified UII), default code cannot execute erase<br></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#killTag-java.lang.String-java.lang.String-">killTag</a></span>(java.lang.String&nbsp;killPwd,
       java.lang.String&nbsp;uii)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem</a></span>(java.lang.String&nbsp;accessPwd,
       java.lang.String&nbsp;lockCode)</code>
<div class="block">锁定标签<br>
 Lock tag</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#lockMem-java.lang.String-java.lang.String-java.lang.String-">lockMem</a></span>(java.lang.String&nbsp;accessPwd,
       java.lang.String&nbsp;lockCode,
       java.lang.String&nbsp;uii)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#readData-java.lang.String-int-int-int-">readData</a></span>(java.lang.String&nbsp;accessPwd,
        int&nbsp;bank,
        int&nbsp;ptr,
        int&nbsp;cnt)</code>
<div class="block">读取标签数据 <br>
 Read tag data</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#readData-java.lang.String-int-int-int-java.lang.String-">readData</a></span>(java.lang.String&nbsp;accessPwd,
        int&nbsp;bank,
        int&nbsp;ptr,
        int&nbsp;cnt,
        java.lang.String&nbsp;uii)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#readTagFromBuffer--">readTagFromBuffer</a></span>()</code>
<div class="block">Deprecated.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#setFrequencyMode-int-">setFrequencyMode</a></span>(int&nbsp;freMode)</code>
<div class="block">设置模块的工作模式 <br>
 Setup work mode of module</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#startInventoryTag-int-int-int-">startInventoryTag</a></span>(int&nbsp;flagAnti,
                 int&nbsp;initQ,
                 int&nbsp;cnt)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a></span>(java.lang.String&nbsp;accessPwd,
         int&nbsp;bank,
         int&nbsp;ptr,
         int&nbsp;cnt,
         java.lang.String&nbsp;data)</code>
<div class="block">向标签写入数据 <br>
 Write data in tag</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeData</a></span>(java.lang.String&nbsp;accessPwd,
         int&nbsp;bank,
         int&nbsp;ptr,
         int&nbsp;cnt,
         java.lang.String&nbsp;data,
         java.lang.String&nbsp;uii)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.team.qcom.deviceapi.RFIDWithUHFUART_qcom">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.team.qcom.deviceapi.<a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></h3>
<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#eraseData-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#factoryReset--">factoryReset</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#free--">free</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getConnectStatus--">getConnectStatus</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getCW--">getCW</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getEPCAndTIDUserModeEx-int:A-int:A-int:A-int:A-">getEPCAndTIDUserModeEx</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getErrCode--">getErrCode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getFastInventoryMode--">getFastInventoryMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getGen2--">getGen2</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getHardwareVersion--">getHardwareVersion</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getLBTMode--">getLBTMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getPower--">getPower</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getProtocol--">getProtocol</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getRFLink--">getRFLink</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getTagLocate-android.content.Context-">getTagLocate</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getTemperature--">getTemperature</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getVersion--">getVersion</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#init-android.content.Context-">init</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#inventorySingleTag-com.rscja.deviceapi.entity.InventoryParameter-">inventorySingleTag</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#isInventorying--">isInventorying</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#isPowerOn--">isPowerOn</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">readData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setCW-int-">setCW</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setDynamicDistance-int-">setDynamicDistance</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setEPCAndTIDMode--">setEPCAndTIDMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setEPCAndTIDUserMode-int-int-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setEPCAndTIDUserModeEx-int-int-int-int-int-">setEPCAndTIDUserModeEx</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setEPCAndUserReservedModeEx-int-int-int-int-int-">setEPCAndUserReservedModeEx</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setEPCMode--">setEPCMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setFastID-boolean-">setFastID</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setFastInventoryMode-boolean-">setFastInventoryMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setFilter-int-int-int-java.lang.String-">setFilter</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setFreHop-float-">setFreHop</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">setGen2</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setGen2-int-int-int-int-int-int-int-int-int-int-int-int-int-int-">setGen2</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setLBTMode-boolean-">setLBTMode</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setPower-int-">setPower</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setPowerOnBySystem-android.content.Context-">setPowerOnBySystem</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setProtocol-int-">setProtocol</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setRFLink-int-">setRFLink</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setTagFocus-boolean-">setTagFocus</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setUart-java.lang.String-">setUart</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#startInventoryTag--">startInventoryTag</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#startInventoryTag-com.rscja.deviceapi.entity.InventoryParameter-">startInventoryTag</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#startLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFLocationCallback-">startLocation</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#startRadarLocation-android.content.Context-java.lang.String-int-int-com.rscja.deviceapi.interfaces.IUHFRadarLocationCallback-">startRadarLocation</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#stopInventory--">stopInventory</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#stopLocation--">stopLocation</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#stopRadarLocation--">stopRadarLocation</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfJump2Boot--">uhfJump2Boot</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfJump2Boot-int-">uhfJump2Boot</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfStartUpdate--">uhfStartUpdate</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfStopUpdate--">uhfStopUpdate</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#uhfUpdating-byte:A-">uhfUpdating</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#UHFVerifyVoltage--">UHFVerifyVoltage</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc</a>, <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.UhfBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.deviceapi.<a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/UhfBase.html#readTcpServiceState--">readTcpServiceState</a>, <a href="../../../../../com/rscja/deviceapi/UhfBase.html#setOnLowBatteryListener-android.content.Context-com.rscja.deviceapi.entity.LowBatteryEntity-com.rscja.deviceapi.interfaces.OnLowBatteryListener-">setOnLowBatteryListener</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUHFOfAndroidUart">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces">IUHFOfAndroidUart</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#getErrCode--">getErrCode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#getHardwareVersion--">getHardwareVersion</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#isPowerOn--">isPowerOn</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#setOnLowBatteryListener-android.content.Context-com.rscja.deviceapi.entity.LowBatteryEntity-com.rscja.deviceapi.interfaces.OnLowBatteryListener-">setOnLowBatteryListener</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#setPowerOnBySystem-android.content.Context-">setPowerOnBySystem</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html#setUart-java.lang.String-">setUart</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#factoryReset--">factoryReset</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#free--">free</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getConnectStatus--">getConnectStatus</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getCW--">getCW</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getFastInventoryMode--">getFastInventoryMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getGen2--">getGen2</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getProtocol--">getProtocol</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getRFLink--">getRFLink</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getTemperature--">getTemperature</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getVersion--">getVersion</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#init-android.content.Context-">init</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#isInventorying--">isInventorying</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">readData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setCW-int-">setCW</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDMode--">setEPCAndTIDMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-int-int-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCMode--">setEPCMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFastID-boolean-">setFastID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFastInventoryMode-boolean-">setFastInventoryMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFilter-int-int-int-java.lang.String-">setFilter</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFreHop-float-">setFreHop</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">setGen2</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setProtocol-int-">setProtocol</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setRFLink-int-">setRFLink</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setTagFocus-boolean-">setTagFocus</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--">startInventoryTag</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--">stopInventory</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot--">uhfJump2Boot</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot-int-">uhfJump2Boot</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStartUpdate--">uhfStartUpdate</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStopUpdate--">uhfStopUpdate</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfUpdating-byte:A-">uhfUpdating</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="readData-java.lang.String-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readData</h4>
<pre>public&nbsp;java.lang.String&nbsp;readData(java.lang.String&nbsp;accessPwd,
                                 int&nbsp;bank,
                                 int&nbsp;ptr,
                                 int&nbsp;cnt)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-">IUHF</a></code></span></div>
<div class="block">读取标签数据 <br>
 Read tag data</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#readData-java.lang.String-int-int-int-">readData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-">readData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#readData-java.lang.String-int-int-int-">readData</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - Access password</dd>
<dd><code>bank</code> - 读取的存储区(read storage area): <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED"><code>IUHF.Bank_RESERVED</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>ptr</code> - 读取的起始地址(单位:字)  (read start address(unit: word))</dd>
<dd><code>cnt</code> - 读取的数据长度(单位:字)  (read data length(unit: word))</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回获取的数据, null表示读取失败 (return acquired data, null means read failure)</dd>
</dl>
</li>
</ul>
<a name="readData-java.lang.String-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readData</h4>
<pre>public&nbsp;java.lang.String&nbsp;readData(java.lang.String&nbsp;accessPwd,
                                 int&nbsp;bank,
                                 int&nbsp;ptr,
                                 int&nbsp;cnt,
                                 java.lang.String&nbsp;uii)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#readData-java.lang.String-int-int-int-java.lang.String-">readData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></code></dd>
</dl>
</li>
</ul>
<a name="writeData-java.lang.String-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeData</h4>
<pre>public&nbsp;boolean&nbsp;writeData(java.lang.String&nbsp;accessPwd,
                         int&nbsp;bank,
                         int&nbsp;ptr,
                         int&nbsp;cnt,
                         java.lang.String&nbsp;data)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-">IUHF</a></code></span></div>
<div class="block">向标签写入数据 <br>
 Write data in tag</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - ACCESS PASSWORD (4 bites)</dd>
<dd><code>bank</code> - 标签的存储区(Storage area):<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED"><code>IUHF.Bank_RESERVED</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a></dd>
<dd><code>ptr</code> - 起始地址的偏移量(start address(unit: word))</dd>
<dd><code>cnt</code> - 数据的长度（Word为单位，不能为0）(Data length(Word is unit, cannot be 0))</dd>
<dd><code>data</code> - 要写入的数据,十六进制格式 (Data format should be hexvalue)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="writeData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeData</h4>
<pre>public&nbsp;boolean&nbsp;writeData(java.lang.String&nbsp;accessPwd,
                         int&nbsp;bank,
                         int&nbsp;ptr,
                         int&nbsp;cnt,
                         java.lang.String&nbsp;data,
                         java.lang.String&nbsp;uii)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#writeData-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></code></dd>
</dl>
</li>
</ul>
<a name="killTag-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>killTag</h4>
<pre>public&nbsp;boolean&nbsp;killTag(java.lang.String&nbsp;killPwd)</pre>
<div class="block">销毁指定标签（不指定UII）,默认密码不能执行销毁<br>
 destroy specified tag (non-specified UII), default code cannot execute erase<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#killTag-java.lang.String-">killTag</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-">killTag</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#killTag-java.lang.String-">killTag</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>killPwd</code> - 销毁密码<br>
                kill password<br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true成功，false失败<br>
 ture is success, false is failed<br></dd>
</dl>
</li>
</ul>
<a name="killTag-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>killTag</h4>
<pre>public&nbsp;boolean&nbsp;killTag(java.lang.String&nbsp;killPwd,
                       java.lang.String&nbsp;uii)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#killTag-java.lang.String-java.lang.String-">killTag</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></code></dd>
</dl>
</li>
</ul>
<a name="lockMem-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lockMem</h4>
<pre>public&nbsp;boolean&nbsp;lockMem(java.lang.String&nbsp;accessPwd,
                       java.lang.String&nbsp;lockCode)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-java.lang.String-">IUHF</a></code></span></div>
<div class="block">锁定标签<br>
 Lock tag</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#lockMem-java.lang.String-java.lang.String-">lockMem</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-java.lang.String-">lockMem</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - ACCESS PASSWORD (4 bites)</dd>
<dd><code>lockCode</code> - 锁定码 (Lock Code)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="lockMem-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lockMem</h4>
<pre>public&nbsp;boolean&nbsp;lockMem(java.lang.String&nbsp;accessPwd,
                       java.lang.String&nbsp;lockCode,
                       java.lang.String&nbsp;uii)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#lockMem-java.lang.String-java.lang.String-java.lang.String-">lockMem</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></code></dd>
</dl>
</li>
</ul>
<a name="startInventoryTag-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startInventoryTag</h4>
<pre>public&nbsp;boolean&nbsp;startInventoryTag(int&nbsp;flagAnti,
                                 int&nbsp;initQ,
                                 int&nbsp;cnt)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#startInventoryTag-int-int-int-">startInventoryTag</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></code></dd>
</dl>
</li>
</ul>
<a name="readTagFromBuffer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagFromBuffer</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;readTagFromBuffer()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readTagFromBuffer--">IUHF</a></code></span></div>
<div class="block">Deprecated. Use <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>IUHF.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>  instead .</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readTagFromBuffer--">readTagFromBuffer</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#readTagFromBuffer--">readTagFromBuffer</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></code></dd>
</dl>
</li>
</ul>
<a name="inventorySingleTag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inventorySingleTag</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;inventorySingleTag()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#inventorySingleTag--">IUHF</a></code></span></div>
<div class="block">单步识别标签<br>
 Identify tag in single mode</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#inventorySingleTag--">inventorySingleTag</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#inventorySingleTag--">inventorySingleTag</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#inventorySingleTag--">inventorySingleTag</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回标签信息(return tag infor)</dd>
</dl>
</li>
</ul>
<a name="inventorySingleTagUii--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inventorySingleTagUii</h4>
<pre>public&nbsp;java.lang.String&nbsp;inventorySingleTagUii()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#inventorySingleTagUii--">inventorySingleTagUii</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></code></dd>
</dl>
</li>
</ul>
<a name="getFrequencyMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrequencyMode</h4>
<pre>public&nbsp;int&nbsp;getFrequencyMode()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getFrequencyMode--">IUHF</a></code></span></div>
<div class="block">读取模块的工作模式<br>
 Read work mode of module</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#getFrequencyMode--">getFrequencyMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getFrequencyMode--">getFrequencyMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#getFrequencyMode--">getFrequencyMode</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回工作模式，返回-1为失败 (return work mode, return -1 means failure) <br>
 0x01：China Standard(840~845MHz) <br>
 0x02：China Standard2(920~925MHz)  <br>
 0x04：Europe Standard(865~868MHz)  <br>
 0x08：USA(902-928MHz)    <br>
 0x16：Korea(917~923MHz)     <br>
 0x32: Japan(916.8~920.8MHz) <br>
 0x33: South Africa(915~919MHz)<br>
 0x34: China Taiwan(920~928Mhz)<br>
 0x35:Vietnam(918~923MHz)<br>
 0x36:Peru(915MHz-928MHz)<br>
 0x37:Russia( 860MHz-867.6MHz)<br>
 0x3B:Malaysia(919-923MHz)<br>
 0x3C:Brazil<br>
 0x3D:ETSI_UPPER<br>
 0x3E:Australia<br>
 0x3F:Indonesia(920-923MHz)<br>
 0x40:Israel<br>
 0x41:HK <br>
 0x42:New Zealand <br></dd>
</dl>
</li>
</ul>
<a name="setFrequencyMode-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setFrequencyMode</h4>
<pre>public&nbsp;boolean&nbsp;setFrequencyMode(int&nbsp;freMode)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFrequencyMode-int-">IUHF</a></code></span></div>
<div class="block">设置模块的工作模式 <br>
 Setup work mode of module</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html#setFrequencyMode-int-">setFrequencyMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFRLM</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFrequencyMode-int-">setFrequencyMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html#setFrequencyMode-int-">setFrequencyMode</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>freMode</code> - 0x01：China Standard(840~845MHz) <br>
 0x02：China Standard2(920~925MHz)  <br>
 0x04：Europe Standard(865~868MHz)  <br>
 0x08：USA(902-928MHz)    <br>
 0x16：Korea(917~923MHz)     <br>
 0x32: Japan(916.8~920.8MHz) <br>
 0x33: South Africa(915~919MHz)<br>
 0x34: China Taiwan(920~928Mhz)<br>
 0x35:Vietnam(918~923MHz)<br>
 0x36:Peru(915MHz-928MHz)<br>
 0x37:Russia( 860MHz-867.6MHz)<br>
 0x3B:Malaysia(919-923MHz)<br>
 0x3C:Brazil<br>
 0x3D:ETSI_UPPER<br>
 0x3E:Australia<br>
 0x3F:Indonesia(920-923MHz)<br>
 0x40:Israel<br>
 0x41:HK <br>
 0x42:New Zealand <br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithUHFRLM_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" target="_top">Frames</a></li>
<li><a href="RFIDWithUHFRLM_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
