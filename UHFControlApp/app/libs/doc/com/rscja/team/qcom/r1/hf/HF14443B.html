<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>HF14443B</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HF14443B";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HF14443B.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/rscja/team/qcom/r1/hf/HF14443A.html" title="class in com.rscja.team.qcom.r1.hf"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../../com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/qcom/r1/hf/HF14443B.html" target="_top">Frames</a></li>
<li><a href="HF14443B.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.rscja.team.qcom.r1.hf.HFBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.r1.hf</div>
<h2 title="Class HF14443B" class="title">Class HF14443B</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html" title="class in com.rscja.team.qcom.r1.hf">com.rscja.team.qcom.r1.hf.HFBase</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.r1.hf.HF14443B</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443B</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HF14443B</span>
extends <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html" title="class in com.rscja.team.qcom.r1.hf">HFBase</a>
implements <a href="../../../../../../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443B</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.team.qcom.r1.hf.HFBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.rscja.team.qcom.r1.hf.<a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html" title="class in com.rscja.team.qcom.r1.hf">HFBase</a></h3>
<code><a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_15693_SELECT">COMMAND_15693_SELECT</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ACTIVATEIDLE">COMMAND_ACTIVATEIDLE</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ANTICOLL">COMMAND_ANTICOLL</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ANTICOLLINVENTORY">COMMAND_ANTICOLLINVENTORY</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_AUTHENTICATION">COMMAND_AUTHENTICATION</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_COS_COMMAND">COMMAND_COS_COMMAND</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_DECREMENT">COMMAND_DECREMENT</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_FM1216RESET">COMMAND_FM1216RESET</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_GET_MUL_BLOCK">COMMAND_GET_MUL_BLOCK</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_GET_SYSTEM_INFO">COMMAND_GET_SYSTEM_INFO</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_HALT">COMMAND_HALT</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_HALT_TYPEB">COMMAND_HALT_TYPEB</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_INCREMENT">COMMAND_INCREMENT</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_INITVAL">COMMAND_INITVAL</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_INVENTORY">COMMAND_INVENTORY</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_LOCK_AFI">COMMAND_LOCK_AFI</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_LOCK_BLOCK">COMMAND_LOCK_BLOCK</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_LOCK_DSFID">COMMAND_LOCK_DSFID</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_READ">COMMAND_READ</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_READ_SM">COMMAND_READ_SM</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_READVAL">COMMAND_READVAL</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_REQUESTA">COMMAND_REQUESTA</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_RESET_TO_READY">COMMAND_RESET_TO_READY</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_RESTORE">COMMAND_RESTORE</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_SELECT">COMMAND_SELECT</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TRANSFER">COMMAND_TRANSFER</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TRANSFERCOMMAND">COMMAND_TRANSFERCOMMAND</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TYPEARATS">COMMAND_TYPEARATS</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TYPEAREST">COMMAND_TYPEAREST</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TYPEB_GET_UID">COMMAND_TYPEB_GET_UID</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_TYPEBREST">COMMAND_TYPEBREST</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ULANTICOLL">COMMAND_ULANTICOLL</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_ULWRITE">COMMAND_ULWRITE</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_WRITE">COMMAND_WRITE</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_WRITE_AFI">COMMAND_WRITE_AFI</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_WRITE_DSFID">COMMAND_WRITE_DSFID</a>, <a href="../../../../../../com/rscja/team/qcom/r1/hf/HFBase.html#COMMAND_WRITE_SM">COMMAND_WRITE_SM</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HF14443B.html#HF14443B-com.rscja.team.qcom.usb.R1HFUSB-">HF14443B</a></span>(<a href="../../../../../../com/rscja/team/qcom/usb/R1HFUSB.html" title="class in com.rscja.team.qcom.usb">R1HFUSB</a>&nbsp;usb)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HF14443B.html#getUidTypeB--">getUidTypeB</a></span>()</code>
<div class="block">获取卡片ID (Acquire card ID)</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HF14443B.html#resetTypeB--">resetTypeB</a></span>()</code>
<div class="block">寻卡(Search card)<br></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/r1/hf/HF14443B.html#sendCommand-byte:A-">sendCommand</a></span>(byte[]&nbsp;cmd)</code>
<div class="block">发送命令(send command)</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HF14443B-com.rscja.team.qcom.usb.R1HFUSB-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HF14443B</h4>
<pre>public&nbsp;HF14443B(<a href="../../../../../../com/rscja/team/qcom/usb/R1HFUSB.html" title="class in com.rscja.team.qcom.usb">R1HFUSB</a>&nbsp;usb)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="resetTypeB--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resetTypeB</h4>
<pre>public&nbsp;byte[]&nbsp;resetTypeB()</pre>
<div class="block">寻卡(Search card)<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/deviceapi/interfaces/IHF14443B.html#resetTypeB--">resetTypeB</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443B</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success will return HF14443RequestEntity data, failure will return null.<br></dd>
</dl>
</li>
</ul>
<a name="getUidTypeB--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUidTypeB</h4>
<pre>public&nbsp;byte[]&nbsp;getUidTypeB()</pre>
<div class="block">获取卡片ID (Acquire card ID)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/deviceapi/interfaces/IHF14443B.html#getUidTypeB--">getUidTypeB</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443B</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success will return HF14443RequestEntity data, failure will return null.<br></dd>
</dl>
</li>
</ul>
<a name="sendCommand-byte:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>sendCommand</h4>
<pre>public&nbsp;byte[]&nbsp;sendCommand(byte[]&nbsp;cmd)</pre>
<div class="block">发送命令(send command)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../../com/rscja/deviceapi/interfaces/IHF14443B.html#sendCommand-byte:A-">sendCommand</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../../com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces">IHF14443B</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cmd</code> - 命令数据</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>success will return data, failure will return null.<br></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HF14443B.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/rscja/team/qcom/r1/hf/HF14443A.html" title="class in com.rscja.team.qcom.r1.hf"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../../com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/qcom/r1/hf/HF14443B.html" target="_top">Frames</a></li>
<li><a href="HF14443B.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.rscja.team.qcom.r1.hf.HFBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
