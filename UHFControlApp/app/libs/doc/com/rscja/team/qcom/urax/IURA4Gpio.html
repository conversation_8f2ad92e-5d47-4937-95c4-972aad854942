<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>IURA4Gpio</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IURA4Gpio";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IURA4Gpio.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../../com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/urax/IURA4Gpio.html" target="_top">Frames</a></li>
<li><a href="IURA4Gpio.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.urax</div>
<h2 title="Interface IURA4Gpio" class="title">Interface IURA4Gpio</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../../com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax">QcomURA4Gpio</a>, <a href="../../../../../com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax">RKURA4Gpio</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IURA4Gpio</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity">GPIStateEntity</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html#inputStatus--">inputStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html#output1Off--">output1Off</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html#output1On--">output1On</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html#output2Off--">output2Off</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html#output2On--">output2On</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html#output3Off--">output3Off</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html#output3On--">output3On</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html#output4Off--">output4Off</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html#output4On--">output4On</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html#outputWgData0Off--">outputWgData0Off</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html#outputWgData0On--">outputWgData0On</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html#outputWgData1Off--">outputWgData1Off</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/urax/IURA4Gpio.html#outputWgData1On--">outputWgData1On</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="output1Off--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>output1Off</h4>
<pre>boolean&nbsp;output1Off()</pre>
</li>
</ul>
<a name="output1On--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>output1On</h4>
<pre>boolean&nbsp;output1On()</pre>
</li>
</ul>
<a name="output2Off--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>output2Off</h4>
<pre>boolean&nbsp;output2Off()</pre>
</li>
</ul>
<a name="output2On--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>output2On</h4>
<pre>boolean&nbsp;output2On()</pre>
</li>
</ul>
<a name="output3On--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>output3On</h4>
<pre>boolean&nbsp;output3On()</pre>
</li>
</ul>
<a name="output3Off--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>output3Off</h4>
<pre>boolean&nbsp;output3Off()</pre>
</li>
</ul>
<a name="output4On--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>output4On</h4>
<pre>boolean&nbsp;output4On()</pre>
</li>
</ul>
<a name="output4Off--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>output4Off</h4>
<pre>boolean&nbsp;output4Off()</pre>
</li>
</ul>
<a name="outputWgData0On--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>outputWgData0On</h4>
<pre>boolean&nbsp;outputWgData0On()</pre>
</li>
</ul>
<a name="outputWgData0Off--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>outputWgData0Off</h4>
<pre>boolean&nbsp;outputWgData0Off()</pre>
</li>
</ul>
<a name="outputWgData1On--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>outputWgData1On</h4>
<pre>boolean&nbsp;outputWgData1On()</pre>
</li>
</ul>
<a name="outputWgData1Off--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>outputWgData1Off</h4>
<pre>boolean&nbsp;outputWgData1Off()</pre>
</li>
</ul>
<a name="inputStatus--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>inputStatus</h4>
<pre>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity">GPIStateEntity</a>&gt;&nbsp;inputStatus()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IURA4Gpio.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../../com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/urax/IURA4Gpio.html" target="_top">Frames</a></li>
<li><a href="IURA4Gpio.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
