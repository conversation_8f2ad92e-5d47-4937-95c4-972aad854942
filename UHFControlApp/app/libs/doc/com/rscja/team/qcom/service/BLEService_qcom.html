<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>BLEService_qcom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BLEService_qcom";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BLEService_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.BluetoothStateReceiver.html" title="class in com.rscja.team.qcom.service"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/service/BLEService_qcom.html" target="_top">Frames</a></li>
<li><a href="BLEService_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.service</div>
<h2 title="Class BLEService_qcom" class="title">Class BLEService_qcom</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.service.BLEService_qcom</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">BLEService_qcom</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.BluetoothStateReceiver.html" title="class in com.rscja.team.qcom.service">BLEService_qcom.BluetoothStateReceiver</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.IDataCallBack.html" title="interface in com.rscja.team.qcom.service">BLEService_qcom.IDataCallBack</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#ACTION_DATA_AVAILABLE">ACTION_DATA_AVAILABLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#ACTION_GATT_DISCONNECTED">ACTION_GATT_DISCONNECTED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#ACTION_GATT_SERVICES_DISCOVERED">ACTION_GATT_SERVICES_DISCOVERED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#ACTION_SEARCH_DEVICES">ACTION_SEARCH_DEVICES</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.util.UUID</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#BLE_UUID_GAP">BLE_UUID_GAP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.util.UUID</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#BLE_UUID_GAP_CHARACTERISTIC_DEVICE_NAME">BLE_UUID_GAP_CHARACTERISTIC_DEVICE_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#BT_DEVICE">BT_DEVICE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#BT_RECORD">BT_RECORD</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#BT_RSSI">BT_RSSI</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.util.UUID</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#CCCD">CCCD</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#DEVICE_DOES_NOT_SUPPORT_UART">DEVICE_DOES_NOT_SUPPORT_UART</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#EXTRA_DATA">EXTRA_DATA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#isRead">isRead</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#isWrite">isWrite</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.util.UUID</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#RX_CHAR_UUID">RX_CHAR_UUID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.util.UUID</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#RX_SERVICE_UUID">RX_SERVICE_UUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.util.UUID</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#TX_CHAR_UUID">TX_CHAR_UUID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.util.UUID</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#VERSION_FIRMWARE_UUID">VERSION_FIRMWARE_UUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.util.UUID</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#VERSION_HARDWARE_UUID">VERSION_HARDWARE_UUID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.util.UUID</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#VERSION_SOFTWARE_UUID">VERSION_SOFTWARE_UUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.util.UUID</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#VERSION_UUID">VERSION_UUID</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#BLEService_qcom--">BLEService_qcom</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#close--">close</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#connect-java.lang.String-">connect</a></span>(java.lang.String&nbsp;address)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#connect-java.lang.String-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">connect</a></span>(java.lang.String&nbsp;address,
       <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&nbsp;btStatusCallback)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#disconnect--">disconnect</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#enableTXNotification--">enableTXNotification</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#free--">free</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#getBluetoothDeviceAddress--">getBluetoothDeviceAddress</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#getBTConnectStatus--">getBTConnectStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#getHardwareVersion--">getHardwareVersion</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.util.HashMap&lt;java.lang.String,java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#getVersion--">getVersion</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#initialize-android.content.Context-">initialize</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#readCharacteristic-android.bluetooth.BluetoothGattCharacteristic-">readCharacteristic</a></span>(android.bluetooth.BluetoothGattCharacteristic&nbsp;characteristic)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#setBtName-java.lang.String-">setBtName</a></span>(java.lang.String&nbsp;name)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#setDataCallBack-com.rscja.team.qcom.service.BLEService_qcom.IDataCallBack-">setDataCallBack</a></span>(<a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.IDataCallBack.html" title="interface in com.rscja.team.qcom.service">BLEService_qcom.IDataCallBack</a>&nbsp;dataCallBack)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#setStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setStatusCallback</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&nbsp;btStatusCallback)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#startBTScan-com.rscja.deviceapi.interfaces.ScanBTCallback-">startBTScan</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces">ScanBTCallback</a>&nbsp;scanBTCallback)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#stoptBTScan--">stoptBTScan</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.html#writeRXCharacteristic-byte:A-java.lang.String:A-">writeRXCharacteristic</a></span>(byte[]&nbsp;value,
                     java.lang.String[]&nbsp;msg)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="BT_RSSI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BT_RSSI</h4>
<pre>public static final&nbsp;java.lang.String BT_RSSI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.qcom.service.BLEService_qcom.BT_RSSI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BT_RECORD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BT_RECORD</h4>
<pre>public static final&nbsp;java.lang.String BT_RECORD</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.qcom.service.BLEService_qcom.BT_RECORD">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BT_DEVICE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BT_DEVICE</h4>
<pre>public static final&nbsp;java.lang.String BT_DEVICE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.qcom.service.BLEService_qcom.BT_DEVICE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="isWrite">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWrite</h4>
<pre>public static&nbsp;boolean isWrite</pre>
</li>
</ul>
<a name="isRead">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRead</h4>
<pre>public static&nbsp;boolean isRead</pre>
</li>
</ul>
<a name="ACTION_SEARCH_DEVICES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTION_SEARCH_DEVICES</h4>
<pre>public static final&nbsp;java.lang.String ACTION_SEARCH_DEVICES</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.qcom.service.BLEService_qcom.ACTION_SEARCH_DEVICES">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ACTION_GATT_DISCONNECTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTION_GATT_DISCONNECTED</h4>
<pre>public static final&nbsp;java.lang.String ACTION_GATT_DISCONNECTED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.qcom.service.BLEService_qcom.ACTION_GATT_DISCONNECTED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ACTION_GATT_SERVICES_DISCOVERED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTION_GATT_SERVICES_DISCOVERED</h4>
<pre>public static final&nbsp;java.lang.String ACTION_GATT_SERVICES_DISCOVERED</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.qcom.service.BLEService_qcom.ACTION_GATT_SERVICES_DISCOVERED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ACTION_DATA_AVAILABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTION_DATA_AVAILABLE</h4>
<pre>public static final&nbsp;java.lang.String ACTION_DATA_AVAILABLE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.qcom.service.BLEService_qcom.ACTION_DATA_AVAILABLE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="EXTRA_DATA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXTRA_DATA</h4>
<pre>public static final&nbsp;java.lang.String EXTRA_DATA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.qcom.service.BLEService_qcom.EXTRA_DATA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DEVICE_DOES_NOT_SUPPORT_UART">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEVICE_DOES_NOT_SUPPORT_UART</h4>
<pre>public static final&nbsp;java.lang.String DEVICE_DOES_NOT_SUPPORT_UART</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../constant-values.html#com.rscja.team.qcom.service.BLEService_qcom.DEVICE_DOES_NOT_SUPPORT_UART">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CCCD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CCCD</h4>
<pre>public static final&nbsp;java.util.UUID CCCD</pre>
</li>
</ul>
<a name="RX_SERVICE_UUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RX_SERVICE_UUID</h4>
<pre>public static final&nbsp;java.util.UUID RX_SERVICE_UUID</pre>
</li>
</ul>
<a name="RX_CHAR_UUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RX_CHAR_UUID</h4>
<pre>public static final&nbsp;java.util.UUID RX_CHAR_UUID</pre>
</li>
</ul>
<a name="TX_CHAR_UUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TX_CHAR_UUID</h4>
<pre>public static final&nbsp;java.util.UUID TX_CHAR_UUID</pre>
</li>
</ul>
<a name="BLE_UUID_GAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BLE_UUID_GAP</h4>
<pre>public static final&nbsp;java.util.UUID BLE_UUID_GAP</pre>
</li>
</ul>
<a name="BLE_UUID_GAP_CHARACTERISTIC_DEVICE_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BLE_UUID_GAP_CHARACTERISTIC_DEVICE_NAME</h4>
<pre>public static final&nbsp;java.util.UUID BLE_UUID_GAP_CHARACTERISTIC_DEVICE_NAME</pre>
</li>
</ul>
<a name="VERSION_UUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VERSION_UUID</h4>
<pre>public static final&nbsp;java.util.UUID VERSION_UUID</pre>
</li>
</ul>
<a name="VERSION_FIRMWARE_UUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VERSION_FIRMWARE_UUID</h4>
<pre>public static final&nbsp;java.util.UUID VERSION_FIRMWARE_UUID</pre>
</li>
</ul>
<a name="VERSION_HARDWARE_UUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VERSION_HARDWARE_UUID</h4>
<pre>public static final&nbsp;java.util.UUID VERSION_HARDWARE_UUID</pre>
</li>
</ul>
<a name="VERSION_SOFTWARE_UUID">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>VERSION_SOFTWARE_UUID</h4>
<pre>public static final&nbsp;java.util.UUID VERSION_SOFTWARE_UUID</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BLEService_qcom--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BLEService_qcom</h4>
<pre>public&nbsp;BLEService_qcom()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getBluetoothDeviceAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBluetoothDeviceAddress</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBluetoothDeviceAddress()</pre>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;boolean&nbsp;free()</pre>
</li>
</ul>
<a name="initialize-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initialize</h4>
<pre>public&nbsp;boolean&nbsp;initialize(android.content.Context&nbsp;context)</pre>
</li>
</ul>
<a name="setBtName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBtName</h4>
<pre>public&nbsp;boolean&nbsp;setBtName(java.lang.String&nbsp;name)</pre>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;java.util.HashMap&lt;java.lang.String,java.lang.String&gt;&nbsp;getVersion()</pre>
</li>
</ul>
<a name="getHardwareVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHardwareVersion</h4>
<pre>public&nbsp;java.lang.String&nbsp;getHardwareVersion()</pre>
</li>
</ul>
<a name="setStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStatusCallback</h4>
<pre>public&nbsp;void&nbsp;setStatusCallback(<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&nbsp;btStatusCallback)</pre>
</li>
</ul>
<a name="connect-java.lang.String-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>connect</h4>
<pre>public&nbsp;boolean&nbsp;connect(java.lang.String&nbsp;address,
                       <a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&nbsp;btStatusCallback)</pre>
</li>
</ul>
<a name="connect-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>connect</h4>
<pre>public&nbsp;boolean&nbsp;connect(java.lang.String&nbsp;address)</pre>
</li>
</ul>
<a name="disconnect--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disconnect</h4>
<pre>public&nbsp;void&nbsp;disconnect()</pre>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;void&nbsp;close()</pre>
</li>
</ul>
<a name="readCharacteristic-android.bluetooth.BluetoothGattCharacteristic-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readCharacteristic</h4>
<pre>public&nbsp;boolean&nbsp;readCharacteristic(android.bluetooth.BluetoothGattCharacteristic&nbsp;characteristic)</pre>
</li>
</ul>
<a name="enableTXNotification--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableTXNotification</h4>
<pre>public&nbsp;void&nbsp;enableTXNotification()</pre>
</li>
</ul>
<a name="writeRXCharacteristic-byte:A-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeRXCharacteristic</h4>
<pre>public&nbsp;boolean&nbsp;writeRXCharacteristic(byte[]&nbsp;value,
                                     java.lang.String[]&nbsp;msg)</pre>
</li>
</ul>
<a name="startBTScan-com.rscja.deviceapi.interfaces.ScanBTCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startBTScan</h4>
<pre>public&nbsp;void&nbsp;startBTScan(<a href="../../../../../com/rscja/deviceapi/interfaces/ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces">ScanBTCallback</a>&nbsp;scanBTCallback)</pre>
</li>
</ul>
<a name="setDataCallBack-com.rscja.team.qcom.service.BLEService_qcom.IDataCallBack-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDataCallBack</h4>
<pre>public&nbsp;void&nbsp;setDataCallBack(<a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.IDataCallBack.html" title="interface in com.rscja.team.qcom.service">BLEService_qcom.IDataCallBack</a>&nbsp;dataCallBack)</pre>
</li>
</ul>
<a name="getBTConnectStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBTConnectStatus</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;getBTConnectStatus()</pre>
</li>
</ul>
<a name="stoptBTScan--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>stoptBTScan</h4>
<pre>public&nbsp;void&nbsp;stoptBTScan()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BLEService_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../../com/rscja/team/qcom/service/BLEService_qcom.BluetoothStateReceiver.html" title="class in com.rscja.team.qcom.service"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/service/BLEService_qcom.html" target="_top">Frames</a></li>
<li><a href="BLEService_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
