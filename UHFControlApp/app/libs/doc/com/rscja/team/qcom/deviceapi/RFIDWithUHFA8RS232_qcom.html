<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>RFIDWithUHFA8RS232_qcom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RFIDWithUHFA8RS232_qcom";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":9,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithUHFA8RS232_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" target="_top">Frames</a></li>
<li><a href="RFIDWithUHFA8RS232_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.deviceapi</div>
<h2 title="Class RFIDWithUHFA8RS232_qcom" class="title">Class RFIDWithUHFA8RS232_qcom</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">com.rscja.deviceapi.UhfBase</a></li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.deviceapi.RFIDWithUHFA8RS232_qcom</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8RS232</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IUHFA8</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">RFIDWithUHFA8RS232_qcom</span>
extends <a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a>
implements <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8RS232</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.rscja.deviceapi.<a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/UhfBase.ErrorCode.html" title="class in com.rscja.deviceapi">UhfBase.ErrorCode</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC">Bank_EPC</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED">Bank_RESERVED</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID">Bank_TID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER">Bank_USER</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_ACCESS">LockBank_ACCESS</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_EPC">LockBank_EPC</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_KILL">LockBank_KILL</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_TID">LockBank_TID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_USER">LockBank_USER</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_LOCK">LockMode_LOCK</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_OPEN">LockMode_OPEN</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_PLOCK">LockMode_PLOCK</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_POPEN">LockMode_POPEN</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#UPDATE_UHF">UPDATE_UHF</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#RFIDWithUHFA8RS232_qcom--">RFIDWithUHFA8RS232_qcom</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData</a></span>(java.lang.String&nbsp;accessPwd,
              int&nbsp;filterBank,
              int&nbsp;filterPtr,
              int&nbsp;filterCnt,
              java.lang.String&nbsp;filterData,
              int&nbsp;bank,
              int&nbsp;ptr,
              int&nbsp;cnt,
              java.lang.String&nbsp;writeData)</code>
<div class="block">向标签写入数据(支持大数据标签写入)、当需要写入的标签数据比较大可以使用此函数写入<br>
 Write data into tag(support big-sized data writing), user could use this function when big-sized data need to be written.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#closeWifi--">closeWifi</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#disableBeep--">disableBeep</a></span>()</code>
<div class="block">禁用盘点标签蜂鸣器</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#enableBeep--">enableBeep</a></span>()</code>
<div class="block">盘点有标签返回触发蜂鸣器</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#eraseData-java.lang.String-int-int-int-">eraseData</a></span>(java.lang.String&nbsp;accessPwd,
         int&nbsp;bank,
         int&nbsp;ptr,
         int&nbsp;cnt)</code>
<div class="block">擦除数据，被擦除的数据块其内容将被设置成0.<br>
 Data block that has been cleaned will be setup to 0.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData</a></span>(java.lang.String&nbsp;accessPwd,
         int&nbsp;filterBank,
         int&nbsp;filterPtr,
         int&nbsp;filterCnt,
         java.lang.String&nbsp;filterData,
         int&nbsp;bank,
         int&nbsp;ptr,
         int&nbsp;cnt)</code>
<div class="block">擦除指定标签的数据，被擦除的数据块其内容将被设置成0.<br>
 Clean specific tag data, data block after cleaned will be set to 0.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#free--">free</a></span>()</code>
<div class="block">断开uhf连接<br>
 Switch OFF UHF module</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a></span>(java.util.ArrayList&lt;java.lang.Integer&gt;&nbsp;lockBank,
                int&nbsp;lockMode)</code>
<div class="block">获取锁定码<br>
 Get lock code</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getAndroidDeviceHardwareVersion--">getAndroidDeviceHardwareVersion</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity">AntennaState</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getANT--">getANT</a></span>()</code>
<div class="block">获取当前设置的天线</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity">AntennaPowerEntity</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getAntennaPower--">getAntennaPower</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</code>
<div class="block">获取单个天线的功率</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getConnectStatus--">getConnectStatus</a></span>()</code>
<div class="block">获取UHF连接状态<br>
 Acquire UHF connection status</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getCW--">getCW</a></span>()</code>
<div class="block">获取连续波设置<br>
 Acquire CW setup</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></span>()</code>
<div class="block">获取当前设置的盘点模式 (Get the currently set inventory mode)</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getEthernetIpConfig--">getEthernetIpConfig</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getEthernetMac--">getEthernetMac</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getFrequencyMode--">getFrequencyMode</a></span>()</code>
<div class="block">读取模块的工作模式<br>
 Read work mode of module</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getGen2--">getGen2</a></span>()</code>
<div class="block">获取GEN2参数值(Get GEN2 parameter value)</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getProtocol--">getProtocol</a></span>()</code>
<div class="block">获取协议<br>
 Acquire protocol</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getReaderCurrentIp--">getReaderCurrentIp</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getRFLink--">getRFLink</a></span>()</code>
<div class="block">获取链路参数<br>
 acquire link parameter</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getTcpServiceVersion--">getTcpServiceVersion</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getTemperature--">getTemperature</a></span>()</code>
<div class="block">获取模块温度<br>
 Acquire module Temperature</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getVersion--">getVersion</a></span>()</code>
<div class="block">读取UHF模块版本号<br>
 Read UHF module version</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getWifiInfo--">getWifiInfo</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#getWifiIpConfig--">getWifiIpConfig</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#init-android.content.Context-">init</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">初始化UHF模块<br>
 Initiate UHF module</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity">GPIStateEntity</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#inputStatus--">inputStatus</a></span>()</code>
<div class="block">input status</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#inventorySingleTag--">inventorySingleTag</a></span>()</code>
<div class="block">单步识别标签<br>
 Identify tag in single mode</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#isEnableBeep--">isEnableBeep</a></span>()</code>
<div class="block">盘点标签蜂鸣器是否被禁用</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#isInventorying--">isInventorying</a></span>()</code>
<div class="block">uhf 是否正在盘点</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#killTag-java.lang.String-">killTag</a></span>(java.lang.String&nbsp;killPwd)</code>
<div class="block">销毁指定标签,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Kill specific tag, default password(0x00 0x00 0x00 0x00)</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag</a></span>(java.lang.String&nbsp;accessPwd,
       int&nbsp;filterBank,
       int&nbsp;filterPtr,
       int&nbsp;filterCnt,
       java.lang.String&nbsp;filterData)</code>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Kill specific tag, default password(0x00 0x00 0x00 0x00)<br></div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem</a></span>(java.lang.String&nbsp;accessPwd,
       int&nbsp;bank,
       int&nbsp;ptr,
       int&nbsp;cnt,
       java.lang.String&nbsp;filterData,
       java.lang.String&nbsp;lockCode)</code>
<div class="block">锁定指定标签<br>
 Lock specific tag</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#lockMem-java.lang.String-java.lang.String-">lockMem</a></span>(java.lang.String&nbsp;accessPwd,
       java.lang.String&nbsp;lockCode)</code>
<div class="block">锁定标签<br>
 Lock tag</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#openWifi--">openWifi</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#output3Off--">output3Off</a></span>()</code>
<div class="block">gpio OptoCoupler3 off</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#output3On--">output3On</a></span>()</code>
<div class="block">gpio OptoCoupler3 on</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#output4Off--">output4Off</a></span>()</code>
<div class="block">gpio OptoCoupler4 off</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#output4On--">output4On</a></span>()</code>
<div class="block">gpio OptoCoupler4 on</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#outputOnAndOff-java.util.List-">outputOnAndOff</a></span>(java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a>&gt;&nbsp;list)</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#readData-java.lang.String-int-int-int-">readData</a></span>(java.lang.String&nbsp;accessPwd,
        int&nbsp;bank,
        int&nbsp;ptr,
        int&nbsp;cnt)</code>
<div class="block">读取标签数据 <br>
 Read tag data</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">readData</a></span>(java.lang.String&nbsp;accessPwd,
        int&nbsp;filterBank,
        int&nbsp;filterPtr,
        int&nbsp;filterCnt,
        java.lang.String&nbsp;filterData,
        int&nbsp;bank,
        int&nbsp;ptr,
        int&nbsp;cnt)</code>
<div class="block">读取指定标签数据<br>
 Read specific tag data</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#readTagFromBuffer--">readTagFromBuffer</a></span>()</code>
<div class="block">Deprecated.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#readyUpgradeTcpService--">readyUpgradeTcpService</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#rebootDevice--">rebootDevice</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#sendAndReceive-byte:A-">sendAndReceive</a></span>(byte[]&nbsp;sendData)</code>&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#sendAndReceive-byte:A-int-">sendAndReceive</a></span>(byte[]&nbsp;sendData,
              int&nbsp;controlWord)</code>&nbsp;</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setANT-java.util.List-">setANT</a></span>(java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity">AntennaState</a>&gt;&nbsp;antStatus)</code>
<div class="block">设置天线</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></span>(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
               int&nbsp;power)</code>
<div class="block">设置单个天线的功率</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setBuzzerOff--">setBuzzerOff</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setBuzzerOn-int-">setBuzzerOn</a></span>(int&nbsp;time)</code>
<div class="block">单位:毫秒,time:100-65535</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;java.lang.Object&gt;&nbsp;statusCallback)</code>
<div class="block">设置UHF连接状态回调<br>
 Setup UHF continuous stauts call-back</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setCW-int-">setCW</a></span>(int&nbsp;flag)</code>
<div class="block">设置连续波<br>
 Setup CW</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setEPCAndTIDMode--">setEPCAndTIDMode</a></span>()</code>
<div class="block">设置循环盘点同时读取 EPC、TID 模式 <br>
 Setup auto scan to acquire EPC, TID mode</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setEPCAndTIDUserMode-int-int-">setEPCAndTIDUserMode</a></span>(int&nbsp;user_prt,
                    int&nbsp;user_len)</code>
<div class="block">设置循环盘点同时读取 EPC、TID、USER 模式  <br>
 Setup auto scan to acquire EPC, TID, User mode</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setEPCMode--">setEPCMode</a></span>()</code>
<div class="block">设置循环盘点只获取EPC的数据<br>
 Setup auto scan to acquire EPC only</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setEthernetConfigInfo-com.rscja.deviceapi.entity.ReaderIPEntity-">setEthernetConfigInfo</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;ipconfig)</code>&nbsp;</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setEthernetIpDynamicAssign--">setEthernetIpDynamicAssign</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setFastID-boolean-">setFastID</a></span>(boolean&nbsp;enalbe)</code>
<div class="block">开关FastID功能<br>
 ON/OFF FastID function</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setFilter-int-int-int-java.lang.String-">setFilter</a></span>(int&nbsp;bank,
         int&nbsp;ptr,
         int&nbsp;cnt,
         java.lang.String&nbsp;data)</code>
<div class="block">过滤循环盘点的标签，在<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--"><code>IUHF.startInventoryTag()</code></a>循环盘点标签之前，可以设置要过滤的数据。<br>
 Filter tag in auto scan mode, before scanning tags <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--"><code>IUHF.startInventoryTag()</code></a>, setup data that needs to be filtered.<br></div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setFreHop-float-">setFreHop</a></span>(float&nbsp;fre)</code>
<div class="block">设置模块频点<br>
 Setup frequency Hop</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setFrequencyMode-int-">setFrequencyMode</a></span>(int&nbsp;freMode)</code>
<div class="block">设置模块的工作模式 <br>
 Setup work mode of module</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">setGen2</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a>&nbsp;entity)</code>
<div class="block">设置GEN2参数值 (Set GEN2 parameter value)</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setGPIStateCallback-com.rscja.deviceapi.interfaces.IGPIStateCallback-">setGPIStateCallback</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/IGPIStateCallback.html" title="interface in com.rscja.deviceapi.interfaces">IGPIStateCallback</a>&nbsp;uhfGPIOStateCallback)</code>&nbsp;</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setGPIStateReverse-boolean-">setGPIStateReverse</a></span>(boolean&nbsp;isReverse)</code>&nbsp;</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/IUHFInventoryCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFInventoryCallback</a>&nbsp;inventoryCallback)</code>
<div class="block">设置盘点回调接口，接收循环盘点到的标签数据<br>
 Set the inventory callback interface to receive the label data from the cyclic inventory<br>
 备注：需要在开始循环盘点<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--"><code>IUHF.startInventoryTag()</code></a>之前调用此方法。<br>
 Note: This method needs to be called before starting the loop inventory <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--"><code>IUHF.startInventoryTag()</code></a>.<br></div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setProtocol-int-">setProtocol</a></span>(int&nbsp;protocol)</code>
<div class="block">设置协议<br>
 Setup protocol</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setRFLink-int-">setRFLink</a></span>(int&nbsp;mode)</code>
<div class="block">设置链路参数<br>
 Setup RFlink</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setTagFocus-boolean-">setTagFocus</a></span>(boolean&nbsp;enalbe)</code>
<div class="block">开关TagFocus功能 <br>
 ON/OFF TagFocus</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setTcpServicePort-int-">setTcpServicePort</a></span>(int&nbsp;port)</code>&nbsp;</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setUart-java.lang.String-">setUart</a></span>(java.lang.String&nbsp;uart)</code>&nbsp;</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setUpgradeProgress-com.rscja.deviceapi.interfaces.IUpgradeProgress-">setUpgradeProgress</a></span>(<a href="../../../../../com/rscja/deviceapi/interfaces/IUpgradeProgress.html" title="interface in com.rscja.deviceapi.interfaces">IUpgradeProgress</a>&nbsp;iUpgradeProgress)</code>&nbsp;</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setUpgradeTcpServiceData-byte:A-">setUpgradeTcpServiceData</a></span>(byte[]&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#setWifiConfigInfo-com.rscja.deviceapi.entity.WifiConfig-">setWifiConfigInfo</a></span>(<a href="../../../../../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a>&nbsp;wificonfig)</code>&nbsp;</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#startInventoryTag--">startInventoryTag</a></span>()</code>
<div class="block">开始循环识别标签。</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#startUpgradeTcpService--">startUpgradeTcpService</a></span>()</code>
<div class="block">tcp服务升级第三步,开始发送数据到服务器</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#stopInventory--">stopInventory</a></span>()</code>
<div class="block">停止循环识别，在调用此函数之后应当退出循环获取缓冲区的标签信息的子线程<br>
 Stop auto reading, after call this function to exit sub threads of tag data of buffer.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock</a></span>(java.lang.String&nbsp;accessPwd,
                 int&nbsp;filterBank,
                 int&nbsp;filterPtr,
                 int&nbsp;filterCnt,
                 java.lang.String&nbsp;filterData,
                 int&nbsp;ReadLock,
                 int&nbsp;uBank,
                 int&nbsp;uPtr,
                 int&nbsp;uRange,
                 byte[]&nbsp;uMaskbuf)</code>
<div class="block">数据块操作</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#uhfJump2Boot--">uhfJump2Boot</a></span>()</code>
<div class="block">升级STM32主板需要调用此函数进入boot模式<br>
 Upgrade STM32 mainboard that need to call this function to enter boot mode.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#uhfJump2Boot-int-">uhfJump2Boot</a></span>(int&nbsp;type)</code>
<div class="block">uhf进入boot模式 ，开始升级之前需要调用此函数 <br>
 UHF enter boot mode, need to call this formula before upgrade</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#uhfStartUpdate--">uhfStartUpdate</a></span>()</code>
<div class="block">开始升级uhf模块  <br>
 Start upgrade UHF module</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#uhfStopUpdate--">uhfStopUpdate</a></span>()</code>
<div class="block">停止升级uhf模块，uhf模块升级完成后需要调用此函数<br>
 Stop upgrade UHF module, call this formula after module has been upgraded.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#uhfUpdating-byte:A-">uhfUpdating</a></span>(byte[]&nbsp;buff)</code>
<div class="block">升级uhf模块，发送uhf固件数据 <br>
 Upgrade UHF module, send UHF firmware data</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a></span>(java.lang.String&nbsp;accessPwd,
         int&nbsp;bank,
         int&nbsp;ptr,
         int&nbsp;cnt,
         java.lang.String&nbsp;data)</code>
<div class="block">向标签写入数据 <br>
 Write data in tag</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData</a></span>(java.lang.String&nbsp;accessPwd,
         int&nbsp;filterBank,
         int&nbsp;filterPtr,
         int&nbsp;filterCnt,
         java.lang.String&nbsp;filterData,
         int&nbsp;bank,
         int&nbsp;ptr,
         int&nbsp;cnt,
         java.lang.String&nbsp;writeData)</code>
<div class="block">将数据写入指定标签<br>
 Write data</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc</a></span>(java.lang.String&nbsp;accessPwd,
              int&nbsp;filterBank,
              int&nbsp;filterPtr,
              int&nbsp;filterCnt,
              java.lang.String&nbsp;filterData,
              java.lang.String&nbsp;writeData)</code>
<div class="block">将数据写入到EPC,而且自动适配盘点的EPC长度<br>
 Write data to epc</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc</a></span>(java.lang.String&nbsp;accessPwd,
              java.lang.String&nbsp;writeData)</code>
<div class="block">将数据写入到EPC,而且自动适配盘点的EPC长度<br>
 Write data to epc</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.UhfBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.rscja.deviceapi.<a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/UhfBase.html#factoryReset--">factoryReset</a>, <a href="../../../../../com/rscja/deviceapi/UhfBase.html#getFastInventoryMode--">getFastInventoryMode</a>, <a href="../../../../../com/rscja/deviceapi/UhfBase.html#readTcpServiceState--">readTcpServiceState</a>, <a href="../../../../../com/rscja/deviceapi/UhfBase.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/deviceapi/UhfBase.html#setFastInventoryMode-boolean-">setFastInventoryMode</a>, <a href="../../../../../com/rscja/deviceapi/UhfBase.html#setOnLowBatteryListener-android.content.Context-com.rscja.deviceapi.entity.LowBatteryEntity-com.rscja.deviceapi.interfaces.OnLowBatteryListener-">setOnLowBatteryListener</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IRFIDWithUHFA8RS232">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8RS232</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html#getEthernetMac--">getEthernetMac</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html#setUart-java.lang.String-">setUart</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IMultipleAntenna">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getANT--">getANT</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getAntennaPower--">getAntennaPower</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#setANT-java.util.List-">setANT</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUHFURAxExtend">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#closeWifi--">closeWifi</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#disableBeep--">disableBeep</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#enableBeep--">enableBeep</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getAndroidDeviceHardwareVersion--">getAndroidDeviceHardwareVersion</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getEthernetIpConfig--">getEthernetIpConfig</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getReaderCurrentIp--">getReaderCurrentIp</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getTcpServiceVersion--">getTcpServiceVersion</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getWifiInfo--">getWifiInfo</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getWifiIpConfig--">getWifiIpConfig</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#isEnableBeep--">isEnableBeep</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#openWifi--">openWifi</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#readyUpgradeTcpService--">readyUpgradeTcpService</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#rebootDevice--">rebootDevice</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setBuzzerOff--">setBuzzerOff</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setBuzzerOn-int-">setBuzzerOn</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setEthernetConfigInfo-com.rscja.deviceapi.entity.ReaderIPEntity-">setEthernetConfigInfo</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setEthernetIpDynamicAssign--">setEthernetIpDynamicAssign</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setGPIStateCallback-com.rscja.deviceapi.interfaces.IGPIStateCallback-">setGPIStateCallback</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setTcpServicePort-int-">setTcpServicePort</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setUpgradeProgress-com.rscja.deviceapi.interfaces.IUpgradeProgress-">setUpgradeProgress</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setUpgradeTcpServiceData-byte:A-">setUpgradeTcpServiceData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setWifiConfigInfo-com.rscja.deviceapi.entity.WifiConfig-">setWifiConfigInfo</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#startUpgradeTcpService--">startUpgradeTcpService</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.rscja.deviceapi.interfaces.IUHF">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.rscja.deviceapi.interfaces.<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></h3>
<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#factoryReset--">factoryReset</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getConnectStatus--">getConnectStatus</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getCW--">getCW</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getFastInventoryMode--">getFastInventoryMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getFrequencyMode--">getFrequencyMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getGen2--">getGen2</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getProtocol--">getProtocol</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getRFLink--">getRFLink</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getTemperature--">getTemperature</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getVersion--">getVersion</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#inventorySingleTag--">inventorySingleTag</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#isInventorying--">isInventorying</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-">killTag</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-java.lang.String-">lockMem</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-">readData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">readData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readTagFromBuffer--">readTagFromBuffer</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setCW-int-">setCW</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDMode--">setEPCAndTIDMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-int-int-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-com.rscja.deviceapi.entity.InventoryModeEntity-">setEPCAndTIDUserMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCMode--">setEPCMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFastID-boolean-">setFastID</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFastInventoryMode-boolean-">setFastInventoryMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFilter-int-int-int-java.lang.String-">setFilter</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFreHop-float-">setFreHop</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFrequencyMode-int-">setFrequencyMode</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">setGen2</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setProtocol-int-">setProtocol</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setRFLink-int-">setRFLink</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setTagFocus-boolean-">setTagFocus</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--">startInventoryTag</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--">stopInventory</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot--">uhfJump2Boot</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot-int-">uhfJump2Boot</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStartUpdate--">uhfStartUpdate</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStopUpdate--">uhfStopUpdate</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfUpdating-byte:A-">uhfUpdating</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc</a>, <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="RFIDWithUHFA8RS232_qcom--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RFIDWithUHFA8RS232_qcom</h4>
<pre>public&nbsp;RFIDWithUHFA8RS232_qcom()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="init-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;boolean&nbsp;init(android.content.Context&nbsp;context)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#init-android.content.Context-">IUHF</a></code></span></div>
<div class="block">初始化UHF模块<br>
 Initiate UHF module</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#init-android.content.Context-">init</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - context</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="free--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>free</h4>
<pre>public&nbsp;boolean&nbsp;free()</pre>
<div class="block">断开uhf连接<br>
 Switch OFF UHF module</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#free--">free</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="inputStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inputStatus</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity">GPIStateEntity</a>&gt;&nbsp;inputStatus()</pre>
<div class="block">input status</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#inputStatus--">inputStatus</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>null:fail  ;  [0]:input1  ,  [1]:input2    [2]:OptoCoupler_input3   [3]:OptoCoupler_input4</dd>
</dl>
</li>
</ul>
<a name="output3On--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>output3On</h4>
<pre>public&nbsp;boolean&nbsp;output3On()</pre>
<div class="block">gpio OptoCoupler3 on</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#output3On--">output3On</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true;success   false:fail    OPTOCOUPLER</dd>
</dl>
</li>
</ul>
<a name="output3Off--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>output3Off</h4>
<pre>public&nbsp;boolean&nbsp;output3Off()</pre>
<div class="block">gpio OptoCoupler3 off</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#output3Off--">output3Off</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true;success   false:fail</dd>
</dl>
</li>
</ul>
<a name="output4On--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>output4On</h4>
<pre>public&nbsp;boolean&nbsp;output4On()</pre>
<div class="block">gpio OptoCoupler4 on</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#output4On--">output4On</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true;success   false:fail</dd>
</dl>
</li>
</ul>
<a name="output4Off--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>output4Off</h4>
<pre>public&nbsp;boolean&nbsp;output4Off()</pre>
<div class="block">gpio OptoCoupler4 off</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html#output4Off--">output4Off</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces">IRFIDWithUHFA8</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true;success   false:fail</dd>
</dl>
</li>
</ul>
<a name="setGPIStateReverse-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGPIStateReverse</h4>
<pre>public&nbsp;void&nbsp;setGPIStateReverse(boolean&nbsp;isReverse)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setGPIStateReverse-boolean-">setGPIStateReverse</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="outputOnAndOff-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>outputOnAndOff</h4>
<pre>public&nbsp;void&nbsp;outputOnAndOff(java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity">GPOEntity</a>&gt;&nbsp;list)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#outputOnAndOff-java.util.List-">outputOnAndOff</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="setUart-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUart</h4>
<pre>public&nbsp;void&nbsp;setUart(java.lang.String&nbsp;uart)</pre>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;java.lang.String&nbsp;getVersion()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getVersion--">IUHF</a></code></span></div>
<div class="block">读取UHF模块版本号<br>
 Read UHF module version</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getVersion--">getVersion</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回模块类型，null为读取失败(Resture module type, null means read failure.)</dd>
</dl>
</li>
</ul>
<a name="startInventoryTag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startInventoryTag</h4>
<pre>public&nbsp;boolean&nbsp;startInventoryTag()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--">IUHF</a></code></span></div>
<div class="block"><p>开始循环识别标签。</p>
 <p>Begin looping through the identification labels.</p>
 <p>通过 <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>IUHF.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a> 这个回调接口获取标签数据，需要在开始盘点之前调用setInventoryCallback方法。</p>
 <p>Get the label data through the callback interface <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>IUHF.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>, you need to call the setInventoryCallback method before starting the inventory.</p>
 <p>备注：开启循环识别标签后模块只能响应<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--"><code>IUHF.stopInventory()</code></a>函数 。</p>
 <p>Note: The module can only respond to the <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--"><code>IUHF.stopInventory()</code></a> function after the loop identification tag is turned on.</p></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--">startInventoryTag</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success)  false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="stopInventory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopInventory</h4>
<pre>public&nbsp;boolean&nbsp;stopInventory()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--">IUHF</a></code></span></div>
<div class="block">停止循环识别，在调用此函数之后应当退出循环获取缓冲区的标签信息的子线程<br>
 Stop auto reading, after call this function to exit sub threads of tag data of buffer. <br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#stopInventory--">stopInventory</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/UhfBase.html#stopInventory--">stopInventory</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="inventorySingleTag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inventorySingleTag</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;inventorySingleTag()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#inventorySingleTag--">IUHF</a></code></span></div>
<div class="block">单步识别标签<br>
 Identify tag in single mode</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#inventorySingleTag--">inventorySingleTag</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回标签信息(return tag infor)</dd>
</dl>
</li>
</ul>
<a name="readTagFromBuffer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTagFromBuffer</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity">UHFTAGInfo</a>&nbsp;readTagFromBuffer()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readTagFromBuffer--">IUHF</a></code></span></div>
<div class="block">Deprecated. Use <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-"><code>IUHF.setInventoryCallback(IUHFInventoryCallback inventoryCallback)</code></a>  instead .</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readTagFromBuffer--">readTagFromBuffer</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
</dl>
</li>
</ul>
<a name="getFrequencyMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrequencyMode</h4>
<pre>public&nbsp;int&nbsp;getFrequencyMode()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getFrequencyMode--">IUHF</a></code></span></div>
<div class="block">读取模块的工作模式<br>
 Read work mode of module</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getFrequencyMode--">getFrequencyMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回工作模式，返回-1为失败 (return work mode, return -1 means failure) <br>
 0x01：China Standard(840~845MHz) <br>
 0x02：China Standard2(920~925MHz)  <br>
 0x04：Europe Standard(865~868MHz)  <br>
 0x08：USA(902-928MHz)    <br>
 0x16：Korea(917~923MHz)     <br>
 0x32: Japan(916.8~920.8MHz) <br>
 0x33: South Africa(915~919MHz)<br>
 0x34: China Taiwan(920~928Mhz)<br>
 0x35:Vietnam(918~923MHz)<br>
 0x36:Peru(915MHz-928MHz)<br>
 0x37:Russia( 860MHz-867.6MHz)<br>
 0x3B:Malaysia(919-923MHz)<br>
 0x3C:Brazil<br>
 0x3D:ETSI_UPPER<br>
 0x3E:Australia<br>
 0x3F:Indonesia(920-923MHz)<br>
 0x40:Israel<br>
 0x41:HK <br>
 0x42:New Zealand <br></dd>
</dl>
</li>
</ul>
<a name="setFrequencyMode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFrequencyMode</h4>
<pre>public&nbsp;boolean&nbsp;setFrequencyMode(int&nbsp;freMode)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFrequencyMode-int-">IUHF</a></code></span></div>
<div class="block">设置模块的工作模式 <br>
 Setup work mode of module</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFrequencyMode-int-">setFrequencyMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>freMode</code> - 0x01：China Standard(840~845MHz) <br>
 0x02：China Standard2(920~925MHz)  <br>
 0x04：Europe Standard(865~868MHz)  <br>
 0x08：USA(902-928MHz)    <br>
 0x16：Korea(917~923MHz)     <br>
 0x32: Japan(916.8~920.8MHz) <br>
 0x33: South Africa(915~919MHz)<br>
 0x34: China Taiwan(920~928Mhz)<br>
 0x35:Vietnam(918~923MHz)<br>
 0x36:Peru(915MHz-928MHz)<br>
 0x37:Russia( 860MHz-867.6MHz)<br>
 0x3B:Malaysia(919-923MHz)<br>
 0x3C:Brazil<br>
 0x3D:ETSI_UPPER<br>
 0x3E:Australia<br>
 0x3F:Indonesia(920-923MHz)<br>
 0x40:Israel<br>
 0x41:HK <br>
 0x42:New Zealand <br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="setProtocol-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProtocol</h4>
<pre>public&nbsp;boolean&nbsp;setProtocol(int&nbsp;protocol)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setProtocol-int-">IUHF</a></code></span></div>
<div class="block">设置协议<br>
 Setup protocol</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setProtocol-int-">setProtocol</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>protocol</code> - 0x00： ISO18000-6C   <br>
                 0x01 ：GB/T 29768 <br>
                 0x02 ： GJB 7377.1  <br>
                 0x03 ： ISO 18000-6B  <br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="getProtocol--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProtocol</h4>
<pre>public&nbsp;int&nbsp;getProtocol()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getProtocol--">IUHF</a></code></span></div>
<div class="block">获取协议<br>
 Acquire protocol</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getProtocol--">getProtocol</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0x00 ：ISO18000-6C  <br>
 0x01 ： GB/T 29768 <br>
 0x02 ： GJB 7377.1 <br></dd>
</dl>
</li>
</ul>
<a name="setGen2-com.rscja.deviceapi.entity.Gen2Entity-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGen2</h4>
<pre>public&nbsp;boolean&nbsp;setGen2(<a href="../../../../../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a>&nbsp;entity)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">IUHF</a></code></span></div>
<div class="block">设置GEN2参数值 (Set GEN2 parameter value)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setGen2-com.rscja.deviceapi.entity.Gen2Entity-">setGen2</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>entity</code> - GEN2参数实体对象(GEN2 parameter entity object)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="getGen2--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGen2</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity">Gen2Entity</a>&nbsp;getGen2()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getGen2--">IUHF</a></code></span></div>
<div class="block">获取GEN2参数值(Get GEN2 parameter value)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getGen2--">getGen2</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>GEN2参数值实体对象 (GEN2 parameter value entity object)</dd>
</dl>
</li>
</ul>
<a name="setRFLink-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRFLink</h4>
<pre>public&nbsp;boolean&nbsp;setRFLink(int&nbsp;mode)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setRFLink-int-">IUHF</a></code></span></div>
<div class="block">设置链路参数<br>
 Setup RFlink</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setRFLink-int-">setRFLink</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mode</code> - 0-PR ASK/Miller8/160KHz <br>
            1-PR ASK /Miller4/250KHz <br>
            2-PR ASK/Miller4/ 320KHz <br>
            3-PR ASK/Miller4/640KHz <br>
            4-PR ASK/Miller2/320KHz <br>
            5-PR ASK/Miller2/640KHz <br>
            A-Gen2X/Miller8/ 160KHz <br>
            B-Gen2X/Miller4/ 250KHz <br>
            C-Gen2X/Miller4/320KHz <br>
            D-Gen2x/Miller4/ 640KHz <br>
            E-Gen2X/Miller2/320KHz <br>
            F-Gen2X/Miller2/ 640KHz</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="getRFLink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRFLink</h4>
<pre>public&nbsp;int&nbsp;getRFLink()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getRFLink--">IUHF</a></code></span></div>
<div class="block">获取链路参数<br>
 acquire link parameter</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getRFLink--">getRFLink</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>0: DSB_ASK/FM0/40KH;  <br>
 1:PR_ASK/Miller4/250KHz; <br>
 2:PR_ASK/Miller4/300KHz;  <br>
 3:DSB_ASK/FM0/400KHz <br></dd>
</dl>
</li>
</ul>
<a name="setFreHop-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFreHop</h4>
<pre>public&nbsp;boolean&nbsp;setFreHop(float&nbsp;fre)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFreHop-float-">IUHF</a></code></span></div>
<div class="block">设置模块频点<br>
 Setup frequency Hop</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFreHop-float-">setFreHop</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fre</code> - 要设置的频点(frequency need to be set)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="setFastID-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFastID</h4>
<pre>public&nbsp;boolean&nbsp;setFastID(boolean&nbsp;enalbe)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFastID-boolean-">IUHF</a></code></span></div>
<div class="block">开关FastID功能<br>
 ON/OFF FastID function</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFastID-boolean-">setFastID</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enalbe</code> - true：开(on)，false：关(off)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="setTagFocus-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTagFocus</h4>
<pre>public&nbsp;boolean&nbsp;setTagFocus(boolean&nbsp;enalbe)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setTagFocus-boolean-">IUHF</a></code></span></div>
<div class="block">开关TagFocus功能 <br>
 ON/OFF TagFocus</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setTagFocus-boolean-">setTagFocus</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enalbe</code> - true：开(on)，false：关(off)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="setEPCMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCMode</h4>
<pre>public&nbsp;boolean&nbsp;setEPCMode()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCMode--">IUHF</a></code></span></div>
<div class="block">设置循环盘点只获取EPC的数据<br>
 Setup auto scan to acquire EPC only</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCMode--">setEPCMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="setEPCAndTIDMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCAndTIDMode</h4>
<pre>public&nbsp;boolean&nbsp;setEPCAndTIDMode()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDMode--">IUHF</a></code></span></div>
<div class="block">设置循环盘点同时读取 EPC、TID 模式 <br>
 Setup auto scan to acquire EPC, TID mode</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDMode--">setEPCAndTIDMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="setEPCAndTIDUserMode-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEPCAndTIDUserMode</h4>
<pre>public&nbsp;boolean&nbsp;setEPCAndTIDUserMode(int&nbsp;user_prt,
                                    int&nbsp;user_len)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-int-int-">IUHF</a></code></span></div>
<div class="block">设置循环盘点同时读取 EPC、TID、USER 模式  <br>
 Setup auto scan to acquire EPC, TID, User mode</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setEPCAndTIDUserMode-int-int-">setEPCAndTIDUserMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>user_prt</code> - USER区起始地址(Start addressin USER area)</dd>
<dd><code>user_len</code> - USER区长度(Data length in USER area)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="setCW-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCW</h4>
<pre>public&nbsp;boolean&nbsp;setCW(int&nbsp;flag)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setCW-int-">IUHF</a></code></span></div>
<div class="block">设置连续波<br>
 Setup CW</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setCW-int-">setCW</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - 1:表示开， 0：表示关</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="getCW--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCW</h4>
<pre>public&nbsp;int&nbsp;getCW()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getCW--">IUHF</a></code></span></div>
<div class="block">获取连续波设置<br>
 Acquire CW setup</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getCW--">getCW</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>reutrn 1:表示开， 0：表示关</dd>
</dl>
</li>
</ul>
<a name="enableBeep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableBeep</h4>
<pre>public&nbsp;boolean&nbsp;enableBeep()</pre>
<div class="block">盘点有标签返回触发蜂鸣器</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#enableBeep--">enableBeep</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="disableBeep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disableBeep</h4>
<pre>public&nbsp;boolean&nbsp;disableBeep()</pre>
<div class="block">禁用盘点标签蜂鸣器</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#disableBeep--">disableBeep</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="isEnableBeep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableBeep</h4>
<pre>public&nbsp;boolean&nbsp;isEnableBeep()</pre>
<div class="block">盘点标签蜂鸣器是否被禁用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#isEnableBeep--">isEnableBeep</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="readData-java.lang.String-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readData</h4>
<pre>public&nbsp;java.lang.String&nbsp;readData(java.lang.String&nbsp;accessPwd,
                                 int&nbsp;bank,
                                 int&nbsp;ptr,
                                 int&nbsp;cnt)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-">IUHF</a></code></span></div>
<div class="block">读取标签数据 <br>
 Read tag data</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-">readData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - Access password</dd>
<dd><code>bank</code> - 读取的存储区(read storage area): <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED"><code>IUHF.Bank_RESERVED</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>ptr</code> - 读取的起始地址(单位:字)  (read start address(unit: word))</dd>
<dd><code>cnt</code> - 读取的数据长度(单位:字)  (read data length(unit: word))</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回获取的数据, null表示读取失败 (return acquired data, null means read failure)</dd>
</dl>
</li>
</ul>
<a name="readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readData</h4>
<pre>public&nbsp;java.lang.String&nbsp;readData(java.lang.String&nbsp;accessPwd,
                                 int&nbsp;filterBank,
                                 int&nbsp;filterPtr,
                                 int&nbsp;filterCnt,
                                 java.lang.String&nbsp;filterData,
                                 int&nbsp;bank,
                                 int&nbsp;ptr,
                                 int&nbsp;cnt)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">IUHF</a></code></span></div>
<div class="block">读取指定标签数据<br>
 Read specific tag data</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#readData-java.lang.String-int-int-int-java.lang.String-int-int-int-">readData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - ACCESS PASSWORD (4 bites)</dd>
<dd><code>filterBank</code> - 过滤的存储区(Filtered storage area ):  <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>filterPtr</code> - 过滤的起始地址(Filter start address)</dd>
<dd><code>filterCnt</code> - 过滤数据长度(单位:bit),当过滤的数据长度为0时，表示无过滤 ( Filter data length(unit:bit), when filter data length is 0, it means no filter.)</dd>
<dd><code>filterData</code> - 过滤的数据(Filtered data)</dd>
<dd><code>bank</code> - 读取的存储区(Read storage area) <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED"><code>IUHF.Bank_RESERVED</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>ptr</code> - 读取的起始地址(单位:字)  (read start address(unit: word))</dd>
<dd><code>cnt</code> - 读取的数据长度(单位:字)  (read data length(unit: word))</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回获取的数据, null表示读取失败 (return acquired data, null means read failure)</dd>
</dl>
</li>
</ul>
<a name="writeData-java.lang.String-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeData</h4>
<pre>public&nbsp;boolean&nbsp;writeData(java.lang.String&nbsp;accessPwd,
                         int&nbsp;bank,
                         int&nbsp;ptr,
                         int&nbsp;cnt,
                         java.lang.String&nbsp;data)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-">IUHF</a></code></span></div>
<div class="block">向标签写入数据 <br>
 Write data in tag</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-">writeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - ACCESS PASSWORD (4 bites)</dd>
<dd><code>bank</code> - 标签的存储区(Storage area):<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED"><code>IUHF.Bank_RESERVED</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a></dd>
<dd><code>ptr</code> - 起始地址的偏移量(start address(unit: word))</dd>
<dd><code>cnt</code> - 数据的长度（Word为单位，不能为0）(Data length(Word is unit, cannot be 0))</dd>
<dd><code>data</code> - 要写入的数据,十六进制格式 (Data format should be hexvalue)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeData</h4>
<pre>public&nbsp;boolean&nbsp;writeData(java.lang.String&nbsp;accessPwd,
                         int&nbsp;filterBank,
                         int&nbsp;filterPtr,
                         int&nbsp;filterCnt,
                         java.lang.String&nbsp;filterData,
                         int&nbsp;bank,
                         int&nbsp;ptr,
                         int&nbsp;cnt,
                         java.lang.String&nbsp;writeData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">IUHF</a></code></span></div>
<div class="block">将数据写入指定标签<br>
 Write data</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">writeData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - ACCESS PASSWORD (4 bites)</dd>
<dd><code>filterBank</code> - 过滤的存储区(Filtered storage area ):  <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>filterPtr</code> - 过滤的起始地址(Filter start address)</dd>
<dd><code>filterCnt</code> - 过滤数据长度(单位:bit),当过滤的数据长度为0时，表示无过滤 ( Filter data length(unit:bit), when filter data length is 0, it means no filter.)</dd>
<dd><code>filterData</code> - 过滤的数据(Filtered data)</dd>
<dd><code>bank</code> - 标签的存储区(Storage area): <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED"><code>IUHF.Bank_RESERVED</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>ptr</code> - 起始地址的偏移量(start address(unit: word))</dd>
<dd><code>cnt</code> - 数据的长度（Word为单位，不能为0）(Data length(Word is unit, cannot be 0))</dd>
<dd><code>writeData</code> - 要写入的数据,十六进制格式 (Data format should be hexvalue)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blockWriteData</h4>
<pre>public&nbsp;boolean&nbsp;blockWriteData(java.lang.String&nbsp;accessPwd,
                              int&nbsp;filterBank,
                              int&nbsp;filterPtr,
                              int&nbsp;filterCnt,
                              java.lang.String&nbsp;filterData,
                              int&nbsp;bank,
                              int&nbsp;ptr,
                              int&nbsp;cnt,
                              java.lang.String&nbsp;writeData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">IUHF</a></code></span></div>
<div class="block">向标签写入数据(支持大数据标签写入)、当需要写入的标签数据比较大可以使用此函数写入<br>
 Write data into tag(support big-sized data writing), user could use this function when big-sized data need to be written.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#blockWriteData-java.lang.String-int-int-int-java.lang.String-int-int-int-java.lang.String-">blockWriteData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - ACCESS PASSWORD (4 bites)</dd>
<dd><code>filterBank</code> - 过滤的存储区(Filtered storage area ):  <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>filterPtr</code> - 过滤的起始地址(Filter start address)</dd>
<dd><code>filterCnt</code> - 过滤数据长度(单位:bit),当过滤的数据长度为0时，表示无过滤 ( Filter data length(unit:bit), when filter data length is 0, it means no filter.)</dd>
<dd><code>filterData</code> - 过滤的数据(Filtered data)</dd>
<dd><code>bank</code> - 标签的存储区(Storage area): <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED"><code>IUHF.Bank_RESERVED</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>ptr</code> - 起始地址的偏移量(start address(unit: word))</dd>
<dd><code>cnt</code> - 数据的长度（Word为单位，不能为0）(Data length(Word is unit, cannot be 0))</dd>
<dd><code>writeData</code> - 要写入的数据,十六进制格式 (Data format should be hexvalue)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eraseData</h4>
<pre>public&nbsp;boolean&nbsp;eraseData(java.lang.String&nbsp;accessPwd,
                         int&nbsp;filterBank,
                         int&nbsp;filterPtr,
                         int&nbsp;filterCnt,
                         java.lang.String&nbsp;filterData,
                         int&nbsp;bank,
                         int&nbsp;ptr,
                         int&nbsp;cnt)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">IUHF</a></code></span></div>
<div class="block">擦除指定标签的数据，被擦除的数据块其内容将被设置成0.<br>
 Clean specific tag data, data block after cleaned will be set to 0.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-java.lang.String-int-int-int-">eraseData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - ACCESS PASSWORD (4 bites)</dd>
<dd><code>filterBank</code> - 过滤的存储区(Filtered storage area ):<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>filterPtr</code> - 过滤的起始地址(Filter start address)</dd>
<dd><code>filterCnt</code> - 过滤数据长度(单位:bit),当过滤的数据长度为0时，表示无过滤 ( Filter data length(unit:bit), when filter data length is 0, it means no filter.)</dd>
<dd><code>filterData</code> - 过滤的数据(Filtered data)</dd>
<dd><code>bank</code> - 擦除的数据区域(Cleaned data block):<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED"><code>IUHF.Bank_RESERVED</code></a>、  <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>ptr</code> - 擦除的起始地址(单位:字) (Cleaned start address(Unit: word))</dd>
<dd><code>cnt</code> - 擦除的数据长度(单位:字) (Cleaned data length(Unit: word))</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="eraseData-java.lang.String-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eraseData</h4>
<pre>public&nbsp;boolean&nbsp;eraseData(java.lang.String&nbsp;accessPwd,
                         int&nbsp;bank,
                         int&nbsp;ptr,
                         int&nbsp;cnt)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-">IUHF</a></code></span></div>
<div class="block">擦除数据，被擦除的数据块其内容将被设置成0.<br>
 Data block that has been cleaned will be setup to 0.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#eraseData-java.lang.String-int-int-int-">eraseData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - ACCESS PASSWORD (4 bites)</dd>
<dd><code>bank</code> - 擦除的数据区域(Cleaned data block):<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED"><code>IUHF.Bank_RESERVED</code></a>、  <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>ptr</code> - 擦除的起始地址(单位:字) (Cleaned start address(Unit: word))</dd>
<dd><code>cnt</code> - 擦除的数据长度(单位:字) (Cleaned data length(Unit: word))</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="setFilter-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFilter</h4>
<pre>public&nbsp;boolean&nbsp;setFilter(int&nbsp;bank,
                         int&nbsp;ptr,
                         int&nbsp;cnt,
                         java.lang.String&nbsp;data)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFilter-int-int-int-java.lang.String-">IUHF</a></code></span></div>
<div class="block">过滤循环盘点的标签，在<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--"><code>IUHF.startInventoryTag()</code></a>循环盘点标签之前，可以设置要过滤的数据。<br>
 Filter tag in auto scan mode, before scanning tags <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--"><code>IUHF.startInventoryTag()</code></a>, setup data that needs to be filtered.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setFilter-int-int-int-java.lang.String-">setFilter</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bank</code> - 过滤的存储区(Filtered storage area ): <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>ptr</code> - 过滤的起始地址(Filter start address)</dd>
<dd><code>cnt</code> - 过滤数据长度(单位:bit),当过滤的数据长度为0时，表示无过滤 ( Filter data length(unit:bit), when filter data length is 0, it means no filter.)</dd>
<dd><code>data</code> - 过滤的数据(Filtered data)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="killTag-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>killTag</h4>
<pre>public&nbsp;boolean&nbsp;killTag(java.lang.String&nbsp;killPwd)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-">IUHF</a></code></span></div>
<div class="block">销毁指定标签,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Kill specific tag, default password(0x00 0x00 0x00 0x00)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-">killTag</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>killPwd</code> - 销毁密码(Kill password)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="killTag-java.lang.String-int-int-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>killTag</h4>
<pre>public&nbsp;boolean&nbsp;killTag(java.lang.String&nbsp;accessPwd,
                       int&nbsp;filterBank,
                       int&nbsp;filterPtr,
                       int&nbsp;filterCnt,
                       java.lang.String&nbsp;filterData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-int-int-int-java.lang.String-">IUHF</a></code></span></div>
<div class="block">销毁指定标签 ,默认密码(0x00 0x00 0x00 0x00)不能执行销毁<br>
 Kill specific tag, default password(0x00 0x00 0x00 0x00)<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#killTag-java.lang.String-int-int-int-java.lang.String-">killTag</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - 销毁密码(Kill password)</dd>
<dd><code>filterBank</code> - 过滤的存储区(Filtered storage area ):<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>filterPtr</code> - 过滤的起始地址(Filter start address)</dd>
<dd><code>filterCnt</code> - 过滤数据长度(单位:bit),当过滤的数据长度为0时，表示无过滤 ( Filter data length(unit:bit), when filter data length is 0, it means no filter.)</dd>
<dd><code>filterData</code> - 过滤的数据(Filtered data)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="getTemperature--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTemperature</h4>
<pre>public&nbsp;int&nbsp;getTemperature()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getTemperature--">IUHF</a></code></span></div>
<div class="block">获取模块温度<br>
 Acquire module Temperature</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getTemperature--">getTemperature</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>模块温度(Temperature)</dd>
</dl>
</li>
</ul>
<a name="uhfJump2Boot--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uhfJump2Boot</h4>
<pre>public&nbsp;boolean&nbsp;uhfJump2Boot()</pre>
<div class="block">升级STM32主板需要调用此函数进入boot模式<br>
 Upgrade STM32 mainboard that need to call this function to enter boot mode.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot--">uhfJump2Boot</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success)   false:失败(failure)</dd>
</dl>
</li>
</ul>
<a name="uhfJump2Boot-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uhfJump2Boot</h4>
<pre>public&nbsp;boolean&nbsp;uhfJump2Boot(int&nbsp;type)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot-int-">IUHF</a></code></span></div>
<div class="block">uhf进入boot模式 ，开始升级之前需要调用此函数 <br>
 UHF enter boot mode, need to call this formula before upgrade</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfJump2Boot-int-">uhfJump2Boot</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - 0,upgrade reader mainboard
               1,upgrade UHF module
               2,upgrade reader bootloader
               3,upgrade Ex10 SDK firmware</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="uhfStartUpdate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uhfStartUpdate</h4>
<pre>public&nbsp;boolean&nbsp;uhfStartUpdate()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStartUpdate--">IUHF</a></code></span></div>
<div class="block">开始升级uhf模块  <br>
 Start upgrade UHF module</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStartUpdate--">uhfStartUpdate</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="uhfUpdating-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uhfUpdating</h4>
<pre>public&nbsp;boolean&nbsp;uhfUpdating(byte[]&nbsp;buff)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfUpdating-byte:A-">IUHF</a></code></span></div>
<div class="block">升级uhf模块，发送uhf固件数据 <br>
 Upgrade UHF module, send UHF firmware data</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfUpdating-byte:A-">uhfUpdating</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buff</code> - 固件数据(buff firmware data)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="uhfStopUpdate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uhfStopUpdate</h4>
<pre>public&nbsp;boolean&nbsp;uhfStopUpdate()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStopUpdate--">IUHF</a></code></span></div>
<div class="block">停止升级uhf模块，uhf模块升级完成后需要调用此函数<br>
 Stop upgrade UHF module, call this formula after module has been upgraded.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfStopUpdate--">uhfStopUpdate</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="rebootDevice--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rebootDevice</h4>
<pre>public&nbsp;boolean&nbsp;rebootDevice()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#rebootDevice--">rebootDevice</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="generateLockCode-java.util.ArrayList-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateLockCode</h4>
<pre>public&nbsp;java.lang.String&nbsp;generateLockCode(java.util.ArrayList&lt;java.lang.Integer&gt;&nbsp;lockBank,
                                         int&nbsp;lockMode)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#generateLockCode-java.util.ArrayList-int-">IUHF</a></code></span></div>
<div class="block">获取锁定码<br>
 Get lock code</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#generateLockCode-java.util.ArrayList-int-">generateLockCode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>lockBank</code> - 要锁定的区域 (lockBank areas that need to lock ): <br>
                 <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_KILL"><code>IUHF.LockBank_KILL</code></a><br>
                 <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_ACCESS"><code>IUHF.LockBank_ACCESS</code></a><br>
                 <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_TID"><code>IUHF.LockBank_TID</code></a><br>
                 <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_USER"><code>IUHF.LockBank_USER</code></a><br>
                 <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockBank_EPC"><code>IUHF.LockBank_EPC</code></a><br></dd>
<dd><code>lockMode</code> - 锁定的模式(lockMode lock modes):<br>
                 <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_LOCK"><code>IUHF.LockMode_LOCK</code></a><br>
                 <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_OPEN"><code>IUHF.LockMode_OPEN</code></a><br>
                 <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_PLOCK"><code>IUHF.LockMode_PLOCK</code></a><br>
                 <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#LockMode_POPEN"><code>IUHF.LockMode_POPEN</code></a><br></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回锁定码，null表示失败(return lock code, null means failure)</dd>
</dl>
</li>
</ul>
<a name="lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lockMem</h4>
<pre>public&nbsp;boolean&nbsp;lockMem(java.lang.String&nbsp;accessPwd,
                       int&nbsp;bank,
                       int&nbsp;ptr,
                       int&nbsp;cnt,
                       java.lang.String&nbsp;filterData,
                       java.lang.String&nbsp;lockCode)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">IUHF</a></code></span></div>
<div class="block">锁定指定标签<br>
 Lock specific tag</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-int-int-int-java.lang.String-java.lang.String-">lockMem</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - ACCESS PASSWORD (4 bites)</dd>
<dd><code>bank</code> - 标签的存储区(memory area):<br><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_RESERVED"><code>IUHF.Bank_RESERVED</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a></dd>
<dd><code>ptr</code> - 过滤起始地址,单位:bit (Filter start address,unit:bit)</dd>
<dd><code>cnt</code> - 过滤数据长度(单位:bit),当过滤的数据长度为0时，表示无过滤。 (Filter data length(unit:bit)  when filter length is 0, it means no filter.)</dd>
<dd><code>filterData</code> - 过滤数据(Filter data)</dd>
<dd><code>lockCode</code> - 锁定码 (Lock Code)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="lockMem-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lockMem</h4>
<pre>public&nbsp;boolean&nbsp;lockMem(java.lang.String&nbsp;accessPwd,
                       java.lang.String&nbsp;lockCode)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-java.lang.String-">IUHF</a></code></span></div>
<div class="block">锁定标签<br>
 Lock tag</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#lockMem-java.lang.String-java.lang.String-">lockMem</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - ACCESS PASSWORD (4 bites)</dd>
<dd><code>lockCode</code> - 锁定码 (Lock Code)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="setBuzzerOff--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuzzerOff</h4>
<pre>public&nbsp;void&nbsp;setBuzzerOff()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setBuzzerOff--">setBuzzerOff</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="setBuzzerOn-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuzzerOn</h4>
<pre>public&nbsp;void&nbsp;setBuzzerOn(int&nbsp;time)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setBuzzerOn-int-">IUHFURAxExtend</a></code></span></div>
<div class="block">单位:毫秒,time:100-65535</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setBuzzerOn-int-">setBuzzerOn</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="getTcpServiceVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTcpServiceVersion</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTcpServiceVersion()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getTcpServiceVersion--">getTcpServiceVersion</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="setEthernetConfigInfo-com.rscja.deviceapi.entity.ReaderIPEntity-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEthernetConfigInfo</h4>
<pre>public&nbsp;boolean&nbsp;setEthernetConfigInfo(<a href="../../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;ipconfig)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setEthernetConfigInfo-com.rscja.deviceapi.entity.ReaderIPEntity-">setEthernetConfigInfo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="setWifiConfigInfo-com.rscja.deviceapi.entity.WifiConfig-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWifiConfigInfo</h4>
<pre>public&nbsp;boolean&nbsp;setWifiConfigInfo(<a href="../../../../../com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity">WifiConfig</a>&nbsp;wificonfig)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setWifiConfigInfo-com.rscja.deviceapi.entity.WifiConfig-">setWifiConfigInfo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="setTcpServicePort-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTcpServicePort</h4>
<pre>public&nbsp;boolean&nbsp;setTcpServicePort(int&nbsp;port)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setTcpServicePort-int-">setTcpServicePort</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="getWifiInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWifiInfo</h4>
<pre>public&nbsp;java.lang.String&nbsp;getWifiInfo()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getWifiInfo--">getWifiInfo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="openWifi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openWifi</h4>
<pre>public&nbsp;boolean&nbsp;openWifi()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#openWifi--">openWifi</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="closeWifi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeWifi</h4>
<pre>public&nbsp;boolean&nbsp;closeWifi()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#closeWifi--">closeWifi</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="getAndroidDeviceHardwareVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAndroidDeviceHardwareVersion</h4>
<pre>public&nbsp;java.lang.String&nbsp;getAndroidDeviceHardwareVersion()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getAndroidDeviceHardwareVersion--">getAndroidDeviceHardwareVersion</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="readyUpgradeTcpService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readyUpgradeTcpService</h4>
<pre>public&nbsp;void&nbsp;readyUpgradeTcpService()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#readyUpgradeTcpService--">readyUpgradeTcpService</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="setUpgradeTcpServiceData-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUpgradeTcpServiceData</h4>
<pre>public&nbsp;boolean&nbsp;setUpgradeTcpServiceData(byte[]&nbsp;data)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setUpgradeTcpServiceData-byte:A-">setUpgradeTcpServiceData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="startUpgradeTcpService--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startUpgradeTcpService</h4>
<pre>public&nbsp;boolean&nbsp;startUpgradeTcpService()</pre>
<div class="block">tcp服务升级第三步,开始发送数据到服务器</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#startUpgradeTcpService--">startUpgradeTcpService</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:升级成功     false:升级失败</dd>
</dl>
</li>
</ul>
<a name="setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInventoryCallback</h4>
<pre>public&nbsp;void&nbsp;setInventoryCallback(<a href="../../../../../com/rscja/deviceapi/interfaces/IUHFInventoryCallback.html" title="interface in com.rscja.deviceapi.interfaces">IUHFInventoryCallback</a>&nbsp;inventoryCallback)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">IUHF</a></code></span></div>
<div class="block">设置盘点回调接口，接收循环盘点到的标签数据<br>
 Set the inventory callback interface to receive the label data from the cyclic inventory<br>
 备注：需要在开始循环盘点<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--"><code>IUHF.startInventoryTag()</code></a>之前调用此方法。<br>
 Note: This method needs to be called before starting the loop inventory <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#startInventoryTag--"><code>IUHF.startInventoryTag()</code></a>.<br></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setInventoryCallback-com.rscja.deviceapi.interfaces.IUHFInventoryCallback-">setInventoryCallback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inventoryCallback</code> - 盘点回调接口(inventory callback interface)</dd>
</dl>
</li>
</ul>
<a name="setUpgradeProgress-com.rscja.deviceapi.interfaces.IUpgradeProgress-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUpgradeProgress</h4>
<pre>public&nbsp;void&nbsp;setUpgradeProgress(<a href="../../../../../com/rscja/deviceapi/interfaces/IUpgradeProgress.html" title="interface in com.rscja.deviceapi.interfaces">IUpgradeProgress</a>&nbsp;iUpgradeProgress)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setUpgradeProgress-com.rscja.deviceapi.interfaces.IUpgradeProgress-">setUpgradeProgress</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="setGPIStateCallback-com.rscja.deviceapi.interfaces.IGPIStateCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGPIStateCallback</h4>
<pre>public&nbsp;void&nbsp;setGPIStateCallback(<a href="../../../../../com/rscja/deviceapi/interfaces/IGPIStateCallback.html" title="interface in com.rscja.deviceapi.interfaces">IGPIStateCallback</a>&nbsp;uhfGPIOStateCallback)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setGPIStateCallback-com.rscja.deviceapi.interfaces.IGPIStateCallback-">setGPIStateCallback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="getEthernetIpConfig--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEthernetIpConfig</h4>
<pre>public&nbsp;java.lang.String&nbsp;getEthernetIpConfig()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getEthernetIpConfig--">getEthernetIpConfig</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="getWifiIpConfig--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWifiIpConfig</h4>
<pre>public&nbsp;java.lang.String&nbsp;getWifiIpConfig()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getWifiIpConfig--">getWifiIpConfig</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="setEthernetIpDynamicAssign--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEthernetIpDynamicAssign</h4>
<pre>public&nbsp;boolean&nbsp;setEthernetIpDynamicAssign()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#setEthernetIpDynamicAssign--">setEthernetIpDynamicAssign</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="getReaderCurrentIp--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReaderCurrentIp</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity">ReaderIPEntity</a>&nbsp;getReaderCurrentIp()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html#getReaderCurrentIp--">getReaderCurrentIp</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces">IUHFURAxExtend</a></code></dd>
</dl>
</li>
</ul>
<a name="writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeDataToEpc</h4>
<pre>public&nbsp;boolean&nbsp;writeDataToEpc(java.lang.String&nbsp;accessPwd,
                              int&nbsp;filterBank,
                              int&nbsp;filterPtr,
                              int&nbsp;filterCnt,
                              java.lang.String&nbsp;filterData,
                              java.lang.String&nbsp;writeData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">IUHF</a></code></span></div>
<div class="block">将数据写入到EPC,而且自动适配盘点的EPC长度<br>
 Write data to epc</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-int-int-int-java.lang.String-java.lang.String-">writeDataToEpc</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - ACCESS PASSWORD (4 bites)</dd>
<dd><code>filterBank</code> - 过滤的存储区(Filtered storage area ):  <a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>filterPtr</code> - 过滤的起始地址(Filter start address)</dd>
<dd><code>filterCnt</code> - 过滤数据长度(单位:bit),当过滤的数据长度为0时，表示无过滤 ( Filter data length(unit:bit), when filter data length is 0, it means no filter.)</dd>
<dd><code>filterData</code> - 过滤的数据(Filtered data)</dd>
<dd><code>writeData</code> - 要写入的数据,十六进制格式 (Data format should be hexvalue)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="writeDataToEpc-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeDataToEpc</h4>
<pre>public&nbsp;boolean&nbsp;writeDataToEpc(java.lang.String&nbsp;accessPwd,
                              java.lang.String&nbsp;writeData)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-java.lang.String-">IUHF</a></code></span></div>
<div class="block">将数据写入到EPC,而且自动适配盘点的EPC长度<br>
 Write data to epc</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#writeDataToEpc-java.lang.String-java.lang.String-">writeDataToEpc</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - ACCESS PASSWORD (4 bites)</dd>
<dd><code>writeData</code> - 要写入的数据,十六进制格式 (Data format should be hexvalue)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="getEPCAndTIDUserMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEPCAndTIDUserMode</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity">InventoryModeEntity</a>&nbsp;getEPCAndTIDUserMode()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getEPCAndTIDUserMode--">IUHF</a></code></span></div>
<div class="block">获取当前设置的盘点模式 (Get the currently set inventory mode)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getEPCAndTIDUserMode--">getEPCAndTIDUserMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回盘点模式的对象(the object that returns the inventory mode)</dd>
</dl>
</li>
</ul>
<a name="uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uhfBlockPermalock</h4>
<pre>public&nbsp;boolean&nbsp;uhfBlockPermalock(java.lang.String&nbsp;accessPwd,
                                 int&nbsp;filterBank,
                                 int&nbsp;filterPtr,
                                 int&nbsp;filterCnt,
                                 java.lang.String&nbsp;filterData,
                                 int&nbsp;ReadLock,
                                 int&nbsp;uBank,
                                 int&nbsp;uPtr,
                                 int&nbsp;uRange,
                                 byte[]&nbsp;uMaskbuf)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">IUHF</a></code></span></div>
<div class="block">数据块操作</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#uhfBlockPermalock-java.lang.String-int-int-int-java.lang.String-int-int-int-int-byte:A-">uhfBlockPermalock</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>accessPwd</code> - ACCESS PASSWORD (4 bites)</dd>
<dd><code>filterBank</code> - 过滤的存储区(Filtered storage area ):<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_EPC"><code>IUHF.Bank_EPC</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_TID"><code>IUHF.Bank_TID</code></a>、<a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#Bank_USER"><code>IUHF.Bank_USER</code></a><br></dd>
<dd><code>filterPtr</code> - 过滤的起始地址(Filter start address)</dd>
<dd><code>filterCnt</code> - 过滤数据长度(单位:bit),当过滤的数据长度为0时，表示无过滤 ( Filter data length(unit:bit), when filter data length is 0, it means no filter.)</dd>
<dd><code>filterData</code> - 过滤的数据(Filtered data)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功(success) false:失败(failure)<br></dd>
</dl>
</li>
</ul>
<a name="setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConnectionStatusCallback</h4>
<pre>public&nbsp;void&nbsp;setConnectionStatusCallback(<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces">ConnectionStatusCallback</a>&lt;java.lang.Object&gt;&nbsp;statusCallback)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">IUHF</a></code></span></div>
<div class="block">设置UHF连接状态回调<br>
 Setup UHF continuous stauts call-back</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#setConnectionStatusCallback-com.rscja.deviceapi.interfaces.ConnectionStatusCallback-">setConnectionStatusCallback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>statusCallback</code> - 回调接口(Callback interface)</dd>
</dl>
</li>
</ul>
<a name="getConnectStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConnectStatus</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces">ConnectionStatus</a>&nbsp;getConnectStatus()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getConnectStatus--">IUHF</a></code></span></div>
<div class="block">获取UHF连接状态<br>
 Acquire UHF connection status</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#getConnectStatus--">getConnectStatus</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/UhfBase.html#getConnectStatus--">getConnectStatus</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>UHF连接状态(UHF connection status)</dd>
</dl>
</li>
</ul>
<a name="isInventorying--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isInventorying</h4>
<pre>public&nbsp;boolean&nbsp;isInventorying()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#isInventorying--">IUHF</a></code></span></div>
<div class="block">uhf 是否正在盘点</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html#isInventorying--">isInventorying</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces">IUHF</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/UhfBase.html#isInventorying--">isInventorying</a></code>&nbsp;in class&nbsp;<code><a href="../../../../../com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi">UhfBase</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getANT--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getANT</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity">AntennaState</a>&gt;&nbsp;getANT()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getANT--">IMultipleAntenna</a></code></span></div>
<div class="block">获取当前设置的天线</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getANT--">getANT</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a></code></dd>
</dl>
</li>
</ul>
<a name="setANT-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setANT</h4>
<pre>public&nbsp;boolean&nbsp;setANT(java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity">AntennaState</a>&gt;&nbsp;antStatus)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#setANT-java.util.List-">IMultipleAntenna</a></code></span></div>
<div class="block">设置天线</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#setANT-java.util.List-">setANT</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>antStatus</code> - 天线号</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAntennaPower</h4>
<pre>public&nbsp;boolean&nbsp;setAntennaPower(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant,
                               int&nbsp;power)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">IMultipleAntenna</a></code></span></div>
<div class="block">设置单个天线的功率</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#setAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-int-">setAntennaPower</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ant</code> - 天线号</dd>
<dd><code>power</code> - 功率</dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAntennaPower</h4>
<pre>public&nbsp;int&nbsp;getAntennaPower(<a href="../../../../../com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums">AntennaEnum</a>&nbsp;ant)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">IMultipleAntenna</a></code></span></div>
<div class="block">获取单个天线的功率</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getAntennaPower-com.rscja.deviceapi.enums.AntennaEnum-">getAntennaPower</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getAntennaPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAntennaPower</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../../com/rscja/deviceapi/entity/AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity">AntennaPowerEntity</a>&gt;&nbsp;getAntennaPower()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html#getAntennaPower--">getAntennaPower</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces">IMultipleAntenna</a></code></dd>
</dl>
</li>
</ul>
<a name="getEthernetMac--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEthernetMac</h4>
<pre>public&nbsp;java.lang.String&nbsp;getEthernetMac()</pre>
</li>
</ul>
<a name="sendAndReceive-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sendAndReceive</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;sendAndReceive(byte[]&nbsp;sendData)</pre>
</li>
</ul>
<a name="sendAndReceive-byte:A-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>sendAndReceive</h4>
<pre>public&nbsp;<a href="../../../../../com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.CMDInfo.html" title="class in com.rscja.team.qcom.uhfhandler">UHFDataHandleBase.CMDInfo</a>&nbsp;sendAndReceive(byte[]&nbsp;sendData,
                                                int&nbsp;controlWord)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RFIDWithUHFA8RS232_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" target="_top">Frames</a></li>
<li><a href="RFIDWithUHFA8RS232_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.rscja.deviceapi.UhfBase">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
