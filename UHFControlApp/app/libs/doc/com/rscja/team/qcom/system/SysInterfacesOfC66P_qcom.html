<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>SysInterfacesOfC66P_qcom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SysInterfacesOfC66P_qcom";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SysInterfacesOfC66P_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../../com/rscja/team/qcom/system/SystemInterfacesFactory_qcom.html" title="class in com.rscja.team.qcom.system"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html" target="_top">Frames</a></li>
<li><a href="SysInterfacesOfC66P_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.system</div>
<h2 title="Class SysInterfacesOfC66P_qcom" class="title">Class SysInterfacesOfC66P_qcom</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.team.qcom.system.SysInterfacesOfC66P_qcom</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">SysInterfacesOfC66P_qcom</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#SysInterfacesOfC66P_qcom-android.content.Context-">SysInterfacesOfC66P_qcom</a></span>(android.content.Context&nbsp;context)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#addLiveAPP-java.lang.String:A-">addLiveAPP</a></span>(java.lang.String[]&nbsp;packageName)</code>
<div class="block">增加保活APP</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#getAppWhiteList--">getAppWhiteList</a></span>()</code>
<div class="block">获取App白名单列表</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#getSN--">getSN</a></span>()</code>
<div class="block">获取SN号</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#isEnable4G--">isEnable4G</a></span>()</code>
<div class="block">判断4G是否启用</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#isEnableBackKey--">isEnableBackKey</a></span>()</code>
<div class="block">返回按键是否启用</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#isEnableBlueth--">isEnableBlueth</a></span>()</code>
<div class="block">判断Blueth是否启用</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#isEnableCamera--">isEnableCamera</a></span>()</code>
<div class="block">判断Camera是否启用</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#isEnableHomeKey--">isEnableHomeKey</a></span>()</code>
<div class="block">判断Home按键是否可用</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#isEnableMenuKey--">isEnableMenuKey</a></span>()</code>
<div class="block">判断MenuKey是否启用</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#isEnableNavigationBar--">isEnableNavigationBar</a></span>()</code>
<div class="block">判断虚拟导航栏是否可用</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#isEnableOtg--">isEnableOtg</a></span>()</code>
<div class="block">判断Otg是否启用</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#isEnablePanelBar--">isEnablePanelBar</a></span>()</code>
<div class="block">判断下拉面板是否可用</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#isEnableRecentKey--">isEnableRecentKey</a></span>()</code>
<div class="block">最近APP使用记录按键是否启用</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#isEnableScreensho--">isEnableScreensho</a></span>()</code>
<div class="block">判断屏幕截图是否启用</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#isEnableSystemSettingButton--">isEnableSystemSettingButton</a></span>()</code>
<div class="block">判断音量加减界面的设置按钮是否可用</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#isEnableWifi--">isEnableWifi</a></span>()</code>
<div class="block">判断wifi是否启用</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#removeLiveAPP-java.lang.String:A-">removeLiveAPP</a></span>(java.lang.String[]&nbsp;packageName)</code>
<div class="block">移除保活APP</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#sendContentToCursor-java.lang.String-">sendContentToCursor</a></span>(java.lang.String&nbsp;data)</code>
<div class="block">将数据发送到光标位置</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setEnable4G-boolean-">setEnable4G</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置4G 禁用、启用</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setEnableBackKey-boolean-">setEnableBackKey</a></span>(boolean&nbsp;enable)</code>
<div class="block">返回按键 禁用、启用</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setEnableBlueth-boolean-">setEnableBlueth</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置Blueth 禁用、启用</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setEnableCamera-boolean-">setEnableCamera</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置Camera 禁用、启用</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setEnableHomeKey-boolean-">setEnableHomeKey</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置Home按键禁用、启用</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setEnableMenuKey-boolean-">setEnableMenuKey</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置Menu按键 禁用、启用</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setEnableNavigationBar-boolean-">setEnableNavigationBar</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置虚拟导航栏禁用、启用</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setEnableOtg-boolean-">setEnableOtg</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置OTG 禁用、启用</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setEnablePanelBar-boolean-">setEnablePanelBar</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置下拉面板禁用、启用</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setEnableRecentKey-boolean-">setEnableRecentKey</a></span>(boolean&nbsp;enable)</code>
<div class="block">最近APP使用记录按键 禁用、启用</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setEnableScreensho-boolean-">setEnableScreensho</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置屏幕截图 禁用、启用</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setEnableSystemSettingButton-boolean-">setEnableSystemSettingButton</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置音量加减界面的设置按钮的禁用、启用</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setEnableWifi-boolean-">setEnableWifi</a></span>(boolean&nbsp;enable)</code>
<div class="block">设置wifi 禁用、启用,需要申请android.permission.CHANGE_WIFI_STATE权限</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#setSystemTime-long-">setSystemTime</a></span>(long&nbsp;timestamp)</code>
<div class="block">设置系统时间</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#silentInstall-java.lang.String-">silentInstall</a></span>(java.lang.String&nbsp;apkPath)</code>
<div class="block">静默安装apk</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html#silentUninstall-java.lang.String-">silentUninstall</a></span>(java.lang.String&nbsp;apkPackage)</code>
<div class="block">静默卸载apk</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SysInterfacesOfC66P_qcom-android.content.Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SysInterfacesOfC66P_qcom</h4>
<pre>public&nbsp;SysInterfacesOfC66P_qcom(android.content.Context&nbsp;context)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isEnableHomeKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableHomeKey</h4>
<pre>public&nbsp;boolean&nbsp;isEnableHomeKey()</pre>
<div class="block">判断Home按键是否可用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableHomeKey--">isEnableHomeKey</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:启用   false:禁用</dd>
</dl>
</li>
</ul>
<a name="setEnableHomeKey-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableHomeKey</h4>
<pre>public&nbsp;boolean&nbsp;setEnableHomeKey(boolean&nbsp;enable)</pre>
<div class="block">设置Home按键禁用、启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableHomeKey-boolean-">setEnableHomeKey</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - true:启用  false:禁用</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false;失败</dd>
</dl>
</li>
</ul>
<a name="isEnableMenuKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableMenuKey</h4>
<pre>public&nbsp;boolean&nbsp;isEnableMenuKey()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableMenuKey--">ISystemInterfaces</a></code></span></div>
<div class="block">判断MenuKey是否启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableMenuKey--">isEnableMenuKey</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:启用   false:禁用</dd>
</dl>
</li>
</ul>
<a name="setEnableMenuKey-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableMenuKey</h4>
<pre>public&nbsp;boolean&nbsp;setEnableMenuKey(boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableMenuKey-boolean-">ISystemInterfaces</a></code></span></div>
<div class="block">设置Menu按键 禁用、启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableMenuKey-boolean-">setEnableMenuKey</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - true:启用  false:禁用</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false;失败</dd>
</dl>
</li>
</ul>
<a name="isEnablePanelBar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnablePanelBar</h4>
<pre>public&nbsp;boolean&nbsp;isEnablePanelBar()</pre>
<div class="block">判断下拉面板是否可用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnablePanelBar--">isEnablePanelBar</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:启用   false:禁用</dd>
</dl>
</li>
</ul>
<a name="setEnablePanelBar-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnablePanelBar</h4>
<pre>public&nbsp;boolean&nbsp;setEnablePanelBar(boolean&nbsp;enable)</pre>
<div class="block">设置下拉面板禁用、启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnablePanelBar-boolean-">setEnablePanelBar</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - true:启用  false:禁用</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false;失败</dd>
</dl>
</li>
</ul>
<a name="isEnableBackKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableBackKey</h4>
<pre>public&nbsp;boolean&nbsp;isEnableBackKey()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableBackKey--">ISystemInterfaces</a></code></span></div>
<div class="block">返回按键是否启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableBackKey--">isEnableBackKey</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:启用   false:禁用</dd>
</dl>
</li>
</ul>
<a name="setEnableBackKey-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableBackKey</h4>
<pre>public&nbsp;boolean&nbsp;setEnableBackKey(boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableBackKey-boolean-">ISystemInterfaces</a></code></span></div>
<div class="block">返回按键 禁用、启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableBackKey-boolean-">setEnableBackKey</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - true:启用  false:禁用</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false;失败</dd>
</dl>
</li>
</ul>
<a name="isEnableRecentKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableRecentKey</h4>
<pre>public&nbsp;boolean&nbsp;isEnableRecentKey()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableRecentKey--">ISystemInterfaces</a></code></span></div>
<div class="block">最近APP使用记录按键是否启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableRecentKey--">isEnableRecentKey</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:启用   false:禁用</dd>
</dl>
</li>
</ul>
<a name="setEnableRecentKey-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableRecentKey</h4>
<pre>public&nbsp;boolean&nbsp;setEnableRecentKey(boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableRecentKey-boolean-">ISystemInterfaces</a></code></span></div>
<div class="block">最近APP使用记录按键 禁用、启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableRecentKey-boolean-">setEnableRecentKey</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - true:启用  false:禁用</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false;失败</dd>
</dl>
</li>
</ul>
<a name="isEnableSystemSettingButton--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableSystemSettingButton</h4>
<pre>public&nbsp;boolean&nbsp;isEnableSystemSettingButton()</pre>
<div class="block">判断音量加减界面的设置按钮是否可用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableSystemSettingButton--">isEnableSystemSettingButton</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:启用   false:禁用</dd>
</dl>
</li>
</ul>
<a name="setEnableSystemSettingButton-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableSystemSettingButton</h4>
<pre>public&nbsp;boolean&nbsp;setEnableSystemSettingButton(boolean&nbsp;enable)</pre>
<div class="block">设置音量加减界面的设置按钮的禁用、启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableSystemSettingButton-boolean-">setEnableSystemSettingButton</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - true:启用  false:禁用</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false;失败</dd>
</dl>
</li>
</ul>
<a name="isEnableNavigationBar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableNavigationBar</h4>
<pre>public&nbsp;boolean&nbsp;isEnableNavigationBar()</pre>
<div class="block">判断虚拟导航栏是否可用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableNavigationBar--">isEnableNavigationBar</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:启用   false:禁用</dd>
</dl>
</li>
</ul>
<a name="setEnableNavigationBar-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableNavigationBar</h4>
<pre>public&nbsp;boolean&nbsp;setEnableNavigationBar(boolean&nbsp;enable)</pre>
<div class="block">设置虚拟导航栏禁用、启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableNavigationBar-boolean-">setEnableNavigationBar</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - true:启用  false:禁用</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false;失败</dd>
</dl>
</li>
</ul>
<a name="setEnableWifi-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableWifi</h4>
<pre>public&nbsp;boolean&nbsp;setEnableWifi(boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableWifi-boolean-">ISystemInterfaces</a></code></span></div>
<div class="block">设置wifi 禁用、启用,需要申请android.permission.CHANGE_WIFI_STATE权限</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableWifi-boolean-">setEnableWifi</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - true:启用  false:禁用</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false;失败</dd>
</dl>
</li>
</ul>
<a name="isEnableWifi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableWifi</h4>
<pre>public&nbsp;boolean&nbsp;isEnableWifi()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableWifi--">ISystemInterfaces</a></code></span></div>
<div class="block">判断wifi是否启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableWifi--">isEnableWifi</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:启用   false:禁用</dd>
</dl>
</li>
</ul>
<a name="setEnable4G-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnable4G</h4>
<pre>public&nbsp;boolean&nbsp;setEnable4G(boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnable4G-boolean-">ISystemInterfaces</a></code></span></div>
<div class="block">设置4G 禁用、启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnable4G-boolean-">setEnable4G</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - true:启用  false:禁用</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false;失败</dd>
</dl>
</li>
</ul>
<a name="isEnable4G--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnable4G</h4>
<pre>public&nbsp;boolean&nbsp;isEnable4G()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnable4G--">ISystemInterfaces</a></code></span></div>
<div class="block">判断4G是否启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnable4G--">isEnable4G</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:启用   false:禁用</dd>
</dl>
</li>
</ul>
<a name="setEnableCamera-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableCamera</h4>
<pre>public&nbsp;boolean&nbsp;setEnableCamera(boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableCamera-boolean-">ISystemInterfaces</a></code></span></div>
<div class="block">设置Camera 禁用、启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableCamera-boolean-">setEnableCamera</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - true:启用  false:禁用</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false;失败</dd>
</dl>
</li>
</ul>
<a name="isEnableCamera--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableCamera</h4>
<pre>public&nbsp;boolean&nbsp;isEnableCamera()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableCamera--">ISystemInterfaces</a></code></span></div>
<div class="block">判断Camera是否启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableCamera--">isEnableCamera</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:启用   false:禁用</dd>
</dl>
</li>
</ul>
<a name="setEnableBlueth-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableBlueth</h4>
<pre>public&nbsp;boolean&nbsp;setEnableBlueth(boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableBlueth-boolean-">ISystemInterfaces</a></code></span></div>
<div class="block">设置Blueth 禁用、启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableBlueth-boolean-">setEnableBlueth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - true:启用  false:禁用</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false;失败</dd>
</dl>
</li>
</ul>
<a name="isEnableBlueth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableBlueth</h4>
<pre>public&nbsp;boolean&nbsp;isEnableBlueth()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableBlueth--">ISystemInterfaces</a></code></span></div>
<div class="block">判断Blueth是否启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableBlueth--">isEnableBlueth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:启用   false:禁用</dd>
</dl>
</li>
</ul>
<a name="setEnableOtg-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableOtg</h4>
<pre>public&nbsp;boolean&nbsp;setEnableOtg(boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableOtg-boolean-">ISystemInterfaces</a></code></span></div>
<div class="block">设置OTG 禁用、启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableOtg-boolean-">setEnableOtg</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - true:启用  false:禁用</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false;失败</dd>
</dl>
</li>
</ul>
<a name="isEnableOtg--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableOtg</h4>
<pre>public&nbsp;boolean&nbsp;isEnableOtg()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableOtg--">ISystemInterfaces</a></code></span></div>
<div class="block">判断Otg是否启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableOtg--">isEnableOtg</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:启用   false:禁用</dd>
</dl>
</li>
</ul>
<a name="setEnableScreensho-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableScreensho</h4>
<pre>public&nbsp;boolean&nbsp;setEnableScreensho(boolean&nbsp;enable)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableScreensho-boolean-">ISystemInterfaces</a></code></span></div>
<div class="block">设置屏幕截图 禁用、启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setEnableScreensho-boolean-">setEnableScreensho</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enable</code> - true:启用  false:禁用</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:成功   false;失败</dd>
</dl>
</li>
</ul>
<a name="isEnableScreensho--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableScreensho</h4>
<pre>public&nbsp;boolean&nbsp;isEnableScreensho()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableScreensho--">ISystemInterfaces</a></code></span></div>
<div class="block">判断屏幕截图是否启用</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#isEnableScreensho--">isEnableScreensho</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true:启用   false:禁用</dd>
</dl>
</li>
</ul>
<a name="getSN--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSN</h4>
<pre>public&nbsp;java.lang.String&nbsp;getSN()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#getSN--">ISystemInterfaces</a></code></span></div>
<div class="block">获取SN号</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#getSN--">getSN</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>返回sn号</dd>
</dl>
</li>
</ul>
<a name="setSystemTime-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSystemTime</h4>
<pre>public&nbsp;void&nbsp;setSystemTime(long&nbsp;timestamp)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setSystemTime-long-">ISystemInterfaces</a></code></span></div>
<div class="block">设置系统时间</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#setSystemTime-long-">setSystemTime</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>timestamp</code> - 时间戳</dd>
</dl>
</li>
</ul>
<a name="addLiveAPP-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addLiveAPP</h4>
<pre>public&nbsp;void&nbsp;addLiveAPP(java.lang.String[]&nbsp;packageName)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#addLiveAPP-java.lang.String:A-">ISystemInterfaces</a></code></span></div>
<div class="block">增加保活APP</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#addLiveAPP-java.lang.String:A-">addLiveAPP</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>packageName</code> - App包名</dd>
</dl>
</li>
</ul>
<a name="removeLiveAPP-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeLiveAPP</h4>
<pre>public&nbsp;void&nbsp;removeLiveAPP(java.lang.String[]&nbsp;packageName)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#removeLiveAPP-java.lang.String:A-">ISystemInterfaces</a></code></span></div>
<div class="block">移除保活APP</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#removeLiveAPP-java.lang.String:A-">removeLiveAPP</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>packageName</code> - App包名</dd>
</dl>
</li>
</ul>
<a name="silentInstall-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>silentInstall</h4>
<pre>public&nbsp;void&nbsp;silentInstall(java.lang.String&nbsp;apkPath)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#silentInstall-java.lang.String-">ISystemInterfaces</a></code></span></div>
<div class="block">静默安装apk</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#silentInstall-java.lang.String-">silentInstall</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>apkPath</code> - apk路径</dd>
</dl>
</li>
</ul>
<a name="silentUninstall-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>silentUninstall</h4>
<pre>public&nbsp;void&nbsp;silentUninstall(java.lang.String&nbsp;apkPackage)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#silentUninstall-java.lang.String-">ISystemInterfaces</a></code></span></div>
<div class="block">静默卸载apk</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#silentUninstall-java.lang.String-">silentUninstall</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>apkPackage</code> - apk包名</dd>
</dl>
</li>
</ul>
<a name="getAppWhiteList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAppWhiteList</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getAppWhiteList()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#getAppWhiteList--">ISystemInterfaces</a></code></span></div>
<div class="block">获取App白名单列表</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#getAppWhiteList--">getAppWhiteList</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
</dl>
</li>
</ul>
<a name="sendContentToCursor-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>sendContentToCursor</h4>
<pre>public&nbsp;void&nbsp;sendContentToCursor(java.lang.String&nbsp;data)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#sendContentToCursor-java.lang.String-">ISystemInterfaces</a></code></span></div>
<div class="block">将数据发送到光标位置</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../../com/rscja/system/ISystemInterfaces.html#sendContentToCursor-java.lang.String-">sendContentToCursor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../../com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system">ISystemInterfaces</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SysInterfacesOfC66P_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../../com/rscja/team/qcom/system/SystemInterfacesFactory_qcom.html" title="class in com.rscja.team.qcom.system"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html" target="_top">Frames</a></li>
<li><a href="SysInterfacesOfC66P_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
