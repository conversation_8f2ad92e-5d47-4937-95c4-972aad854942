<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>UsbSerialPort_qcom</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UsbSerialPort_qcom";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UsbSerialPort_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html" title="class in com.rscja.team.qcom.usb.pl2302"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" target="_top">Frames</a></li>
<li><a href="UsbSerialPort_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.team.qcom.usb.pl2302</div>
<h2 title="Interface UsbSerialPort_qcom" class="title">Interface UsbSerialPort_qcom</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302">UsbSerialPortImpl_qcom</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">UsbSerialPort_qcom</span></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#DATABITS_5">DATABITS_5</a></span></code>
<div class="block">5 data bits.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#DATABITS_6">DATABITS_6</a></span></code>
<div class="block">6 data bits.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#DATABITS_7">DATABITS_7</a></span></code>
<div class="block">7 data bits.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#DATABITS_8">DATABITS_8</a></span></code>
<div class="block">8 data bits.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_NONE">FLOWCONTROL_NONE</a></span></code>
<div class="block">No flow control.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_RTSCTS_IN">FLOWCONTROL_RTSCTS_IN</a></span></code>
<div class="block">RTS/CTS input flow control.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_RTSCTS_OUT">FLOWCONTROL_RTSCTS_OUT</a></span></code>
<div class="block">RTS/CTS output flow control.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_XONXOFF_IN">FLOWCONTROL_XONXOFF_IN</a></span></code>
<div class="block">XON/XOFF input flow control.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#FLOWCONTROL_XONXOFF_OUT">FLOWCONTROL_XONXOFF_OUT</a></span></code>
<div class="block">XON/XOFF output flow control.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_EVEN">PARITY_EVEN</a></span></code>
<div class="block">Even parity.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_MARK">PARITY_MARK</a></span></code>
<div class="block">Mark parity.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_NONE">PARITY_NONE</a></span></code>
<div class="block">No parity.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_ODD">PARITY_ODD</a></span></code>
<div class="block">Odd parity.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_SPACE">PARITY_SPACE</a></span></code>
<div class="block">Space parity.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#STOPBITS_1">STOPBITS_1</a></span></code>
<div class="block">1 stop bit.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#STOPBITS_1_5">STOPBITS_1_5</a></span></code>
<div class="block">1.5 stop bits.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#STOPBITS_2">STOPBITS_2</a></span></code>
<div class="block">2 stop bits.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#close--">close</a></span>()</code>
<div class="block">Closes the port.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getCD--">getCD</a></span>()</code>
<div class="block">Gets the CD (Carrier Detect) bit from the underlying UART.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getCTS--">getCTS</a></span>()</code>
<div class="block">Gets the CTS (Clear To Send) bit from the underlying UART.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getDSR--">getDSR</a></span>()</code>
<div class="block">Gets the DSR (Data Set Ready) bit from the underlying UART.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getDTR--">getDTR</a></span>()</code>
<div class="block">Gets the DTR (Data Terminal Ready) bit from the underlying UART.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getPortNumber--">getPortNumber</a></span>()</code>
<div class="block">Port number within driver.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getRI--">getRI</a></span>()</code>
<div class="block">Gets the RI (Ring Indicator) bit from the underlying UART.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getRTS--">getRTS</a></span>()</code>
<div class="block">Gets the RTS (Request To Send) bit from the underlying UART.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#getSerial--">getSerial</a></span>()</code>
<div class="block">The serial number of the underlying UsbDeviceConnection, or <code>null</code>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#open-android.hardware.usb.UsbDeviceConnection-">open</a></span>(android.hardware.usb.UsbDeviceConnection&nbsp;connection)</code>
<div class="block">Opens and initializes the port.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#purgeHwBuffers-boolean-boolean-">purgeHwBuffers</a></span>(boolean&nbsp;flushRX,
              boolean&nbsp;flushTX)</code>
<div class="block">Flush non-transmitted output data and / or non-read input data</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#read-byte:A-int-">read</a></span>(byte[]&nbsp;dest,
    int&nbsp;timeoutMillis)</code>
<div class="block">Reads as many bytes as possible into the destination buffer.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#setDTR-boolean-">setDTR</a></span>(boolean&nbsp;value)</code>
<div class="block">Sets the DTR (Data Terminal Ready) bit on the underlying UART, if
 supported.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#setParameters--">setParameters</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#setParameters-int-int-int-int-">setParameters</a></span>(int&nbsp;baudRate,
             int&nbsp;dataBits,
             int&nbsp;stopBits,
             int&nbsp;parity)</code>
<div class="block">Sets various serial port parameters.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#setRTS-boolean-">setRTS</a></span>(boolean&nbsp;value)</code>
<div class="block">Sets the RTS (Request To Send) bit on the underlying UART, if
 supported.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#write-byte:A-int-">write</a></span>(byte[]&nbsp;src,
     int&nbsp;timeoutMillis)</code>
<div class="block">Writes as many bytes as possible from the source buffer.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DATABITS_5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATABITS_5</h4>
<pre>static final&nbsp;int DATABITS_5</pre>
<div class="block">5 data bits.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.DATABITS_5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DATABITS_6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATABITS_6</h4>
<pre>static final&nbsp;int DATABITS_6</pre>
<div class="block">6 data bits.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.DATABITS_6">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DATABITS_7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATABITS_7</h4>
<pre>static final&nbsp;int DATABITS_7</pre>
<div class="block">7 data bits.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.DATABITS_7">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DATABITS_8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATABITS_8</h4>
<pre>static final&nbsp;int DATABITS_8</pre>
<div class="block">8 data bits.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.DATABITS_8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FLOWCONTROL_NONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLOWCONTROL_NONE</h4>
<pre>static final&nbsp;int FLOWCONTROL_NONE</pre>
<div class="block">No flow control.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.FLOWCONTROL_NONE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FLOWCONTROL_RTSCTS_IN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLOWCONTROL_RTSCTS_IN</h4>
<pre>static final&nbsp;int FLOWCONTROL_RTSCTS_IN</pre>
<div class="block">RTS/CTS input flow control.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.FLOWCONTROL_RTSCTS_IN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FLOWCONTROL_RTSCTS_OUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLOWCONTROL_RTSCTS_OUT</h4>
<pre>static final&nbsp;int FLOWCONTROL_RTSCTS_OUT</pre>
<div class="block">RTS/CTS output flow control.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.FLOWCONTROL_RTSCTS_OUT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FLOWCONTROL_XONXOFF_IN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLOWCONTROL_XONXOFF_IN</h4>
<pre>static final&nbsp;int FLOWCONTROL_XONXOFF_IN</pre>
<div class="block">XON/XOFF input flow control.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.FLOWCONTROL_XONXOFF_IN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="FLOWCONTROL_XONXOFF_OUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLOWCONTROL_XONXOFF_OUT</h4>
<pre>static final&nbsp;int FLOWCONTROL_XONXOFF_OUT</pre>
<div class="block">XON/XOFF output flow control.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.FLOWCONTROL_XONXOFF_OUT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PARITY_NONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PARITY_NONE</h4>
<pre>static final&nbsp;int PARITY_NONE</pre>
<div class="block">No parity.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.PARITY_NONE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PARITY_ODD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PARITY_ODD</h4>
<pre>static final&nbsp;int PARITY_ODD</pre>
<div class="block">Odd parity.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.PARITY_ODD">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PARITY_EVEN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PARITY_EVEN</h4>
<pre>static final&nbsp;int PARITY_EVEN</pre>
<div class="block">Even parity.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.PARITY_EVEN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PARITY_MARK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PARITY_MARK</h4>
<pre>static final&nbsp;int PARITY_MARK</pre>
<div class="block">Mark parity.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.PARITY_MARK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PARITY_SPACE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PARITY_SPACE</h4>
<pre>static final&nbsp;int PARITY_SPACE</pre>
<div class="block">Space parity.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.PARITY_SPACE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STOPBITS_1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STOPBITS_1</h4>
<pre>static final&nbsp;int STOPBITS_1</pre>
<div class="block">1 stop bit.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.STOPBITS_1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STOPBITS_1_5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STOPBITS_1_5</h4>
<pre>static final&nbsp;int STOPBITS_1_5</pre>
<div class="block">1.5 stop bits.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.STOPBITS_1_5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STOPBITS_2">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>STOPBITS_2</h4>
<pre>static final&nbsp;int STOPBITS_2</pre>
<div class="block">2 stop bits.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../../constant-values.html#com.rscja.team.qcom.usb.pl2302.UsbSerialPort_qcom.STOPBITS_2">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setParameters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParameters</h4>
<pre>boolean&nbsp;setParameters()</pre>
</li>
</ul>
<a name="getPortNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPortNumber</h4>
<pre>int&nbsp;getPortNumber()</pre>
<div class="block">Port number within driver.</div>
</li>
</ul>
<a name="getSerial--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSerial</h4>
<pre>java.lang.String&nbsp;getSerial()</pre>
<div class="block">The serial number of the underlying UsbDeviceConnection, or <code>null</code>.</div>
</li>
</ul>
<a name="open-android.hardware.usb.UsbDeviceConnection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>void&nbsp;open(android.hardware.usb.UsbDeviceConnection&nbsp;connection)
   throws java.io.IOException</pre>
<div class="block">Opens and initializes the port. Upon success, caller must ensure that
  is eventually called.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connection</code> - an open device connection, acquired with
            <code>UsbManager.openDevice(android.hardware.usb.UsbDevice)</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - on error opening or initializing the port.</dd>
</dl>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>void&nbsp;close()
    throws java.io.IOException</pre>
<div class="block">Closes the port.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - on error closing the port.</dd>
</dl>
</li>
</ul>
<a name="read-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>int&nbsp;read(byte[]&nbsp;dest,
         int&nbsp;timeoutMillis)
  throws java.io.IOException</pre>
<div class="block">Reads as many bytes as possible into the destination buffer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dest</code> - the destination byte buffer</dd>
<dd><code>timeoutMillis</code> - the timeout for reading</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the actual number of bytes read</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if an error occurred during reading</dd>
</dl>
</li>
</ul>
<a name="write-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>int&nbsp;write(byte[]&nbsp;src,
          int&nbsp;timeoutMillis)
   throws java.io.IOException</pre>
<div class="block">Writes as many bytes as possible from the source buffer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>src</code> - the source byte buffer</dd>
<dd><code>timeoutMillis</code> - the timeout for writing</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the actual number of bytes written</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if an error occurred during writing</dd>
</dl>
</li>
</ul>
<a name="setParameters-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParameters</h4>
<pre>void&nbsp;setParameters(int&nbsp;baudRate,
                   int&nbsp;dataBits,
                   int&nbsp;stopBits,
                   int&nbsp;parity)
            throws java.io.IOException</pre>
<div class="block">Sets various serial port parameters.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baudRate</code> - baud rate as an integer, for example <code>115200</code>.</dd>
<dd><code>dataBits</code> - one of <a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#DATABITS_5"><code>DATABITS_5</code></a>, <a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#DATABITS_6"><code>DATABITS_6</code></a>,
            <a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#DATABITS_7"><code>DATABITS_7</code></a>, or <a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#DATABITS_8"><code>DATABITS_8</code></a>.</dd>
<dd><code>stopBits</code> - one of <a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#STOPBITS_1"><code>STOPBITS_1</code></a>, <a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#STOPBITS_1_5"><code>STOPBITS_1_5</code></a>, or
            <a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#STOPBITS_2"><code>STOPBITS_2</code></a>.</dd>
<dd><code>parity</code> - one of <a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_NONE"><code>PARITY_NONE</code></a>, <a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_ODD"><code>PARITY_ODD</code></a>,
            <a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_EVEN"><code>PARITY_EVEN</code></a>, <a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_MARK"><code>PARITY_MARK</code></a>, or
            <a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html#PARITY_SPACE"><code>PARITY_SPACE</code></a>.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - on error setting the port parameters</dd>
</dl>
</li>
</ul>
<a name="getCD--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCD</h4>
<pre>boolean&nbsp;getCD()
       throws java.io.IOException</pre>
<div class="block">Gets the CD (Carrier Detect) bit from the underlying UART.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current state, or <code>false</code> if not supported.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if an error occurred during reading</dd>
</dl>
</li>
</ul>
<a name="getCTS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCTS</h4>
<pre>boolean&nbsp;getCTS()
        throws java.io.IOException</pre>
<div class="block">Gets the CTS (Clear To Send) bit from the underlying UART.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current state, or <code>false</code> if not supported.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if an error occurred during reading</dd>
</dl>
</li>
</ul>
<a name="getDSR--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDSR</h4>
<pre>boolean&nbsp;getDSR()
        throws java.io.IOException</pre>
<div class="block">Gets the DSR (Data Set Ready) bit from the underlying UART.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current state, or <code>false</code> if not supported.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if an error occurred during reading</dd>
</dl>
</li>
</ul>
<a name="getDTR--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDTR</h4>
<pre>boolean&nbsp;getDTR()
        throws java.io.IOException</pre>
<div class="block">Gets the DTR (Data Terminal Ready) bit from the underlying UART.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current state, or <code>false</code> if not supported.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if an error occurred during reading</dd>
</dl>
</li>
</ul>
<a name="setDTR-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDTR</h4>
<pre>void&nbsp;setDTR(boolean&nbsp;value)
     throws java.io.IOException</pre>
<div class="block">Sets the DTR (Data Terminal Ready) bit on the underlying UART, if
 supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - the value to set</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if an error occurred during writing</dd>
</dl>
</li>
</ul>
<a name="getRI--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRI</h4>
<pre>boolean&nbsp;getRI()
       throws java.io.IOException</pre>
<div class="block">Gets the RI (Ring Indicator) bit from the underlying UART.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current state, or <code>false</code> if not supported.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if an error occurred during reading</dd>
</dl>
</li>
</ul>
<a name="getRTS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRTS</h4>
<pre>boolean&nbsp;getRTS()
        throws java.io.IOException</pre>
<div class="block">Gets the RTS (Request To Send) bit from the underlying UART.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current state, or <code>false</code> if not supported.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if an error occurred during reading</dd>
</dl>
</li>
</ul>
<a name="setRTS-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRTS</h4>
<pre>void&nbsp;setRTS(boolean&nbsp;value)
     throws java.io.IOException</pre>
<div class="block">Sets the RTS (Request To Send) bit on the underlying UART, if
 supported.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - the value to set</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if an error occurred during writing</dd>
</dl>
</li>
</ul>
<a name="purgeHwBuffers-boolean-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>purgeHwBuffers</h4>
<pre>boolean&nbsp;purgeHwBuffers(boolean&nbsp;flushRX,
                       boolean&nbsp;flushTX)
                throws java.io.IOException</pre>
<div class="block">Flush non-transmitted output data and / or non-read input data</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flushRX</code> - <code>true</code> to flush non-transmitted output data</dd>
<dd><code>flushTX</code> - <code>true</code> to flush non-read input data</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the operation was successful, or
 <code>false</code> if the operation is not supported by the driver or device</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if an error occurred during flush</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UsbSerialPort_qcom.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbPL2302.html" title="class in com.rscja.team.qcom.usb.pl2302"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../../com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" target="_top">Frames</a></li>
<li><a href="UsbSerialPort_qcom.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
