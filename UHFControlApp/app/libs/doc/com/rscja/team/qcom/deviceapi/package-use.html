<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Package com.rscja.team.qcom.deviceapi</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package com.rscja.team.qcom.deviceapi";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package com.rscja.team.qcom.deviceapi" class="title">Uses of Package<br>com.rscja.team.qcom.deviceapi</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.custom">com.rscja.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi">com.rscja.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.custom">com.rscja.team.qcom.custom</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.deviceapi">com.rscja.team.qcom.deviceapi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.rs232utils">com.rscja.team.qcom.rs232utils</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> used by <a href="../../../../../com/rscja/custom/package-summary.html">com.rscja.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/BluetoothReader_qcom.html#com.rscja.custom">BluetoothReader_qcom</a>
<div class="block">蓝牙读写器操作类：<br>
 Bluetoot reader operation
 第一步：调用<a href="../../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#init-android.content.Context-"><code>BluetoothReader_qcom.init(Context context)</code></a> 函数初始化蓝牙相关服务，退出应用程序需要调用<a href="../../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#free--"><code>BluetoothReader_qcom.free()</code></a>是否蓝牙相关资源<br>
 First step: Call <a href="../../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#init-android.content.Context-"><code>BluetoothReader_qcom.init(Context context)</code></a>to initiate BT service, call <a href="../../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#free--"><code>BluetoothReader_qcom.free()</code></a> to exit application.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFA4NetWork_qcom.html#com.rscja.custom">RFIDWithUHFA4NetWork_qcom</a>
<div class="block">操作URA4设备以及UHF模块相关接口。<br>

 第一步：连接通过 <code>RFIDWithUHFAxNetWorkBase_qcom.setIPAndPort(String host, int port)</code>设置连接的读写IP地址。然后调用 <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#init-android.content.Context-"><code>RFIDWithUHFA4NetWork_qcom.init(Context)</code></a>连接读写器。
         同时可以设置回调接口 <code>RFIDWithUHFAxNetWorkBase_qcom.setConnectionStatusCallback(ConnectionStatusCallback)</code>监听连接状态<br>

 第二步： 如果是设置参数，连接成功之后，调用对应的函数设置参数。<br>
         如果是盘点标签先调用<code>RFIDWithUHFAxNetWorkBase_qcom.setInventoryCallback(IUHFInventoryCallback)</code>设置标签回调接口，标签数据会上传到这个接口函数。<br>
         然后在调用<code>RFIDWithUHFAxNetWorkBase_qcom.startInventoryTag()</code>函数开始执行盘点。注意:在盘点标签的时候rfid模块只能响应<code>RFIDWithUHFAxNetWorkBase_qcom.stopInventory()</code>函数。<br>

 第三步：退出app调用 <a href="../../../../../com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html#free--"><code>RFIDWithUHFA4NetWork_qcom.free()</code></a>断开连接，如果断开之前正在盘点，请先停止盘点，在断开连接。<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFBLE_qcom.html#com.rscja.custom">RFIDWithUHFBLE_qcom</a>
<div class="block">UHF模块低功耗蓝牙操作类<br>
 UHF module operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFUART_qcom.html#com.rscja.custom">RFIDWithUHFUART_qcom</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFUrxNetwork_qcom.html#com.rscja.custom">RFIDWithUHFUrxNetwork_qcom</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFUrxUart_qcom.html#com.rscja.custom">RFIDWithUHFUrxUart_qcom</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> used by <a href="../../../../../com/rscja/deviceapi/package-summary.html">com.rscja.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFAxBase_qcom.html#com.rscja.deviceapi">RFIDWithUHFAxBase_qcom</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFUART_qcom.html#com.rscja.deviceapi">RFIDWithUHFUART_qcom</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.custom">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> used by <a href="../../../../../com/rscja/team/qcom/custom/package-summary.html">com.rscja.team.qcom.custom</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/BluetoothReader_qcom.html#com.rscja.team.qcom.custom">BluetoothReader_qcom</a>
<div class="block">蓝牙读写器操作类：<br>
 Bluetoot reader operation
 第一步：调用<a href="../../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#init-android.content.Context-"><code>BluetoothReader_qcom.init(Context context)</code></a> 函数初始化蓝牙相关服务，退出应用程序需要调用<a href="../../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#free--"><code>BluetoothReader_qcom.free()</code></a>是否蓝牙相关资源<br>
 First step: Call <a href="../../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#init-android.content.Context-"><code>BluetoothReader_qcom.init(Context context)</code></a>to initiate BT service, call <a href="../../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#free--"><code>BluetoothReader_qcom.free()</code></a> to exit application.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFA4_qcom.html#com.rscja.team.qcom.custom">RFIDWithUHFA4_qcom</a>
<div class="block">UHF模块 A4操作类<br>
 UHF module operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFAxBase_qcom.html#com.rscja.team.qcom.custom">RFIDWithUHFAxBase_qcom</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFBLE_qcom.html#com.rscja.team.qcom.custom">RFIDWithUHFBLE_qcom</a>
<div class="block">UHF模块低功耗蓝牙操作类<br>
 UHF module operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFUART_qcom.html#com.rscja.team.qcom.custom">RFIDWithUHFUART_qcom</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.deviceapi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> used by <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/Barcode1D_qcom.html#com.rscja.team.qcom.deviceapi">Barcode1D_qcom</a>
<div class="block">一维条码操作类<br>1D barcode operation class<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/Barcode2D_qcom.html#com.rscja.team.qcom.deviceapi">Barcode2D_qcom</a>
<div class="block">二维条码操作类 <br>
 2D barcode operation class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/BluetoothReader_qcom.html#com.rscja.team.qcom.deviceapi">BluetoothReader_qcom</a>
<div class="block">蓝牙读写器操作类：<br>
 Bluetoot reader operation
 第一步：调用<a href="../../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#init-android.content.Context-"><code>BluetoothReader_qcom.init(Context context)</code></a> 函数初始化蓝牙相关服务，退出应用程序需要调用<a href="../../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#free--"><code>BluetoothReader_qcom.free()</code></a>是否蓝牙相关资源<br>
 First step: Call <a href="../../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#init-android.content.Context-"><code>BluetoothReader_qcom.init(Context context)</code></a>to initiate BT service, call <a href="../../../../../com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html#free--"><code>BluetoothReader_qcom.free()</code></a> to exit application.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/CardWithBYL_qcom.html#com.rscja.team.qcom.deviceapi">CardWithBYL_qcom</a>
<div class="block">白玉兰公交卡操作类</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/Device_qcom.html#com.rscja.team.qcom.deviceapi">Device_qcom</a>
<div class="block">设备根类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/DeviceAPI.html#com.rscja.team.qcom.deviceapi">DeviceAPI</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/Fingerprint_qcom.html#com.rscja.team.qcom.deviceapi">Fingerprint_qcom</a>
<div class="block">指纹识别模块操作类,<br>
 Fingerprint identify module operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/FingerprintSM206B_qcom.html#com.rscja.team.qcom.deviceapi">FingerprintSM206B_qcom</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/FingerprintWithFIPS_qcom.html#com.rscja.team.qcom.deviceapi">FingerprintWithFIPS_qcom</a>
<div class="block">FIPS指纹识别模块操作类,<br>
 FIPS fingerprint indentify module operation type,<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/FingerprintWithMorpho_qcom.html#com.rscja.team.qcom.deviceapi">FingerprintWithMorpho_qcom</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/FingerprintWithTLK1NC_qcom.html#com.rscja.team.qcom.deviceapi">FingerprintWithTLK1NC_qcom</a>
<div class="block">迪安杰</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/FingerprintWithZAZ_qcom.html#com.rscja.team.qcom.deviceapi">FingerprintWithZAZ_qcom</a>
<div class="block">指昂</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/HardwareInterface_qcom.html#com.rscja.team.qcom.deviceapi">HardwareInterface_qcom</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/HardwareInterface_qcom.FunctionEnum.html#com.rscja.team.qcom.deviceapi">HardwareInterface_qcom.FunctionEnum</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/LedLight_qcom.html#com.rscja.team.qcom.deviceapi">LedLight_qcom</a>
<div class="block">手柄LED灯控制类<br>
 Handdeld LED control type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/Module_qcom.html#com.rscja.team.qcom.deviceapi">Module_qcom</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/PSAM_qcom.html#com.rscja.team.qcom.deviceapi">PSAM_qcom</a>
<div class="block">PSAM操作类<br>
 PSAM operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDBase_qcom.html#com.rscja.team.qcom.deviceapi">RFIDBase_qcom</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithISO14443A_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithISO14443A_qcom</a>
<div class="block">RFID模块ISO14443A协议操作类<br>
 RFID module ISO 14443A protocol operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithISO14443A4CPU_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithISO14443A4CPU_qcom</a>
<div class="block">RFID模块ISO14443A CPU卡协议操作类<br>
 RFID module ISO14443A CPU card protocol operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithISO14443B_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithISO14443B_qcom</a>
<div class="block">RFID模块ISO14443B协议操作类<br>
 RFID module ISO14443B protocol operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithISO15693_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithISO15693_qcom</a>
<div class="block">RFID模块ISO15693协议操作类,<br>
 RFID module ISO15639 protocol operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithLF_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithLF_qcom</a>
<div class="block">RFID低频（125K）操作类<br>
 RFID LF (125K) operation type<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFA4_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithUHFA4_qcom</a>
<div class="block">UHF模块 A4操作类<br>
 UHF module operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFA4RS232_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithUHFA4RS232_qcom</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFA8RS232_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithUHFA8RS232_qcom</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFAxBase_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithUHFAxBase_qcom</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFBLE_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithUHFBLE_qcom</a>
<div class="block">UHF模块低功耗蓝牙操作类<br>
 UHF module operation type<br></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFRLM_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithUHFRLM_qcom</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFUART_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithUHFUART_qcom</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFUrxUart_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart_qcom</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFUrxUart2_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUart2_qcom</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFUrxUsbToUart_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithUHFUrxUsbToUart_qcom</a>
<div class="block">UR4 设备USB转串口,目前已经适配 pl2302芯片的usb转接线</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/RFIDWithUHFUSB_qcom.html#com.rscja.team.qcom.deviceapi">RFIDWithUHFUSB_qcom</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/ScanerLedLight_qcom.html#com.rscja.team.qcom.deviceapi">ScanerLedLight_qcom</a>
<div class="block">扫描LED灯控制类（仅C6000有效）<br>
 Scanning LED light control type ( valid for C6000 only)<br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/UHFProtocolParseBLE.html#com.rscja.team.qcom.deviceapi">UHFProtocolParseBLE</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/UHFProtocolParseBleByJava_qcom.html#com.rscja.team.qcom.deviceapi">UHFProtocolParseBleByJava_qcom</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/UHFProtocolParseByJava.html#com.rscja.team.qcom.deviceapi">UHFProtocolParseByJava</a>
<div class="block">java 解析R2\R5\R6 uhf协议</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/UHFProtocolParseUrxByJava_qcom.html#com.rscja.team.qcom.deviceapi">UHFProtocolParseUrxByJava_qcom</a>
<div class="block">java 解析UR4 协议</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/UHFUrxAutoInventoryTagFactory_qcom.html#com.rscja.team.qcom.deviceapi">UHFUrxAutoInventoryTagFactory_qcom</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/UsbFingerprint_qcom.html#com.rscja.team.qcom.deviceapi">UsbFingerprint_qcom</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.rs232utils">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/rscja/team/qcom/deviceapi/package-summary.html">com.rscja.team.qcom.deviceapi</a> used by <a href="../../../../../com/rscja/team/qcom/rs232utils/package-summary.html">com.rscja.team.qcom.rs232utils</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../../com/rscja/team/qcom/deviceapi/class-use/UHFProtocolParseByJava.html#com.rscja.team.qcom.rs232utils">UHFProtocolParseByJava</a>
<div class="block">java 解析R2\R5\R6 uhf协议</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/rscja/team/qcom/deviceapi/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
