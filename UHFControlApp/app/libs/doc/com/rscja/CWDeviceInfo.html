<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:05 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>CWDeviceInfo</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CWDeviceInfo";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CWDeviceInfo.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-files/index-1.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../index.html?com/rscja/CWDeviceInfo.html" target="_top">Frames</a></li>
<li><a href="CWDeviceInfo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja</div>
<h2 title="Class CWDeviceInfo" class="title">Class CWDeviceInfo</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.CWDeviceInfo</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">CWDeviceInfo</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C5_6765">C5_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C60_MTK_6765_110">C60_MTK_6765_110</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C60_QCM2150_100">C60_QCM2150_100</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C60_SMD450_100">C60_SMD450_100</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C6000_6735">C6000_6735</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C6000_6762">C6000_6762</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C61_SMD450_90">C61_SMD450_90</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C61P_SM6115_110">C61P_SM6115_110</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C66_8953">C66_8953</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C66_SMD450_90">C66_SMD450_90</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C66P_SM6115_110">C66P_SM6115_110</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C70_6735">C70_6735</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C70_6763">C70_6763</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C70_6765">C70_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C71_6763">C71_6763</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C71_6765">C71_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C72_6735">C72_6735</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C72_6763">C72_6763</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C72_6765">C72_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C75_6765">C75_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C76_6735">C76_6735</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C76_6765">C76_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#C90_6762_10">C90_6762_10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#CW102_6761">CW102_6761</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#CW103_6765">CW103_6765</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#CW107_6762">CW107_6762</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#CW108_8781">CW108_8781</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#CW108_8791">CW108_8791</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#CW95_6762">CW95_6762</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#H100_6735">H100_6735</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#H100_8953">H100_8953</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#MC50_4350_120">MC50_4350_120</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#MC51_4350_120">MC51_4350_120</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#P80_8786_130">P80_8786_130</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#P80_8953">P80_8953</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#P80_8953_90">P80_8953_90</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#PLATFORM_MTK">PLATFORM_MTK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#PLATFORM_QCOM">PLATFORM_QCOM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#TEAM_MTK">TEAM_MTK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#TEAM_QCOM">TEAM_QCOM</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#getCpuType--">getCpuType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../com/rscja/CWDeviceInfo.html" title="class in com.rscja">CWDeviceInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#getDeviceInfo--">getDeviceInfo</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#getModel--">getModel</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#getModelAndCpu--">getModelAndCpu</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#getPlatform--">getPlatform</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../com/rscja/CWDeviceInfo.html#getTeam--">getTeam</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="P80_8786_130">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P80_8786_130</h4>
<pre>public static final&nbsp;java.lang.String P80_8786_130</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.P80_8786_130">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P80_8953">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P80_8953</h4>
<pre>public static final&nbsp;java.lang.String P80_8953</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.P80_8953">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="P80_8953_90">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>P80_8953_90</h4>
<pre>public static final&nbsp;java.lang.String P80_8953_90</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.P80_8953_90">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="H100_8953">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>H100_8953</h4>
<pre>public static final&nbsp;java.lang.String H100_8953</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.H100_8953">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C66_8953">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C66_8953</h4>
<pre>public static final&nbsp;java.lang.String C66_8953</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.C66_8953">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C66P_SM6115_110">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C66P_SM6115_110</h4>
<pre>public static final&nbsp;java.lang.String C66P_SM6115_110</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.C66P_SM6115_110">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C61_SMD450_90">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C61_SMD450_90</h4>
<pre>public static final&nbsp;java.lang.String C61_SMD450_90</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.C61_SMD450_90">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C61P_SM6115_110">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C61P_SM6115_110</h4>
<pre>public static final&nbsp;java.lang.String C61P_SM6115_110</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.C61P_SM6115_110">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C66_SMD450_90">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C66_SMD450_90</h4>
<pre>public static final&nbsp;java.lang.String C66_SMD450_90</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.C66_SMD450_90">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C60_SMD450_100">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C60_SMD450_100</h4>
<pre>public static final&nbsp;java.lang.String C60_SMD450_100</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.C60_SMD450_100">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C60_QCM2150_100">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C60_QCM2150_100</h4>
<pre>public static final&nbsp;java.lang.String C60_QCM2150_100</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.C60_QCM2150_100">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C60_MTK_6765_110">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C60_MTK_6765_110</h4>
<pre>public static final&nbsp;java.lang.String C60_MTK_6765_110</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.C60_MTK_6765_110">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MC50_4350_120">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MC50_4350_120</h4>
<pre>public static final&nbsp;java.lang.String MC50_4350_120</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.MC50_4350_120">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MC51_4350_120">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MC51_4350_120</h4>
<pre>public static final&nbsp;java.lang.String MC51_4350_120</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.MC51_4350_120">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C6000_6735">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C6000_6735</h4>
<pre>public static&nbsp;java.lang.String C6000_6735</pre>
</li>
</ul>
<a name="H100_6735">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>H100_6735</h4>
<pre>public static&nbsp;java.lang.String H100_6735</pre>
</li>
</ul>
<a name="C70_6735">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C70_6735</h4>
<pre>public static&nbsp;java.lang.String C70_6735</pre>
</li>
</ul>
<a name="C72_6735">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C72_6735</h4>
<pre>public static&nbsp;java.lang.String C72_6735</pre>
</li>
</ul>
<a name="C76_6735">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C76_6735</h4>
<pre>public static&nbsp;java.lang.String C76_6735</pre>
</li>
</ul>
<a name="C70_6763">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C70_6763</h4>
<pre>public static&nbsp;java.lang.String C70_6763</pre>
</li>
</ul>
<a name="C71_6763">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C71_6763</h4>
<pre>public static&nbsp;java.lang.String C71_6763</pre>
</li>
</ul>
<a name="C72_6763">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C72_6763</h4>
<pre>public static&nbsp;java.lang.String C72_6763</pre>
</li>
</ul>
<a name="C70_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C70_6765</h4>
<pre>public static&nbsp;java.lang.String C70_6765</pre>
</li>
</ul>
<a name="C71_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C71_6765</h4>
<pre>public static&nbsp;java.lang.String C71_6765</pre>
</li>
</ul>
<a name="C72_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C72_6765</h4>
<pre>public static&nbsp;java.lang.String C72_6765</pre>
</li>
</ul>
<a name="C75_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C75_6765</h4>
<pre>public static&nbsp;java.lang.String C75_6765</pre>
</li>
</ul>
<a name="C76_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C76_6765</h4>
<pre>public static&nbsp;java.lang.String C76_6765</pre>
</li>
</ul>
<a name="C5_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C5_6765</h4>
<pre>public static&nbsp;java.lang.String C5_6765</pre>
</li>
</ul>
<a name="CW103_6765">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CW103_6765</h4>
<pre>public static&nbsp;java.lang.String CW103_6765</pre>
</li>
</ul>
<a name="C6000_6762">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C6000_6762</h4>
<pre>public static final&nbsp;java.lang.String C6000_6762</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.C6000_6762">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="C90_6762_10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>C90_6762_10</h4>
<pre>public static final&nbsp;java.lang.String C90_6762_10</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.C90_6762_10">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CW95_6762">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CW95_6762</h4>
<pre>public static final&nbsp;java.lang.String CW95_6762</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.CW95_6762">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CW102_6761">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CW102_6761</h4>
<pre>public static final&nbsp;java.lang.String CW102_6761</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.CW102_6761">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CW107_6762">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CW107_6762</h4>
<pre>public static final&nbsp;java.lang.String CW107_6762</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.CW107_6762">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CW108_8781">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CW108_8781</h4>
<pre>public static final&nbsp;java.lang.String CW108_8781</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.CW108_8781">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="CW108_8791">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CW108_8791</h4>
<pre>public static final&nbsp;java.lang.String CW108_8791</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.CW108_8791">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PLATFORM_MTK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLATFORM_MTK</h4>
<pre>public static final&nbsp;java.lang.String PLATFORM_MTK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.PLATFORM_MTK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="PLATFORM_QCOM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLATFORM_QCOM</h4>
<pre>public static final&nbsp;java.lang.String PLATFORM_QCOM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.PLATFORM_QCOM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TEAM_MTK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEAM_MTK</h4>
<pre>public static final&nbsp;int TEAM_MTK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.TEAM_MTK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TEAM_QCOM">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TEAM_QCOM</h4>
<pre>public static final&nbsp;int TEAM_QCOM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#com.rscja.CWDeviceInfo.TEAM_QCOM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCpuType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCpuType</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCpuType()</pre>
</li>
</ul>
<a name="getModel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModel</h4>
<pre>public&nbsp;java.lang.String&nbsp;getModel()</pre>
</li>
</ul>
<a name="getPlatform--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlatform</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPlatform()</pre>
</li>
</ul>
<a name="getTeam--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTeam</h4>
<pre>public&nbsp;int&nbsp;getTeam()</pre>
</li>
</ul>
<a name="getModelAndCpu--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelAndCpu</h4>
<pre>public&nbsp;java.lang.String&nbsp;getModelAndCpu()</pre>
</li>
</ul>
<a name="getDeviceInfo--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getDeviceInfo</h4>
<pre>public static&nbsp;<a href="../../com/rscja/CWDeviceInfo.html" title="class in com.rscja">CWDeviceInfo</a>&nbsp;getDeviceInfo()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CWDeviceInfo.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-files/index-1.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../index.html?com/rscja/CWDeviceInfo.html" target="_top">Frames</a></li>
<li><a href="CWDeviceInfo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
