<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>BarcodeSymbolUtility</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BarcodeSymbolUtility";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BarcodeSymbolUtility.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/barcode/BarcodeFactory.html" title="class in com.rscja.barcode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/barcode/BarcodeSymbolUtility.html" target="_top">Frames</a></li>
<li><a href="BarcodeSymbolUtility.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.barcode</div>
<h2 title="Class BarcodeSymbolUtility" class="title">Class BarcodeSymbolUtility</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.barcode.BarcodeSymbolUtility</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">BarcodeSymbolUtility</span>
extends java.lang.Object</pre>
<div class="block">条码符号工具类</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_AIM_128">INT_AIM_128</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_AUSPOST">INT_AUSPOST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_AZTEC">INT_AZTEC</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_BOOKLAND">INT_BOOKLAND</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_BPO">INT_BPO</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CANPOST">INT_CANPOST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CHINAPOST">INT_CHINAPOST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODABAR">INT_CODABAR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODABLOCK">INT_CODABLOCK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODABLOCK_A">INT_CODABLOCK_A</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE_16K">INT_CODE_16K</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE11">INT_CODE11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE128">INT_CODE128</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE32">INT_CODE32</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE39">INT_CODE39</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE39_FULL_ASCII">INT_CODE39_FULL_ASCII</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE49">INT_CODE49</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CODE93">INT_CODE93</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_COUPON_CODE">INT_COUPON_CODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_CUSTOM_GS1_DATAMATRIX">INT_CUSTOM_GS1_DATAMATRIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_DATAMATRIX">INT_DATAMATRIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_DISCRETE_2_OF_5">INT_DISCRETE_2_OF_5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_DUTCHPOST">INT_DUTCHPOST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_EAN_128">INT_EAN_128</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_EAN13">INT_EAN13</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_EAN8">INT_EAN8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_GRIDMATRIX">INT_GRIDMATRIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_GS1_128">INT_GS1_128</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_GS1_DATA_MATRIX">INT_GS1_DATA_MATRIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_GS1_DataBar">INT_GS1_DataBar</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_GS1_DataBar_Expanded">INT_GS1_DataBar_Expanded</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_GS1_DataBar_Limited">INT_GS1_DataBar_Limited</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_GS1_QR_CODE">INT_GS1_QR_CODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_HANXIN">INT_HANXIN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_IATA">INT_IATA</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_INFOMAIL">INT_INFOMAIL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_INTERLEAVED_2_OF_5">INT_INTERLEAVED_2_OF_5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_ISBT_128">INT_ISBT_128</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_ISSN">INT_ISSN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_JAPOST">INT_JAPOST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_KOREAPOST">INT_KOREAPOST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_MACRO_PDF">INT_MACRO_PDF</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_MACRO_QR">INT_MACRO_QR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_MATRIX25">INT_MATRIX25</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_MAXICODE">INT_MAXICODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_MICRO_QR">INT_MICRO_QR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_MICROPDF">INT_MICROPDF</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_MSI">INT_MSI</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_NEC_2_OF_5">INT_NEC_2_OF_5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_PDF417">INT_PDF417</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_PLANET">INT_PLANET</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_POSTAL_4I">INT_POSTAL_4I</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_QR">INT_QR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_TELEPEN">INT_TELEPEN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_TLCODE39">INT_TLCODE39</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_TRIOPTIC">INT_TRIOPTIC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_UPC_D">INT_UPC_D</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_UPC_E1">INT_UPC_E1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_UPCA">INT_UPCA</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_UPCE0">INT_UPCE0</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_US_POSTALS1">INT_US_POSTALS1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#INT_USPS4CB">INT_USPS4CB</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_AIM_128">STR_AIM_128</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_AUSPOST">STR_AUSPOST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_AZTEC">STR_AZTEC</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_BOOKLAND">STR_BOOKLAND</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_BPO">STR_BPO</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CANPOST">STR_CANPOST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CHINAPOST">STR_CHINAPOST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODABAR">STR_CODABAR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODABLOCK">STR_CODABLOCK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODABLOCK_A">STR_CODABLOCK_A</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE_16K">STR_CODE_16K</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE11">STR_CODE11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE128">STR_CODE128</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE32">STR_CODE32</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE39">STR_CODE39</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE39_FULL_ASCII">STR_CODE39_FULL_ASCII</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE49">STR_CODE49</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CODE93">STR_CODE93</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_COUPON_CODE">STR_COUPON_CODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_CUSTOM_GS1_DATAMATRIX">STR_CUSTOM_GS1_DATAMATRIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_DATAMATRIX">STR_DATAMATRIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_DISCRETE_2_OF_5">STR_DISCRETE_2_OF_5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_DUTCHPOST">STR_DUTCHPOST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_EAN_128">STR_EAN_128</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_EAN13">STR_EAN13</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_EAN8">STR_EAN8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_GRIDMATRIX">STR_GRIDMATRIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_GS1_128">STR_GS1_128</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_GS1_DATA_MATRIX">STR_GS1_DATA_MATRIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_GS1_DataBar">STR_GS1_DataBar</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_GS1_DataBar_Expanded">STR_GS1_DataBar_Expanded</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_GS1_DataBar_Limited">STR_GS1_DataBar_Limited</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_GS1_QR">STR_GS1_QR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_HANXIN">STR_HANXIN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_IATA">STR_IATA</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_INFOMAIL">STR_INFOMAIL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_INTERLEAVED_2_OF_5">STR_INTERLEAVED_2_OF_5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_ISBT_128">STR_ISBT_128</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_ISSN">STR_ISSN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_JAPOST">STR_JAPOST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_KOREAPOST">STR_KOREAPOST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_MACRO_PDF">STR_MACRO_PDF</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_MACRO_QR">STR_MACRO_QR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_MATRIX25">STR_MATRIX25</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_MAXICODE">STR_MAXICODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_MICRO_QR">STR_MICRO_QR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_MICROPDF">STR_MICROPDF</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_MSI">STR_MSI</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_NEC_2_OF_5">STR_NEC_2_OF_5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_PDF417">STR_PDF417</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_PLANET">STR_PLANET</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_POSTAL_4I">STR_POSTAL_4I</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_QR">STR_QR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_TELEPEN">STR_TELEPEN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_TLCODE39">STR_TLCODE39</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_TRIOPTIC">STR_TRIOPTIC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_UPC_D">STR_UPC_D</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_UPC_E1">STR_UPC_E1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_UPCA">STR_UPCA</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_UPCE0">STR_UPCE0</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_US_POSTALS1">STR_US_POSTALS1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#STR_USPS4CB">STR_USPS4CB</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#getBarcodeName-int-">getBarcodeName</a></span>(int&nbsp;symbol)</code>
<div class="block">获取条码名称(get barcode name)</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html" title="class in com.rscja.barcode">BarcodeSymbolUtility</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="STR_AZTEC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_AZTEC</h4>
<pre>public static final&nbsp;java.lang.String STR_AZTEC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_AZTEC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CODABAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CODABAR</h4>
<pre>public static final&nbsp;java.lang.String STR_CODABAR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CODABAR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CODE11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CODE11</h4>
<pre>public static final&nbsp;java.lang.String STR_CODE11</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CODE11">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CODE128">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CODE128</h4>
<pre>public static final&nbsp;java.lang.String STR_CODE128</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CODE128">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CODE39">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CODE39</h4>
<pre>public static final&nbsp;java.lang.String STR_CODE39</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CODE39">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CODE93">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CODE93</h4>
<pre>public static final&nbsp;java.lang.String STR_CODE93</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CODE93">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_EAN8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_EAN8</h4>
<pre>public static final&nbsp;java.lang.String STR_EAN8</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_EAN8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_EAN13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_EAN13</h4>
<pre>public static final&nbsp;java.lang.String STR_EAN13</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_EAN13">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_MAXICODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_MAXICODE</h4>
<pre>public static final&nbsp;java.lang.String STR_MAXICODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_MAXICODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_MICROPDF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_MICROPDF</h4>
<pre>public static final&nbsp;java.lang.String STR_MICROPDF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_MICROPDF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_PDF417">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_PDF417</h4>
<pre>public static final&nbsp;java.lang.String STR_PDF417</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_PDF417">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_UPCA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_UPCA</h4>
<pre>public static final&nbsp;java.lang.String STR_UPCA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_UPCA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_UPCE0">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_UPCE0</h4>
<pre>public static final&nbsp;java.lang.String STR_UPCE0</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_UPCE0">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_BPO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_BPO</h4>
<pre>public static final&nbsp;java.lang.String STR_BPO</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_BPO">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CANPOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CANPOST</h4>
<pre>public static final&nbsp;java.lang.String STR_CANPOST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CANPOST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_AUSPOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_AUSPOST</h4>
<pre>public static final&nbsp;java.lang.String STR_AUSPOST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_AUSPOST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_IATA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_IATA</h4>
<pre>public static final&nbsp;java.lang.String STR_IATA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_IATA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CODABLOCK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CODABLOCK</h4>
<pre>public static final&nbsp;java.lang.String STR_CODABLOCK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CODABLOCK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_JAPOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_JAPOST</h4>
<pre>public static final&nbsp;java.lang.String STR_JAPOST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_JAPOST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_PLANET">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_PLANET</h4>
<pre>public static final&nbsp;java.lang.String STR_PLANET</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_PLANET">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_DUTCHPOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_DUTCHPOST</h4>
<pre>public static final&nbsp;java.lang.String STR_DUTCHPOST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_DUTCHPOST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_TELEPEN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_TELEPEN</h4>
<pre>public static final&nbsp;java.lang.String STR_TELEPEN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_TELEPEN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_MSI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_MSI</h4>
<pre>public static final&nbsp;java.lang.String STR_MSI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_MSI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_TLCODE39">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_TLCODE39</h4>
<pre>public static final&nbsp;java.lang.String STR_TLCODE39</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_TLCODE39">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_QR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_QR</h4>
<pre>public static final&nbsp;java.lang.String STR_QR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_QR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_MICRO_QR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_MICRO_QR</h4>
<pre>public static final&nbsp;java.lang.String STR_MICRO_QR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_MICRO_QR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_KOREAPOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_KOREAPOST</h4>
<pre>public static final&nbsp;java.lang.String STR_KOREAPOST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_KOREAPOST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CHINAPOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CHINAPOST</h4>
<pre>public static final&nbsp;java.lang.String STR_CHINAPOST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CHINAPOST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_MATRIX25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_MATRIX25</h4>
<pre>public static final&nbsp;java.lang.String STR_MATRIX25</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_MATRIX25">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CODE32">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CODE32</h4>
<pre>public static final&nbsp;java.lang.String STR_CODE32</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CODE32">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_GRIDMATRIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_GRIDMATRIX</h4>
<pre>public static final&nbsp;java.lang.String STR_GRIDMATRIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_GRIDMATRIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_HANXIN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_HANXIN</h4>
<pre>public static final&nbsp;java.lang.String STR_HANXIN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_HANXIN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_GS1_128">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_GS1_128</h4>
<pre>public static final&nbsp;java.lang.String STR_GS1_128</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_GS1_128">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_USPS4CB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_USPS4CB</h4>
<pre>public static final&nbsp;java.lang.String STR_USPS4CB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_USPS4CB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_US_POSTALS1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_US_POSTALS1</h4>
<pre>public static final&nbsp;java.lang.String STR_US_POSTALS1</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_US_POSTALS1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_POSTAL_4I">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_POSTAL_4I</h4>
<pre>public static final&nbsp;java.lang.String STR_POSTAL_4I</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_POSTAL_4I">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CODABLOCK_A">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CODABLOCK_A</h4>
<pre>public static final&nbsp;java.lang.String STR_CODABLOCK_A</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CODABLOCK_A">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CODE49">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CODE49</h4>
<pre>public static final&nbsp;java.lang.String STR_CODE49</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CODE49">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_INFOMAIL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_INFOMAIL</h4>
<pre>public static final&nbsp;java.lang.String STR_INFOMAIL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_INFOMAIL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_GS1_DataBar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_GS1_DataBar</h4>
<pre>public static final&nbsp;java.lang.String STR_GS1_DataBar</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_GS1_DataBar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_GS1_DataBar_Limited">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_GS1_DataBar_Limited</h4>
<pre>public static final&nbsp;java.lang.String STR_GS1_DataBar_Limited</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_GS1_DataBar_Limited">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_GS1_DataBar_Expanded">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_GS1_DataBar_Expanded</h4>
<pre>public static final&nbsp;java.lang.String STR_GS1_DataBar_Expanded</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_GS1_DataBar_Expanded">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_NEC_2_OF_5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_NEC_2_OF_5</h4>
<pre>public static final&nbsp;java.lang.String STR_NEC_2_OF_5</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_NEC_2_OF_5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_DATAMATRIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_DATAMATRIX</h4>
<pre>public static final&nbsp;java.lang.String STR_DATAMATRIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_DATAMATRIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_DISCRETE_2_OF_5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_DISCRETE_2_OF_5</h4>
<pre>public static final&nbsp;java.lang.String STR_DISCRETE_2_OF_5</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_DISCRETE_2_OF_5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_INTERLEAVED_2_OF_5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_INTERLEAVED_2_OF_5</h4>
<pre>public static final&nbsp;java.lang.String STR_INTERLEAVED_2_OF_5</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_INTERLEAVED_2_OF_5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_EAN_128">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_EAN_128</h4>
<pre>public static final&nbsp;java.lang.String STR_EAN_128</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_EAN_128">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_UPC_E1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_UPC_E1</h4>
<pre>public static final&nbsp;java.lang.String STR_UPC_E1</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_UPC_E1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CODE_16K">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CODE_16K</h4>
<pre>public static final&nbsp;java.lang.String STR_CODE_16K</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CODE_16K">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_UPC_D">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_UPC_D</h4>
<pre>public static final&nbsp;java.lang.String STR_UPC_D</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_UPC_D">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_TRIOPTIC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_TRIOPTIC</h4>
<pre>public static final&nbsp;java.lang.String STR_TRIOPTIC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_TRIOPTIC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_AIM_128">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_AIM_128</h4>
<pre>public static final&nbsp;java.lang.String STR_AIM_128</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_AIM_128">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CUSTOM_GS1_DATAMATRIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CUSTOM_GS1_DATAMATRIX</h4>
<pre>public static final&nbsp;java.lang.String STR_CUSTOM_GS1_DATAMATRIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CUSTOM_GS1_DATAMATRIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_CODE39_FULL_ASCII">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_CODE39_FULL_ASCII</h4>
<pre>public static final&nbsp;java.lang.String STR_CODE39_FULL_ASCII</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_CODE39_FULL_ASCII">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_BOOKLAND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_BOOKLAND</h4>
<pre>public static final&nbsp;java.lang.String STR_BOOKLAND</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_BOOKLAND">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_ISBT_128">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_ISBT_128</h4>
<pre>public static final&nbsp;java.lang.String STR_ISBT_128</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_ISBT_128">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_COUPON_CODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_COUPON_CODE</h4>
<pre>public static final&nbsp;java.lang.String STR_COUPON_CODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_COUPON_CODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_MACRO_PDF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_MACRO_PDF</h4>
<pre>public static final&nbsp;java.lang.String STR_MACRO_PDF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_MACRO_PDF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_MACRO_QR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_MACRO_QR</h4>
<pre>public static final&nbsp;java.lang.String STR_MACRO_QR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_MACRO_QR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_ISSN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_ISSN</h4>
<pre>public static final&nbsp;java.lang.String STR_ISSN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_ISSN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_GS1_DATA_MATRIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_GS1_DATA_MATRIX</h4>
<pre>public static final&nbsp;java.lang.String STR_GS1_DATA_MATRIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_GS1_DATA_MATRIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="STR_GS1_QR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STR_GS1_QR</h4>
<pre>public static final&nbsp;java.lang.String STR_GS1_QR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.STR_GS1_QR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_AZTEC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_AZTEC</h4>
<pre>public static final&nbsp;int INT_AZTEC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_AZTEC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CODABAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_CODABAR</h4>
<pre>public static final&nbsp;int INT_CODABAR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CODABAR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CODE11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_CODE11</h4>
<pre>public static final&nbsp;int INT_CODE11</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CODE11">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CODE128">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_CODE128</h4>
<pre>public static final&nbsp;int INT_CODE128</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CODE128">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CODE39">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_CODE39</h4>
<pre>public static final&nbsp;int INT_CODE39</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CODE39">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CODE93">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_CODE93</h4>
<pre>public static final&nbsp;int INT_CODE93</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CODE93">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_EAN8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_EAN8</h4>
<pre>public static final&nbsp;int INT_EAN8</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_EAN8">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_EAN13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_EAN13</h4>
<pre>public static final&nbsp;int INT_EAN13</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_EAN13">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_MAXICODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_MAXICODE</h4>
<pre>public static final&nbsp;int INT_MAXICODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_MAXICODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_MICROPDF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_MICROPDF</h4>
<pre>public static final&nbsp;int INT_MICROPDF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_MICROPDF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_PDF417">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_PDF417</h4>
<pre>public static final&nbsp;int INT_PDF417</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_PDF417">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_UPCA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_UPCA</h4>
<pre>public static final&nbsp;int INT_UPCA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_UPCA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_UPCE0">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_UPCE0</h4>
<pre>public static final&nbsp;int INT_UPCE0</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_UPCE0">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_BPO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_BPO</h4>
<pre>public static final&nbsp;int INT_BPO</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_BPO">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CANPOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_CANPOST</h4>
<pre>public static final&nbsp;int INT_CANPOST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CANPOST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_AUSPOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_AUSPOST</h4>
<pre>public static final&nbsp;int INT_AUSPOST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_AUSPOST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_IATA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_IATA</h4>
<pre>public static final&nbsp;int INT_IATA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_IATA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CODABLOCK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_CODABLOCK</h4>
<pre>public static final&nbsp;int INT_CODABLOCK</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CODABLOCK">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_JAPOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_JAPOST</h4>
<pre>public static final&nbsp;int INT_JAPOST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_JAPOST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_PLANET">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_PLANET</h4>
<pre>public static final&nbsp;int INT_PLANET</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_PLANET">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_DUTCHPOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_DUTCHPOST</h4>
<pre>public static final&nbsp;int INT_DUTCHPOST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_DUTCHPOST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_TELEPEN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_TELEPEN</h4>
<pre>public static final&nbsp;int INT_TELEPEN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_TELEPEN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_MSI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_MSI</h4>
<pre>public static final&nbsp;int INT_MSI</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_MSI">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_TLCODE39">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_TLCODE39</h4>
<pre>public static final&nbsp;int INT_TLCODE39</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_TLCODE39">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_QR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_QR</h4>
<pre>public static final&nbsp;int INT_QR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_QR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_KOREAPOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_KOREAPOST</h4>
<pre>public static final&nbsp;int INT_KOREAPOST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_KOREAPOST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_MATRIX25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_MATRIX25</h4>
<pre>public static final&nbsp;int INT_MATRIX25</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_MATRIX25">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CODE32">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_CODE32</h4>
<pre>public static final&nbsp;int INT_CODE32</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CODE32">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_GRIDMATRIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_GRIDMATRIX</h4>
<pre>public static final&nbsp;int INT_GRIDMATRIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_GRIDMATRIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_HANXIN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_HANXIN</h4>
<pre>public static final&nbsp;int INT_HANXIN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_HANXIN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_GS1_128">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_GS1_128</h4>
<pre>public static final&nbsp;int INT_GS1_128</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_GS1_128">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_USPS4CB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_USPS4CB</h4>
<pre>public static final&nbsp;int INT_USPS4CB</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_USPS4CB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_US_POSTALS1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_US_POSTALS1</h4>
<pre>public static final&nbsp;int INT_US_POSTALS1</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_US_POSTALS1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_POSTAL_4I">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_POSTAL_4I</h4>
<pre>public static final&nbsp;int INT_POSTAL_4I</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_POSTAL_4I">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CODABLOCK_A">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_CODABLOCK_A</h4>
<pre>public static final&nbsp;int INT_CODABLOCK_A</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CODABLOCK_A">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CODE49">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_CODE49</h4>
<pre>public static final&nbsp;int INT_CODE49</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CODE49">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_INFOMAIL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_INFOMAIL</h4>
<pre>public static final&nbsp;int INT_INFOMAIL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_INFOMAIL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_GS1_DataBar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_GS1_DataBar</h4>
<pre>public static final&nbsp;int INT_GS1_DataBar</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_GS1_DataBar">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_GS1_DataBar_Limited">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_GS1_DataBar_Limited</h4>
<pre>public static final&nbsp;int INT_GS1_DataBar_Limited</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_GS1_DataBar_Limited">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_GS1_DataBar_Expanded">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_GS1_DataBar_Expanded</h4>
<pre>public static final&nbsp;int INT_GS1_DataBar_Expanded</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_GS1_DataBar_Expanded">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_NEC_2_OF_5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_NEC_2_OF_5</h4>
<pre>public static final&nbsp;int INT_NEC_2_OF_5</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_NEC_2_OF_5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CHINAPOST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_CHINAPOST</h4>
<pre>public static final&nbsp;int INT_CHINAPOST</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CHINAPOST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_DATAMATRIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_DATAMATRIX</h4>
<pre>public static final&nbsp;int INT_DATAMATRIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_DATAMATRIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_DISCRETE_2_OF_5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_DISCRETE_2_OF_5</h4>
<pre>public static final&nbsp;int INT_DISCRETE_2_OF_5</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_DISCRETE_2_OF_5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_INTERLEAVED_2_OF_5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_INTERLEAVED_2_OF_5</h4>
<pre>public static final&nbsp;int INT_INTERLEAVED_2_OF_5</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_INTERLEAVED_2_OF_5">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_EAN_128">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_EAN_128</h4>
<pre>public static final&nbsp;int INT_EAN_128</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_EAN_128">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_UPC_E1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_UPC_E1</h4>
<pre>public static final&nbsp;int INT_UPC_E1</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_UPC_E1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CODE_16K">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_CODE_16K</h4>
<pre>public static final&nbsp;int INT_CODE_16K</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CODE_16K">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_UPC_D">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_UPC_D</h4>
<pre>public static final&nbsp;int INT_UPC_D</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_UPC_D">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_TRIOPTIC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_TRIOPTIC</h4>
<pre>public static final&nbsp;int INT_TRIOPTIC</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_TRIOPTIC">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_MICRO_QR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_MICRO_QR</h4>
<pre>public static final&nbsp;int INT_MICRO_QR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_MICRO_QR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_AIM_128">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_AIM_128</h4>
<pre>public static final&nbsp;int INT_AIM_128</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_AIM_128">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_BOOKLAND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_BOOKLAND</h4>
<pre>public static final&nbsp;int INT_BOOKLAND</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_BOOKLAND">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_ISBT_128">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_ISBT_128</h4>
<pre>public static final&nbsp;int INT_ISBT_128</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_ISBT_128">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_COUPON_CODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_COUPON_CODE</h4>
<pre>public static final&nbsp;int INT_COUPON_CODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_COUPON_CODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_MACRO_PDF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_MACRO_PDF</h4>
<pre>public static final&nbsp;int INT_MACRO_PDF</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_MACRO_PDF">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_MACRO_QR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_MACRO_QR</h4>
<pre>public static final&nbsp;int INT_MACRO_QR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_MACRO_QR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_ISSN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_ISSN</h4>
<pre>public static final&nbsp;int INT_ISSN</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_ISSN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CODE39_FULL_ASCII">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_CODE39_FULL_ASCII</h4>
<pre>public static final&nbsp;int INT_CODE39_FULL_ASCII</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CODE39_FULL_ASCII">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_GS1_DATA_MATRIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_GS1_DATA_MATRIX</h4>
<pre>public static final&nbsp;int INT_GS1_DATA_MATRIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_GS1_DATA_MATRIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_GS1_QR_CODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INT_GS1_QR_CODE</h4>
<pre>public static final&nbsp;int INT_GS1_QR_CODE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_GS1_QR_CODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="INT_CUSTOM_GS1_DATAMATRIX">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>INT_CUSTOM_GS1_DATAMATRIX</h4>
<pre>public static final&nbsp;int INT_CUSTOM_GS1_DATAMATRIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeSymbolUtility.INT_CUSTOM_GS1_DATAMATRIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html" title="class in com.rscja.barcode">BarcodeSymbolUtility</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="getBarcodeName-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getBarcodeName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getBarcodeName(int&nbsp;symbol)</pre>
<div class="block">获取条码名称(get barcode name)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>symbol</code> - 条码符号(barcode symbol)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>条码名称(barcode name)</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BarcodeSymbolUtility.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/barcode/BarcodeFactory.html" title="class in com.rscja.barcode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/barcode/BarcodeSymbolUtility.html" target="_top">Frames</a></li>
<li><a href="BarcodeSymbolUtility.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
