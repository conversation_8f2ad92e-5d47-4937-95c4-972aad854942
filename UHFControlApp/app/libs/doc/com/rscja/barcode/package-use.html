<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Uses of Package com.rscja.barcode</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package com.rscja.barcode";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/barcode/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package com.rscja.barcode" class="title">Uses of Package<br>com.rscja.barcode</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../com/rscja/barcode/package-summary.html">com.rscja.barcode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.barcode">com.rscja.barcode</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.deviceapi.interfaces">com.rscja.deviceapi.interfaces</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.barcode">com.rscja.team.mtk.barcode</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.mtk.barcode.barcode2d">com.rscja.team.mtk.barcode.barcode2d</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.barcode">com.rscja.team.qcom.barcode</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.rscja.team.qcom.barcode.barcode2d">com.rscja.team.qcom.barcode.barcode2d</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.barcode">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/barcode/package-summary.html">com.rscja.barcode</a> used by <a href="../../../com/rscja/barcode/package-summary.html">com.rscja.barcode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/Barcode2DSHardwareInfo.html#com.rscja.barcode">Barcode2DSHardwareInfo</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeDecoder.html#com.rscja.barcode">BarcodeDecoder</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeDecoder.DecodeCallback.html#com.rscja.barcode">BarcodeDecoder.DecodeCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeDecoder.IBarcodeImageCallback.html#com.rscja.barcode">BarcodeDecoder.IBarcodeImageCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeFactory.html#com.rscja.barcode">BarcodeFactory</a>
<div class="block">条码工厂类</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeSymbolUtility.html#com.rscja.barcode">BarcodeSymbolUtility</a>
<div class="block">条码符号工具类</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeUtility.html#com.rscja.barcode">BarcodeUtility</a>
<div class="block">条码操作类
 barcode operate class



 注意：
 Attention:
 1、使用前请确认您的机器已安装键盘助手v1.9.0 之后的版本。
 1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeUtility.ModuleType.html#com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/IBarcode2DSHardwareInfo.html#com.rscja.barcode">IBarcode2DSHardwareInfo</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.deviceapi.interfaces">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/barcode/package-summary.html">com.rscja.barcode</a> used by <a href="../../../com/rscja/deviceapi/interfaces/package-summary.html">com.rscja.deviceapi.interfaces</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeUtility.ModuleType.html#com.rscja.deviceapi.interfaces">BarcodeUtility.ModuleType</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.barcode">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/barcode/package-summary.html">com.rscja.barcode</a> used by <a href="../../../com/rscja/team/mtk/barcode/package-summary.html">com.rscja.team.mtk.barcode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeDecoder.html#com.rscja.team.mtk.barcode">BarcodeDecoder</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeUtility.ModuleType.html#com.rscja.team.mtk.barcode">BarcodeUtility.ModuleType</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/IBarcode2DSHardwareInfo.html#com.rscja.team.mtk.barcode">IBarcode2DSHardwareInfo</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.mtk.barcode.barcode2d">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/barcode/package-summary.html">com.rscja.barcode</a> used by <a href="../../../com/rscja/team/mtk/barcode/barcode2d/package-summary.html">com.rscja.team.mtk.barcode.barcode2d</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeDecoder.html#com.rscja.team.mtk.barcode.barcode2d">BarcodeDecoder</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeDecoder.DecodeCallback.html#com.rscja.team.mtk.barcode.barcode2d">BarcodeDecoder.DecodeCallback</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.barcode">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/barcode/package-summary.html">com.rscja.barcode</a> used by <a href="../../../com/rscja/team/qcom/barcode/package-summary.html">com.rscja.team.qcom.barcode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeDecoder.html#com.rscja.team.qcom.barcode">BarcodeDecoder</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeUtility.ModuleType.html#com.rscja.team.qcom.barcode">BarcodeUtility.ModuleType</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/IBarcode2DSHardwareInfo.html#com.rscja.team.qcom.barcode">IBarcode2DSHardwareInfo</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.rscja.team.qcom.barcode.barcode2d">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../com/rscja/barcode/package-summary.html">com.rscja.barcode</a> used by <a href="../../../com/rscja/team/qcom/barcode/barcode2d/package-summary.html">com.rscja.team.qcom.barcode.barcode2d</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeDecoder.html#com.rscja.team.qcom.barcode.barcode2d">BarcodeDecoder</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeDecoder.DecodeCallback.html#com.rscja.team.qcom.barcode.barcode2d">BarcodeDecoder.DecodeCallback</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../com/rscja/barcode/class-use/BarcodeDecoder.IBarcodeImageCallback.html#com.rscja.team.qcom.barcode.barcode2d">BarcodeDecoder.IBarcodeImageCallback</a>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/barcode/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
