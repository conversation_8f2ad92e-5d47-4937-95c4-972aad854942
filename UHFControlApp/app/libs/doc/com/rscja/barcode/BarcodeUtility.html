<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>BarcodeUtility</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BarcodeUtility";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":9,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BarcodeUtility.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html" title="class in com.rscja.barcode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/barcode/BarcodeUtility.html" target="_top">Frames</a></li>
<li><a href="BarcodeUtility.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.barcode</div>
<h2 title="Class BarcodeUtility" class="title">Class BarcodeUtility</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.barcode.BarcodeUtility</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">BarcodeUtility</span>
extends java.lang.Object
implements <a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></pre>
<div class="block">条码操作类
 barcode operate class



 注意：
 Attention:
 1、使用前请确认您的机器已安装键盘助手v1.9.0 之后的版本。
 1. Confirm keyboardeumulator v1.9.0 has been installed before using.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>zhoupin</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#ACTION_SCAN_KEYBOARD_HELPER_PARAM_RESPONSE">ACTION_SCAN_KEYBOARD_HELPER_PARAM_RESPONSE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_BARCODE_1D">SCANNER_BARCODE_1D</a></span></code>
<div class="block">1D是否已经开启</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_BARCODE_2D">SCANNER_BARCODE_2D</a></span></code>
<div class="block">2D是否已经开启</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_BARCODENOTREPEAT">SCANNER_BARCODENOTREPEAT</a></span></code>
<div class="block">条码是否重复显示</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_BROADCAST_ACTION">SCANNER_BROADCAST_ACTION</a></span></code>
<div class="block">接收扫描数据的广播ACTION</br></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_BROADCAST_EXTRA">SCANNER_BROADCAST_EXTRA</a></span></code>
<div class="block">接收扫描数据的广播的extra数据</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_CONTINUOUS">SCANNER_CONTINUOUS</a></span></code>
<div class="block">是否是连续扫描</br>
 Whether enable continuous scan</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_CONTINUOUS_INTREVALTIME">SCANNER_CONTINUOUS_INTREVALTIME</a></span></code>
<div class="block">连续扫描时间间隔</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_CONTINUOUSMODE">SCANNER_CONTINUOUSMODE</a></span></code>
<div class="block">是否启用了连续扫描模式</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_CONTINUOUSTIMEOUT">SCANNER_CONTINUOUSTIMEOUT</a></span></code>
<div class="block">连续扫描超时时间</br>
 continuous scanning overtime duration</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_ENDINDEX">SCANNER_ENDINDEX</a></span></code>
<div class="block">后面去掉多少个字符</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_ENTER">SCANNER_ENTER</a></span></code>
<div class="block">是否启用回车</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_FAILUREBROADCAST">SCANNER_FAILUREBROADCAST</a></span></code>
<div class="block">扫描失败是否发送广播</br>
 Send broadcast when scanning failure.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_FAILURESOUND">SCANNER_FAILURESOUND</a></span></code>
<div class="block">扫描失败是否播放声音</br>
 whether playing notification when scanning failure.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_FILTERCHARS">SCANNER_FILTERCHARS</a></span></code>
<div class="block">条码数据需要过滤的字符串</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_FORMAT_BARCODE">SCANNER_FORMAT_BARCODE</a></span></code>
<div class="block">条码格式    0: Default   1: ASCII    2:GB2312   3:UTF8   4:Unicode   10:GBK,    11:GB18030</br>
 Barcode format 0: Default   1: ASCII    2:GB2312   3:UTF8   4:Unicode   10:GBK,    11:GB18030</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_ILLUMINATIONPOWERLEVEL">SCANNER_ILLUMINATIONPOWERLEVEL</a></span></code>
<div class="block">4710 扫描头亮度等级</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_KEYBORADHELPER_OPEN">SCANNER_KEYBORADHELPER_OPEN</a></span></code>
<div class="block">键盘助手总开关是否已经打开</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_OUTPUTMODE">SCANNER_OUTPUTMODE</a></span></code>
<div class="block">数据接收方式    0:键盘模拟    1:剪切板   2:系统广播</br>
 ouput mode 0: virtual key   1: clipboard   2: broadcast</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_PREFIX">SCANNER_PREFIX</a></span></code>
<div class="block">条码前缀</br>
 barcode prefix.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_RELEASESCAN">SCANNER_RELEASESCAN</a></span></code>
<div class="block">松开扫描按键是否停止扫描</br>
 loose scanning button to stop scanning or not</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_SCANKEYCODE_1">SCANNER_SCANKEYCODE_1</a></span></code>
<div class="block">扫描按键值
 scan key code.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_SCANKEYCODE_3">SCANNER_SCANKEYCODE_3</a></span></code>
<div class="block">扫描按键值</br>
 scan key code.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_SOUND">SCANNER_SOUND</a></span></code>
<div class="block">扫描成功是否播放声音</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_STARTINDEX">SCANNER_STARTINDEX</a></span></code>
<div class="block">前面去掉多少个字符</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_SUFFIX">SCANNER_SUFFIX</a></span></code>
<div class="block">条码后缀</br>
 barcode suffix.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_TAB">SCANNER_TAB</a></span></code>
<div class="block">是否启用tab</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_TIMEOUT">SCANNER_TIMEOUT</a></span></code>
<div class="block">扫描超时</br>
 scanning time out.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#SCANNER_VIBRATE">SCANNER_VIBRATE</a></span></code>
<div class="block">是否启用震动</br>
 whether vibrating when scanning success</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#close-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">close</a></span>(android.content.Context&nbsp;context,
     <a href="../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</code>
<div class="block">关闭指定功能</br>
 Switch off designated function</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#closeKeyboardHelper-android.content.Context-">closeKeyboardHelper</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">关闭键盘助手功能总开关</br>
 Switch off main function switch of keyboardemulator</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#enableContinuousScan-android.content.Context-boolean-">enableContinuousScan</a></span>(android.content.Context&nbsp;context,
                    boolean&nbsp;isContinuous)</code>
<div class="block">启用连续扫描</br>
 Enable continuous scan</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#enableEnter-android.content.Context-boolean-">enableEnter</a></span>(android.content.Context&nbsp;context,
           boolean&nbsp;isEnter)</code>
<div class="block">是否启用回车</br>
 Whether enable Enter function</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#enablePlayFailureSound-android.content.Context-boolean-">enablePlayFailureSound</a></span>(android.content.Context&nbsp;context,
                      boolean&nbsp;isFailureSound)</code>
<div class="block">扫描失败是否播放提示音</br>
 whether playing notification when scanning failure.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#enablePlaySuccessSound-android.content.Context-boolean-">enablePlaySuccessSound</a></span>(android.content.Context&nbsp;context,
                      boolean&nbsp;isSuccessSound)</code>
<div class="block">是否播放成功的提示音</br>
 whether playing notification when scanning success.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#enableTAB-android.content.Context-boolean-">enableTAB</a></span>(android.content.Context&nbsp;context,
         boolean&nbsp;isTab)</code>
<div class="block">是否启用TAB</br>
 Whether enable TAB function</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#enableVibrate-android.content.Context-boolean-">enableVibrate</a></span>(android.content.Context&nbsp;context,
             boolean&nbsp;isVibrate)</code>
<div class="block">扫描成功是否震动提示</br>
 whether vibrating when scanning success</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#filterCharacter-android.content.Context-java.lang.String-">filterCharacter</a></span>(android.content.Context&nbsp;context,
               java.lang.String&nbsp;chars)</code>
<div class="block">过滤字符串</br>
 Filter charactor string</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#getInstance--">getInstance</a></span>()</code>
<div class="block">获取条码操作实例</br>
 Acquire barcode operation Instance.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#interceptTrimLeft-android.content.Context-int-">interceptTrimLeft</a></span>(android.content.Context&nbsp;context,
                 int&nbsp;num)</code>
<div class="block">截取左边字符串数量</br>
 Capture left side charactor string amount</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#interceptTrimRight-android.content.Context-int-">interceptTrimRight</a></span>(android.content.Context&nbsp;context,
                  int&nbsp;num)</code>
<div class="block">截取右边字符串数量</br>
 Capture right side charactor string amount</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#open-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">open</a></span>(android.content.Context&nbsp;context,
    <a href="../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</code>
<div class="block">打开指定功能</br>
 Switch on designated function</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#openKeyboardHelper-android.content.Context-">openKeyboardHelper</a></span>(android.content.Context&nbsp;context)</code>
<div class="block">打开键盘助手功能总开关</br>
 Switch on main function switch of keyboardemulator</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#setBarcodeEncodingFormat-android.content.Context-int-">setBarcodeEncodingFormat</a></span>(android.content.Context&nbsp;context,
                        int&nbsp;format)</code>
<div class="block">设置条码编码格式</br>
 Setup barcode coding format</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#setContinuousScanIntervalTime-android.content.Context-int-">setContinuousScanIntervalTime</a></span>(android.content.Context&nbsp;context,
                             int&nbsp;intervalTime)</code>
<div class="block">设置连续扫描间隔时间</br>
 Setup continuous scanning duration</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#setContinuousScanTimeOut-android.content.Context-int-">setContinuousScanTimeOut</a></span>(android.content.Context&nbsp;context,
                        int&nbsp;timeOut)</code>
<div class="block">设置连续扫描超时时间</br>
 Setup continuous scanning overtime duration</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#setOutputMode-android.content.Context-int-">setOutputMode</a></span>(android.content.Context&nbsp;context,
             int&nbsp;mode)</code>
<div class="block">设置输出模式</br>
 Setup ouput mode</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#setParam_zebra-android.content.Context-int-int-">setParam_zebra</a></span>(android.content.Context&nbsp;context,
              int&nbsp;paramId,
              int&nbsp;paramValue)</code>
<div class="block">设置斑马扫描头参数,扫描头上电之后设置一次即可,扫描头断电之后失效。(备注：键盘助手v2.2.0.3 之后的版本才支持)</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#setPrefix-android.content.Context-java.lang.String-">setPrefix</a></span>(android.content.Context&nbsp;context,
         java.lang.String&nbsp;prefix)</code>
<div class="block">设置前缀</br>
 Setup prefix</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#setReleaseScan-android.content.Context-boolean-">setReleaseScan</a></span>(android.content.Context&nbsp;context,
              boolean&nbsp;enable)</code>
<div class="block">松开扫描按键是否停止扫描</br>
 loose scanning button to stop scanning or not</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#setScanFailureBroadcast-android.content.Context-boolean-">setScanFailureBroadcast</a></span>(android.content.Context&nbsp;context,
                       boolean&nbsp;enable)</code>
<div class="block">扫描失败是否发送广播</br>
 Send broadcast when scanning failure.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#setScanOutTime-android.content.Context-int-">setScanOutTime</a></span>(android.content.Context&nbsp;context,
              int&nbsp;time)</code>
<div class="block">设置超时时间</br>
 Setup overtime duration</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#setScanResultBroadcast-android.content.Context-java.lang.String-java.lang.String-">setScanResultBroadcast</a></span>(android.content.Context&nbsp;context,
                      java.lang.String&nbsp;broadcastAction,
                      java.lang.String&nbsp;extraName)</code>
<div class="block">设置接收扫描数据的广播</br>
 Setup broad cast of received scanning data</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#setSuffix-android.content.Context-java.lang.String-">setSuffix</a></span>(android.content.Context&nbsp;context,
         java.lang.String&nbsp;suffix)</code>
<div class="block">设置后缀</br>
 Setup suffix</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#startScan-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">startScan</a></span>(android.content.Context&nbsp;context,
         <a href="../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</code>
<div class="block">开始扫描 </br>
 Start scanning</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/BarcodeUtility.html#stopScan-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">stopScan</a></span>(android.content.Context&nbsp;context,
        <a href="../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</code>
<div class="block">停止扫描</br>
 Stop scanning</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ACTION_SCAN_KEYBOARD_HELPER_PARAM_RESPONSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTION_SCAN_KEYBOARD_HELPER_PARAM_RESPONSE</h4>
<pre>public static final&nbsp;java.lang.String ACTION_SCAN_KEYBOARD_HELPER_PARAM_RESPONSE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.ACTION_SCAN_KEYBOARD_HELPER_PARAM_RESPONSE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_RELEASESCAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_RELEASESCAN</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_RELEASESCAN</pre>
<div class="block">松开扫描按键是否停止扫描</br>
 loose scanning button to stop scanning or not</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_RELEASESCAN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_BROADCAST_ACTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_BROADCAST_ACTION</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_BROADCAST_ACTION</pre>
<div class="block">接收扫描数据的广播ACTION</br></div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_BROADCAST_ACTION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_CONTINUOUS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_CONTINUOUS</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_CONTINUOUS</pre>
<div class="block">是否是连续扫描</br>
 Whether enable continuous scan</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_CONTINUOUS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_CONTINUOUS_INTREVALTIME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_CONTINUOUS_INTREVALTIME</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_CONTINUOUS_INTREVALTIME</pre>
<div class="block">连续扫描时间间隔</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_CONTINUOUS_INTREVALTIME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_FORMAT_BARCODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_FORMAT_BARCODE</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_FORMAT_BARCODE</pre>
<div class="block">条码格式    0: Default   1: ASCII    2:GB2312   3:UTF8   4:Unicode   10:GBK,    11:GB18030</br>
 Barcode format 0: Default   1: ASCII    2:GB2312   3:UTF8   4:Unicode   10:GBK,    11:GB18030</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_FORMAT_BARCODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_BARCODENOTREPEAT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_BARCODENOTREPEAT</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_BARCODENOTREPEAT</pre>
<div class="block">条码是否重复显示</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_BARCODENOTREPEAT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_BROADCAST_EXTRA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_BROADCAST_EXTRA</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_BROADCAST_EXTRA</pre>
<div class="block">接收扫描数据的广播的extra数据</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_BROADCAST_EXTRA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_SCANKEYCODE_3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_SCANKEYCODE_3</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_SCANKEYCODE_3</pre>
<div class="block">扫描按键值</br>
 scan key code.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_SCANKEYCODE_3">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_SCANKEYCODE_1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_SCANKEYCODE_1</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_SCANKEYCODE_1</pre>
<div class="block">扫描按键值
 scan key code.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_SCANKEYCODE_1">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_FILTERCHARS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_FILTERCHARS</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_FILTERCHARS</pre>
<div class="block">条码数据需要过滤的字符串</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_FILTERCHARS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_FAILUREBROADCAST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_FAILUREBROADCAST</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_FAILUREBROADCAST</pre>
<div class="block">扫描失败是否发送广播</br>
 Send broadcast when scanning failure.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_FAILUREBROADCAST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_BARCODE_1D">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_BARCODE_1D</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_BARCODE_1D</pre>
<div class="block">1D是否已经开启</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_BARCODE_1D">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_VIBRATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_VIBRATE</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_VIBRATE</pre>
<div class="block">是否启用震动</br>
 whether vibrating when scanning success</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_VIBRATE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_SUFFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_SUFFIX</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_SUFFIX</pre>
<div class="block">条码后缀</br>
 barcode suffix.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_SUFFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_TAB">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_TAB</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_TAB</pre>
<div class="block">是否启用tab</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_TAB">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_ENTER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_ENTER</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_ENTER</pre>
<div class="block">是否启用回车</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_ENTER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_ENDINDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_ENDINDEX</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_ENDINDEX</pre>
<div class="block">后面去掉多少个字符</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_ENDINDEX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_FAILURESOUND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_FAILURESOUND</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_FAILURESOUND</pre>
<div class="block">扫描失败是否播放声音</br>
 whether playing notification when scanning failure.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_FAILURESOUND">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_OUTPUTMODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_OUTPUTMODE</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_OUTPUTMODE</pre>
<div class="block">数据接收方式    0:键盘模拟    1:剪切板   2:系统广播</br>
 ouput mode 0: virtual key   1: clipboard   2: broadcast</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_OUTPUTMODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_SOUND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_SOUND</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_SOUND</pre>
<div class="block">扫描成功是否播放声音</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_SOUND">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_TIMEOUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_TIMEOUT</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_TIMEOUT</pre>
<div class="block">扫描超时</br>
 scanning time out.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_TIMEOUT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_PREFIX</pre>
<div class="block">条码前缀</br>
 barcode prefix.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_CONTINUOUSTIMEOUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_CONTINUOUSTIMEOUT</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_CONTINUOUSTIMEOUT</pre>
<div class="block">连续扫描超时时间</br>
 continuous scanning overtime duration</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_CONTINUOUSTIMEOUT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_KEYBORADHELPER_OPEN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_KEYBORADHELPER_OPEN</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_KEYBORADHELPER_OPEN</pre>
<div class="block">键盘助手总开关是否已经打开</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_KEYBORADHELPER_OPEN">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_CONTINUOUSMODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_CONTINUOUSMODE</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_CONTINUOUSMODE</pre>
<div class="block">是否启用了连续扫描模式</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_CONTINUOUSMODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_ILLUMINATIONPOWERLEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_ILLUMINATIONPOWERLEVEL</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_ILLUMINATIONPOWERLEVEL</pre>
<div class="block">4710 扫描头亮度等级</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_ILLUMINATIONPOWERLEVEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_STARTINDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCANNER_STARTINDEX</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_STARTINDEX</pre>
<div class="block">前面去掉多少个字符</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_STARTINDEX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SCANNER_BARCODE_2D">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SCANNER_BARCODE_2D</h4>
<pre>public static final&nbsp;java.lang.String SCANNER_BARCODE_2D</pre>
<div class="block">2D是否已经开启</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.BarcodeUtility.SCANNER_BARCODE_2D">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode">BarcodeUtility</a>&nbsp;getInstance()</pre>
<div class="block">获取条码操作实例</br>
 Acquire barcode operation Instance.</div>
</li>
</ul>
<a name="open-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;void&nbsp;open(android.content.Context&nbsp;context,
                 <a href="../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</pre>
<div class="block">打开指定功能</br>
 Switch on designated function</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#open-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">open</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>modul</code> - 功能模块</dd>
</dl>
</li>
</ul>
<a name="close-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;void&nbsp;close(android.content.Context&nbsp;context,
                  <a href="../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</pre>
<div class="block">关闭指定功能</br>
 Switch off designated function</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#close-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">close</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>modul</code> - 功能模块</dd>
</dl>
</li>
</ul>
<a name="startScan-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startScan</h4>
<pre>public&nbsp;void&nbsp;startScan(android.content.Context&nbsp;context,
                      <a href="../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</pre>
<div class="block">开始扫描 </br>
 Start scanning</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#startScan-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">startScan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>modul</code> - 功能模块</dd>
</dl>
</li>
</ul>
<a name="stopScan-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopScan</h4>
<pre>public&nbsp;void&nbsp;stopScan(android.content.Context&nbsp;context,
                     <a href="../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode">BarcodeUtility.ModuleType</a>&nbsp;modul)</pre>
<div class="block">停止扫描</br>
 Stop scanning</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#stopScan-android.content.Context-com.rscja.barcode.BarcodeUtility.ModuleType-">stopScan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>modul</code> - 功能模块</dd>
</dl>
</li>
</ul>
<a name="setOutputMode-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutputMode</h4>
<pre>public&nbsp;void&nbsp;setOutputMode(android.content.Context&nbsp;context,
                          int&nbsp;mode)</pre>
<div class="block">设置输出模式</br>
 Setup ouput mode</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setOutputMode-android.content.Context-int-">setOutputMode</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>mode</code> - 0:扫描到光标位置(scan content to cursor)    1:剪切板(clipboard)   2:广播(broadcast)    3:模拟键盘(analog keyboard)</dd>
</dl>
</li>
</ul>
<a name="setScanOutTime-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScanOutTime</h4>
<pre>public&nbsp;void&nbsp;setScanOutTime(android.content.Context&nbsp;context,
                           int&nbsp;time)</pre>
<div class="block">设置超时时间</br>
 Setup overtime duration</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setScanOutTime-android.content.Context-int-">setScanOutTime</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>time</code> - 超时时间，单位秒</br>
                time overtime time, unit is second</dd>
</dl>
</li>
</ul>
<a name="setScanResultBroadcast-android.content.Context-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScanResultBroadcast</h4>
<pre>public&nbsp;void&nbsp;setScanResultBroadcast(android.content.Context&nbsp;context,
                                   java.lang.String&nbsp;broadcastAction,
                                   java.lang.String&nbsp;extraName)</pre>
<div class="block">设置接收扫描数据的广播</br>
 Setup broad cast of received scanning data</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setScanResultBroadcast-android.content.Context-java.lang.String-java.lang.String-">setScanResultBroadcast</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>broadcastAction</code> - 接收扫描数据的action</dd>
<dd><code>extraName</code> - Intent返回的扩展数据项目名称</dd>
</dl>
</li>
</ul>
<a name="openKeyboardHelper-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>openKeyboardHelper</h4>
<pre>public&nbsp;void&nbsp;openKeyboardHelper(android.content.Context&nbsp;context)</pre>
<div class="block">打开键盘助手功能总开关</br>
 Switch on main function switch of keyboardemulator</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#openKeyboardHelper-android.content.Context-">openKeyboardHelper</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
</dl>
</li>
</ul>
<a name="closeKeyboardHelper-android.content.Context-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>closeKeyboardHelper</h4>
<pre>public&nbsp;void&nbsp;closeKeyboardHelper(android.content.Context&nbsp;context)</pre>
<div class="block">关闭键盘助手功能总开关</br>
 Switch off main function switch of keyboardemulator</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#closeKeyboardHelper-android.content.Context-">closeKeyboardHelper</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
</dl>
</li>
</ul>
<a name="enablePlaySuccessSound-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enablePlaySuccessSound</h4>
<pre>public&nbsp;void&nbsp;enablePlaySuccessSound(android.content.Context&nbsp;context,
                                   boolean&nbsp;isSuccessSound)</pre>
<div class="block">是否播放成功的提示音</br>
 whether playing notification when scanning success.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enablePlaySuccessSound-android.content.Context-boolean-">enablePlaySuccessSound</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isSuccessSound</code> - true:播放成功提示音,false:不播放成功提示音</br>
                       true: enable false:disable</dd>
</dl>
</li>
</ul>
<a name="enablePlayFailureSound-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enablePlayFailureSound</h4>
<pre>public&nbsp;void&nbsp;enablePlayFailureSound(android.content.Context&nbsp;context,
                                   boolean&nbsp;isFailureSound)</pre>
<div class="block">扫描失败是否播放提示音</br>
 whether playing notification when scanning failure.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enablePlayFailureSound-android.content.Context-boolean-">enablePlayFailureSound</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isFailureSound</code> - true:播放失败提示音,false:不播放失败提示音</br>
                       true: enable false:disable</dd>
</dl>
</li>
</ul>
<a name="enableVibrate-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableVibrate</h4>
<pre>public&nbsp;void&nbsp;enableVibrate(android.content.Context&nbsp;context,
                          boolean&nbsp;isVibrate)</pre>
<div class="block">扫描成功是否震动提示</br>
 whether vibrating when scanning success</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enableVibrate-android.content.Context-boolean-">enableVibrate</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isVibrate</code> - true:震动,false:不震动 </br>
                  true: enable false:disable</dd>
</dl>
</li>
</ul>
<a name="enableEnter-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableEnter</h4>
<pre>public&nbsp;void&nbsp;enableEnter(android.content.Context&nbsp;context,
                        boolean&nbsp;isEnter)</pre>
<div class="block">是否启用回车</br>
 Whether enable Enter function</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enableEnter-android.content.Context-boolean-">enableEnter</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isEnter</code> - true: 启用回车   false:不启用回车</br>
                true: enable false:disable</dd>
</dl>
</li>
</ul>
<a name="enableTAB-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableTAB</h4>
<pre>public&nbsp;void&nbsp;enableTAB(android.content.Context&nbsp;context,
                      boolean&nbsp;isTab)</pre>
<div class="block">是否启用TAB</br>
 Whether enable TAB function</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enableTAB-android.content.Context-boolean-">enableTAB</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isTab</code> - true: 启用回车   false:不启用回车</br>
                true: enable false:disable</dd>
</dl>
</li>
</ul>
<a name="setSuffix-android.content.Context-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSuffix</h4>
<pre>public&nbsp;void&nbsp;setSuffix(android.content.Context&nbsp;context,
                      java.lang.String&nbsp;suffix)</pre>
<div class="block">设置后缀</br>
 Setup suffix</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setSuffix-android.content.Context-java.lang.String-">setSuffix</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>suffix</code> - 后缀字符</dd>
</dl>
</li>
</ul>
<a name="setPrefix-android.content.Context-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrefix</h4>
<pre>public&nbsp;void&nbsp;setPrefix(android.content.Context&nbsp;context,
                      java.lang.String&nbsp;prefix)</pre>
<div class="block">设置前缀</br>
 Setup prefix</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setPrefix-android.content.Context-java.lang.String-">setPrefix</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>prefix</code> - 前缀字符</dd>
</dl>
</li>
</ul>
<a name="interceptTrimLeft-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>interceptTrimLeft</h4>
<pre>public&nbsp;void&nbsp;interceptTrimLeft(android.content.Context&nbsp;context,
                              int&nbsp;num)</pre>
<div class="block">截取左边字符串数量</br>
 Capture left side charactor string amount</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#interceptTrimLeft-android.content.Context-int-">interceptTrimLeft</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>num</code> - 左边截取的字符数量</dd>
</dl>
</li>
</ul>
<a name="interceptTrimRight-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>interceptTrimRight</h4>
<pre>public&nbsp;void&nbsp;interceptTrimRight(android.content.Context&nbsp;context,
                               int&nbsp;num)</pre>
<div class="block">截取右边字符串数量</br>
 Capture right side charactor string amount</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#interceptTrimRight-android.content.Context-int-">interceptTrimRight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>num</code> - 右边截取的字符数量</dd>
</dl>
</li>
</ul>
<a name="filterCharacter-android.content.Context-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filterCharacter</h4>
<pre>public&nbsp;void&nbsp;filterCharacter(android.content.Context&nbsp;context,
                            java.lang.String&nbsp;chars)</pre>
<div class="block">过滤字符串</br>
 Filter charactor string</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#filterCharacter-android.content.Context-java.lang.String-">filterCharacter</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>chars</code> - 过滤的字符</dd>
</dl>
</li>
</ul>
<a name="setBarcodeEncodingFormat-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarcodeEncodingFormat</h4>
<pre>public&nbsp;void&nbsp;setBarcodeEncodingFormat(android.content.Context&nbsp;context,
                                     int&nbsp;format)</pre>
<div class="block">设置条码编码格式</br>
 Setup barcode coding format</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setBarcodeEncodingFormat-android.content.Context-int-">setBarcodeEncodingFormat</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>format</code> - 0: Default   1: ASCII    2:GB2312   3:UTF8   4:Unicode    10:GBK,    11:GB18030</dd>
</dl>
</li>
</ul>
<a name="enableContinuousScan-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableContinuousScan</h4>
<pre>public&nbsp;void&nbsp;enableContinuousScan(android.content.Context&nbsp;context,
                                 boolean&nbsp;isContinuous)</pre>
<div class="block">启用连续扫描</br>
 Enable continuous scan</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#enableContinuousScan-android.content.Context-boolean-">enableContinuousScan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>isContinuous</code> - true: 连续扫描    false：单次扫描</br>
                     true: enable  false：disable</dd>
</dl>
</li>
</ul>
<a name="setContinuousScanIntervalTime-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContinuousScanIntervalTime</h4>
<pre>public&nbsp;void&nbsp;setContinuousScanIntervalTime(android.content.Context&nbsp;context,
                                          int&nbsp;intervalTime)</pre>
<div class="block">设置连续扫描间隔时间</br>
 Setup continuous scanning duration</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setContinuousScanIntervalTime-android.content.Context-int-">setContinuousScanIntervalTime</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>intervalTime</code> - 间隔时间，单位毫秒</br>
                     interval Time, (unit: millisecond)</dd>
</dl>
</li>
</ul>
<a name="setContinuousScanTimeOut-android.content.Context-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContinuousScanTimeOut</h4>
<pre>public&nbsp;void&nbsp;setContinuousScanTimeOut(android.content.Context&nbsp;context,
                                     int&nbsp;timeOut)</pre>
<div class="block">设置连续扫描超时时间</br>
 Setup continuous scanning overtime duration</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setContinuousScanTimeOut-android.content.Context-int-">setContinuousScanTimeOut</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>timeOut</code> - 超时时间，单位秒</br>
                time Out(unit:second)</dd>
</dl>
</li>
</ul>
<a name="setScanFailureBroadcast-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScanFailureBroadcast</h4>
<pre>public&nbsp;void&nbsp;setScanFailureBroadcast(android.content.Context&nbsp;context,
                                    boolean&nbsp;enable)</pre>
<div class="block">扫描失败是否发送广播</br>
 Send broadcast when scanning failure.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setScanFailureBroadcast-android.content.Context-boolean-">setScanFailureBroadcast</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>enable</code> - true:发送    false：不发送</dd>
</dl>
</li>
</ul>
<a name="setReleaseScan-android.content.Context-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReleaseScan</h4>
<pre>public&nbsp;void&nbsp;setReleaseScan(android.content.Context&nbsp;context,
                           boolean&nbsp;enable)</pre>
<div class="block">松开扫描按键是否停止扫描</br>
 loose scanning button to stop scanning or not</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setReleaseScan-android.content.Context-boolean-">setReleaseScan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>enable</code> - true:停止扫描    false：不停止扫描</dd>
</dl>
</li>
</ul>
<a name="setParam_zebra-android.content.Context-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setParam_zebra</h4>
<pre>public&nbsp;void&nbsp;setParam_zebra(android.content.Context&nbsp;context,
                           int&nbsp;paramId,
                           int&nbsp;paramValue)</pre>
<div class="block">设置斑马扫描头参数,扫描头上电之后设置一次即可,扫描头断电之后失效。(备注：键盘助手v2.2.0.3 之后的版本才支持)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html#setParam_zebra-android.content.Context-int-int-">setParam_zebra</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces">IBarcodeUtility</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>context</code> - </dd>
<dd><code>paramId</code> - id</dd>
<dd><code>paramValue</code> - value</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BarcodeUtility.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/barcode/BarcodeSymbolUtility.html" title="class in com.rscja.barcode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/barcode/BarcodeUtility.html" target="_top">Frames</a></li>
<li><a href="BarcodeUtility.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
