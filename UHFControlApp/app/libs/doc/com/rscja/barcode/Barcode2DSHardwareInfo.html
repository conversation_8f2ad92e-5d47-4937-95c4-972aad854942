<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>Barcode2DSHardwareInfo</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Barcode2DSHardwareInfo";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":9,"i3":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Barcode2DSHardwareInfo.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/barcode/Barcode2DSHardwareInfo.html" target="_top">Frames</a></li>
<li><a href="Barcode2DSHardwareInfo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.barcode</div>
<h2 title="Class Barcode2DSHardwareInfo" class="title">Class Barcode2DSHardwareInfo</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.barcode.Barcode2DSHardwareInfo</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../com/rscja/barcode/IBarcode2DSHardwareInfo.html" title="interface in com.rscja.barcode">IBarcode2DSHardwareInfo</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Barcode2DSHardwareInfo</span>
extends java.lang.Object
implements <a href="../../../com/rscja/barcode/IBarcode2DSHardwareInfo.html" title="interface in com.rscja.barcode">IBarcode2DSHardwareInfo</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_CW">MANUFACTOR_CW</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_HONYWELL">MANUFACTOR_HONYWELL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_IA">MANUFACTOR_IA</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_IDATA">MANUFACTOR_IDATA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_MOBYDATA">MANUFACTOR_MOBYDATA</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_NEWLAND">MANUFACTOR_NEWLAND</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#MANUFACTOR_ZEBRA">MANUFACTOR_ZEBRA</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#MODEL_CW_CW9281">MODEL_CW_CW9281</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_3601">Model_HONYWELL_3601</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_3603">Model_HONYWELL_3603</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_6603">Model_HONYWELL_6603</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_EX30">Model_HONYWELL_EX30</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_N5703">Model_HONYWELL_N5703</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_HONYWELL_N6703">Model_HONYWELL_N6703</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_100S">Model_IA_100S</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_101S">Model_IA_101S</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_166S">Model_IA_166S</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_171S">Model_IA_171S</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_181S">Model_IA_181S</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_400S">Model_IA_400S</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IA_417S">Model_IA_417S</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_IDATA_DS7000">Model_IDATA_DS7000</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_MOBYDATA_E3200">Model_MOBYDATA_E3200</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_MOTO_5500">Model_MOTO_5500</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_NEWLAND_CM30">Model_NEWLAND_CM30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_NEWLAND_CM47">Model_NEWLAND_CM47</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_NEWLAND_CM60">Model_NEWLAND_CM60</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_2100">Model_ZEBRA_2100</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4100">Model_ZEBRA_4100</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4710">Model_ZEBRA_4710</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4720">Model_ZEBRA_4720</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4750">Model_ZEBRA_4750</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4770">Model_ZEBRA_4770</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Model_ZEBRA_4850">Model_ZEBRA_4850</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#UNKNOWN">UNKNOWN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#ZEBRA_ENGINE_SUFFIX_DP">ZEBRA_ENGINE_SUFFIX_DP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#ZEBRA_ENGINE_SUFFIX_MR">ZEBRA_ENGINE_SUFFIX_MR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#ZEBRA_ENGINE_SUFFIX_SR">ZEBRA_ENGINE_SUFFIX_SR</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#Barcode2DSHardwareInfo--">Barcode2DSHardwareInfo</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#getEngineExtrasInfo--">getEngineExtrasInfo</a></span>()</code>
<div class="block">扫描头额外的扩展信息</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#getEngineName--">getEngineName</a></span>()</code>
<div class="block">扫描头型号</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#getInstance--">getInstance</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html#getManufactor--">getManufactor</a></span>()</code>
<div class="block">扫描头厂家</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="UNKNOWN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNKNOWN</h4>
<pre>public static&nbsp;java.lang.String UNKNOWN</pre>
</li>
</ul>
<a name="MANUFACTOR_ZEBRA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANUFACTOR_ZEBRA</h4>
<pre>public static final&nbsp;java.lang.String MANUFACTOR_ZEBRA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_ZEBRA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANUFACTOR_HONYWELL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANUFACTOR_HONYWELL</h4>
<pre>public static final&nbsp;java.lang.String MANUFACTOR_HONYWELL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_HONYWELL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANUFACTOR_IA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANUFACTOR_IA</h4>
<pre>public static final&nbsp;java.lang.String MANUFACTOR_IA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_IA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANUFACTOR_IDATA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANUFACTOR_IDATA</h4>
<pre>public static final&nbsp;java.lang.String MANUFACTOR_IDATA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_IDATA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANUFACTOR_MOBYDATA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANUFACTOR_MOBYDATA</h4>
<pre>public static final&nbsp;java.lang.String MANUFACTOR_MOBYDATA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_MOBYDATA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANUFACTOR_NEWLAND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANUFACTOR_NEWLAND</h4>
<pre>public static final&nbsp;java.lang.String MANUFACTOR_NEWLAND</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_NEWLAND">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANUFACTOR_CW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANUFACTOR_CW</h4>
<pre>public static final&nbsp;java.lang.String MANUFACTOR_CW</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.MANUFACTOR_CW">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_ZEBRA_4100">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_ZEBRA_4100</h4>
<pre>public static final&nbsp;java.lang.String Model_ZEBRA_4100</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_4100">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_ZEBRA_4710">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_ZEBRA_4710</h4>
<pre>public static final&nbsp;java.lang.String Model_ZEBRA_4710</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_4710">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_ZEBRA_4750">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_ZEBRA_4750</h4>
<pre>public static final&nbsp;java.lang.String Model_ZEBRA_4750</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_4750">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_ZEBRA_2100">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_ZEBRA_2100</h4>
<pre>public static final&nbsp;java.lang.String Model_ZEBRA_2100</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_2100">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_ZEBRA_4850">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_ZEBRA_4850</h4>
<pre>public static final&nbsp;java.lang.String Model_ZEBRA_4850</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_4850">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_ZEBRA_4720">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_ZEBRA_4720</h4>
<pre>public static final&nbsp;java.lang.String Model_ZEBRA_4720</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_4720">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_ZEBRA_4770">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_ZEBRA_4770</h4>
<pre>public static final&nbsp;java.lang.String Model_ZEBRA_4770</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_ZEBRA_4770">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_MOTO_5500">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_MOTO_5500</h4>
<pre>public static final&nbsp;java.lang.String Model_MOTO_5500</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_MOTO_5500">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ZEBRA_ENGINE_SUFFIX_DP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZEBRA_ENGINE_SUFFIX_DP</h4>
<pre>public static final&nbsp;java.lang.String ZEBRA_ENGINE_SUFFIX_DP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.ZEBRA_ENGINE_SUFFIX_DP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ZEBRA_ENGINE_SUFFIX_SR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZEBRA_ENGINE_SUFFIX_SR</h4>
<pre>public static final&nbsp;java.lang.String ZEBRA_ENGINE_SUFFIX_SR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.ZEBRA_ENGINE_SUFFIX_SR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ZEBRA_ENGINE_SUFFIX_MR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZEBRA_ENGINE_SUFFIX_MR</h4>
<pre>public static final&nbsp;java.lang.String ZEBRA_ENGINE_SUFFIX_MR</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.ZEBRA_ENGINE_SUFFIX_MR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_HONYWELL_6603">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_HONYWELL_6603</h4>
<pre>public static final&nbsp;java.lang.String Model_HONYWELL_6603</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_HONYWELL_6603">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_HONYWELL_3601">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_HONYWELL_3601</h4>
<pre>public static final&nbsp;java.lang.String Model_HONYWELL_3601</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_HONYWELL_3601">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_HONYWELL_3603">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_HONYWELL_3603</h4>
<pre>public static final&nbsp;java.lang.String Model_HONYWELL_3603</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_HONYWELL_3603">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_HONYWELL_N6703">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_HONYWELL_N6703</h4>
<pre>public static final&nbsp;java.lang.String Model_HONYWELL_N6703</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_HONYWELL_N6703">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_HONYWELL_N5703">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_HONYWELL_N5703</h4>
<pre>public static final&nbsp;java.lang.String Model_HONYWELL_N5703</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_HONYWELL_N5703">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_HONYWELL_EX30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_HONYWELL_EX30</h4>
<pre>public static final&nbsp;java.lang.String Model_HONYWELL_EX30</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_HONYWELL_EX30">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_IA_400S">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_IA_400S</h4>
<pre>public static final&nbsp;java.lang.String Model_IA_400S</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_400S">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_IA_100S">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_IA_100S</h4>
<pre>public static final&nbsp;java.lang.String Model_IA_100S</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_100S">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_IA_417S">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_IA_417S</h4>
<pre>public static final&nbsp;java.lang.String Model_IA_417S</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_417S">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_IA_101S">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_IA_101S</h4>
<pre>public static final&nbsp;java.lang.String Model_IA_101S</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_101S">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_IA_166S">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_IA_166S</h4>
<pre>public static final&nbsp;java.lang.String Model_IA_166S</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_166S">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_IA_171S">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_IA_171S</h4>
<pre>public static final&nbsp;java.lang.String Model_IA_171S</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_171S">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_IA_181S">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_IA_181S</h4>
<pre>public static final&nbsp;java.lang.String Model_IA_181S</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_IA_181S">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_IDATA_DS7000">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_IDATA_DS7000</h4>
<pre>public static final&nbsp;java.lang.String Model_IDATA_DS7000</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_IDATA_DS7000">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_MOBYDATA_E3200">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_MOBYDATA_E3200</h4>
<pre>public static final&nbsp;java.lang.String Model_MOBYDATA_E3200</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_MOBYDATA_E3200">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_NEWLAND_CM60">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_NEWLAND_CM60</h4>
<pre>public static final&nbsp;java.lang.String Model_NEWLAND_CM60</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_NEWLAND_CM60">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_NEWLAND_CM47">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_NEWLAND_CM47</h4>
<pre>public static final&nbsp;java.lang.String Model_NEWLAND_CM47</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_NEWLAND_CM47">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="Model_NEWLAND_CM30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Model_NEWLAND_CM30</h4>
<pre>public static final&nbsp;java.lang.String Model_NEWLAND_CM30</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.Model_NEWLAND_CM30">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MODEL_CW_CW9281">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MODEL_CW_CW9281</h4>
<pre>public static final&nbsp;java.lang.String MODEL_CW_CW9281</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#com.rscja.barcode.Barcode2DSHardwareInfo.MODEL_CW_CW9281">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Barcode2DSHardwareInfo--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Barcode2DSHardwareInfo</h4>
<pre>public&nbsp;Barcode2DSHardwareInfo()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode">Barcode2DSHardwareInfo</a>&nbsp;getInstance()</pre>
</li>
</ul>
<a name="getEngineName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEngineName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getEngineName()</pre>
<div class="block">扫描头型号</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/barcode/IBarcode2DSHardwareInfo.html#getEngineName--">getEngineName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/barcode/IBarcode2DSHardwareInfo.html" title="interface in com.rscja.barcode">IBarcode2DSHardwareInfo</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getManufactor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getManufactor</h4>
<pre>public&nbsp;java.lang.String&nbsp;getManufactor()</pre>
<div class="block">扫描头厂家</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/barcode/IBarcode2DSHardwareInfo.html#getManufactor--">getManufactor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/barcode/IBarcode2DSHardwareInfo.html" title="interface in com.rscja.barcode">IBarcode2DSHardwareInfo</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="getEngineExtrasInfo--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getEngineExtrasInfo</h4>
<pre>public&nbsp;java.lang.String&nbsp;getEngineExtrasInfo()</pre>
<div class="block">扫描头额外的扩展信息</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../com/rscja/barcode/IBarcode2DSHardwareInfo.html#getEngineExtrasInfo--">getEngineExtrasInfo</a></code>&nbsp;in interface&nbsp;<code><a href="../../../com/rscja/barcode/IBarcode2DSHardwareInfo.html" title="interface in com.rscja.barcode">IBarcode2DSHardwareInfo</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Barcode2DSHardwareInfo.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/barcode/Barcode2DSHardwareInfo.html" target="_top">Frames</a></li>
<li><a href="Barcode2DSHardwareInfo.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
