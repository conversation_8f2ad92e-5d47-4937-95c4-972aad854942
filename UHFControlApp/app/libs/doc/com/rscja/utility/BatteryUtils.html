<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>BatteryUtils</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BatteryUtils";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BatteryUtils.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../com/rscja/utility/FileUtility.html" title="class in com.rscja.utility"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/utility/BatteryUtils.html" target="_top">Frames</a></li>
<li><a href="BatteryUtils.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.utility</div>
<h2 title="Class BatteryUtils" class="title">Class BatteryUtils</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.utility.BatteryUtils</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">BatteryUtils</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/BatteryUtils.html#BatteryUtils--">BatteryUtils</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/BatteryUtils.html#disableCharge--">disableCharge</a></span>()</code>
<div class="block">关闭充电.<br/>
 Disable charging.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/BatteryUtils.html#enableCharge--">enableCharge</a></span>()</code>
<div class="block">开启充电.<br/>
 Enable charging.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/BatteryUtils.html#getBatteryHealth--">getBatteryHealth</a></span>()</code>
<div class="block">电池健康状况，单位 %.<br/>
 Battery health status, unit %.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/BatteryUtils.html#getBatteryUniqueId--">getBatteryUniqueId</a></span>()</code>
<div class="block">电池ID.<br/>
 Battery ID.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/BatteryUtils.html#getCurrentCapacity--">getCurrentCapacity</a></span>()</code>
<div class="block">实际容量，单位mAh.<br/>
 Battery Actual capacity, unit mAh.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/BatteryUtils.html#getCurrentCharge--">getCurrentCharge</a></span>()</code>
<div class="block">可用容量，单位mAh.<br/>
 Battery Available capacity, unit mAh.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/BatteryUtils.html#getCycleCount--">getCycleCount</a></span>()</code>
<div class="block">循环次数.<br/>
 Cycle count.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/BatteryUtils.html#getManufactureDate--">getManufactureDate</a></span>()</code>
<div class="block">生产日期.<br/>
 Battery production date.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/BatteryUtils.html#getPartNumber--">getPartNumber</a></span>()</code>
<div class="block">部件号.<br/>
 Part number.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/BatteryUtils.html#getRatedCapacity--">getRatedCapacity</a></span>()</code>
<div class="block">额定容量，单位mAh.<br/>
 Battery Rated capacity, unit mAh.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/BatteryUtils.html#getSerialNumber--">getSerialNumber</a></span>()</code>
<div class="block">序列号.<br/>
 Serial number.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/BatteryUtils.html#getSmartBattery--">getSmartBattery</a></span>()</code>
<div class="block">智能电池.<br/>
 Smart battery.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BatteryUtils--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BatteryUtils</h4>
<pre>public&nbsp;BatteryUtils()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getBatteryUniqueId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBatteryUniqueId</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getBatteryUniqueId()</pre>
<div class="block">电池ID.<br/>
 Battery ID.</div>
</li>
</ul>
<a name="getSerialNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSerialNumber</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getSerialNumber()</pre>
<div class="block">序列号.<br/>
 Serial number.</div>
</li>
</ul>
<a name="getPartNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPartNumber</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getPartNumber()</pre>
<div class="block">部件号.<br/>
 Part number.</div>
</li>
</ul>
<a name="getManufactureDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getManufactureDate</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getManufactureDate()</pre>
<div class="block">生产日期.<br/>
 Battery production date.</div>
</li>
</ul>
<a name="getRatedCapacity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRatedCapacity</h4>
<pre>public static&nbsp;int&nbsp;getRatedCapacity()</pre>
<div class="block">额定容量，单位mAh.<br/>
 Battery Rated capacity, unit mAh.</div>
</li>
</ul>
<a name="getCurrentCapacity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentCapacity</h4>
<pre>public static&nbsp;int&nbsp;getCurrentCapacity()</pre>
<div class="block">实际容量，单位mAh.<br/>
 Battery Actual capacity, unit mAh.</div>
</li>
</ul>
<a name="getCurrentCharge--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentCharge</h4>
<pre>public static&nbsp;int&nbsp;getCurrentCharge()</pre>
<div class="block">可用容量，单位mAh.<br/>
 Battery Available capacity, unit mAh.</div>
</li>
</ul>
<a name="getBatteryHealth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBatteryHealth</h4>
<pre>public static&nbsp;int&nbsp;getBatteryHealth()</pre>
<div class="block">电池健康状况，单位 %.<br/>
 Battery health status, unit %.</div>
</li>
</ul>
<a name="getCycleCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCycleCount</h4>
<pre>public static&nbsp;int&nbsp;getCycleCount()</pre>
<div class="block">循环次数.<br/>
 Cycle count.</div>
</li>
</ul>
<a name="getSmartBattery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSmartBattery</h4>
<pre>public static&nbsp;boolean&nbsp;getSmartBattery()</pre>
<div class="block">智能电池.<br/>
 Smart battery.</div>
</li>
</ul>
<a name="enableCharge--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableCharge</h4>
<pre>public static&nbsp;boolean&nbsp;enableCharge()</pre>
<div class="block">开启充电.<br/>
 Enable charging.</div>
</li>
</ul>
<a name="disableCharge--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>disableCharge</h4>
<pre>public static&nbsp;boolean&nbsp;disableCharge()</pre>
<div class="block">关闭充电.<br/>
 Disable charging.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BatteryUtils.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../com/rscja/utility/FileUtility.html" title="class in com.rscja.utility"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/utility/BatteryUtils.html" target="_top">Frames</a></li>
<li><a href="BatteryUtils.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
