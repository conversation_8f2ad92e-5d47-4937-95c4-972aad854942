<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:06 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>StringUtility</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="StringUtility";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9,"i31":9,"i32":9,"i33":41,"i34":9,"i35":9,"i36":9,"i37":9,"i38":9,"i39":9,"i40":9,"i41":9,"i42":9,"i43":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/StringUtility.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/utility/NumberTool.html" title="class in com.rscja.utility"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/utility/UhfUtils.html" title="class in com.rscja.utility"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/utility/StringUtility.html" target="_top">Frames</a></li>
<li><a href="StringUtility.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.rscja.utility</div>
<h2 title="Class StringUtility" class="title">Class StringUtility</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.rscja.utility.StringUtility</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">StringUtility</span>
extends java.lang.Object</pre>
<div class="block">字符工具类</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>liuruifeng</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#DEBUG">DEBUG</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#TAG">TAG</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#StringUtility--">StringUtility</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static byte</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#BitToByte-java.lang.String-">BitToByte</a></span>(java.lang.String&nbsp;byteStr)</code>
<div class="block">Bit转Byte</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#byte2Bit-byte-">byte2Bit</a></span>(byte&nbsp;b)</code>
<div class="block">Byte转Bit</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#byte2HexString-byte-">byte2HexString</a></span>(byte&nbsp;b)</code>
<div class="block">byte转十六进制字符</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#byteArrayTolong-byte:A-">byteArrayTolong</a></span>(byte[]&nbsp;byteArray)</code>
<div class="block">将8字节的byte数组转成一个long值</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#bytes2HexString-byte:A-">bytes2HexString</a></span>(byte[]&nbsp;b)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#bytes2HexString-byte:A-int-">bytes2HexString</a></span>(byte[]&nbsp;b,
               int&nbsp;size)</code>
<div class="block">byte类型数组转十六进制字符串</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#bytes2HexString-byte:A-int-int-">bytes2HexString</a></span>(byte[]&nbsp;b,
               int&nbsp;index,
               int&nbsp;len)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#bytes2HexString2-java.util.ArrayList-">bytes2HexString2</a></span>(java.util.ArrayList&lt;java.lang.Byte&gt;&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#bytes2HexString2-byte:A-int-">bytes2HexString2</a></span>(byte[]&nbsp;b,
                int&nbsp;size)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#bytesConvertHexString-byte:A-int-int-">bytesConvertHexString</a></span>(byte[]&nbsp;b,
                     int&nbsp;index,
                     int&nbsp;len)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#bytesHexString-java.util.ArrayList-">bytesHexString</a></span>(java.util.ArrayList&lt;java.lang.Byte&gt;&nbsp;data)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#bytesHexString-byte:A-">bytesHexString</a></span>(byte[]&nbsp;b)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#bytesHexString-byte:A-int-">bytesHexString</a></span>(byte[]&nbsp;b,
              int&nbsp;size)</code>
<div class="block">byte类型数组转十六进制字符串</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#bytesHexString-byte:A-int-int-boolean-">bytesHexString</a></span>(byte[]&nbsp;b,
              int&nbsp;index,
              int&nbsp;len,
              boolean&nbsp;isSpacing)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#bytesTochars-byte:A-int-">bytesTochars</a></span>(byte[]&nbsp;c,
            int&nbsp;size)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#bytesToHexString-byte:A-int-">bytesToHexString</a></span>(byte[]&nbsp;b,
                int&nbsp;size)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#bytesToInt-byte:A-">bytesToInt</a></span>(byte[]&nbsp;bytes)</code>
<div class="block">byte数组转成int值</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#char2HexString-char-">char2HexString</a></span>(char&nbsp;c)</code>
<div class="block">char转十六进制</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#charArrayTolong-char:A-">charArrayTolong</a></span>(char[]&nbsp;array)</code>
<div class="block">将8字节的Char数组转成一个long值</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#chars2HexString-char:A-int-">chars2HexString</a></span>(char[]&nbsp;c,
               int&nbsp;size)</code>
<div class="block">char类型数组转十六进制字符串</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#chars2Long-char:A-int-int-">chars2Long</a></span>(char[]&nbsp;c,
          int&nbsp;start,
          int&nbsp;len)</code>
<div class="block">char数组转long</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#charsTobytes-char:A-int-">charsTobytes</a></span>(char[]&nbsp;c,
            int&nbsp;size)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#getBytes-char:A-">getBytes</a></span>(char[]&nbsp;chars)</code>
<div class="block">char类型数组转byte类型数组</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#getChars-byte:A-">getChars</a></span>(byte[]&nbsp;bytes)</code>
<div class="block">byte类型数组转char类型数组</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#hexString2Bytes-java.lang.String-">hexString2Bytes</a></span>(java.lang.String&nbsp;s)</code>
<div class="block">十六进制字符串转byte数组</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static char[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#hexString2Chars-java.lang.String-">hexString2Chars</a></span>(java.lang.String&nbsp;s)</code>
<div class="block">十六进制字符串转换成char数组</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#hexStringToBytes-java.lang.String-">hexStringToBytes</a></span>(java.lang.String&nbsp;hexString)</code>
<div class="block">十六进制字符串转byte数组</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#int2Bytes-int-">int2Bytes</a></span>(int&nbsp;num)</code>
<div class="block">int转byte数组</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#Int2bytes-int-">Int2bytes</a></span>(int&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#int2HexString-int-">int2HexString</a></span>(int&nbsp;n)</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#ints2HexString-int:A-int-">ints2HexString</a></span>(int[]&nbsp;c,
              int&nbsp;size)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#isDecimal-java.lang.String-">isDecimal</a></span>(java.lang.String&nbsp;decimal)</code>
<div class="block">判断字符串是否是十进制数字</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#isEmpty-java.lang.CharSequence-">isEmpty</a></span>(java.lang.CharSequence&nbsp;cs)</code>
<div class="block">判断字符串是否为空</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#isHexNumber-java.lang.String-">isHexNumber</a></span>(java.lang.String&nbsp;str)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#isHexNumberRex-java.lang.String-">isHexNumberRex</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">判断字符串是否是十六进制数字，使用正则表达式方式</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#isIP-java.lang.String-">isIP</a></span>(java.lang.String&nbsp;text)</code>
<div class="block">验证ip是否合法</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#isNum-java.lang.String-">isNum</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">判断是否为数字</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#isOctNumber-java.lang.String-">isOctNumber</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">判断字符串是否是十进制数字</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#isOctNumberRex-java.lang.String-">isOctNumberRex</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">判断字符串是否是十进制数字，使用正则表达式方式</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#long2Bytes-long-">long2Bytes</a></span>(long&nbsp;num)</code>
<div class="block">long转byte数组</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>static java.math.BigInteger</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#readUnsignedInt64-byte:A-">readUnsignedInt64</a></span>(byte[]&nbsp;readBuffer)</code>
<div class="block">读取无符号位的长整数，64位</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#reverse-byte:A-">reverse</a></span>(byte[]&nbsp;b)</code>
<div class="block">逆转字节数组</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#setDebug-boolean-">setDebug</a></span>(boolean&nbsp;debug)</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../com/rscja/utility/StringUtility.html#string2Int-java.lang.String-int-">string2Int</a></span>(java.lang.String&nbsp;str,
          int&nbsp;defValue)</code>
<div class="block">字符串转整数</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DEBUG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEBUG</h4>
<pre>public static&nbsp;boolean DEBUG</pre>
</li>
</ul>
<a name="TAG">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TAG</h4>
<pre>public static&nbsp;java.lang.String TAG</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="StringUtility--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>StringUtility</h4>
<pre>public&nbsp;StringUtility()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="byte2Bit-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>byte2Bit</h4>
<pre>public static&nbsp;java.lang.String&nbsp;byte2Bit(byte&nbsp;b)</pre>
<div class="block">Byte转Bit</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>b</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="BitToByte-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BitToByte</h4>
<pre>public static&nbsp;byte&nbsp;BitToByte(java.lang.String&nbsp;byteStr)</pre>
<div class="block">Bit转Byte</div>
</li>
</ul>
<a name="bytes2HexString-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bytes2HexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;bytes2HexString(byte[]&nbsp;b,
                                               int&nbsp;size)</pre>
<div class="block">byte类型数组转十六进制字符串</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>b</code> - byte类型数组</dd>
<dd><code>size</code> - 数组长度</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>十六进制字符串</dd>
</dl>
</li>
</ul>
<a name="bytes2HexString2-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bytes2HexString2</h4>
<pre>public static&nbsp;java.lang.String&nbsp;bytes2HexString2(byte[]&nbsp;b,
                                                int&nbsp;size)</pre>
</li>
</ul>
<a name="bytes2HexString2-java.util.ArrayList-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bytes2HexString2</h4>
<pre>public static&nbsp;java.lang.String&nbsp;bytes2HexString2(java.util.ArrayList&lt;java.lang.Byte&gt;&nbsp;data)</pre>
</li>
</ul>
<a name="bytesHexString-java.util.ArrayList-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bytesHexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;bytesHexString(java.util.ArrayList&lt;java.lang.Byte&gt;&nbsp;data)</pre>
</li>
</ul>
<a name="bytes2HexString-byte:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bytes2HexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;bytes2HexString(byte[]&nbsp;b,
                                               int&nbsp;index,
                                               int&nbsp;len)</pre>
</li>
</ul>
<a name="bytes2HexString-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bytes2HexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;bytes2HexString(byte[]&nbsp;b)</pre>
</li>
</ul>
<a name="bytesToHexString-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bytesToHexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;bytesToHexString(byte[]&nbsp;b,
                                                int&nbsp;size)</pre>
</li>
</ul>
<a name="bytesHexString-byte:A-int-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bytesHexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;bytesHexString(byte[]&nbsp;b,
                                              int&nbsp;index,
                                              int&nbsp;len,
                                              boolean&nbsp;isSpacing)</pre>
</li>
</ul>
<a name="bytesHexString-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bytesHexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;bytesHexString(byte[]&nbsp;b)</pre>
</li>
</ul>
<a name="bytesHexString-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bytesHexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;bytesHexString(byte[]&nbsp;b,
                                              int&nbsp;size)</pre>
<div class="block">byte类型数组转十六进制字符串</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>b</code> - byte类型数组</dd>
<dd><code>size</code> - 数组长度</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>十六进制字符串</dd>
</dl>
</li>
</ul>
<a name="charsTobytes-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>charsTobytes</h4>
<pre>public static&nbsp;byte[]&nbsp;charsTobytes(char[]&nbsp;c,
                                  int&nbsp;size)</pre>
</li>
</ul>
<a name="bytesTochars-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bytesTochars</h4>
<pre>public static&nbsp;char[]&nbsp;bytesTochars(byte[]&nbsp;c,
                                  int&nbsp;size)</pre>
</li>
</ul>
<a name="byte2HexString-byte-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>byte2HexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;byte2HexString(byte&nbsp;b)</pre>
<div class="block">byte转十六进制字符</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>b</code> - byte值</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>十六进制字符</dd>
</dl>
</li>
</ul>
<a name="chars2HexString-char:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>chars2HexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;chars2HexString(char[]&nbsp;c,
                                               int&nbsp;size)</pre>
<div class="block">char类型数组转十六进制字符串</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>c</code> - char类型数组</dd>
<dd><code>size</code> - 数组大小</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>十六进制字符串</dd>
</dl>
</li>
</ul>
<a name="long2Bytes-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>long2Bytes</h4>
<pre>public static&nbsp;byte[]&nbsp;long2Bytes(long&nbsp;num)</pre>
<div class="block">long转byte数组</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>num</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="int2Bytes-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>int2Bytes</h4>
<pre>public static&nbsp;byte[]&nbsp;int2Bytes(int&nbsp;num)</pre>
<div class="block">int转byte数组</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>num</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="byteArrayTolong-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>byteArrayTolong</h4>
<pre>public static&nbsp;long&nbsp;byteArrayTolong(byte[]&nbsp;byteArray)</pre>
<div class="block">将8字节的byte数组转成一个long值</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>byteArray</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>转换后的long型数值</dd>
</dl>
</li>
</ul>
<a name="bytesToInt-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bytesToInt</h4>
<pre>public static&nbsp;int&nbsp;bytesToInt(byte[]&nbsp;bytes)</pre>
<div class="block">byte数组转成int值</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bytes</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="Int2bytes-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Int2bytes</h4>
<pre>public static&nbsp;byte[]&nbsp;Int2bytes(int&nbsp;value)</pre>
</li>
</ul>
<a name="charArrayTolong-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>charArrayTolong</h4>
<pre>public static&nbsp;long&nbsp;charArrayTolong(char[]&nbsp;array)</pre>
<div class="block">将8字节的Char数组转成一个long值</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>array</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>转换后的long型数值</dd>
</dl>
</li>
</ul>
<a name="reverse-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reverse</h4>
<pre>public static&nbsp;byte[]&nbsp;reverse(byte[]&nbsp;b)</pre>
<div class="block">逆转字节数组</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>b</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="readUnsignedInt64-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readUnsignedInt64</h4>
<pre>public static final&nbsp;java.math.BigInteger&nbsp;readUnsignedInt64(byte[]&nbsp;readBuffer)
                                                    throws java.io.IOException</pre>
<div class="block">读取无符号位的长整数，64位</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>readBuffer</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="chars2Long-char:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>chars2Long</h4>
<pre>public static&nbsp;long&nbsp;chars2Long(char[]&nbsp;c,
                              int&nbsp;start,
                              int&nbsp;len)</pre>
<div class="block">char数组转long</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>c</code> - </dd>
<dd><code>start</code> - </dd>
<dd><code>len</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="char2HexString-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>char2HexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;char2HexString(char&nbsp;c)</pre>
<div class="block">char转十六进制</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>c</code> - char值</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>十六进制字符</dd>
</dl>
</li>
</ul>
<a name="isOctNumber-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOctNumber</h4>
<pre>public static&nbsp;boolean&nbsp;isOctNumber(java.lang.String&nbsp;str)</pre>
<div class="block">判断字符串是否是十进制数字</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>str</code> - 要判断的字符串</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true是，false否</dd>
</dl>
</li>
</ul>
<a name="isHexNumber-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHexNumber</h4>
<pre>@Deprecated
public static&nbsp;boolean&nbsp;isHexNumber(java.lang.String&nbsp;str)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block">判断字符串是否是十六进制数字</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>str</code> - 要判断的字符串</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true是，false否</dd>
</dl>
</li>
</ul>
<a name="isOctNumberRex-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOctNumberRex</h4>
<pre>public static&nbsp;boolean&nbsp;isOctNumberRex(java.lang.String&nbsp;str)</pre>
<div class="block">判断字符串是否是十进制数字，使用正则表达式方式</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>str</code> - 要判断的字符串</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true是，false否</dd>
</dl>
</li>
</ul>
<a name="isHexNumberRex-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHexNumberRex</h4>
<pre>public static&nbsp;boolean&nbsp;isHexNumberRex(java.lang.String&nbsp;str)</pre>
<div class="block">判断字符串是否是十六进制数字，使用正则表达式方式</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>str</code> - 要判断的字符串</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true是，false否</dd>
</dl>
</li>
</ul>
<a name="hexString2Chars-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hexString2Chars</h4>
<pre>public static&nbsp;char[]&nbsp;hexString2Chars(java.lang.String&nbsp;s)</pre>
<div class="block">十六进制字符串转换成char数组</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>s</code> - 十六进制字符串</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>char数组</dd>
</dl>
</li>
</ul>
<a name="getBytes-char:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBytes</h4>
<pre>public static&nbsp;byte[]&nbsp;getBytes(char[]&nbsp;chars)</pre>
<div class="block">char类型数组转byte类型数组</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>chars</code> - char类型数组</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>byte类型数组</dd>
</dl>
</li>
</ul>
<a name="getChars-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChars</h4>
<pre>public static&nbsp;char[]&nbsp;getChars(byte[]&nbsp;bytes)</pre>
<div class="block">byte类型数组转char类型数组</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bytes</code> - byte类型数组</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>char类型数组</dd>
</dl>
</li>
</ul>
<a name="isDecimal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDecimal</h4>
<pre>public static&nbsp;boolean&nbsp;isDecimal(java.lang.String&nbsp;decimal)</pre>
<div class="block">判断字符串是否是十进制数字</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>decimal</code> - 要判断的字符串</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true是，false否</dd>
</dl>
</li>
</ul>
<a name="hexString2Bytes-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hexString2Bytes</h4>
<pre>public static&nbsp;byte[]&nbsp;hexString2Bytes(java.lang.String&nbsp;s)</pre>
<div class="block">十六进制字符串转byte数组</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>s</code> - 十六进制字符串</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>byte数组</dd>
</dl>
</li>
</ul>
<a name="hexStringToBytes-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hexStringToBytes</h4>
<pre>public static&nbsp;byte[]&nbsp;hexStringToBytes(java.lang.String&nbsp;hexString)</pre>
<div class="block">十六进制字符串转byte数组</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>hexString</code> - 十六进制字符串</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>byte数组</dd>
</dl>
</li>
</ul>
<a name="isEmpty-java.lang.CharSequence-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEmpty</h4>
<pre>public static&nbsp;boolean&nbsp;isEmpty(java.lang.CharSequence&nbsp;cs)</pre>
<div class="block">判断字符串是否为空</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cs</code> - 字符串</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true为空，false不为空</dd>
</dl>
</li>
</ul>
<a name="isNum-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNum</h4>
<pre>public static&nbsp;boolean&nbsp;isNum(java.lang.String&nbsp;str)</pre>
<div class="block">判断是否为数字</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>str</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="int2HexString-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>int2HexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;int2HexString(int&nbsp;n)</pre>
</li>
</ul>
<a name="ints2HexString-int:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ints2HexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;ints2HexString(int[]&nbsp;c,
                                              int&nbsp;size)</pre>
</li>
</ul>
<a name="string2Int-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>string2Int</h4>
<pre>public static&nbsp;int&nbsp;string2Int(java.lang.String&nbsp;str,
                             int&nbsp;defValue)</pre>
<div class="block">字符串转整数</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>str</code> - </dd>
<dd><code>defValue</code> - </dd>
<dt><span class="returnLabel">Returns:</span></dt>
</dl>
</li>
</ul>
<a name="setDebug-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDebug</h4>
<pre>public static&nbsp;void&nbsp;setDebug(boolean&nbsp;debug)</pre>
</li>
</ul>
<a name="bytesConvertHexString-byte:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bytesConvertHexString</h4>
<pre>public static&nbsp;java.lang.String&nbsp;bytesConvertHexString(byte[]&nbsp;b,
                                                     int&nbsp;index,
                                                     int&nbsp;len)</pre>
</li>
</ul>
<a name="isIP-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isIP</h4>
<pre>public static&nbsp;boolean&nbsp;isIP(java.lang.String&nbsp;text)</pre>
<div class="block">验证ip是否合法</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - ip地址</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>验证信息</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/StringUtility.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../com/rscja/utility/NumberTool.html" title="class in com.rscja.utility"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../com/rscja/utility/UhfUtils.html" title="class in com.rscja.utility"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?com/rscja/utility/StringUtility.html" target="_top">Frames</a></li>
<li><a href="StringUtility.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
