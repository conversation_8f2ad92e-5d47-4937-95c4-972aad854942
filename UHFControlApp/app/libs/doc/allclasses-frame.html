<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_302) on Mon Nov 25 17:26:07 CST 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>All Classes</title>
<meta name="date" content="2024-11-25">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<h1 class="bar">All&nbsp;Classes</h1>
<div class="indexContainer">
<ul>
<li><a href="com/rscja/deviceapi/entity/AnimalEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">AnimalEntity</a></li>
<li><a href="com/rscja/deviceapi/entity/AntennaConnectState.html" title="class in com.rscja.deviceapi.entity" target="classFrame">AntennaConnectState</a></li>
<li><a href="com/rscja/deviceapi/enums/AntennaEnum.html" title="enum in com.rscja.deviceapi.enums" target="classFrame">AntennaEnum</a></li>
<li><a href="com/rscja/deviceapi/entity/AntennaPowerEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">AntennaPowerEntity</a></li>
<li><a href="com/rscja/deviceapi/entity/AntennaState.html" title="class in com.rscja.deviceapi.entity" target="classFrame">AntennaState</a></li>
<li><a href="com/rscja/deviceapi/Barcode1D.html" title="class in com.rscja.deviceapi" target="classFrame">Barcode1D</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/Barcode1D_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">Barcode1D_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/Barcode1D_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">Barcode1D_qcom</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/Barcode1D_qcom.UHFProtocolParseBase.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">Barcode1D_qcom.UHFProtocolParseBase</a></li>
<li><a href="com/rscja/deviceapi/Barcode2D.html" title="class in com.rscja.deviceapi" target="classFrame">Barcode2D</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/Barcode2D_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">Barcode2D_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/Barcode2D_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">Barcode2D_qcom</a></li>
<li><a href="com/rscja/team/mtk/barcode/barcode2d/Barcode2DFactory_mtk.html" title="class in com.rscja.team.mtk.barcode.barcode2d" target="classFrame">Barcode2DFactory_mtk</a></li>
<li><a href="com/rscja/team/qcom/barcode/barcode2d/Barcode2DFactory_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d" target="classFrame">Barcode2DFactory_qcom</a></li>
<li><a href="com/rscja/barcode/Barcode2DSHardwareInfo.html" title="class in com.rscja.barcode" target="classFrame">Barcode2DSHardwareInfo</a></li>
<li><a href="com/rscja/team/mtk/barcode/Barcode2DSHardwareInfo_mtk.html" title="class in com.rscja.team.mtk.barcode" target="classFrame">Barcode2DSHardwareInfo_mtk</a></li>
<li><a href="com/rscja/team/qcom/barcode/Barcode2DSHardwareInfo_qcom.html" title="class in com.rscja.team.qcom.barcode" target="classFrame">Barcode2DSHardwareInfo_qcom</a></li>
<li><a href="com/rscja/team/mtk/barcode/Barcode2DSoftCommon_mtk.html" title="class in com.rscja.team.mtk.barcode" target="classFrame">Barcode2DSoftCommon_mtk</a></li>
<li><a href="com/rscja/barcode/BarcodeDecoder.html" title="class in com.rscja.barcode" target="classFrame">BarcodeDecoder</a></li>
<li><a href="com/rscja/barcode/BarcodeDecoder.DecodeCallback.html" title="interface in com.rscja.barcode" target="classFrame"><span class="interfaceName">BarcodeDecoder.DecodeCallback</span></a></li>
<li><a href="com/rscja/barcode/BarcodeDecoder.IBarcodeImageCallback.html" title="interface in com.rscja.barcode" target="classFrame"><span class="interfaceName">BarcodeDecoder.IBarcodeImageCallback</span></a></li>
<li><a href="com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.html" title="class in com.rscja.team.mtk.barcode" target="classFrame">BarcodeDecoder_mtk</a></li>
<li><a href="com/rscja/team/mtk/barcode/BarcodeDecoder_mtk.DecodeCallback.html" title="interface in com.rscja.team.mtk.barcode" target="classFrame"><span class="interfaceName">BarcodeDecoder_mtk.DecodeCallback</span></a></li>
<li><a href="com/rscja/deviceapi/entity/BarcodeEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">BarcodeEntity</a></li>
<li><a href="com/rscja/barcode/BarcodeFactory.html" title="class in com.rscja.barcode" target="classFrame">BarcodeFactory</a></li>
<li><a href="com/rscja/team/mtk/barcode/BarcodeFactory_mtk.html" title="class in com.rscja.team.mtk.barcode" target="classFrame">BarcodeFactory_mtk</a></li>
<li><a href="com/rscja/team/qcom/barcode/BarcodeFactory_qcom.html" title="class in com.rscja.team.qcom.barcode" target="classFrame">BarcodeFactory_qcom</a></li>
<li><a href="com/rscja/deviceapi/entity/BarcodeResult.html" title="class in com.rscja.deviceapi.entity" target="classFrame">BarcodeResult</a></li>
<li><a href="com/rscja/barcode/BarcodeSymbolUtility.html" title="class in com.rscja.barcode" target="classFrame">BarcodeSymbolUtility</a></li>
<li><a href="com/rscja/team/mtk/barcode/BarcodeSymbolUtility_mtk.html" title="class in com.rscja.team.mtk.barcode" target="classFrame">BarcodeSymbolUtility_mtk</a></li>
<li><a href="com/rscja/team/qcom/barcode/BarcodeSymbolUtility_qcom.html" title="class in com.rscja.team.qcom.barcode" target="classFrame">BarcodeSymbolUtility_qcom</a></li>
<li><a href="com/rscja/barcode/BarcodeUtility.html" title="class in com.rscja.barcode" target="classFrame">BarcodeUtility</a></li>
<li><a href="com/rscja/barcode/BarcodeUtility.ModuleType.html" title="enum in com.rscja.barcode" target="classFrame">BarcodeUtility.ModuleType</a></li>
<li><a href="com/rscja/team/mtk/barcode/BarcodeUtility_mtk.html" title="class in com.rscja.team.mtk.barcode" target="classFrame">BarcodeUtility_mtk</a></li>
<li><a href="com/rscja/team/qcom/barcode/BarcodeUtility_qcom.html" title="class in com.rscja.team.qcom.barcode" target="classFrame">BarcodeUtility_qcom</a></li>
<li><a href="com/rscja/deviceapi/entity/BatteryEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">BatteryEntity</a></li>
<li><a href="com/rscja/team/mtk/utility/BatteryManage.html" title="class in com.rscja.team.mtk.utility" target="classFrame">BatteryManage</a></li>
<li><a href="com/rscja/utility/BatteryUtils.html" title="class in com.rscja.utility" target="classFrame">BatteryUtils</a></li>
<li><a href="com/rscja/deviceapi/BleDevice.html" title="class in com.rscja.deviceapi" target="classFrame">BleDevice</a></li>
<li><a href="com/rscja/deviceapi/BleDevice.BleDeviceInfo.html" title="class in com.rscja.deviceapi" target="classFrame">BleDevice.BleDeviceInfo</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/BleDevice_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">BleDevice_qcom</a></li>
<li><a href="com/rscja/team/qcom/service/BLEService_qcom.html" title="class in com.rscja.team.qcom.service" target="classFrame">BLEService_qcom</a></li>
<li><a href="com/rscja/team/qcom/service/BLEService_qcom.IDataCallBack.html" title="interface in com.rscja.team.qcom.service" target="classFrame"><span class="interfaceName">BLEService_qcom.IDataCallBack</span></a></li>
<li><a href="com/rscja/deviceapi/BluetoothReader.html" title="class in com.rscja.deviceapi" target="classFrame">BluetoothReader</a></li>
<li><a href="com/rscja/deviceapi/BluetoothReader.DecodeCallback.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">BluetoothReader.DecodeCallback</span></a></li>
<li><a href="com/rscja/deviceapi/BluetoothReader.OnDataChangeListener.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">BluetoothReader.OnDataChangeListener</span></a></li>
<li><a href="com/rscja/team/qcom/deviceapi/BluetoothReader_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">BluetoothReader_qcom</a></li>
<li><a href="com/rscja/team/mtk/scanner/led/C6000_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led" target="classFrame">C6000_6762_ScanLed_mtk</a></li>
<li><a href="com/rscja/team/qcom/scanner/led/C60_6765_11_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led" target="classFrame">C60_6765_11_ScanLed_qcom</a></li>
<li><a href="com/rscja/team/qcom/scanner/led/C60_qcm2150_10_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led" target="classFrame">C60_qcm2150_10_ScanLed_qcom</a></li>
<li><a href="com/rscja/team/qcom/scanner/led/C61_smd450_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led" target="classFrame">C61_smd450_90_ScanLed_qcom</a></li>
<li><a href="com/rscja/team/qcom/scanner/led/C66_smd450_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led" target="classFrame">C66_smd450_90_ScanLed_qcom</a></li>
<li><a href="com/rscja/team/qcom/scanner/led/C66m_sm6115_10_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led" target="classFrame">C66m_sm6115_10_ScanLed_qcom</a></li>
<li><a href="com/rscja/team/mtk/scanner/led/C7X_6765_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led" target="classFrame">C7X_6765_ScanLed_mtk</a></li>
<li><a href="com/rscja/team/mtk/scanner/led/C90_6762_ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led" target="classFrame">C90_6762_ScanLed_mtk</a></li>
<li><a href="com/rscja/deviceapi/CardWithBYL.html" title="class in com.rscja.deviceapi" target="classFrame">CardWithBYL</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/CardWithBYL_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">CardWithBYL_qcom</a></li>
<li><a href="com/rscja/team/qcom/utility/CheckUtils.html" title="class in com.rscja.team.qcom.utility" target="classFrame">CheckUtils</a></li>
<li><a href="com/rscja/team/qcom/barcode/symbol/CoAsiaBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol" target="classFrame">CoAsiaBarcodeSymbol_qcom</a></li>
<li><a href="com/rscja/deviceapi/exception/ConfigurationException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">ConfigurationException</a></li>
<li><a href="com/rscja/deviceapi/interfaces/ConnectionStatus.html" title="enum in com.rscja.deviceapi.interfaces" target="classFrame">ConnectionStatus</a></li>
<li><a href="com/rscja/deviceapi/interfaces/ConnectionStatusCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ConnectionStatusCallback</span></a></li>
<li><a href="com/rscja/barcode/ConstantUtil.html" title="class in com.rscja.barcode" target="classFrame">ConstantUtil</a></li>
<li><a href="com/rscja/CWDeviceInfo.html" title="class in com.rscja" target="classFrame">CWDeviceInfo</a></li>
<li><a href="com/rscja/deviceapi/entity/DESFireFile.html" title="class in com.rscja.deviceapi.entity" target="classFrame">DESFireFile</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/Device_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">Device_qcom</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/DeviceAPI.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">DeviceAPI</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/DeviceAPI.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">DeviceAPI</a></li>
<li><a href="com/rscja/team/mtk/DeviceConfiguration_mtk.html" title="class in com.rscja.team.mtk" target="classFrame">DeviceConfiguration_mtk</a></li>
<li><a href="com/rscja/team/mtk/DeviceConfiguration_mtk.Platform.html" title="class in com.rscja.team.mtk" target="classFrame">DeviceConfiguration_mtk.Platform</a></li>
<li><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.html" title="class in com.rscja.team.qcom" target="classFrame">DeviceConfiguration_qcom</a></li>
<li><a href="com/rscja/team/qcom/DeviceConfiguration_qcom.Platform.html" title="class in com.rscja.team.qcom" target="classFrame">DeviceConfiguration_qcom.Platform</a></li>
<li><a href="com/rscja/deviceapi/exception/DeviceNotConnectException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">DeviceNotConnectException</a></li>
<li><a href="com/rscja/team/mtk/barcode/symbol/DlBarcodeSymbol_mtk.html" title="class in com.rscja.team.mtk.barcode.symbol" target="classFrame">DlBarcodeSymbol_mtk</a></li>
<li><a href="com/rscja/team/qcom/barcode/symbol/DlBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol" target="classFrame">DlBarcodeSymbol_qcom</a></li>
<li><a href="com/rscja/team/qcom/ble/EmptyUhfBle.html" title="class in com.rscja.team.qcom.ble" target="classFrame">EmptyUhfBle</a></li>
<li><a href="com/rscja/utility/FileUtility.html" title="class in com.rscja.utility" target="classFrame">FileUtility</a></li>
<li><a href="com/rscja/team/mtk/utility/FileUtils.html" title="class in com.rscja.team.mtk.utility" target="classFrame">FileUtils</a></li>
<li><a href="com/rscja/deviceapi/Fingerprint.html" title="class in com.rscja.deviceapi" target="classFrame">Fingerprint</a></li>
<li><a href="com/rscja/deviceapi/Fingerprint.BufferEnum.html" title="enum in com.rscja.deviceapi" target="classFrame">Fingerprint.BufferEnum</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/Fingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">Fingerprint_qcom</a></li>
<li><a href="com/rscja/deviceapi/exception/FingerprintAlreadyEnrolledException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">FingerprintAlreadyEnrolledException</a></li>
<li><a href="com/rscja/deviceapi/exception/FingerprintInvalidIDException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">FingerprintInvalidIDException</a></li>
<li><a href="com/rscja/utility/FingerprintPictureUtility.html" title="class in com.rscja.utility" target="classFrame">FingerprintPictureUtility</a></li>
<li><a href="com/rscja/deviceapi/FingerprintSM206B.html" title="class in com.rscja.deviceapi" target="classFrame">FingerprintSM206B</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/FingerprintSM206B_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">FingerprintSM206B_qcom</a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithFIPS.html" title="class in com.rscja.deviceapi" target="classFrame">FingerprintWithFIPS</a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithFIPS.DataFormat.html" title="enum in com.rscja.deviceapi" target="classFrame">FingerprintWithFIPS.DataFormat</a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithFIPS.EnrollCallBack.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">FingerprintWithFIPS.EnrollCallBack</span></a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithFIPS.FingerprintInfo.html" title="class in com.rscja.deviceapi" target="classFrame">FingerprintWithFIPS.FingerprintInfo</a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithFIPS.GRABCallBack.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">FingerprintWithFIPS.GRABCallBack</span></a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithFIPS.IdentificationCallBack.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">FingerprintWithFIPS.IdentificationCallBack</span></a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithFIPS.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">FingerprintWithFIPS.PtCaptureCallBack</span></a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithFIPS.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">FingerprintWithFIPS.TemplateVerifyCallBack</span></a></li>
<li><a href="com/rscja/team/mtk/deviceapi/FingerprintWithFIPS_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">FingerprintWithFIPS_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/FingerprintWithFIPS_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">FingerprintWithFIPS_qcom</a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithMorpho.html" title="class in com.rscja.deviceapi" target="classFrame">FingerprintWithMorpho</a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithMorpho.EnrollCallBack.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">FingerprintWithMorpho.EnrollCallBack</span></a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithMorpho.GrabCallBack.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">FingerprintWithMorpho.GrabCallBack</span></a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithMorpho.IdentificationCallBack.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">FingerprintWithMorpho.IdentificationCallBack</span></a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithMorpho.PtCaptureCallBack.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">FingerprintWithMorpho.PtCaptureCallBack</span></a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithMorpho.TemplateVerifyCallBack.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">FingerprintWithMorpho.TemplateVerifyCallBack</span></a></li>
<li><a href="com/rscja/team/mtk/deviceapi/FingerprintWithMorpho_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">FingerprintWithMorpho_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/FingerprintWithMorpho_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">FingerprintWithMorpho_qcom</a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithTLK1NC.html" title="class in com.rscja.deviceapi" target="classFrame">FingerprintWithTLK1NC</a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithTLK1NC.BufferEnum.html" title="enum in com.rscja.deviceapi" target="classFrame">FingerprintWithTLK1NC.BufferEnum</a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithTLK1NC.IUPImageCallback.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">FingerprintWithTLK1NC.IUPImageCallback</span></a></li>
<li><a href="com/rscja/team/mtk/deviceapi/FingerprintWithTLK1NC_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">FingerprintWithTLK1NC_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/FingerprintWithTLK1NC_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">FingerprintWithTLK1NC_qcom</a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithZAZ.html" title="class in com.rscja.deviceapi" target="classFrame">FingerprintWithZAZ</a></li>
<li><a href="com/rscja/deviceapi/FingerprintWithZAZ.BufferEnum.html" title="enum in com.rscja.deviceapi" target="classFrame">FingerprintWithZAZ.BufferEnum</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/FingerprintWithZAZ_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">FingerprintWithZAZ_qcom</a></li>
<li><a href="com/rscja/deviceapi/entity/Gen2Entity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">Gen2Entity</a></li>
<li><a href="com/rscja/deviceapi/entity/GPIOInfo.html" title="class in com.rscja.deviceapi.entity" target="classFrame">GPIOInfo</a></li>
<li><a href="com/rscja/deviceapi/entity/GPIStateEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">GPIStateEntity</a></li>
<li><a href="com/rscja/deviceapi/entity/GPOEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">GPOEntity</a></li>
<li><a href="com/rscja/team/qcom/utility/GyroAngle.html" title="class in com.rscja.team.qcom.utility" target="classFrame">GyroAngle</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">HardwareInterface_qcom</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/HardwareInterface_qcom.FunctionEnum.html" title="enum in com.rscja.team.qcom.deviceapi" target="classFrame">HardwareInterface_qcom.FunctionEnum</a></li>
<li><a href="com/rscja/team/qcom/r1/hf/HF14443A.html" title="class in com.rscja.team.qcom.r1.hf" target="classFrame">HF14443A</a></li>
<li><a href="com/rscja/team/qcom/r1/hf/HF14443B.html" title="class in com.rscja.team.qcom.r1.hf" target="classFrame">HF14443B</a></li>
<li><a href="com/rscja/deviceapi/entity/HF14443RequestEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">HF14443RequestEntity</a></li>
<li><a href="com/rscja/team/qcom/r1/hf/HF15693.html" title="class in com.rscja.team.qcom.r1.hf" target="classFrame">HF15693</a></li>
<li><a href="com/rscja/deviceapi/entity/HF15693RequestEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">HF15693RequestEntity</a></li>
<li><a href="com/rscja/deviceapi/entity/HF15693RequestEntity.Builder.html" title="class in com.rscja.deviceapi.entity" target="classFrame">HF15693RequestEntity.Builder</a></li>
<li><a href="com/rscja/deviceapi/entity/HF15693RequestEntity.TagType.html" title="enum in com.rscja.deviceapi.entity" target="classFrame">HF15693RequestEntity.TagType</a></li>
<li><a href="com/rscja/team/qcom/r1/hf/HFBase.html" title="class in com.rscja.team.qcom.r1.hf" target="classFrame">HFBase</a></li>
<li><a href="com/rscja/team/qcom/uhfhandler/HFR1UsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler" target="classFrame">HFR1UsbDataHandle</a></li>
<li><a href="com/rscja/team/qcom/barcode/symbol/HoneywellBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol" target="classFrame">HoneywellBarcodeSymbol_qcom</a></li>
<li><a href="com/rscja/team/qcom/http/HttpUtils_qcom.html" title="class in com.rscja.team.qcom.http" target="classFrame">HttpUtils_qcom</a></li>
<li><a href="com/rscja/deviceapi/interfaces/IBarcode1D.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcode1D</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IBarcode2D.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcode2D</span></a></li>
<li><a href="com/rscja/barcode/IBarcode2DSHardwareInfo.html" title="interface in com.rscja.barcode" target="classFrame"><span class="interfaceName">IBarcode2DSHardwareInfo</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IBarcodePhoto.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcodePhoto</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IBarcodePictureCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcodePictureCallback</span></a></li>
<li><a href="com/rscja/team/mtk/barcode/symbol/IBarcodeSymbol_mtk.html" title="interface in com.rscja.team.mtk.barcode.symbol" target="classFrame"><span class="interfaceName">IBarcodeSymbol_mtk</span></a></li>
<li><a href="com/rscja/team/qcom/barcode/symbol/IBarcodeSymbol_qcom.html" title="interface in com.rscja.team.qcom.barcode.symbol" target="classFrame"><span class="interfaceName">IBarcodeSymbol_qcom</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IBarcodeSymbolUtility.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcodeSymbolUtility</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IBarcodeUtility.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcodeUtility</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IBarcodeVideoCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBarcodeVideoCallback</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IBleDevice.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBleDevice</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IBluetoothData.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBluetoothData</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IBluetoothReader.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IBluetoothReader</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/ICardWithBYL.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ICardWithBYL</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IConnectionStatusChangedListener.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IConnectionStatusChangedListener</span></a></li>
<li><a href="com/rscja/team/qcom/barcode/symbol/IdataBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol" target="classFrame">IdataBarcodeSymbol_qcom</a></li>
<li><a href="com/rscja/team/qcom/http/IDownLoadProgress_qcom.html" title="interface in com.rscja.team.qcom.http" target="classFrame"><span class="interfaceName">IDownLoadProgress_qcom</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IFingerprint.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IFingerprint</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IFingerprintSM206B.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IFingerprintSM206B</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IFingerprintWithFIPS.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IFingerprintWithFIPS</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IFingerprintWithMorpho.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IFingerprintWithMorpho</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IFingerprintWithTLK1NC.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IFingerprintWithTLK1NC</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IFingerprintWithZAZ.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IFingerprintWithZAZ</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IGPIStateCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IGPIStateCallback</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IHandheldRFID.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IHandheldRFID</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IHF14443A.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IHF14443A</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IHF14443B.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IHF14443B</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IHF15693.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IHF15693</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IInfrared.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IInfrared</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/ILedLight.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ILedLight</span></a></li>
<li><a href="com/rscja/custom/interfaces/IM775Authenticate.html" title="interface in com.rscja.custom.interfaces" target="classFrame"><span class="interfaceName">IM775Authenticate</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IModule.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IModule</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IMultipleAntenna.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IMultipleAntenna</span></a></li>
<li><a href="com/rscja/deviceapi/Infrared.html" title="class in com.rscja.deviceapi" target="classFrame">Infrared</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/Infrared_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">Infrared_mtk</a></li>
<li><a href="com/rscja/deviceapi/entity/InventoryModeEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">InventoryModeEntity</a></li>
<li><a href="com/rscja/deviceapi/entity/InventoryModeEntity.Builder.html" title="class in com.rscja.deviceapi.entity" target="classFrame">InventoryModeEntity.Builder</a></li>
<li><a href="com/rscja/deviceapi/entity/InventoryParameter.html" title="class in com.rscja.deviceapi.entity" target="classFrame">InventoryParameter</a></li>
<li><a href="com/rscja/deviceapi/entity/InventoryParameter.ResultData.html" title="class in com.rscja.deviceapi.entity" target="classFrame">InventoryParameter.ResultData</a></li>
<li><a href="com/rscja/deviceapi/interfaces/IPrinter.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IPrinter</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IPSAM.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IPSAM</span></a></li>
<li><a href="com/rscja/team/qcom/r1/IPSAM.html" title="interface in com.rscja.team.qcom.r1" target="classFrame"><span class="interfaceName">IPSAM</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IReader.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IReader</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDBase.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDBase</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443A.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithISO14443A</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443A4CPU.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithISO14443A4CPU</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithISO14443B.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithISO14443B</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithISO15693.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithISO15693</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithLF.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithLF</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA4</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4NetWork.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA4NetWork</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4RS232.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA4RS232</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA4Uart.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA4Uart</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA8</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8NetWork.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA8NetWork</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8RS232.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA8RS232</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFA8Uart.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFA8Uart</span></a></li>
<li><a href="com/rscja/custom/interfaces/IRFIDWithUHFJieCe.html" title="interface in com.rscja.custom.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFJieCe</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFRLM.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFRLM</span></a></li>
<li><a href="com/rscja/custom/interfaces/IRFIDWithUHFShuangYingDianZi.html" title="interface in com.rscja.custom.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFShuangYingDianZi</span></a></li>
<li><a href="com/rscja/custom/interfaces/IRFIDWithUHFUARTUAE.html" title="interface in com.rscja.custom.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFUARTUAE</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxNetwork.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFUrxNetwork</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUart.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFUrxUart</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUrxUsbToUart.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFUrxUsbToUart</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IRFIDWithUHFUSB.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IRFIDWithUHFUSB</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IScanerLedLight.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IScanerLedLight</span></a></li>
<li><a href="com/rscja/scanner/IScanner.html" title="interface in com.rscja.scanner" target="classFrame"><span class="interfaceName">IScanner</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/ISingleAntenna.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ISingleAntenna</span></a></li>
<li><a href="com/rscja/deviceapi/entity/ISO15693Entity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">ISO15693Entity</a></li>
<li><a href="com/rscja/system/ISystemInterfaces.html" title="interface in com.rscja.system" target="classFrame"><span class="interfaceName">ISystemInterfaces</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/ITagLocate.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ITagLocate</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/ITagLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ITagLocationCallback</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUHF.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHF</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUHFA4.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFA4</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUHFA8.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFA8</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUhfBle.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUhfBle</span></a></li>
<li><a href="com/rscja/custom/interfaces/IUHFCSYX.html" title="interface in com.rscja.custom.interfaces" target="classFrame"><span class="interfaceName">IUHFCSYX</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUHFInventoryCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFInventoryCallback</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUHFLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFLocationCallback</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUHFOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFOfAndroidUart</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUHFProtocolParse.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFProtocolParse</span></a></li>
<li><a href="com/rscja/custom/interfaces/IUHFProtocolParseBLE.html" title="interface in com.rscja.custom.interfaces" target="classFrame"><span class="interfaceName">IUHFProtocolParseBLE</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUHFProtocolParseUrx.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFProtocolParseUrx</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUHFRadarLocationCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFRadarLocationCallback</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUhfReader.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUhfReader</span></a></li>
<li><a href="com/rscja/custom/interfaces/IUHFTamperAPI.html" title="interface in com.rscja.custom.interfaces" target="classFrame"><span class="interfaceName">IUHFTamperAPI</span></a></li>
<li><a href="com/rscja/custom/interfaces/IUHFTemperatureTagsAPI.html" title="interface in com.rscja.custom.interfaces" target="classFrame"><span class="interfaceName">IUHFTemperatureTagsAPI</span></a></li>
<li><a href="com/rscja/custom/interfaces/IUHFUartFoxconn.html" title="interface in com.rscja.custom.interfaces" target="classFrame"><span class="interfaceName">IUHFUartFoxconn</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUHFURAxExtend.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFURAxExtend</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUHFURx.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFURx</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUHFUrxAutoInventoryTag.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUHFUrxAutoInventoryTag</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUpgradeProgress.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUpgradeProgress</span></a></li>
<li><a href="com/rscja/team/qcom/http/IUploadProgress_qcom.html" title="interface in com.rscja.team.qcom.http" target="classFrame"><span class="interfaceName">IUploadProgress_qcom</span></a></li>
<li><a href="com/rscja/team/qcom/urax/IURA4Gpio.html" title="interface in com.rscja.team.qcom.urax" target="classFrame"><span class="interfaceName">IURA4Gpio</span></a></li>
<li><a href="com/rscja/team/qcom/urax/IURAxDevice.html" title="interface in com.rscja.team.qcom.urax" target="classFrame"><span class="interfaceName">IURAxDevice</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IURAxOfAndroidUart.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IURAxOfAndroidUart</span></a></li>
<li><a href="com/rscja/deviceapi/interfaces/IUsbFingerprint.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">IUsbFingerprint</span></a></li>
<li><a href="com/rscja/team/mtk/barcode/barcode2d/KeyboardEmulator2DDecoder_mtk.html" title="class in com.rscja.team.mtk.barcode.barcode2d" target="classFrame">KeyboardEmulator2DDecoder_mtk</a></li>
<li><a href="com/rscja/team/qcom/barcode/barcode2d/KeyboardEmulator2DDecoder_qcom.html" title="class in com.rscja.team.qcom.barcode.barcode2d" target="classFrame">KeyboardEmulator2DDecoder_qcom</a></li>
<li><a href="com/rscja/deviceapi/interfaces/KeyEventCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">KeyEventCallback</span></a></li>
<li><a href="com/rscja/deviceapi/LedLight.html" title="class in com.rscja.deviceapi" target="classFrame">LedLight</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/LedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">LedLight_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/LedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">LedLight_qcom</a></li>
<li><a href="com/rscja/team/mtk/utility/LogUtility_mtk.html" title="class in com.rscja.team.mtk.utility" target="classFrame">LogUtility_mtk</a></li>
<li><a href="com/rscja/team/qcom/utility/LogUtility_qcom.html" title="class in com.rscja.team.qcom.utility" target="classFrame">LogUtility_qcom</a></li>
<li><a href="com/rscja/deviceapi/entity/LowBatteryEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">LowBatteryEntity</a></li>
<li><a href="com/rscja/deviceapi/exception/LowBatteryException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">LowBatteryException</a></li>
<li><a href="com/rscja/custom/M775Authenticate.html" title="class in com.rscja.custom" target="classFrame">M775Authenticate</a></li>
<li><a href="com/rscja/custom/M775Authenticate.AuthenticateInfo.html" title="class in com.rscja.custom" target="classFrame">M775Authenticate.AuthenticateInfo</a></li>
<li><a href="com/rscja/custom/M775Authenticate.IUHFInventoryCallback.html" title="interface in com.rscja.custom" target="classFrame"><span class="interfaceName">M775Authenticate.IUHFInventoryCallback</span></a></li>
<li><a href="com/rscja/team/mtk/custom/M775Authenticate_mtk.html" title="class in com.rscja.team.mtk.custom" target="classFrame">M775Authenticate_mtk</a></li>
<li><a href="com/rscja/team/qcom/custom/M775Authenticate_qcom.html" title="class in com.rscja.team.qcom.custom" target="classFrame">M775Authenticate_qcom</a></li>
<li><a href="com/rscja/team/qcom/scanner/led/MC50_4350_12_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led" target="classFrame">MC50_4350_12_ScanLed_qcom</a></li>
<li><a href="com/rscja/team/qcom/barcode/symbol/MobyDataBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol" target="classFrame">MobyDataBarcodeSymbol_qcom</a></li>
<li><a href="com/rscja/deviceapi/Module.html" title="class in com.rscja.deviceapi" target="classFrame">Module</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/Module_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">Module_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/Module_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">Module_qcom</a></li>
<li><a href="com/rscja/team/qcom/http/NetResult_qcom.html" title="class in com.rscja.team.qcom.http" target="classFrame">NetResult_qcom</a></li>
<li><a href="com/rscja/utility/NetUtils.html" title="class in com.rscja.utility" target="classFrame">NetUtils</a></li>
<li><a href="com/rscja/utility/NumberTool.html" title="class in com.rscja.utility" target="classFrame">NumberTool</a></li>
<li><a href="com/rscja/deviceapi/interfaces/OnLowBatteryListener.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">OnLowBatteryListener</span></a></li>
<li><a href="com/rscja/scanner/OnUhfWorkStateListener.html" title="interface in com.rscja.scanner" target="classFrame"><span class="interfaceName">OnUhfWorkStateListener</span></a></li>
<li><a href="com/rscja/team/qcom/scanner/led/P80_8786_130_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led" target="classFrame">P80_8786_130_ScanLed_qcom</a></li>
<li><a href="com/rscja/team/qcom/scanner/led/P80_8953_90_ScanLed_qcom.html" title="class in com.rscja.team.qcom.scanner.led" target="classFrame">P80_8953_90_ScanLed_qcom</a></li>
<li><a href="com/rscja/deviceapi/Printer.html" title="class in com.rscja.deviceapi" target="classFrame">Printer</a></li>
<li><a href="com/rscja/deviceapi/Printer.BarcodeType.html" title="enum in com.rscja.deviceapi" target="classFrame">Printer.BarcodeType</a></li>
<li><a href="com/rscja/deviceapi/Printer.PrinterStatus.html" title="enum in com.rscja.deviceapi" target="classFrame">Printer.PrinterStatus</a></li>
<li><a href="com/rscja/deviceapi/Printer.PrinterStatusCallBack.html" title="interface in com.rscja.deviceapi" target="classFrame"><span class="interfaceName">Printer.PrinterStatusCallBack</span></a></li>
<li><a href="com/rscja/team/mtk/deviceapi/Printer_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">Printer_mtk</a></li>
<li><a href="com/rscja/deviceapi/exception/PrinterBarcodeInvalidException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">PrinterBarcodeInvalidException</a></li>
<li><a href="com/rscja/deviceapi/exception/PrinterLowPager.html" title="class in com.rscja.deviceapi.exception" target="classFrame">PrinterLowPager</a></li>
<li><a href="com/rscja/deviceapi/PSAM.html" title="class in com.rscja.deviceapi" target="classFrame">PSAM</a></li>
<li><a href="com/rscja/team/qcom/r1/psam/PSAM.html" title="class in com.rscja.team.qcom.r1.psam" target="classFrame">PSAM</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/PSAM_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">PSAM_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/PSAM_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">PSAM_qcom</a></li>
<li><a href="com/rscja/deviceapi/exception/PSAMException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">PSAMException</a></li>
<li><a href="com/rscja/team/qcom/urax/QcomURA4Gpio.html" title="class in com.rscja.team.qcom.urax" target="classFrame">QcomURA4Gpio</a></li>
<li><a href="com/rscja/team/qcom/urax/QcomURAxDevice.html" title="class in com.rscja.team.qcom.urax" target="classFrame">QcomURAxDevice</a></li>
<li><a href="com/rscja/deviceapi/R1HFAndPsamManage.html" title="class in com.rscja.deviceapi" target="classFrame">R1HFAndPsamManage</a></li>
<li><a href="com/rscja/team/qcom/usb/R1HFUSB.html" title="class in com.rscja.team.qcom.usb" target="classFrame">R1HFUSB</a></li>
<li><a href="com/rscja/deviceapi/entity/RadarLocationEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">RadarLocationEntity</a></li>
<li><a href="com/rscja/deviceapi/entity/RadarLocationEntity.Builder.html" title="class in com.rscja.deviceapi.entity" target="classFrame">RadarLocationEntity.Builder</a></li>
<li><a href="com/rscja/deviceapi/entity/ReaderIPEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">ReaderIPEntity</a></li>
<li><a href="com/rscja/deviceapi/exception/RFIDArgumentException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">RFIDArgumentException</a></li>
<li><a href="com/rscja/deviceapi/RFIDBase.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDBase</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/RFIDBase_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">RFIDBase_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDBase_qcom</a></li>
<li><a href="com/rscja/deviceapi/exception/RFIDNotFoundException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">RFIDNotFoundException</a></li>
<li><a href="com/rscja/deviceapi/exception/RFIDReadFailureException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">RFIDReadFailureException</a></li>
<li><a href="com/rscja/deviceapi/exception/RFIDVerificationException.html" title="class in com.rscja.deviceapi.exception" target="classFrame">RFIDVerificationException</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithISO14443A.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithISO14443A</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithISO14443A.DESFireEncryptionTypekEnum.html" title="enum in com.rscja.deviceapi" target="classFrame">RFIDWithISO14443A.DESFireEncryptionTypekEnum</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithISO14443A.DESFireFileTypekEnum.html" title="enum in com.rscja.deviceapi" target="classFrame">RFIDWithISO14443A.DESFireFileTypekEnum</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithISO14443A.KeyType.html" title="enum in com.rscja.deviceapi" target="classFrame">RFIDWithISO14443A.KeyType</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithISO14443A.TagType.html" title="enum in com.rscja.deviceapi" target="classFrame">RFIDWithISO14443A.TagType</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithISO14443A4CPU.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithISO14443A4CPU</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/RFIDWithISO14443A4CPU_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">RFIDWithISO14443A4CPU_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithISO14443A4CPU_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithISO14443A4CPU_qcom</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/RFIDWithISO14443A_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">RFIDWithISO14443A_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithISO14443A_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithISO14443A_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithISO14443B.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithISO14443B</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/RFIDWithISO14443B_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">RFIDWithISO14443B_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithISO14443B_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithISO14443B_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithISO15693.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithISO15693</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithISO15693.TagType.html" title="enum in com.rscja.deviceapi" target="classFrame">RFIDWithISO15693.TagType</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">RFIDWithISO15693_mtk</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/RFIDWithISO15693_mtk.TagType.html" title="enum in com.rscja.team.mtk.deviceapi" target="classFrame">RFIDWithISO15693_mtk.TagType</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithISO15693_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithISO15693_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithLF.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithLF</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithLF_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithLF_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFA4.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFA4</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFA4_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFA4_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFA4NetWork.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFA4NetWork</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFA4NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFA4NetWork_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFA4RS232.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFA4RS232</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFA4RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFA4RS232_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFA8.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFA8</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFA8_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFA8_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFA8NetWork.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFA8NetWork</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFA8NetWork_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFA8NetWork_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFA8RS232.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFA8RS232</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFA8RS232_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFA8RS232_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFAxBase.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFAxBase</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFAxBase_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFAxBase_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFBLE.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFBLE</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFBLE_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFBLE_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFBLEManage.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFBLEManage</a></li>
<li><a href="com/rscja/team/qcom/ble/RFIDWithUHFBLEN51_qcom.html" title="class in com.rscja.team.qcom.ble" target="classFrame">RFIDWithUHFBLEN51_qcom</a></li>
<li><a href="com/rscja/team/qcom/ble/RFIDWithUHFBLEN52_qcom.html" title="class in com.rscja.team.qcom.ble" target="classFrame">RFIDWithUHFBLEN52_qcom</a></li>
<li><a href="com/rscja/custom/RFIDWithUHFJieCe.html" title="class in com.rscja.custom" target="classFrame">RFIDWithUHFJieCe</a></li>
<li><a href="com/rscja/custom/RFIDWithUHFJieCe.TemperatureTagInfo.html" title="class in com.rscja.custom" target="classFrame">RFIDWithUHFJieCe.TemperatureTagInfo</a></li>
<li><a href="com/rscja/team/qcom/custom/RFIDWithUHFJieCe_qcom.html" title="class in com.rscja.team.qcom.custom" target="classFrame">RFIDWithUHFJieCe_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFRLM.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFRLM</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFRLM_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFRLM_qcom</a></li>
<li><a href="com/rscja/team/qcom/custom/RFIDWithUHFShuangYingDianZi_qcom.html" title="class in com.rscja.team.qcom.custom" target="classFrame">RFIDWithUHFShuangYingDianZi_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFUART.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFUART</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/RFIDWithUHFUART_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">RFIDWithUHFUART_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFUART_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFUART_qcom</a></li>
<li><a href="com/rscja/team/qcom/custom/RFIDWithUHFUARTUAE_qcom.html" title="class in com.rscja.team.qcom.custom" target="classFrame">RFIDWithUHFUARTUAE_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFUrxNetwork.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFUrxNetwork</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxNetwork_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFUrxNetwork_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFUrxUart.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFUrxUart</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart2_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFUrxUart2_qcom</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFUrxUart_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFUrxUsbToUart.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFUrxUsbToUart</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFUrxUsbToUart_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFUrxUsbToUart_qcom</a></li>
<li><a href="com/rscja/deviceapi/RFIDWithUHFUSB.html" title="class in com.rscja.deviceapi" target="classFrame">RFIDWithUHFUSB</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/RFIDWithUHFUSB_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">RFIDWithUHFUSB_qcom</a></li>
<li><a href="com/rscja/team/qcom/urax/RKURA4C8Device.html" title="class in com.rscja.team.qcom.urax" target="classFrame">RKURA4C8Device</a></li>
<li><a href="com/rscja/team/qcom/urax/RKURA4Gpio.html" title="class in com.rscja.team.qcom.urax" target="classFrame">RKURA4Gpio</a></li>
<li><a href="com/rscja/team/qcom/urax/RKURAxDevice.html" title="class in com.rscja.team.qcom.urax" target="classFrame">RKURAxDevice</a></li>
<li><a href="com/rscja/team/qcom/usb/RxUsb_qcom.html" title="class in com.rscja.team.qcom.usb" target="classFrame">RxUsb_qcom</a></li>
<li><a href="com/rscja/deviceapi/interfaces/ScanBTCallback.html" title="interface in com.rscja.deviceapi.interfaces" target="classFrame"><span class="interfaceName">ScanBTCallback</span></a></li>
<li><a href="com/rscja/deviceapi/ScanerLedLight.html" title="class in com.rscja.deviceapi" target="classFrame">ScanerLedLight</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/ScanerLedLight_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">ScanerLedLight_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/ScanerLedLight_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">ScanerLedLight_qcom</a></li>
<li><a href="com/rscja/scanner/led/ScanLed.html" title="class in com.rscja.scanner.led" target="classFrame">ScanLed</a></li>
<li><a href="com/rscja/team/mtk/scanner/led/ScanLed_mtk.html" title="class in com.rscja.team.mtk.scanner.led" target="classFrame">ScanLed_mtk</a></li>
<li><a href="com/rscja/scanner/led/ScanLedManage.html" title="class in com.rscja.scanner.led" target="classFrame">ScanLedManage</a></li>
<li><a href="com/rscja/team/mtk/scanner/led/ScanLedManage_mtk.html" title="class in com.rscja.team.mtk.scanner.led" target="classFrame">ScanLedManage_mtk</a></li>
<li><a href="com/rscja/team/qcom/scanner/led/ScanLedManage_qcom.html" title="class in com.rscja.team.qcom.scanner.led" target="classFrame">ScanLedManage_qcom</a></li>
<li><a href="com/rscja/deviceapi/entity/ScannerParameterEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">ScannerParameterEntity</a></li>
<li><a href="com/rscja/deviceapi/entity/ScannerParameterEntity.Builder.html" title="class in com.rscja.deviceapi.entity" target="classFrame">ScannerParameterEntity.Builder</a></li>
<li><a href="com/rscja/team/qcom/scanner/utility/ScannerParameterUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility" target="classFrame">ScannerParameterUtility_qcom</a></li>
<li><a href="com/rscja/scanner/utility/ScannerUtility.html" title="class in com.rscja.scanner.utility" target="classFrame">ScannerUtility</a></li>
<li><a href="com/rscja/team/mtk/scanner/utility/ScannerUtility_mtk.html" title="class in com.rscja.team.mtk.scanner.utility" target="classFrame">ScannerUtility_mtk</a></li>
<li><a href="com/rscja/team/qcom/scanner/utility/ScannerUtility_qcom.html" title="class in com.rscja.team.qcom.scanner.utility" target="classFrame">ScannerUtility_qcom</a></li>
<li><a href="com/rscja/team/qcom/serialportapi/SerialPort.html" title="class in com.rscja.team.qcom.serialportapi" target="classFrame">SerialPort</a></li>
<li><a href="com/rscja/team/qcom/serialportapi/SerialportAPI.html" title="class in com.rscja.team.qcom.serialportapi" target="classFrame">SerialportAPI</a></li>
<li><a href="com/rscja/deviceapi/entity/SimpleRFIDEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">SimpleRFIDEntity</a></li>
<li><a href="com/rscja/team/qcom/socket/SocketManageA4.html" title="class in com.rscja.team.qcom.socket" target="classFrame">SocketManageA4</a></li>
<li><a href="com/rscja/team/qcom/socket/SocketManageUR4.html" title="class in com.rscja.team.qcom.socket" target="classFrame">SocketManageUR4</a></li>
<li><a href="com/rscja/team/qcom/socket/SocketManageUR4.CheckConnectState.html" title="interface in com.rscja.team.qcom.socket" target="classFrame"><span class="interfaceName">SocketManageUR4.CheckConnectState</span></a></li>
<li><a href="com/rscja/team/qcom/socket/SocketTcpIpBase.html" title="class in com.rscja.team.qcom.socket" target="classFrame">SocketTcpIpBase</a></li>
<li><a href="com/rscja/team/qcom/socket/SocketUdpClient.html" title="class in com.rscja.team.qcom.socket" target="classFrame">SocketUdpClient</a></li>
<li><a href="com/rscja/team/qcom/socket/SocketUdpClientUR4.html" title="class in com.rscja.team.qcom.socket" target="classFrame">SocketUdpClientUR4</a></li>
<li><a href="com/rscja/utility/StringUtility.html" title="class in com.rscja.utility" target="classFrame">StringUtility</a></li>
<li><a href="com/rscja/team/qcom/system/SysInterfacesOfC66P_qcom.html" title="class in com.rscja.team.qcom.system" target="classFrame">SysInterfacesOfC66P_qcom</a></li>
<li><a href="com/rscja/team/mtk/system/SysInterfacesOfC7XA11_mtk.html" title="class in com.rscja.team.mtk.system" target="classFrame">SysInterfacesOfC7XA11_mtk</a></li>
<li><a href="com/rscja/system/SystemInterfacesFactory.html" title="class in com.rscja.system" target="classFrame">SystemInterfacesFactory</a></li>
<li><a href="com/rscja/team/mtk/system/SystemInterfacesFactory_mtk.html" title="class in com.rscja.team.mtk.system" target="classFrame">SystemInterfacesFactory_mtk</a></li>
<li><a href="com/rscja/team/qcom/system/SystemInterfacesFactory_qcom.html" title="class in com.rscja.team.qcom.system" target="classFrame">SystemInterfacesFactory_qcom</a></li>
<li><a href="com/rscja/team/mtk/system/SystemPropValues_mtk.html" title="class in com.rscja.team.mtk.system" target="classFrame">SystemPropValues_mtk</a></li>
<li><a href="com/rscja/team/qcom/system/SystemPropValues_qcom.html" title="class in com.rscja.team.qcom.system" target="classFrame">SystemPropValues_qcom</a></li>
<li><a href="com/rscja/team/mtk/system/SystemSettingsDBData_mtk.html" title="class in com.rscja.team.mtk.system" target="classFrame">SystemSettingsDBData_mtk</a></li>
<li><a href="com/rscja/deviceapi/entity/TagInfoRule.html" title="class in com.rscja.deviceapi.entity" target="classFrame">TagInfoRule</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/TagLocate_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">TagLocate_qcom</a></li>
<li><a href="com/rscja/deviceapi/entity/TagLocationEntity.html" title="class in com.rscja.deviceapi.entity" target="classFrame">TagLocationEntity</a></li>
<li><a href="com/rscja/deviceapi/entity/TagLocationInfo.html" title="class in com.rscja.deviceapi.entity" target="classFrame">TagLocationInfo</a></li>
<li><a href="com/rscja/deviceapi/UhfBase.html" title="class in com.rscja.deviceapi" target="classFrame">UhfBase</a></li>
<li><a href="com/rscja/team/qcom/uhfparse/UHFBLEProtocolParse.html" title="class in com.rscja.team.qcom.uhfparse" target="classFrame">UHFBLEProtocolParse</a></li>
<li><a href="com/rscja/custom/UHFCSYX.html" title="class in com.rscja.custom" target="classFrame">UHFCSYX</a></li>
<li><a href="com/rscja/custom/UHFCSYX.IUHFInventoryCallback.html" title="interface in com.rscja.custom" target="classFrame"><span class="interfaceName">UHFCSYX.IUHFInventoryCallback</span></a></li>
<li><a href="com/rscja/custom/UHFCSYX.TagAuthenticationResponseInfo.html" title="class in com.rscja.custom" target="classFrame">UHFCSYX.TagAuthenticationResponseInfo</a></li>
<li><a href="com/rscja/custom/UHFCSYX_A4NetWork.html" title="class in com.rscja.custom" target="classFrame">UHFCSYX_A4NetWork</a></li>
<li><a href="com/rscja/team/qcom/custom/UHFCSYX_qcom.html" title="class in com.rscja.team.qcom.custom" target="classFrame">UHFCSYX_qcom</a></li>
<li><a href="com/rscja/custom/UHFCSYXForURx.html" title="class in com.rscja.custom" target="classFrame">UHFCSYXForURx</a></li>
<li><a href="com/rscja/team/qcom/uhfhandler/UHFDataHandleBase.html" title="class in com.rscja.team.qcom.uhfhandler" target="classFrame">UHFDataHandleBase</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/UhfLocation_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">UhfLocation_qcom</a></li>
<li><a href="com/rscja/deviceapi/UHFProtocolParseBase.html" title="class in com.rscja.deviceapi" target="classFrame">UHFProtocolParseBase</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/UHFProtocolParseBLE.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">UHFProtocolParseBLE</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/UHFProtocolParseBleByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">UHFProtocolParseBleByJava_qcom</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/UHFProtocolParseByJava.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">UHFProtocolParseByJava</a></li>
<li><a href="com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA4_qcom.html" title="class in com.rscja.team.qcom.rs232utils" target="classFrame">UHFProtocolParseUrA4_qcom</a></li>
<li><a href="com/rscja/team/qcom/rs232utils/UHFProtocolParseUrA8_qcom.html" title="class in com.rscja.team.qcom.rs232utils" target="classFrame">UHFProtocolParseUrA8_qcom</a></li>
<li><a href="com/rscja/team/qcom/rs232utils/UHFProtocolParseUrAxBase_qcom.html" title="class in com.rscja.team.qcom.rs232utils" target="classFrame">UHFProtocolParseUrAxBase_qcom</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/UHFProtocolParseUrxByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">UHFProtocolParseUrxByJava_qcom</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/UHFProtocolParseUSBByJava_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">UHFProtocolParseUSBByJava_qcom</a></li>
<li><a href="com/rscja/team/qcom/rs232utils/UHFProtocolParseUtils_qcom.html" title="class in com.rscja.team.qcom.rs232utils" target="classFrame">UHFProtocolParseUtils_qcom</a></li>
<li><a href="com/rscja/team/qcom/uhfparse/UHFProtocolProtocolParseBase.html" title="class in com.rscja.team.qcom.uhfparse" target="classFrame">UHFProtocolProtocolParseBase</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/UhfRadarLocation_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">UhfRadarLocation_qcom</a></li>
<li><a href="com/rscja/team/qcom/uhfhandler/UHFRxBLEDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler" target="classFrame">UHFRxBLEDataHandle</a></li>
<li><a href="com/rscja/team/qcom/uhfhandler/UHFRxUsbDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler" target="classFrame">UHFRxUsbDataHandle</a></li>
<li><a href="com/rscja/custom/UHFSFForUrxNetwork.html" title="class in com.rscja.custom" target="classFrame">UHFSFForUrxNetwork</a></li>
<li><a href="com/rscja/deviceapi/entity/UHFTAGInfo.html" title="class in com.rscja.deviceapi.entity" target="classFrame">UHFTAGInfo</a></li>
<li><a href="com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.html" title="class in com.rscja.deviceapi.entity" target="classFrame">UHFTAGInfo.ChipInfo</a></li>
<li><a href="com/rscja/deviceapi/entity/UHFTAGInfo.ChipInfo.Builder.html" title="class in com.rscja.deviceapi.entity" target="classFrame">UHFTAGInfo.ChipInfo.Builder</a></li>
<li><a href="com/rscja/custom/UHFTamperAPI.html" title="class in com.rscja.custom" target="classFrame">UHFTamperAPI</a></li>
<li><a href="com/rscja/custom/UHFTamperAPI.TamperInfo.html" title="class in com.rscja.custom" target="classFrame">UHFTamperAPI.TamperInfo</a></li>
<li><a href="com/rscja/team/qcom/custom/UHFTamperAPI_qcom.html" title="class in com.rscja.team.qcom.custom" target="classFrame">UHFTamperAPI_qcom</a></li>
<li><a href="com/rscja/custom/UHFTemperatureSensors.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureSensors</a></li>
<li><a href="com/rscja/custom/UHFTemperatureSensors.IUHFInventoryTemperatureTagCallback.html" title="interface in com.rscja.custom" target="classFrame"><span class="interfaceName">UHFTemperatureSensors.IUHFInventoryTemperatureTagCallback</span></a></li>
<li><a href="com/rscja/custom/UHFTemperatureSensors.TemperatureTag.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureSensors.TemperatureTag</a></li>
<li><a href="com/rscja/custom/UHFTemperatureTag.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureTag</a></li>
<li><a href="com/rscja/custom/UHFTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom" target="classFrame"><span class="interfaceName">UHFTemperatureTag.InventoryTemperatureTagCallback</span></a></li>
<li><a href="com/rscja/custom/UHFTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureTag.TemperatureTagInfo</a></li>
<li><a href="com/rscja/custom/UHFTemperatureTagsAPI.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureTagsAPI</a></li>
<li><a href="com/rscja/custom/UHFTemperatureTagsAPI.IUHFInventoryTempCallback.html" title="interface in com.rscja.custom" target="classFrame"><span class="interfaceName">UHFTemperatureTagsAPI.IUHFInventoryTempCallback</span></a></li>
<li><a href="com/rscja/custom/UHFTemperatureTagsAPI.MultipleTemperatureInfo.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureTagsAPI.MultipleTemperatureInfo</a></li>
<li><a href="com/rscja/custom/UHFTemperatureTagsAPI.TagState.html" title="enum in com.rscja.custom" target="classFrame">UHFTemperatureTagsAPI.TagState</a></li>
<li><a href="com/rscja/custom/UHFTemperatureTagsAPI.TempertureInfo.html" title="class in com.rscja.custom" target="classFrame">UHFTemperatureTagsAPI.TempertureInfo</a></li>
<li><a href="com/rscja/team/mtk/custom/UHFTemperatureTagsAPI_mtk.html" title="class in com.rscja.team.mtk.custom" target="classFrame">UHFTemperatureTagsAPI_mtk</a></li>
<li><a href="com/rscja/team/qcom/custom/UHFTemperatureTagsAPI_qcom.html" title="class in com.rscja.team.qcom.custom" target="classFrame">UHFTemperatureTagsAPI_qcom</a></li>
<li><a href="com/rscja/team/qcom/custom/UHFTemperatureTagsBLEAPI.html" title="class in com.rscja.team.qcom.custom" target="classFrame">UHFTemperatureTagsBLEAPI</a></li>
<li><a href="com/rscja/custom/UHFUartFoxconn.html" title="class in com.rscja.custom" target="classFrame">UHFUartFoxconn</a></li>
<li><a href="com/rscja/team/mtk/custom/UHFUartFoxconn_mtk.html" title="class in com.rscja.team.mtk.custom" target="classFrame">UHFUartFoxconn_mtk</a></li>
<li><a href="com/rscja/team/qcom/custom/UHFUartFoxconn_qcom.html" title="class in com.rscja.team.qcom.custom" target="classFrame">UHFUartFoxconn_qcom</a></li>
<li><a href="com/rscja/team/qcom/rs232utils/UhfUartManage_qcom.html" title="class in com.rscja.team.qcom.rs232utils" target="classFrame">UhfUartManage_qcom</a></li>
<li><a href="com/rscja/custom/UHFUartTemperatureTag.html" title="class in com.rscja.custom" target="classFrame">UHFUartTemperatureTag</a></li>
<li><a href="com/rscja/custom/UHFUartTemperatureTag.InventoryTemperatureTagCallback.html" title="interface in com.rscja.custom" target="classFrame"><span class="interfaceName">UHFUartTemperatureTag.InventoryTemperatureTagCallback</span></a></li>
<li><a href="com/rscja/custom/UHFUartTemperatureTag.TemperatureTagInfo.html" title="class in com.rscja.custom" target="classFrame">UHFUartTemperatureTag.TemperatureTagInfo</a></li>
<li><a href="com/rscja/team/qcom/rs232utils/UhfUartUR4Manage_qcom.html" title="class in com.rscja.team.qcom.rs232utils" target="classFrame">UhfUartUR4Manage_qcom</a></li>
<li><a href="com/rscja/team/qcom/rs232utils/UhfUartUR4Manage_qcom.CheckConnectState.html" title="interface in com.rscja.team.qcom.rs232utils" target="classFrame"><span class="interfaceName">UhfUartUR4Manage_qcom.CheckConnectState</span></a></li>
<li><a href="com/rscja/team/qcom/uhfhandler/UHFUR4DataHandle.html" title="class in com.rscja.team.qcom.uhfhandler" target="classFrame">UHFUR4DataHandle</a></li>
<li><a href="com/rscja/team/qcom/uhfhandler/UHFUrAxDataHandle.html" title="class in com.rscja.team.qcom.uhfhandler" target="classFrame">UHFUrAxDataHandle</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/UHFUrxAutoInventoryTagFactory_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">UHFUrxAutoInventoryTagFactory_qcom</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/UHFUrxNetWorkAutoInventoryTag_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">UHFUrxNetWorkAutoInventoryTag_qcom</a></li>
<li><a href="com/rscja/utility/UhfUtils.html" title="class in com.rscja.utility" target="classFrame">UhfUtils</a></li>
<li><a href="com/rscja/custom/UHFXSAPI.html" title="class in com.rscja.custom" target="classFrame">UHFXSAPI</a></li>
<li><a href="com/rscja/custom/UHFXSAPI.Bank.html" title="enum in com.rscja.custom" target="classFrame">UHFXSAPI.Bank</a></li>
<li><a href="com/rscja/team/qcom/usb/UrxUsb_qcom.html" title="class in com.rscja.team.qcom.usb" target="classFrame">UrxUsb_qcom</a></li>
<li><a href="com/rscja/team/qcom/usb/UsbBase_qcom.html" title="class in com.rscja.team.qcom.usb" target="classFrame">UsbBase_qcom</a></li>
<li><a href="com/rscja/team/qcom/usb/UsbBase_qcom.DataCallback.html" title="interface in com.rscja.team.qcom.usb" target="classFrame"><span class="interfaceName">UsbBase_qcom.DataCallback</span></a></li>
<li><a href="com/rscja/deviceapi/UsbFingerprint.html" title="class in com.rscja.deviceapi" target="classFrame">UsbFingerprint</a></li>
<li><a href="com/rscja/team/mtk/deviceapi/UsbFingerprint_mtk.html" title="class in com.rscja.team.mtk.deviceapi" target="classFrame">UsbFingerprint_mtk</a></li>
<li><a href="com/rscja/team/qcom/deviceapi/UsbFingerprint_qcom.html" title="class in com.rscja.team.qcom.deviceapi" target="classFrame">UsbFingerprint_qcom</a></li>
<li><a href="com/rscja/team/qcom/usb/pl2302/UsbPL2302.html" title="class in com.rscja.team.qcom.usb.pl2302" target="classFrame">UsbPL2302</a></li>
<li><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPort_qcom.html" title="interface in com.rscja.team.qcom.usb.pl2302" target="classFrame"><span class="interfaceName">UsbSerialPort_qcom</span></a></li>
<li><a href="com/rscja/team/qcom/usb/pl2302/UsbSerialPortImpl_qcom.html" title="class in com.rscja.team.qcom.usb.pl2302" target="classFrame">UsbSerialPortImpl_qcom</a></li>
<li><a href="com/rscja/deviceapi/VersionInfo.html" title="class in com.rscja.deviceapi" target="classFrame">VersionInfo</a></li>
<li><a href="com/rscja/deviceapi/entity/WifiConfig.html" title="class in com.rscja.deviceapi.entity" target="classFrame">WifiConfig</a></li>
<li><a href="com/rscja/team/qcom/barcode/symbol/ZebraBarcodeSymbol_qcom.html" title="class in com.rscja.team.qcom.barcode.symbol" target="classFrame">ZebraBarcodeSymbol_qcom</a></li>
</ul>
</div>
</body>
</html>
