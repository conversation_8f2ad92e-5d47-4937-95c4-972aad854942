# 📘 SDK C72 – `IScanner` (QR Code / Code-barres)

## 📦 Interface : `IScanner`
📄 Fichier : `IScanner.html`

### 🔹 Méthode / Constante : `FUNCTION_1D`
```java
static final int FUNCTION_1D
```
📝 1D

### 🔹 Méthode / Constante : `FUNCTION_2D_H`
```java
static final int FUNCTION_2D_H
```
📝 2D硬解码2D Hard_decoding

### 🔹 Méthode / Constante : `FUNCTION_2D`
```java
static final int FUNCTION_2D
```
📝 2D软解码2D soft_decoding

### 🔹 Méthode / Constante : `FUNCTION_14443A`
```java
static final int FUNCTION_14443A
```
📝 14443A

### 🔹 Méthode / Constante : `FUNCTION_15693`
```java
static final int FUNCTION_15693
```
📝 15693

### 🔹 Méthode / Constante : `FUNCTION_LF_ID`
```java
static final int FUNCTION_LF_ID
```
📝 LF-id卡LF_ID card

### 🔹 Méthode / Constante : `FUNCTION_LF_ANIMAL`
```java
static final int FUNCTION_LF_ANIMAL
```
📝 LF-动物标签LF_Animal tag

### 🔹 Méthode / Constante : `FUNCTION_LF_HITAG`
```java
static final int FUNCTION_LF_HITAG
```
📝 LF-hiTag

### 🔹 Méthode / Constante : `FUNCTION_LF_HDX`
```java
static final int FUNCTION_LF_HDX
```
📝 LF-hdx

### 🔹 Méthode / Constante : `FUNCTION_LF_EM4450`
```java
static final int FUNCTION_LF_EM4450
```
📝 LF-EM4450

### 🔹 Méthode / Constante : `FUNCTION_LF_HID`
```java
static final int FUNCTION_LF_HID
```
📝 LF-HID

### 🔹 Méthode / Constante : `FUNCTION_LF_NEEDLE`
```java
static final int FUNCTION_LF_NEEDLE
```
📝 LF二合一模块LF dual_protocol module

### 🔹 Méthode / Constante : `FUNCTION_UHF`
```java
static final int FUNCTION_UHF
```
📝 UHF

### 🔹 Méthode / Constante : `enableFunction`
```java
void enableFunction(android.content.Context context,
                    int function)
```
📝 启用指定功能模块Enable specific function module

### 🔹 Méthode / Constante : `disableFunction`
```java
void disableFunction(android.content.Context context,
                     int function)
```
📝 禁用指定功能模块Disable specific function module

### 🔹 Méthode / Constante : `startScan`
```java
void startScan(android.content.Context context,
               int function)
```
📝 开始扫描或者读卡start scanning or card-reading

### 🔹 Méthode / Constante : `stopScan`
```java
void stopScan(android.content.Context context,
              int function)
```
📝 停止扫描Stop scanning

### 🔹 Méthode / Constante : `setScanOutTime`
```java
void setScanOutTime(android.content.Context context,
                    int time)
```
📝 设置扫码超时时间Setup scan time-out duration

### 🔹 Méthode / Constante : `setContinuousScanRFID`
```java
void setContinuousScanRFID(android.content.Context context,
                           boolean isContinuous)
```
📝 设置UHF连续扫描Setup UHF continuous scann

### 🔹 Méthode / Constante : `setContinuousScanIntervalTimeRFID`
```java
void setContinuousScanIntervalTimeRFID(android.content.Context context,
                                       int intervalTime)
```
📝 设置UHF连续扫描间隔时间Setup UHF continuous scanning intervals

### 🔹 Méthode / Constante : `setContinuousScanTimeOutRFID`
```java
void setContinuousScanTimeOutRFID(android.content.Context context,
                                  int timeOut)
```
📝 设置UHF连续扫描超时时间Setup UHF continuous scanning time-out interval

### 🔹 Méthode / Constante : `setContinuousScan`
```java
void setContinuousScan(android.content.Context context,
                       boolean isContinuous)
```
📝 设置条码连续扫描Setup barcode continuous scanning

### 🔹 Méthode / Constante : `setContinuousScanIntervalTime`
```java
void setContinuousScanIntervalTime(android.content.Context context,
                                   int intervalTime)
```
📝 设置条码连续扫描间隔时间Setup barcode continuous scanning intervals

### 🔹 Méthode / Constante : `setContinuousScanTimeOut`
```java
void setContinuousScanTimeOut(android.content.Context context,
                              int timeOut)
```
📝 设置条码连续扫描超时时间Setup barcode continuous scanning time-out interval

### 🔹 Méthode / Constante : `resetScan`
```java
void resetScan(android.content.Context context)
```
📝 键盘助手恢复出厂设置Restore factory setup

### 🔹 Méthode / Constante : `setScanKey`
```java
void setScanKey(android.content.Context context,
                int type,
                int[] scanKey)
```
📝 设置扫描或者读卡的按键值Setup keycode for barcode scan or card reading

### 🔹 Méthode / Constante : `setScanFailureBroadcast`
```java
void setScanFailureBroadcast(android.content.Context context,
                             boolean enable)
```
📝 扫描失败是否发送广播,接收广播的action和扫描成功的action是同一个Send broadcast when scan failure

### 🔹 Méthode / Constante : `setScanResultBroadcast`
```java
void setScanResultBroadcast(android.content.Context context,
                            java.lang.String broadcastAction,
                            java.lang.String data)
```
📝 设置条码扫描结果接收的广播Setup barcode scanning result

### 🔹 Méthode / Constante : `setScanResultBroadcastRFID`
```java
void setScanResultBroadcastRFID(android.content.Context context,
                                java.lang.String broadcastAction,
                                java.lang.String data)
```
📝 设置RFID扫描结果接收广播Setup RFID scanning result receive broadcast

### 🔹 Méthode / Constante : `setReleaseScan`
```java
void setReleaseScan(android.content.Context context,
                    boolean enable)
```
📝 松开扫描按键是否停止扫描Stop scan after release scan button

### 🔹 Méthode / Constante : `enableBlockScankey`
```java
void enableBlockScankey(android.content.Context context,
                        boolean enable)
```
📝 拦截扫描按键 (备注：键盘助手v2.3.5 之后的版本才支持)Block scan button (Comment: Supports after keyboardemualator v2.3.5 has been released)

### 🔹 Méthode / Constante : `setOnUhfWorkStateListener`
```java
void setOnUhfWorkStateListener(android.content.Context context,OnUhfWorkStateListeneronUhfWorkStateListener)Parameters:context-onUhfWorkStateListener-getLastDecImagevoid getLastDecImage(android.content.Context context)enableScanOnReleasevoid enableScanOnRelease(android.content.Context context,
                         boolean enable)
```
📝 释放扫描按键开始扫描

### 🔹 Méthode / Constante : `setVirtualScanButton`
```java
void setVirtualScanButton(android.content.Context context,
                          int buttonSize)
```
📝 设置虚拟扫描按钮
