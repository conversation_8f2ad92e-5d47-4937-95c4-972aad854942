# Application Mobile – UHFControlApp (Android)

Ce dossier contient l’application mobile Android permettant de scanner des badges RFID/UHF et d’interagir avec le backend du système de contrôle d’accès.

## Fonctionnalités principales
- Lecture/scanner de badges RFID/UHF (SDK Chainway C72)
- Envoi du code EPC au backend via HTTP
- Réception de la réponse (autorisé/refusé)
- Bip sonore à la détection
- Interface de test/configuration (Jetpack Compose)

## Fonctionnalités avancées

- **Serveur HTTP intégré** : l'application lance automatiquement un serveur HTTP local (port 8080) pour recevoir des commandes d'écriture RFID à distance depuis le backend ou le frontend web.
- **Écriture à distance** : le backend envoie une requête POST à `/write-epc` avec `{ "epc": "..." }` pour écrire un EPC sur un badge présenté au lecteur.
- **Bip sonore automatique** : un bip est émis à chaque écriture réussie, qu'elle soit déclenchée localement ou à distance.
- **Compatibilité frontend** : le frontend web permet de saisir l'IP du terminal une seule fois et d'envoyer des commandes d'écriture RFID à distance, avec retour visuel immédiat.

**Pré-requis** :
- Le terminal Android doit être sur le même réseau que le backend.
- L'application doit être lancée et le serveur HTTP démarré.

## Technologies utilisées
- **Kotlin**
- **Jetpack Compose** (UI moderne)
- **OkHttp** (requêtes HTTP)
- **Coroutines** (asynchrone)
- **SDK Chainway C72** (lecture RFID/UHF)

## Installation
1. Ouvrir le dossier `UHFControlApp` dans Android Studio
2. Connecter un appareil Chainway C72 (ou utiliser un émulateur compatible)
3. Compiler et lancer l’application

## Structure des dossiers principaux
- `app/src/main/java/com/example/uhfcontrolapp/` : Code source principal (MainActivity, UI, config…)
- `app/src/main/res/` : Ressources (images, layouts, sons…)
- `app/libs/` : SDK matériel (DeviceAPI)

## Configuration
- L’adresse IP du serveur backend peut être configurée dans l’application (écran de configuration)
- Permissions requises : Bluetooth, Localisation, Internet, Stockage (voir AndroidManifest.xml)

## Utilisation
- Lancer l’application sur l’appareil
- Scanner un badge RFID/UHF
- L’EPC est envoyé automatiquement au backend, la réponse s’affiche à l’écran

## Auteur
- [Ton Nom ou Équipe] 