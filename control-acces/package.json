{"name": "controle-acces-militaire", "version": "1.0.1", "description": "Système de contrôle d'accès pour site militaire avec gestion RFID", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "server:start": "cd server && npm start", "client:build": "cd client && npm run build", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "clean": "rm -rf node_modules client/node_modules server/node_modules", "build:all": "npm run client:build && echo 'Build terminé'"}, "keywords": ["controle-acces", "rfid", "militaire", "express", "react", "postgresql", "securite", "badges", "passages"], "author": "Système de Contrôle d'Accès", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"axios": "^1.10.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/votre-username/control-acces.git"}, "bugs": {"url": "https://github.com/votre-username/control-acces/issues"}, "homepage": "https://github.com/votre-username/control-acces#readme"}