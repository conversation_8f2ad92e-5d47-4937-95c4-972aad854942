--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

-- Started on 2025-07-31 10:37:19

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 5 (class 2615 OID 26743)
-- Name: public; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA public;


ALTER SCHEMA public OWNER TO postgres;

--
-- TOC entry 869 (class 1247 OID 26745)
-- Name: enum_porte_type_acces; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.enum_porte_type_acces AS ENUM (
    'entree',
    'sortie',
    'bidirectionnel'
);


ALTER TYPE public.enum_porte_type_acces OWNER TO postgres;

--
-- TOC entry 240 (class 1255 OID 26924)
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 236 (class 1259 OID 26934)
-- Name: alerte; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.alerte (
    id integer NOT NULL,
    id_passage integer,
    id_personnel integer,
    id_badge integer,
    type_alerte character varying(50) NOT NULL,
    niveau character varying(10) DEFAULT 'orange'::character varying NOT NULL,
    message text NOT NULL,
    statut character varying(20) DEFAULT 'ouvert'::character varying,
    commentaire text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT alerte_niveau_check CHECK (((niveau)::text = ANY ((ARRAY['rouge'::character varying, 'orange'::character varying, 'bleu'::character varying])::text[]))),
    CONSTRAINT alerte_statut_check CHECK (((statut)::text = ANY ((ARRAY['ouvert'::character varying, 'cloture'::character varying])::text[]))),
    CONSTRAINT alerte_type_alerte_check CHECK (((type_alerte)::text = ANY ((ARRAY['badge_inconnu'::character varying, 'badge_invalide'::character varying, 'acces_refuse'::character varying, 'anomalie_passage'::character varying, 'autre'::character varying])::text[])))
);


ALTER TABLE public.alerte OWNER TO postgres;

--
-- TOC entry 235 (class 1259 OID 26933)
-- Name: alerte_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.alerte_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.alerte_id_seq OWNER TO postgres;

--
-- TOC entry 5068 (class 0 OID 0)
-- Dependencies: 235
-- Name: alerte_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.alerte_id_seq OWNED BY public.alerte.id;


--
-- TOC entry 230 (class 1259 OID 26858)
-- Name: attribution_badge; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.attribution_badge (
    id integer NOT NULL,
    id_personnel integer NOT NULL,
    id_badge integer NOT NULL,
    date_attribution timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    date_fin timestamp without time zone,
    statut character varying(20) DEFAULT 'actif'::character varying,
    destination integer,
    objet_visite text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT attribution_badge_statut_check CHECK (((statut)::text = ANY ((ARRAY['actif'::character varying, 'expire'::character varying, 'desactive'::character varying])::text[])))
);


ALTER TABLE public.attribution_badge OWNER TO postgres;

--
-- TOC entry 229 (class 1259 OID 26857)
-- Name: attribution_badge_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.attribution_badge_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.attribution_badge_id_seq OWNER TO postgres;

--
-- TOC entry 5069 (class 0 OID 0)
-- Dependencies: 229
-- Name: attribution_badge_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.attribution_badge_id_seq OWNED BY public.attribution_badge.id;


--
-- TOC entry 228 (class 1259 OID 26838)
-- Name: badge; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.badge (
    id integer NOT NULL,
    epc_code character varying(100) NOT NULL,
    id_type_badge integer NOT NULL,
    permanent boolean DEFAULT false,
    actif boolean DEFAULT true,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    numero_visuel character varying(20)
);


ALTER TABLE public.badge OWNER TO postgres;

--
-- TOC entry 227 (class 1259 OID 26837)
-- Name: badge_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.badge_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.badge_id_seq OWNER TO postgres;

--
-- TOC entry 5070 (class 0 OID 0)
-- Dependencies: 227
-- Name: badge_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.badge_id_seq OWNED BY public.badge.id;


--
-- TOC entry 238 (class 1259 OID 26988)
-- Name: civil_info; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.civil_info (
    id_personnel integer NOT NULL,
    horaire_entree timestamp with time zone,
    objet_visite text,
    destination integer,
    societe character varying(100),
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.civil_info OWNER TO postgres;

--
-- TOC entry 220 (class 1259 OID 26765)
-- Name: grade; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.grade (
    id integer NOT NULL,
    nom_grade character varying(50) NOT NULL,
    niveau integer NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.grade OWNER TO postgres;

--
-- TOC entry 219 (class 1259 OID 26764)
-- Name: grade_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.grade_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.grade_id_seq OWNER TO postgres;

--
-- TOC entry 5071 (class 0 OID 0)
-- Dependencies: 219
-- Name: grade_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.grade_id_seq OWNED BY public.grade.id;


--
-- TOC entry 237 (class 1259 OID 26966)
-- Name: militaire_externe_info; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.militaire_externe_info (
    id_personnel integer NOT NULL,
    horaire_entree timestamp with time zone,
    objet_visite text,
    destination integer,
    id_unite_origine integer,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.militaire_externe_info OWNER TO postgres;

--
-- TOC entry 234 (class 1259 OID 26903)
-- Name: passage; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.passage (
    id integer NOT NULL,
    id_badge integer,
    id_porte integer NOT NULL,
    date_acces timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    type_acces character varying(10) NOT NULL,
    resultat character varying(20) DEFAULT 'autorise'::character varying,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    epc_code character varying(100),
    motif_refus text,
    CONSTRAINT passage_resultat_check CHECK (((resultat)::text = ANY ((ARRAY['autorise'::character varying, 'refuse'::character varying, 'erreur'::character varying])::text[]))),
    CONSTRAINT passage_type_acces_check CHECK (((type_acces)::text = ANY ((ARRAY['entree'::character varying, 'sortie'::character varying])::text[])))
);


ALTER TABLE public.passage OWNER TO postgres;

--
-- TOC entry 233 (class 1259 OID 26902)
-- Name: passage_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.passage_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.passage_id_seq OWNER TO postgres;

--
-- TOC entry 5072 (class 0 OID 0)
-- Dependencies: 233
-- Name: passage_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.passage_id_seq OWNED BY public.passage.id;


--
-- TOC entry 224 (class 1259 OID 26791)
-- Name: personnel; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.personnel (
    id integer NOT NULL,
    nom character varying(100) NOT NULL,
    prenom character varying(100) NOT NULL,
    matricule character varying(50),
    cin character varying(20),
    id_type_personnel integer NOT NULL,
    id_grade integer,
    id_unite integer,
    id_unite_origine integer,
    societe character varying(100),
    photo text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.personnel OWNER TO postgres;

--
-- TOC entry 223 (class 1259 OID 26790)
-- Name: personnel_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.personnel_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.personnel_id_seq OWNER TO postgres;

--
-- TOC entry 5073 (class 0 OID 0)
-- Dependencies: 223
-- Name: personnel_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.personnel_id_seq OWNED BY public.personnel.id;


--
-- TOC entry 232 (class 1259 OID 26890)
-- Name: porte; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.porte (
    id integer NOT NULL,
    libelle character varying(100) NOT NULL,
    description text,
    localisation character varying(200),
    type_acces public.enum_porte_type_acces DEFAULT 'entree'::public.enum_porte_type_acces,
    actif boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.porte OWNER TO postgres;

--
-- TOC entry 231 (class 1259 OID 26889)
-- Name: porte_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.porte_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.porte_id_seq OWNER TO postgres;

--
-- TOC entry 5074 (class 0 OID 0)
-- Dependencies: 231
-- Name: porte_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.porte_id_seq OWNED BY public.porte.id;


--
-- TOC entry 226 (class 1259 OID 26826)
-- Name: type_badge; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.type_badge (
    id integer NOT NULL,
    nom_type_badge character varying(50) NOT NULL,
    description text,
    couleur character varying(7) DEFAULT '#3B82F6'::character varying,
    duree_validite integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.type_badge OWNER TO postgres;

--
-- TOC entry 225 (class 1259 OID 26825)
-- Name: type_badge_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.type_badge_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.type_badge_id_seq OWNER TO postgres;

--
-- TOC entry 5075 (class 0 OID 0)
-- Dependencies: 225
-- Name: type_badge_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.type_badge_id_seq OWNED BY public.type_badge.id;


--
-- TOC entry 218 (class 1259 OID 26752)
-- Name: type_personnel; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.type_personnel (
    id integer NOT NULL,
    nom_type character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.type_personnel OWNER TO postgres;

--
-- TOC entry 217 (class 1259 OID 26751)
-- Name: type_personnel_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.type_personnel_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.type_personnel_id_seq OWNER TO postgres;

--
-- TOC entry 5076 (class 0 OID 0)
-- Dependencies: 217
-- Name: type_personnel_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.type_personnel_id_seq OWNED BY public.type_personnel.id;


--
-- TOC entry 222 (class 1259 OID 26778)
-- Name: unite; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.unite (
    id integer NOT NULL,
    nom_unite character varying(100) NOT NULL,
    code_unite character varying(20),
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.unite OWNER TO postgres;

--
-- TOC entry 221 (class 1259 OID 26777)
-- Name: unite_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.unite_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.unite_id_seq OWNER TO postgres;

--
-- TOC entry 5077 (class 0 OID 0)
-- Dependencies: 221
-- Name: unite_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.unite_id_seq OWNED BY public.unite.id;


--
-- TOC entry 239 (class 1259 OID 34328)
-- Name: v_tracabilite_simple; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.v_tracabilite_simple AS
 SELECT p.nom,
    p.prenom,
    p.cin,
    p.matricule,
    p.societe,
    tp.nom_type AS type_personnel,
    b.numero_visuel,
    b.epc_code,
    tb.nom_type_badge,
    ab.date_attribution AS heure_entree,
    ab.date_fin AS heure_sortie,
    ab.objet_visite,
    u.nom_unite AS destination,
    ab.statut,
        CASE
            WHEN (ab.date_fin IS NULL) THEN (EXTRACT(epoch FROM (CURRENT_TIMESTAMP - (ab.date_attribution)::timestamp with time zone)) / (3600)::numeric)
            ELSE (EXTRACT(epoch FROM (ab.date_fin - ab.date_attribution)) / (3600)::numeric)
        END AS duree_visite_heures
   FROM (((((public.attribution_badge ab
     JOIN public.personnel p ON ((ab.id_personnel = p.id)))
     JOIN public.badge b ON ((ab.id_badge = b.id)))
     JOIN public.type_badge tb ON ((b.id_type_badge = tb.id)))
     JOIN public.type_personnel tp ON ((p.id_type_personnel = tp.id)))
     LEFT JOIN public.unite u ON ((ab.destination = u.id)))
  WHERE (p.id_type_personnel = ANY (ARRAY[2, 3]))
  ORDER BY ab.date_attribution DESC;


ALTER VIEW public.v_tracabilite_simple OWNER TO postgres;

--
-- TOC entry 4839 (class 2604 OID 26937)
-- Name: alerte id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.alerte ALTER COLUMN id SET DEFAULT nextval('public.alerte_id_seq'::regclass);


--
-- TOC entry 4824 (class 2604 OID 26861)
-- Name: attribution_badge id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attribution_badge ALTER COLUMN id SET DEFAULT nextval('public.attribution_badge_id_seq'::regclass);


--
-- TOC entry 4819 (class 2604 OID 26841)
-- Name: badge id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.badge ALTER COLUMN id SET DEFAULT nextval('public.badge_id_seq'::regclass);


--
-- TOC entry 4806 (class 2604 OID 26768)
-- Name: grade id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.grade ALTER COLUMN id SET DEFAULT nextval('public.grade_id_seq'::regclass);


--
-- TOC entry 4834 (class 2604 OID 26906)
-- Name: passage id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.passage ALTER COLUMN id SET DEFAULT nextval('public.passage_id_seq'::regclass);


--
-- TOC entry 4812 (class 2604 OID 26794)
-- Name: personnel id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.personnel ALTER COLUMN id SET DEFAULT nextval('public.personnel_id_seq'::regclass);


--
-- TOC entry 4829 (class 2604 OID 26893)
-- Name: porte id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.porte ALTER COLUMN id SET DEFAULT nextval('public.porte_id_seq'::regclass);


--
-- TOC entry 4815 (class 2604 OID 26829)
-- Name: type_badge id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.type_badge ALTER COLUMN id SET DEFAULT nextval('public.type_badge_id_seq'::regclass);


--
-- TOC entry 4803 (class 2604 OID 26755)
-- Name: type_personnel id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.type_personnel ALTER COLUMN id SET DEFAULT nextval('public.type_personnel_id_seq'::regclass);


--
-- TOC entry 4809 (class 2604 OID 26781)
-- Name: unite id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.unite ALTER COLUMN id SET DEFAULT nextval('public.unite_id_seq'::regclass);


--
-- TOC entry 4884 (class 2606 OID 26948)
-- Name: alerte alerte_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.alerte
    ADD CONSTRAINT alerte_pkey PRIMARY KEY (id);


--
-- TOC entry 4876 (class 2606 OID 26870)
-- Name: attribution_badge attribution_badge_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attribution_badge
    ADD CONSTRAINT attribution_badge_pkey PRIMARY KEY (id);


--
-- TOC entry 4871 (class 2606 OID 26851)
-- Name: badge badge_epc_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.badge
    ADD CONSTRAINT badge_epc_code_key UNIQUE (epc_code);


--
-- TOC entry 4873 (class 2606 OID 26849)
-- Name: badge badge_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.badge
    ADD CONSTRAINT badge_pkey PRIMARY KEY (id);


--
-- TOC entry 4888 (class 2606 OID 26994)
-- Name: civil_info civil_info_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.civil_info
    ADD CONSTRAINT civil_info_pkey PRIMARY KEY (id_personnel);


--
-- TOC entry 4855 (class 2606 OID 26776)
-- Name: grade grade_nom_grade_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.grade
    ADD CONSTRAINT grade_nom_grade_key UNIQUE (nom_grade);


--
-- TOC entry 4857 (class 2606 OID 26774)
-- Name: grade grade_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.grade
    ADD CONSTRAINT grade_pkey PRIMARY KEY (id);


--
-- TOC entry 4886 (class 2606 OID 26972)
-- Name: militaire_externe_info militaire_externe_info_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.militaire_externe_info
    ADD CONSTRAINT militaire_externe_info_pkey PRIMARY KEY (id_personnel);


--
-- TOC entry 4882 (class 2606 OID 26913)
-- Name: passage passage_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.passage
    ADD CONSTRAINT passage_pkey PRIMARY KEY (id);


--
-- TOC entry 4863 (class 2606 OID 26804)
-- Name: personnel personnel_cin_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.personnel
    ADD CONSTRAINT personnel_cin_key UNIQUE (cin);


--
-- TOC entry 4865 (class 2606 OID 26802)
-- Name: personnel personnel_matricule_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.personnel
    ADD CONSTRAINT personnel_matricule_key UNIQUE (matricule);


--
-- TOC entry 4867 (class 2606 OID 26800)
-- Name: personnel personnel_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.personnel
    ADD CONSTRAINT personnel_pkey PRIMARY KEY (id);


--
-- TOC entry 4880 (class 2606 OID 26901)
-- Name: porte porte_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.porte
    ADD CONSTRAINT porte_pkey PRIMARY KEY (id);


--
-- TOC entry 4869 (class 2606 OID 26836)
-- Name: type_badge type_badge_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.type_badge
    ADD CONSTRAINT type_badge_pkey PRIMARY KEY (id);


--
-- TOC entry 4851 (class 2606 OID 26763)
-- Name: type_personnel type_personnel_nom_type_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.type_personnel
    ADD CONSTRAINT type_personnel_nom_type_key UNIQUE (nom_type);


--
-- TOC entry 4853 (class 2606 OID 26761)
-- Name: type_personnel type_personnel_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.type_personnel
    ADD CONSTRAINT type_personnel_pkey PRIMARY KEY (id);


--
-- TOC entry 4878 (class 2606 OID 26872)
-- Name: attribution_badge unique_badge_actif; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attribution_badge
    ADD CONSTRAINT unique_badge_actif UNIQUE (id_badge, statut) DEFERRABLE INITIALLY DEFERRED;


--
-- TOC entry 4859 (class 2606 OID 26789)
-- Name: unite unite_code_unite_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.unite
    ADD CONSTRAINT unite_code_unite_key UNIQUE (code_unite);


--
-- TOC entry 4861 (class 2606 OID 26787)
-- Name: unite unite_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.unite
    ADD CONSTRAINT unite_pkey PRIMARY KEY (id);


--
-- TOC entry 4874 (class 1259 OID 27012)
-- Name: attribution_badge_id_badge_statut; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX attribution_badge_id_badge_statut ON public.attribution_badge USING btree (id_badge, statut);


--
-- TOC entry 4915 (class 2620 OID 26964)
-- Name: alerte update_alerte_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_alerte_updated_at BEFORE UPDATE ON public.alerte FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- TOC entry 4913 (class 2620 OID 26928)
-- Name: attribution_badge update_attribution_badge_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_attribution_badge_updated_at BEFORE UPDATE ON public.attribution_badge FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- TOC entry 4912 (class 2620 OID 26926)
-- Name: badge update_badge_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_badge_updated_at BEFORE UPDATE ON public.badge FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- TOC entry 4908 (class 2620 OID 26930)
-- Name: grade update_grade_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_grade_updated_at BEFORE UPDATE ON public.grade FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- TOC entry 4910 (class 2620 OID 26925)
-- Name: personnel update_personnel_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_personnel_updated_at BEFORE UPDATE ON public.personnel FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- TOC entry 4914 (class 2620 OID 26932)
-- Name: porte update_porte_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_porte_updated_at BEFORE UPDATE ON public.porte FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- TOC entry 4911 (class 2620 OID 26927)
-- Name: type_badge update_type_badge_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_type_badge_updated_at BEFORE UPDATE ON public.type_badge FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- TOC entry 4907 (class 2620 OID 26931)
-- Name: type_personnel update_type_personnel_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_type_personnel_updated_at BEFORE UPDATE ON public.type_personnel FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- TOC entry 4909 (class 2620 OID 26929)
-- Name: unite update_unite_updated_at; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER update_unite_updated_at BEFORE UPDATE ON public.unite FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- TOC entry 4899 (class 2606 OID 26959)
-- Name: alerte alerte_id_badge_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.alerte
    ADD CONSTRAINT alerte_id_badge_fkey FOREIGN KEY (id_badge) REFERENCES public.badge(id) ON DELETE SET NULL;


--
-- TOC entry 4900 (class 2606 OID 26949)
-- Name: alerte alerte_id_passage_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.alerte
    ADD CONSTRAINT alerte_id_passage_fkey FOREIGN KEY (id_passage) REFERENCES public.passage(id) ON DELETE SET NULL;


--
-- TOC entry 4901 (class 2606 OID 26954)
-- Name: alerte alerte_id_personnel_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.alerte
    ADD CONSTRAINT alerte_id_personnel_fkey FOREIGN KEY (id_personnel) REFERENCES public.personnel(id) ON DELETE SET NULL;


--
-- TOC entry 4894 (class 2606 OID 26884)
-- Name: attribution_badge attribution_badge_destination_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attribution_badge
    ADD CONSTRAINT attribution_badge_destination_fkey FOREIGN KEY (destination) REFERENCES public.unite(id);


--
-- TOC entry 4895 (class 2606 OID 26879)
-- Name: attribution_badge attribution_badge_id_badge_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attribution_badge
    ADD CONSTRAINT attribution_badge_id_badge_fkey FOREIGN KEY (id_badge) REFERENCES public.badge(id);


--
-- TOC entry 4896 (class 2606 OID 26874)
-- Name: attribution_badge attribution_badge_id_personnel_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.attribution_badge
    ADD CONSTRAINT attribution_badge_id_personnel_fkey FOREIGN KEY (id_personnel) REFERENCES public.personnel(id);


--
-- TOC entry 4893 (class 2606 OID 26852)
-- Name: badge badge_id_type_badge_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.badge
    ADD CONSTRAINT badge_id_type_badge_fkey FOREIGN KEY (id_type_badge) REFERENCES public.type_badge(id);


--
-- TOC entry 4905 (class 2606 OID 27000)
-- Name: civil_info civil_info_destination_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.civil_info
    ADD CONSTRAINT civil_info_destination_fkey FOREIGN KEY (destination) REFERENCES public.unite(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4906 (class 2606 OID 26995)
-- Name: civil_info civil_info_id_personnel_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.civil_info
    ADD CONSTRAINT civil_info_id_personnel_fkey FOREIGN KEY (id_personnel) REFERENCES public.personnel(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4902 (class 2606 OID 26978)
-- Name: militaire_externe_info militaire_externe_info_destination_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.militaire_externe_info
    ADD CONSTRAINT militaire_externe_info_destination_fkey FOREIGN KEY (destination) REFERENCES public.unite(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4903 (class 2606 OID 26973)
-- Name: militaire_externe_info militaire_externe_info_id_personnel_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.militaire_externe_info
    ADD CONSTRAINT militaire_externe_info_id_personnel_fkey FOREIGN KEY (id_personnel) REFERENCES public.personnel(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- TOC entry 4904 (class 2606 OID 26983)
-- Name: militaire_externe_info militaire_externe_info_id_unite_origine_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.militaire_externe_info
    ADD CONSTRAINT militaire_externe_info_id_unite_origine_fkey FOREIGN KEY (id_unite_origine) REFERENCES public.unite(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4897 (class 2606 OID 26914)
-- Name: passage passage_id_badge_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.passage
    ADD CONSTRAINT passage_id_badge_fkey FOREIGN KEY (id_badge) REFERENCES public.badge(id);


--
-- TOC entry 4898 (class 2606 OID 26919)
-- Name: passage passage_id_porte_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.passage
    ADD CONSTRAINT passage_id_porte_fkey FOREIGN KEY (id_porte) REFERENCES public.porte(id);


--
-- TOC entry 4889 (class 2606 OID 26810)
-- Name: personnel personnel_id_grade_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.personnel
    ADD CONSTRAINT personnel_id_grade_fkey FOREIGN KEY (id_grade) REFERENCES public.grade(id);


--
-- TOC entry 4890 (class 2606 OID 26805)
-- Name: personnel personnel_id_type_personnel_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.personnel
    ADD CONSTRAINT personnel_id_type_personnel_fkey FOREIGN KEY (id_type_personnel) REFERENCES public.type_personnel(id);


--
-- TOC entry 4891 (class 2606 OID 26815)
-- Name: personnel personnel_id_unite_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.personnel
    ADD CONSTRAINT personnel_id_unite_fkey FOREIGN KEY (id_unite) REFERENCES public.unite(id);


--
-- TOC entry 4892 (class 2606 OID 26820)
-- Name: personnel personnel_id_unite_origine_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.personnel
    ADD CONSTRAINT personnel_id_unite_origine_fkey FOREIGN KEY (id_unite_origine) REFERENCES public.unite(id);


--
-- TOC entry 5067 (class 0 OID 0)
-- Dependencies: 5
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: postgres
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;


-- Completed on 2025-07-31 10:37:19

--
-- PostgreSQL database dump complete
--

