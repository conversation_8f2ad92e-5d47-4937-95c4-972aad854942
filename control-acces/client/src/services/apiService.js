import axios from 'axios'

// Utiliser des URLs relatives pour passer par le proxy Vite en développement
const WS_URL = 'ws://localhost:3012'

// Configuration Axios
const api = axios.create({
  baseURL: 'http://localhost:3011/api',
  timeout: 10000
  // On ne met pas de Content-Type global ici pour permettre l'upload de fichiers
})

// Intercepteur pour les erreurs
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Erreur API:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// Services API
export const apiService = {
  // Test de connexion
  test: () => api.get('/test'),
  
  // Personnel
  createPersonnel: (data) => {
    // Si data est un FormData, ne pas mettre de Content-Type
    if (data instanceof FormData) {
      return api.post('/personnel', data);
    } else {
      return api.post('/personnel', data, {
        headers: { 'Content-Type': 'application/json' }
      });
    }
  },
  updatePersonnel: (id, data) => {
    // Si data est un FormData, ne pas mettre de Content-Type
    if (data instanceof FormData) {
      return api.put(`/personnel/${id}`, data);
    } else {
      return api.put(`/personnel/${id}`, data, {
        headers: { 'Content-Type': 'application/json' }
      });
    }
  },
  getPersonnel: () => api.get('/personnel'),
  deletePersonnel: (id) => api.delete(`/personnel/${id}`),
  getPersonnelGrades: () => api.get('/personnel/grades'),
  getPersonnelUnites: () => api.get('/personnel/unites'),
  searchUnites: (search) => api.get('/personnel/unites', { params: { search } }),
  createUnite: (data) => api.post('/personnel/unites', data),
  
  // Visiteurs
  getVisiteurs: () => api.get('/visiteurs'),
  createVisiteur: (data) => {
    if (data instanceof FormData) {
      return api.post('/visiteurs', data);
    } else {
      return api.post('/visiteurs', data, {
        headers: { 'Content-Type': 'application/json' }
      });
    }
  },
  updateVisiteur: (id, data) => api.put(`/visiteurs/${id}`, data),
  deleteVisiteur: (id) => api.delete(`/visiteurs/${id}`),
  terminerVisite: (id) => api.post(`/visiteurs/${id}/terminer`),

  // Badges
  attributeBadge: (data) => api.post('/badges/attribuer', data),
  getBadges: () => api.get('/badges'),
  getBadgesDisponibles: (typeVisiteur) => api.get(`/badges/disponibles?type=${typeVisiteur}`),
  createBadgePermanent: (data) => api.post('/badges/permanent', data),
  createBadgeMultitache: (data) => api.post('/badges/multitache', data),
  getTypesBadges: () => api.get('/badges/types'),
  createTypeBadge: (data) => api.post('/badges/types', data),
  deleteBadge: (id) => api.delete(`/badges/${id}`),
  writeBadgeEpc: (id, ip) => api.post(`/badges/${id}/write`, { ip }),
  
  // Passages
  getPassages: (params = {}) => api.get('/passages', { params }),
  deletePassage: (id) => api.delete(`/passages/${id}`),
  deletePassages: (ids) => api.post('/passages/delete-multiple', { ids }),
  
  // Statistiques
  getStats: () => api.get('/stats')
}

// WebSocket pour le temps réel
export class WebSocketService {
  constructor() {
    this.ws = null
    this.listeners = new Map()
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 1000
  }

  connect() {
    try {
      this.ws = new WebSocket(WS_URL)
      
      this.ws.onopen = () => {
        console.log('📡 WebSocket connecté')
        this.reconnectAttempts = 0
        this.notifyListeners('connection', { status: 'connected' })
      }

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          console.log('📡 Message WebSocket reçu:', data)
          this.notifyListeners(data.type, data.data)
        } catch (error) {
          console.error('Erreur parsing message WebSocket:', error)
        }
      }

      this.ws.onclose = () => {
        console.log('📡 WebSocket déconnecté')
        this.notifyListeners('connection', { status: 'disconnected' })
        this.attemptReconnect()
      }

      this.ws.onerror = (error) => {
        console.error('Erreur WebSocket:', error)
        this.notifyListeners('connection', { status: 'error', error })
      }

    } catch (error) {
      console.error('Erreur connexion WebSocket:', error)
      this.attemptReconnect()
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`🔄 Tentative de reconnexion ${this.reconnectAttempts}/${this.maxReconnectAttempts}`)
      
      setTimeout(() => {
        this.connect()
      }, this.reconnectDelay * this.reconnectAttempts)
    } else {
      console.error('❌ Impossible de se reconnecter au WebSocket')
      this.notifyListeners('connection', { status: 'failed' })
    }
  }

  subscribe(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set())
    }
    this.listeners.get(eventType).add(callback)

    // Retourner une fonction de désabonnement
    return () => {
      const callbacks = this.listeners.get(eventType)
      if (callbacks) {
        callbacks.delete(callback)
        if (callbacks.size === 0) {
          this.listeners.delete(eventType)
        }
      }
    }
  }

  notifyListeners(eventType, data) {
    const callbacks = this.listeners.get(eventType)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('Erreur dans callback WebSocket:', error)
        }
      })
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    this.listeners.clear()
  }

  getConnectionStatus() {
    if (!this.ws) return 'disconnected'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'connecting'
      case WebSocket.OPEN: return 'connected'
      case WebSocket.CLOSING: return 'closing'
      case WebSocket.CLOSED: return 'disconnected'
      default: return 'unknown'
    }
  }
}

// Instance globale du WebSocket
export const wsService = new WebSocketService()

// Utilitaires
export const formatDate = (date) => {
  return new Date(date).toLocaleString('fr-FR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

export const formatTime = (date) => {
  return new Date(date).toLocaleTimeString('fr-FR', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

export const getStatusColor = (status) => {
  switch (status) {
    case 'autorise': return 'text-green-600 bg-green-50'
    case 'refuse': return 'text-red-600 bg-red-50'
    case 'erreur': return 'text-yellow-600 bg-yellow-50'
    default: return 'text-gray-600 bg-gray-50'
  }
}

export const getStatusIcon = (status) => {
  switch (status) {
    case 'autorise': return '✅'
    case 'refuse': return '❌'
    case 'erreur': return '⚠️'
    default: return '❓'
  }
}
