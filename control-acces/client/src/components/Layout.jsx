import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, Users, CreditCard, Calendar, Menu, Wifi, WifiOff, Shield } from 'lucide-react';
import { wsService } from '../services/apiService';

const menuItems = [
  { path: '/', icon: Home, label: 'Tableau de bord', description: 'Vue générale' },
  { path: '/personnel', icon: Users, label: 'Personnel Interne', description: 'Gestion personnel interne' },
  { path: '/visiteurs', icon: Shield, label: 'Visiteurs', description: 'Gestion visiteurs EM/ZS' },
  { path: '/badges', icon: CreditCard, label: 'Badges', description: 'Gestion des badges' },
  { path: '/passages', icon: Calendar, label: 'Passages', description: 'Journal des passages' },
  // Ajoute d'autres liens si besoin
];

function Layout({ children }) {
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [currentTime, setCurrentTime] = useState(new Date());
  // const { user, logout } = useAuth() // décommente si AuthContext existe
  // const user = { nom: 'Dupont', prenom: 'Jean', role: 'admin' }; // temporaire si pas d'auth

  useEffect(() => {
    wsService.connect();
    const unsubscribe = wsService.subscribe('connection', (data) => {
      setConnectionStatus(data.status);
    });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'connected': return <Wifi className="w-4 h-4 text-green-600" />;
      case 'connecting': return <Wifi className="w-4 h-4 text-yellow-600 animate-pulse" />;
      default: return <WifiOff className="w-4 h-4 text-red-600" />;
    }
  };

  const getConnectionText = () => {
    switch (connectionStatus) {
      case 'connected': return 'Temps réel actif';
      case 'connecting': return 'Connexion...';
      case 'disconnected': return 'Hors ligne';
      case 'error': return 'Erreur connexion';
      case 'failed': return 'Connexion échouée';
      default: return 'État inconnu';
    }
  };

  const formatTime = (date) => date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  const formatDate = (date) => date.toLocaleDateString('fr-FR', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });

  return (
    <div className="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
      {/* Header global, fixed en haut */}
      <header className="fixed top-0 left-0 right-0 z-40 bg-white shadow-sm border-b border-gray-200 h-16 flex items-center px-4 md:px-8">
        <button className="md:hidden p-2 rounded hover:bg-gray-100 mr-2" onClick={() => setSidebarOpen(true)}>
          <Menu className="w-6 h-6 text-blue-600" />
        </button>
        <Shield className="h-8 w-8 text-blue-500 mr-2" />
        <span className="text-2xl font-bold text-gray-900 tracking-tight">Accès Sécurisé</span>
        <div className="ml-auto flex items-center space-x-6">
          <div className="hidden md:flex flex-col items-end text-right">
            <span className="text-xs text-gray-500">{formatDate(currentTime)}</span>
            <span className="text-sm font-semibold text-gray-800">{formatTime(currentTime)}</span>
          </div>
          <div className="flex items-center space-x-2">
            {getConnectionIcon()}
            <span className={`text-xs font-medium ${
              connectionStatus === 'connected' ? 'text-green-600' : 
              connectionStatus === 'connecting' ? 'text-yellow-600' : 
              'text-red-600'
            }`}>
              {getConnectionText()}
            </span>
          </div>
        </div>
      </header>

      {/* Sidebar, fixed à gauche sous le header */}
      <aside className={`fixed top-16 left-0 z-30 w-64 h-[calc(100vh-4rem)] bg-gray-900 text-white transition-transform duration-300 ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} md:translate-x-0 md:block`}>
        <div className="p-6 border-b border-gray-800 flex items-center space-x-3">
          <Shield className="h-7 w-7 text-blue-400" />
          <div>
            <div className="text-lg font-bold tracking-tight">Accès Sécurisé</div>
            <div className="text-xs text-gray-400">Système de contrôle</div>
          </div>
        </div>
        <nav className="p-4">
          <div className="space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors group ${
                    isActive
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`}
                  onClick={() => setSidebarOpen(false)}
                >
                  <Icon className="h-5 w-5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium">{item.label}</div>
                    <div className="text-xs text-gray-400 group-hover:text-gray-300">{item.description}</div>
                  </div>
                </Link>
              );
            })}
          </div>
        </nav>
      </aside>
      {/* Overlay mobile */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-20 bg-black bg-opacity-40 md:hidden" onClick={() => setSidebarOpen(false)} />
      )}

      {/* Main content, margin-left sur desktop pour ne pas passer sous la sidebar */}
      <main className="pt-20 px-4 md:px-8 transition-all duration-300 md:ml-64">
        {children}
      </main>
    </div>
  );
}

export default Layout;
