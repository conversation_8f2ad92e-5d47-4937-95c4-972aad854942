import { useState, useEffect, useRef } from 'react';
import { apiService } from '../services/apiService';

function AddVisiteurForm({ onSuccess }) {
  const [typeVisiteur, setTypeVisiteur] = useState('militaire_externe');
  const [formData, setFormData] = useState({
    nom: '',
    prenom: '',
    cin: '',
    matricule: '',
    id_grade: '',
    id_unite_origine: '',
    societe: '',
    destination: '',
    objet_visite: '',
    id_badge: '',
    photo: null
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [grades, setGrades] = useState([]);
  const [unites, setUnites] = useState([]);
  const [badges, setBadges] = useState([]);
  
  // États pour l'autocomplete des unités d'origine
  const [uniteOrigineInput, setUniteOrigineInput] = useState('');
  const [uniteOrigineSuggestions, setUniteOrigineSuggestions] = useState([]);
  const [showUniteOrigineSuggestions, setShowUniteOrigineSuggestions] = useState(false);
  const uniteOrigineInputRef = useRef();

  // Charger les données de référence
  useEffect(() => {
    const loadReferenceData = async () => {
      try {
        const [gradesRes, unitesRes] = await Promise.all([
          apiService.getPersonnelGrades(),
          apiService.getPersonnelUnites()
        ]);
        setGrades(gradesRes.data);
        setUnites(unitesRes.data);
      } catch (err) {
        console.error('Erreur lors du chargement des données:', err);
        setError('Erreur lors du chargement des données de référence');
      }
    };

    loadReferenceData();
  }, []);

  // Charger les badges selon le type de visiteur
  useEffect(() => {
    const loadBadges = async () => {
      try {
        const response = await apiService.getBadgesDisponibles(typeVisiteur);
        setBadges(response.data || []);
      } catch (err) {
        console.error('Erreur lors du chargement des badges:', err);
        setBadges([]);
      }
    };

    loadBadges();
  }, [typeVisiteur]);

  // Recherche dynamique d'unités d'origine
  useEffect(() => {
    if (uniteOrigineInput.length > 0) {
      apiService.searchUnites(uniteOrigineInput)
        .then(res => setUniteOrigineSuggestions(res.data))
        .catch(() => setUniteOrigineSuggestions([]));
    } else {
      setUniteOrigineSuggestions([]);
    }
  }, [uniteOrigineInput]);

  // Gestion des changements de type
  const handleTypeChange = (e) => {
    setTypeVisiteur(e.target.value);
    // Reset des champs spécifiques
    setFormData(prev => ({
      ...prev,
      cin: '',
      matricule: '',
      id_grade: '',
      id_unite_origine: '',
      societe: '',
      id_badge: ''
    }));
    setUniteOrigineInput('');
  };

  const handleChange = (e) => {
    const { name, value, files } = e.target;
    if (name === 'photo') {
      setFormData({ ...formData, [name]: files[0] });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  // Sélection d'une unité d'origine
  const handleUniteOrigineSelect = (unite) => {
    setFormData({ ...formData, id_unite_origine: unite.id });
    setUniteOrigineInput(unite.nom_unite);
    setShowUniteOrigineSuggestions(false);
  };

  // Ajout d'une nouvelle unité d'origine
  const handleAddUniteOrigine = async () => {
    if (!uniteOrigineInput.trim()) return;
    try {
      const res = await apiService.createUnite({ nom_unite: uniteOrigineInput });
      setFormData({ ...formData, id_unite_origine: res.data.id });
      setUniteOrigineInput(res.data.nom_unite);
      setShowUniteOrigineSuggestions(false);
    } catch {
      alert("Erreur lors de l'ajout de l'unité d'origine");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const data = new FormData();
      data.append('nom', formData.nom);
      data.append('prenom', formData.prenom);
      data.append('type', typeVisiteur);
      data.append('destination', formData.destination);
      data.append('objet_visite', formData.objet_visite);
      data.append('id_badge', formData.id_badge);
      data.append('horaire_entree', new Date().toISOString());

      if (typeVisiteur === 'militaire_externe') {
        data.append('cin', formData.cin);
        data.append('matricule', formData.matricule);
        data.append('id_grade', formData.id_grade);
        data.append('id_unite_origine', formData.id_unite_origine);
      } else {
        data.append('cin', formData.cin);
        data.append('societe', formData.societe);
      }

      if (formData.photo) data.append('photo', formData.photo);

      await apiService.createVisiteur(data);
      
      // Reset du formulaire
      setFormData({
        nom: '', prenom: '', cin: '', matricule: '', id_grade: '', 
        id_unite_origine: '', societe: '', destination: '', 
        objet_visite: '', id_badge: '', photo: null
      });
      setUniteOrigineInput('');
      
      onSuccess();
    } catch (err) {
      console.error('Erreur lors de la création:', err);
      setError(err.response?.data?.message || 'Erreur lors de la création du visiteur');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 max-w-xl mx-auto" encType="multipart/form-data">
      <div className="mb-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
        <h3 className="text-lg font-semibold text-orange-800 mb-2">Nouveau Visiteur EM/ZS</h3>
        <p className="text-sm text-orange-600">Formulaire pour l'enregistrement des visiteurs externes avec attribution de badge.</p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {/* Type de visiteur */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Type de visiteur *</label>
        <select 
          name="type" 
          value={typeVisiteur} 
          onChange={handleTypeChange} 
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        >
          <option value="militaire_externe">Militaire Externe</option>
          <option value="civil_externe">Civil Externe</option>
        </select>
      </div>

      {/* Informations de base */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Nom *</label>
          <input 
            name="nom" 
            value={formData.nom} 
            onChange={handleChange} 
            required 
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Prénom *</label>
          <input 
            name="prenom" 
            value={formData.prenom} 
            onChange={handleChange} 
            required 
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
          />
        </div>
      </div>

      {/* CIN (obligatoire pour tous) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">CIN *</label>
        <input 
          name="cin" 
          value={formData.cin} 
          onChange={handleChange} 
          required 
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
          placeholder="Ex: AB123456"
        />
      </div>

      {/* Champs spécifiques selon le type */}
      {typeVisiteur === 'militaire_externe' && (
        <>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Matricule *</label>
              <input 
                name="matricule" 
                value={formData.matricule} 
                onChange={handleChange} 
                required 
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
                placeholder="Ex: M12345"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Grade *</label>
              <select 
                name="id_grade" 
                value={formData.id_grade} 
                onChange={handleChange} 
                required 
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Sélectionner un grade</option>
                {grades.map(g => (
                  <option key={g.id} value={g.id}>{g.nom_grade}</option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-1">Unité d'origine *</label>
            <input
              type="text"
              name="unite_origine_autocomplete"
              value={uniteOrigineInput}
              onChange={e => {
                setUniteOrigineInput(e.target.value);
                setFormData({ ...formData, id_unite_origine: '' });
                setShowUniteOrigineSuggestions(true);
              }}
              onFocus={() => setShowUniteOrigineSuggestions(true)}
              ref={uniteOrigineInputRef}
              autoComplete="off"
              placeholder="Rechercher ou ajouter une unité d'origine"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
            {showUniteOrigineSuggestions && uniteOrigineInput.length > 0 && (
              <div className="absolute z-10 bg-white border border-gray-200 rounded shadow max-h-48 overflow-y-auto w-full mt-1">
                {uniteOrigineSuggestions.length > 0 ? uniteOrigineSuggestions.map(u => (
                  <div
                    key={u.id}
                    className="px-4 py-2 hover:bg-blue-50 cursor-pointer"
                    onClick={() => handleUniteOrigineSelect(u)}
                  >
                    {u.nom_unite}
                  </div>
                )) : (
                  <div className="px-4 py-2 text-gray-500">Aucune unité trouvée.<br />
                    <button type="button" className="text-blue-600 hover:underline mt-1" onClick={handleAddUniteOrigine}>Ajouter cette unité</button>
                  </div>
                )}
              </div>
            )}
          </div>
        </>
      )}

      {typeVisiteur === 'civil_externe' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Société *</label>
          <input 
            name="societe" 
            value={formData.societe} 
            onChange={handleChange} 
            required 
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
            placeholder="Nom de la société"
          />
        </div>
      )}

      {/* Destination */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Destination *</label>
        <select 
          name="destination" 
          value={formData.destination} 
          onChange={handleChange} 
          required 
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Sélectionner une destination</option>
          {unites.map(unite => (
            <option key={unite.id} value={unite.id}>{unite.nom_unite}</option>
          ))}
        </select>
      </div>

      {/* Badge */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Badge visiteur *</label>
        <select 
          name="id_badge" 
          value={formData.id_badge} 
          onChange={handleChange} 
          required 
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Sélectionner un badge</option>
          {badges.map(badge => (
            <option key={badge.id} value={badge.id}>
              Badge #{badge.numero_visuel} - {badge.type_badge}
            </option>
          ))}
        </select>
      </div>

      {/* Objet de la visite */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Objet de la visite *</label>
        <textarea 
          name="objet_visite" 
          value={formData.objet_visite} 
          onChange={handleChange} 
          required 
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
          placeholder="Décrivez l'objet de la visite..."
        />
      </div>

      {/* Photo */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Photo</label>
        <input 
          type="file" 
          name="photo" 
          onChange={handleChange} 
          accept="image/*" 
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
        />
      </div>

      <button 
        type="submit" 
        className="w-full bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold shadow hover:bg-blue-700 transition-colors" 
        disabled={loading}
      >
        {loading ? 'Enregistrement en cours...' : 'Enregistrer le visiteur'}
      </button>
    </form>
  );
}

export { AddVisiteurForm };
