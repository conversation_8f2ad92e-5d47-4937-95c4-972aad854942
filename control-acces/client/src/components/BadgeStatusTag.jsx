import { Badge, Clock, CheckCircle, XCircle, AlertTriangle, User } from 'lucide-react';

function BadgeStatusTag({ status, type, size = 'default', showIcon = true }) {
  const getStatusConfig = (status, type) => {
    const configs = {
      active: {
        label: 'Actif',
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: CheckCircle,
        iconColor: 'text-green-600'
      },
      inactive: {
        label: 'Inactif',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: XCircle,
        iconColor: 'text-gray-600'
      },
      expired: {
        label: 'Expiré',
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: AlertTriangle,
        iconColor: 'text-red-600'
      },
      pending: {
        label: 'En attente',
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: Clock,
        iconColor: 'text-yellow-600'
      },
      returned: {
        label: 'Restitué',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: User,
        iconColor: 'text-blue-600'
      },
      suspended: {
        label: 'Suspendu',
        color: 'bg-orange-100 text-orange-800 border-orange-200',
        icon: XCircle,
        iconColor: 'text-orange-600'
      }
    };

    return configs[status] || configs.inactive;
  };

  const getTypeConfig = (type) => {
    const typeConfigs = {
      militaire_interne: {
        label: 'Interne',
        color: 'bg-blue-50 text-blue-700 border-blue-200'
      },
      militaire_externe: {
        label: 'Externe',
        color: 'bg-green-50 text-green-700 border-green-200'
      },
      civil_externe: {
        label: 'Civil',
        color: 'bg-purple-50 text-purple-700 border-purple-200'
      },
      visiteur: {
        label: 'Visiteur',
        color: 'bg-orange-50 text-orange-700 border-orange-200'
      }
    };

    return typeConfigs[type] || {
      label: 'Standard',
      color: 'bg-gray-50 text-gray-700 border-gray-200'
    };
  };

  const statusConfig = getStatusConfig(status);
  const typeConfig = getTypeConfig(type);

  const getSizeClasses = (size) => {
    switch (size) {
      case 'small':
        return 'px-2 py-1 text-xs';
      case 'large':
        return 'px-4 py-2 text-sm';
      default:
        return 'px-3 py-1 text-sm';
    }
  };

  const IconComponent = statusConfig.icon;

  return (
    <div className="flex items-center space-x-2">
      {/* Tag de statut */}
      <span className={`inline-flex items-center space-x-1 border rounded-full font-medium ${getSizeClasses(size)} ${statusConfig.color}`}>
        {showIcon && (
          <IconComponent size={size === 'small' ? 12 : size === 'large' ? 16 : 14} className={statusConfig.iconColor} />
        )}
        <span>{statusConfig.label}</span>
      </span>

      {/* Tag de type (si fourni) */}
      {type && (
        <span className={`inline-flex items-center space-x-1 border rounded-full font-medium ${getSizeClasses(size)} ${typeConfig.color}`}>
          <Badge size={size === 'small' ? 12 : size === 'large' ? 16 : 14} className="opacity-75" />
          <span>{typeConfig.label}</span>
        </span>
      )}
    </div>
  );
}

// Composant pour afficher uniquement le statut
function StatusOnly({ status, size = 'default', showIcon = true }) {
  return <BadgeStatusTag status={status} size={size} showIcon={showIcon} />;
}

// Composant pour afficher uniquement le type
function TypeOnly({ type, size = 'default' }) {
  return <BadgeStatusTag status="active" type={type} size={size} showIcon={false} />;
}

// Composant pour afficher un badge avec informations complètes
function BadgeInfo({ badge, size = 'default', showType = true }) {
  return (
    <div className="flex items-center space-x-2">
      <BadgeStatusTag 
        status={badge.status} 
        type={showType ? badge.type : null} 
        size={size} 
        showIcon={true} 
      />
      {badge.numero && (
        <span className="text-sm text-gray-600 font-mono">
          #{badge.numero}
        </span>
      )}
    </div>
  );
}

export { BadgeStatusTag, StatusOnly, TypeOnly, BadgeInfo };
export default BadgeStatusTag; 