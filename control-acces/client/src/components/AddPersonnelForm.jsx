import { useState, useEffect, useRef } from 'react';
import { apiService } from '../services/apiService';

export function AddPersonnelForm({ onSuccess }) {
  // Type fixé sur militaire_interne - plus de sélection
  const typePersonnel = 'militaire_interne';
  const [formData, setFormData] = useState({
    nom: '',
    prenom: '',
    matricule: '',
    id_grade: '',
    id_unite: '', // Unité de travail
    id_unite_origine: '', // Unité d'origine
    photo: null
  });
  const [grades, setGrades] = useState([]);
  // const [unites, setUnites] = useState([]); // supprimé car autocomplete utilisé
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [uniteInput, setUniteInput] = useState('');
  const [uniteSuggestions, setUniteSuggestions] = useState([]);
  const [showUniteSuggestions, setShowUniteSuggestions] = useState(false);
  const uniteInputRef = useRef();

  const [uniteOrigineInput, setUniteOrigineInput] = useState('');
  const [uniteOrigineSuggestions, setUniteOrigineSuggestions] = useState([]);
  const [showUniteOrigineSuggestions, setShowUniteOrigineSuggestions] = useState(false);
  const uniteOrigineInputRef = useRef();

  useEffect(() => {
    apiService.getPersonnelGrades?.()
      .then(res => setGrades(res.data))
      .catch(() => setGrades([]));
    // apiService.getPersonnelUnites?.()
    //   .then(res => setUnites(res.data))
    //   .catch(() => setUnites([]));
  }, []);

  // Plus besoin de ce useEffect car le type est fixé

  // Recherche dynamique d'unités de travail
  useEffect(() => {
    if (uniteInput.length > 0) {
      apiService.searchUnites(uniteInput)
        .then(res => setUniteSuggestions(res.data))
        .catch(() => setUniteSuggestions([]));
    } else {
      setUniteSuggestions([]);
    }
  }, [uniteInput]);

  // Recherche dynamique d'unités d'origine
  useEffect(() => {
    if (uniteOrigineInput.length > 0) {
      apiService.searchUnites(uniteOrigineInput)
        .then(res => setUniteOrigineSuggestions(res.data))
        .catch(() => setUniteOrigineSuggestions([]));
    } else {
      setUniteOrigineSuggestions([]);
    }
  }, [uniteOrigineInput]);

  // Sélection d'une suggestion pour unité de travail
  const handleUniteSelect = (unite) => {
    setFormData({ ...formData, id_unite: unite.id });
    setUniteInput(unite.nom_unite);
    setShowUniteSuggestions(false);
  };

  // Sélection d'une suggestion pour unité d'origine
  const handleUniteOrigineSelect = (unite) => {
    setFormData({ ...formData, id_unite_origine: unite.id });
    setUniteOrigineInput(unite.nom_unite);
    setShowUniteOrigineSuggestions(false);
  };

  // Ajout d'une nouvelle unité de travail
  const handleAddUnite = async () => {
    if (!uniteInput.trim()) return;
    try {
      const res = await apiService.createUnite({ nom_unite: uniteInput });
      setFormData({ ...formData, id_unite: res.data.id });
      setUniteInput(res.data.nom_unite);
      setShowUniteSuggestions(false);
    } catch {
      alert("Erreur lors de l'ajout de l'unité de travail");
    }
  };

  // Ajout d'une nouvelle unité d'origine
  const handleAddUniteOrigine = async () => {
    if (!uniteOrigineInput.trim()) return;
    try {
      const res = await apiService.createUnite({ nom_unite: uniteOrigineInput });
      setFormData({ ...formData, id_unite_origine: res.data.id });
      setUniteOrigineInput(res.data.nom_unite);
      setShowUniteOrigineSuggestions(false);
    } catch {
      alert("Erreur lors de l'ajout de l'unité d'origine");
    }
  };

  const handleChange = e => {
    const { name, value, files } = e.target;
    if (name === 'photo') {
      setFormData({ ...formData, photo: files[0] });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  // Plus besoin de handleTypeChange car le type est fixé

  const handleSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      const data = new FormData();
      data.append('nom', formData.nom);
      data.append('prenom', formData.prenom);
      data.append('type', typePersonnel);
      data.append('matricule', formData.matricule);
      data.append('id_grade', formData.id_grade);
      data.append('id_unite', formData.id_unite);
      data.append('id_unite_origine', formData.id_unite_origine);
      if (formData.photo) data.append('photo', formData.photo);
      await apiService.createPersonnel(data);
      if (onSuccess) onSuccess();
      setFormData({ nom: '', prenom: '', matricule: '', id_grade: '', id_unite: '', id_unite_origine: '', photo: null });
      setUniteInput('');
      setUniteOrigineInput('');
    } catch {
      setError('Erreur lors de l’ajout du personnel.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 max-w-xl mx-auto" encType="multipart/form-data">
      <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">Nouveau Militaire Interne</h3>
        <p className="text-sm text-blue-600">Formulaire dédié à l'ajout du personnel militaire interne de l'unité.</p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Nom</label>
          <input name="nom" value={formData.nom} onChange={handleChange} required className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Nom de famille" />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Prénom</label>
          <input name="prenom" value={formData.prenom} onChange={handleChange} required className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Prénom" />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Matricule *</label>
          <input name="matricule" value={formData.matricule} onChange={handleChange} required className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Ex: M12345" />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Grade *</label>
          <select name="id_grade" value={formData.id_grade} onChange={handleChange} required className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Sélectionner un grade</option>
            {grades.map(g => (
              <option key={g.id} value={g.id}>{g.nom_grade}</option>
            ))}
          </select>
        </div>
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-1">Unité de travail *</label>
          <input
            type="text"
            name="unite_autocomplete"
            value={uniteInput}
            onChange={e => {
              setUniteInput(e.target.value);
              setFormData({ ...formData, id_unite: '' });
              setShowUniteSuggestions(true);
            }}
            onFocus={() => setShowUniteSuggestions(true)}
            ref={uniteInputRef}
            autoComplete="off"
            placeholder="Rechercher ou ajouter une unité de travail"
            className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
          {showUniteSuggestions && uniteInput.length > 0 && (
            <div className="absolute z-10 bg-white border border-gray-200 rounded shadow max-h-48 overflow-y-auto w-full mt-1">
              {uniteSuggestions.length > 0 ? uniteSuggestions.map(u => (
                <div
                  key={u.id}
                  className="px-4 py-2 hover:bg-blue-50 cursor-pointer"
                  onClick={() => handleUniteSelect(u)}
                >
                  {u.nom_unite}
                </div>
              )) : (
                <div className="px-4 py-2 text-gray-500">Aucune unité trouvée.<br />
                  <button type="button" className="text-blue-600 hover:underline mt-1" onClick={handleAddUnite}>Ajouter cette unité</button>
                </div>
              )}
            </div>
          )}
        </div>
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-1">Unité d'origine *</label>
          <input
            type="text"
            name="unite_origine_autocomplete"
            value={uniteOrigineInput}
            onChange={e => {
              setUniteOrigineInput(e.target.value);
              setFormData({ ...formData, id_unite_origine: '' });
              setShowUniteOrigineSuggestions(true);
            }}
            onFocus={() => setShowUniteOrigineSuggestions(true)}
            ref={uniteOrigineInputRef}
            autoComplete="off"
            placeholder="Rechercher ou ajouter une unité d'origine"
            className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
          {showUniteOrigineSuggestions && uniteOrigineInput.length > 0 && (
            <div className="absolute z-10 bg-white border border-gray-200 rounded shadow max-h-48 overflow-y-auto w-full mt-1">
              {uniteOrigineSuggestions.length > 0 ? uniteOrigineSuggestions.map(u => (
                <div
                  key={u.id}
                  className="px-4 py-2 hover:bg-blue-50 cursor-pointer"
                  onClick={() => handleUniteOrigineSelect(u)}
                >
                  {u.nom_unite}
                </div>
              )) : (
                <div className="px-4 py-2 text-gray-500">Aucune unité trouvée.<br />
                  <button type="button" className="text-blue-600 hover:underline mt-1" onClick={handleAddUniteOrigine}>Ajouter cette unité</button>
                </div>
              )}
            </div>
          )}
        </div>
        {/* Plus de champs société ou CIN pour militaires internes */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-1">Photo</label>
          <input type="file" name="photo" accept="image/*" onChange={handleChange} className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" />
        </div>
      </div>
      {error && <div className="text-red-600 mt-2 text-sm font-medium">{error}</div>}
      <div className="flex justify-end mt-6">
        <button type="submit" className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold shadow hover:bg-blue-700 transition-colors" disabled={loading}>
          {loading ? 'Ajout en cours...' : 'Ajouter le militaire'}
        </button>
      </div>
    </form>
  );
}
