import { Activity, CreditCard, Users, Shield, UserCheck, Building, TrendingUp, TrendingDown, AlertTriangle, Clock } from 'lucide-react';
import { useState, useEffect } from 'react';

export function StatCards({ passages, badgesActifs, badgesDisponibles, personnels, effectifs }) {
  const [animatedValues, setAnimatedValues] = useState({
    passages: 0,
    badgesActifs: 0,
    badgesDisponibles: 0,
    personnels: 0
  });

  // Animation des valeurs
  useEffect(() => {
    const animateValue = (start, end, duration, setter) => {
      const startTime = performance.now();
      const animate = (currentTime) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const current = Math.floor(start + (end - start) * progress);
        setter(current);
        
        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };
      requestAnimationFrame(animate);
    };

    animateValue(0, passages, 1000, (value) => setAnimatedValues(prev => ({ ...prev, passages: value })));
    animateValue(0, badgesActifs, 1000, (value) => setAnimatedValues(prev => ({ ...prev, badgesActifs: value })));
    animateValue(0, badgesDisponibles, 1000, (value) => setAnimatedValues(prev => ({ ...prev, badgesDisponibles: value })));
    animateValue(0, personnels, 1000, (value) => setAnimatedValues(prev => ({ ...prev, personnels: value })));
  }, [passages, badgesActifs, badgesDisponibles, personnels]);

  const stats = [
    {
      title: 'Passages Aujourd\'hui',
      value: animatedValues.passages,
      icon: Activity,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      trend: passages > 50 ? 'up' : 'down',
      trendValue: '+12%',
      description: 'Accès enregistrés aujourd\'hui'
    },
    {
      title: 'Badges Actifs',
      value: animatedValues.badgesActifs,
      icon: CreditCard,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      trend: badgesActifs > 100 ? 'up' : 'down',
      trendValue: '+5%',
      description: 'Badges actuellement en usage'
    },
    {
      title: 'Badges Disponibles',
      value: animatedValues.badgesDisponibles,
      icon: Shield,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      trend: badgesDisponibles > 50 ? 'up' : 'down',
      trendValue: '-3%',
      description: 'Badges disponibles pour attribution'
    },
    {
      title: 'Total Personnel',
      value: animatedValues.personnels,
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      trend: 'stable',
      trendValue: '0%',
      description: 'Personnel enregistré dans le système'
    }
  ];

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up':
        return <TrendingUp size={16} className="text-green-600" />;
      case 'down':
        return <TrendingDown size={16} className="text-red-600" />;
      default:
        return <Clock size={16} className="text-gray-600" />;
    }
  };

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-6">
      {/* Statistiques principales */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div 
              key={index} 
              className={`bg-white rounded-lg shadow-sm border ${stat.borderColor} p-6 hover:shadow-md transition-shadow duration-200`}
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`${stat.bgColor} ${stat.color} p-2 rounded-lg`}>
                  <Icon className="h-5 w-5" />
                </div>
                <div className="flex items-center space-x-1">
                  {getTrendIcon(stat.trend)}
                  <span className={`text-xs font-medium ${getTrendColor(stat.trend)}`}>
                    {stat.trendValue}
                  </span>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500 mb-1">{stat.title}</p>
                <p className="text-3xl font-bold text-gray-800 mb-2">{stat.value}</p>
                <p className="text-xs text-gray-600">{stat.description}</p>
              </div>
            </div>
          );
        })}
      </div>

      {/* Widget effectifs sur site */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Building className="h-5 w-5 mr-2 text-gray-600" />
          Effectifs Actuellement sur Site
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200 hover:bg-blue-100 transition-colors duration-200">
            <UserCheck className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-600">{effectifs.militairesInternes}</div>
            <div className="text-sm text-gray-600">Militaires Internes</div>
            <div className="text-xs text-blue-500 mt-1">En service</div>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200 hover:bg-green-100 transition-colors duration-200">
            <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-600">{effectifs.militairesExternes}</div>
            <div className="text-sm text-gray-600">Militaires Externes</div>
            <div className="text-xs text-green-500 mt-1">En mission</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200 hover:bg-purple-100 transition-colors duration-200">
            <Users className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-purple-600">{effectifs.civils}</div>
            <div className="text-sm text-gray-600">Civils</div>
            <div className="text-xs text-purple-500 mt-1">Visiteurs</div>
          </div>
        </div>
        
        {/* Total effectifs */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Total effectifs sur site :</span>
            <span className="text-lg font-semibold text-gray-800">
              {effectifs.militairesInternes + effectifs.militairesExternes + effectifs.civils}
            </span>
          </div>
        </div>
      </div>

      {/* Alertes rapides */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <AlertTriangle className="h-5 w-5 mr-2 text-orange-600" />
          Alertes Rapides
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="flex items-center space-x-3 p-3 bg-red-50 rounded-lg border border-red-200">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            <div>
              <div className="text-sm font-medium text-red-800">Badges expirés</div>
              <div className="text-xs text-red-600">À vérifier</div>
            </div>
          </div>
          <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg border border-orange-200">
            <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
            <div>
              <div className="text-sm font-medium text-orange-800">Accès refusés</div>
              <div className="text-xs text-orange-600">Aujourd'hui</div>
            </div>
          </div>
          <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <div>
              <div className="text-sm font-medium text-blue-800">Badges disponibles</div>
              <div className="text-xs text-blue-600">En stock</div>
            </div>
          </div>
          <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <div>
              <div className="text-sm font-medium text-green-800">Système OK</div>
              <div className="text-xs text-green-600">Toutes les portes</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
