import { useState, useEffect } from 'react';
import { apiService } from '../services/apiService';

function VisiteurForm({ visiteur, onSuccess, onCancel }) {
  const [formData, setFormData] = useState({
    nom: '',
    prenom: '',
    cin: '',
    matricule: '',
    id_grade: '',
    societe: '',
    destination: '',
    objet_visite: '',
    id_badge: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [grades, setGrades] = useState([]);
  const [unites, setUnites] = useState([]);
  const [badges, setBadges] = useState([]);

  // Initialiser le formulaire avec les données du visiteur
  useEffect(() => {
    if (visiteur) {
      setFormData({
        nom: visiteur.nom || '',
        prenom: visiteur.prenom || '',
        cin: visiteur.cin || '',
        matricule: visiteur.matricule || '',
        id_grade: visiteur.id_grade ? String(visiteur.id_grade) : '',
        societe: visiteur.societe || '',
        destination: visiteur.destination_id ? String(visiteur.destination_id) : '',
        objet_visite: visiteur.objet_visite || '',
        id_badge: visiteur.badge_id ? String(visiteur.badge_id) : ''
      });
    }
  }, [visiteur]);

  // Charger les données de référence
  useEffect(() => {
    const loadReferenceData = async () => {
      try {
        const [gradesRes, unitesRes, badgesRes] = await Promise.all([
          apiService.getPersonnelGrades(),
          apiService.getPersonnelUnites(),
          apiService.getBadgesDisponibles(visiteur?.type)
        ]);
        setGrades(gradesRes.data);
        setUnites(unitesRes.data);
        setBadges(badgesRes.data || []);
      } catch (err) {
        console.error('Erreur lors du chargement des données:', err);
        setError('Erreur lors du chargement des données de référence');
      }
    };

    if (visiteur) {
      loadReferenceData();
    }
  }, [visiteur]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const dataToSend = {
        nom: formData.nom,
        prenom: formData.prenom,
        cin: formData.cin,
        destination: parseInt(formData.destination),
        objet_visite: formData.objet_visite
      };

      if (visiteur.type === 'militaire_externe') {
        dataToSend.matricule = formData.matricule;
        dataToSend.id_grade = parseInt(formData.id_grade);
      } else {
        dataToSend.societe = formData.societe;
      }

      // Mise à jour du badge si changé
      if (formData.id_badge && formData.id_badge !== String(visiteur.badge_id)) {
        dataToSend.id_badge = parseInt(formData.id_badge);
      }

      await apiService.updateVisiteur(visiteur.id, dataToSend);
      onSuccess();
    } catch (err) {
      console.error('Erreur lors de la modification:', err);
      setError(err.response?.data?.message || 'Erreur lors de la modification du visiteur');
    } finally {
      setLoading(false);
    }
  };

  if (!visiteur) return null;

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">
          Modifier {visiteur.type === 'militaire_externe' ? 'Militaire Externe' : 'Civil Externe'}
        </h3>
        <p className="text-sm text-blue-600">Modification des informations du visiteur.</p>
      </div>

      {/* Informations de base */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Nom *</label>
          <input 
            name="nom" 
            value={formData.nom} 
            onChange={handleChange} 
            required 
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Prénom *</label>
          <input 
            name="prenom" 
            value={formData.prenom} 
            onChange={handleChange} 
            required 
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
          />
        </div>
      </div>

      {/* CIN */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">CIN *</label>
        <input 
          name="cin" 
          value={formData.cin} 
          onChange={handleChange} 
          required 
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
        />
      </div>

      {/* Champs spécifiques selon le type */}
      {visiteur.type === 'militaire_externe' && (
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Matricule *</label>
            <input 
              name="matricule" 
              value={formData.matricule} 
              onChange={handleChange} 
              required 
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Grade *</label>
            <select 
              name="id_grade" 
              value={formData.id_grade} 
              onChange={handleChange} 
              required 
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Sélectionner un grade</option>
              {grades.map(g => (
                <option key={g.id} value={g.id}>{g.nom_grade}</option>
              ))}
            </select>
          </div>
        </div>
      )}

      {visiteur.type === 'civil_externe' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Société *</label>
          <input 
            name="societe" 
            value={formData.societe} 
            onChange={handleChange} 
            required 
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
          />
        </div>
      )}

      {/* Destination */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Destination *</label>
        <select 
          name="destination" 
          value={formData.destination} 
          onChange={handleChange} 
          required 
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Sélectionner une destination</option>
          {unites.map(unite => (
            <option key={unite.id} value={unite.id}>{unite.nom_unite}</option>
          ))}
        </select>
      </div>

      {/* Badge */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Badge visiteur</label>
        <select 
          name="id_badge" 
          value={formData.id_badge} 
          onChange={handleChange} 
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Aucun badge assigné</option>
          {badges.map(badge => (
            <option key={badge.id} value={badge.id}>
              Badge #{badge.numero_visuel} - {badge.type_badge}
            </option>
          ))}
        </select>
        <div className="text-xs text-gray-500 mt-1">
          Badge actuel: {visiteur.badge_numero || 'Aucun'}
        </div>
      </div>

      {/* Objet de la visite */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Objet de la visite *</label>
        <textarea 
          name="objet_visite" 
          value={formData.objet_visite} 
          onChange={handleChange} 
          required 
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" 
        />
      </div>

      {/* Boutons */}
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
        >
          Annuler
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Modification...' : 'Modifier le visiteur'}
        </button>
      </div>
    </form>
  );
}

export { VisiteurForm };
