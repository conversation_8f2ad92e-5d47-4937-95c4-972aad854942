import { useState, useEffect, useRef } from 'react';
import { Search, X, User, Badge, MapPin, Clock } from 'lucide-react';

function SearchBar({ 
  onSearch, 
  placeholder = "Rechercher...", 
  searchTypes = ['personnel', 'badge', 'porte'],
  showFilters = true,
  className = "" 
}) {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [selectedType, setSelectedType] = useState('all');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const searchRef = useRef(null);

  // Données de test pour les suggestions
  const mockSuggestions = {
    personnel: [
      { id: 1, type: 'personnel', nom: 'Ahmed', prenom: 'Mohammed', matricule: 'M12345', unite: '1ère Brigade' },
      { id: 2, type: 'personnel', nom: '<PERSON><PERSON>', prenom: '<PERSON>', matricule: 'M67890', unite: '2ème Brigade' },
      { id: 3, type: 'personnel', nom: '<PERSON>', prenom: '<PERSON>', cin: 'AB123456', unite: 'Service Civil' }
    ],
    badge: [
      { id: 1, type: 'badge', numero: 'BG001', status: 'active', porteur: 'Ahmed Mohammed' },
      { id: 2, type: 'badge', numero: 'BG002', status: 'active', porteur: 'Jean Dupont' },
      { id: 3, type: 'badge', numero: 'BG003', status: 'expired', porteur: 'Marie Martin' }
    ],
    porte: [
      { id: 1, type: 'porte', nom: 'Porte Principale', code: 'PP001', localisation: 'Entrée Nord' },
      { id: 2, type: 'porte', nom: 'Porte Est', code: 'PE001', localisation: 'Entrée Est' },
      { id: 3, type: 'porte', nom: 'Porte Ouest', code: 'PO001', localisation: 'Entrée Ouest' }
    ]
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowSuggestions(false);
        setIsFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (query.length < 2) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    // Simulation de recherche avec les données de test
    const filteredSuggestions = [];
    
    if (selectedType === 'all' || selectedType === 'personnel') {
      const personnelResults = mockSuggestions.personnel.filter(item =>
        item.nom.toLowerCase().includes(query.toLowerCase()) ||
        item.prenom.toLowerCase().includes(query.toLowerCase()) ||
        (item.matricule && item.matricule.toLowerCase().includes(query.toLowerCase())) ||
        (item.cin && item.cin.toLowerCase().includes(query.toLowerCase()))
      );
      filteredSuggestions.push(...personnelResults);
    }

    if (selectedType === 'all' || selectedType === 'badge') {
      const badgeResults = mockSuggestions.badge.filter(item =>
        item.numero.toLowerCase().includes(query.toLowerCase()) ||
        item.porteur.toLowerCase().includes(query.toLowerCase())
      );
      filteredSuggestions.push(...badgeResults);
    }

    if (selectedType === 'all' || selectedType === 'porte') {
      const porteResults = mockSuggestions.porte.filter(item =>
        item.nom.toLowerCase().includes(query.toLowerCase()) ||
        item.code.toLowerCase().includes(query.toLowerCase())
      );
      filteredSuggestions.push(...porteResults);
    }

    setSuggestions(filteredSuggestions.slice(0, 5)); // Limiter à 5 suggestions
    setShowSuggestions(filteredSuggestions.length > 0);
  }, [query, selectedType]);

  const handleSearch = (searchQuery = query, type = selectedType) => {
    if (onSearch) {
      onSearch(searchQuery, type);
    }
    setShowSuggestions(false);
  };

  const handleSuggestionClick = (suggestion) => {
    setQuery(suggestion.nom || suggestion.numero || suggestion.porteur || '');
    handleSearch(suggestion.nom || suggestion.numero || suggestion.porteur || '', suggestion.type);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const clearSearch = () => {
    setQuery('');
    setSuggestions([]);
    setShowSuggestions(false);
    if (onSearch) {
      onSearch('', 'all');
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'personnel':
        return <User size={16} className="text-blue-600" />;
      case 'badge':
        return <Badge size={16} className="text-green-600" />;
      case 'porte':
        return <MapPin size={16} className="text-purple-600" />;
      default:
        return <Search size={16} className="text-gray-600" />;
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'personnel':
        return 'Personnel';
      case 'badge':
        return 'Badge';
      case 'porte':
        return 'Porte';
      default:
        return 'Tout';
    }
  };

  return (
    <div className={`relative ${className}`} ref={searchRef}>
      <div className="flex items-center space-x-2">
        {/* Barre de recherche principale */}
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => setIsFocused(true)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          {query && (
            <button
              onClick={clearSearch}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <X className="h-5 w-5 text-gray-400 hover:text-gray-600" />
            </button>
          )}
        </div>

        {/* Filtre par type */}
        {showFilters && (
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">Tout</option>
            {searchTypes.includes('personnel') && <option value="personnel">Personnel</option>}
            {searchTypes.includes('badge') && <option value="badge">Badge</option>}
            {searchTypes.includes('porte') && <option value="porte">Porte</option>}
          </select>
        )}

        {/* Bouton de recherche */}
        <button
          onClick={() => handleSearch()}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Rechercher
        </button>
      </div>

      {/* Suggestions */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {suggestions.map((suggestion, index) => (
            <button
              key={`${suggestion.type}-${suggestion.id}-${index}`}
              onClick={() => handleSuggestionClick(suggestion)}
              className="w-full px-4 py-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 flex items-center space-x-3"
            >
              {getTypeIcon(suggestion.type)}
              <div className="flex-1">
                <div className="font-medium text-gray-900">
                  {suggestion.nom ? `${suggestion.nom} ${suggestion.prenom}` : 
                   suggestion.numero ? `Badge ${suggestion.numero}` : 
                   suggestion.nom}
                </div>
                <div className="text-sm text-gray-500">
                  {suggestion.matricule && `Matricule: ${suggestion.matricule}`}
                  {suggestion.cin && `CIN: ${suggestion.cin}`}
                  {suggestion.porteur && `Porteur: ${suggestion.porteur}`}
                  {suggestion.unite && `Unité: ${suggestion.unite}`}
                  {suggestion.code && `Code: ${suggestion.code}`}
                </div>
              </div>
              <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                {getTypeLabel(suggestion.type)}
              </span>
            </button>
          ))}
        </div>
      )}

      {/* Indicateur de recherche récente */}
      {!query && isFocused && (
        <div className="absolute z-40 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="text-sm text-gray-500 mb-2">Recherches récentes</div>
          <div className="space-y-2">
            <button className="flex items-center space-x-2 text-sm text-gray-700 hover:text-blue-600">
              <Clock size={14} />
              <span>Ahmed Mohammed</span>
            </button>
            <button className="flex items-center space-x-2 text-sm text-gray-700 hover:text-blue-600">
              <Clock size={14} />
              <span>Badge BG001</span>
            </button>
            <button className="flex items-center space-x-2 text-sm text-gray-700 hover:text-blue-600">
              <Clock size={14} />
              <span>Porte Principale</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default SearchBar; 