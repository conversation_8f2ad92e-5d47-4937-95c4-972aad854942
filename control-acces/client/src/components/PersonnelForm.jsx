import { useState, useEffect, useRef } from 'react';
import { apiService } from '../services/apiService';
import { Alert } from './ui';

export function PersonnelForm({ personnel, onSuccess, onCancel }) {
  // Formulaire simplifié pour militaires internes uniquement
  const [formData, setFormData] = useState({
    type: 'militaire_interne', // Fixé sur militaire interne
    nom: '',
    prenom: '',
    matricule: '',
    grade: '',
    unite: '', // Unité de travail
    unite_origine: '' // Unité d'origine
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [grades, setGrades] = useState([]);
  const [fieldErrors, setFieldErrors] = useState({});
  const [uniteInput, setUniteInput] = useState('');
  const [uniteSuggestions, setUniteSuggestions] = useState([]);
  const [showUniteSuggestions, setShowUniteSuggestions] = useState(false);
  const uniteInputRef = useRef();

  const [uniteOrigineInput, setUniteOrigineInput] = useState('');
  const [uniteOrigineSuggestions, setUniteOrigineSuggestions] = useState([]);
  const [showUniteOrigineSuggestions, setShowUniteOrigineSuggestions] = useState(false);
  const uniteOrigineInputRef = useRef();

  useEffect(() => {
    // Charger seulement les grades pour les militaires internes
    const fetchReferenceData = async () => {
      try {
        const gradesRes = await apiService.getPersonnelGrades();
        setGrades(gradesRes.data);
      } catch (err) {
        console.error('Error fetching reference data:', err);
        setError('Erreur lors du chargement des grades');
      }
    };

    fetchReferenceData();

    // Pré-remplir le formulaire si on modifie un personnel existant
    if (personnel) {
      setFormData({
        type: 'militaire_interne', // Toujours militaire interne
        nom: personnel.nom || '',
        prenom: personnel.prenom || '',
        matricule: personnel.matricule || '',
        grade: personnel.id_grade ? String(personnel.id_grade) : '',
        unite: personnel.id_unite ? String(personnel.id_unite) : '',
        unite_origine: personnel.id_unite_origine ? String(personnel.id_unite_origine) : ''
      });
    }
  }, [personnel]);

  // Initialiser les inputs d'unités lors de l'édition
  useEffect(() => {
    const initializeUniteInputs = async () => {
      if (personnel) {
        // Initialiser l'unité de travail
        if (personnel.unite && personnel.id_unite) {
          setUniteInput(personnel.unite);
          setFormData(f => ({ ...f, unite: String(personnel.id_unite) }));
        } else if (personnel.id_unite && !personnel.unite) {
          // Si on a l'ID mais pas le nom, récupérer le nom depuis l'API
          try {
            const res = await apiService.getPersonnelUnites();
            const unite = res.data.find(u => u.id === personnel.id_unite);
            if (unite) {
              setUniteInput(unite.nom_unite);
              setFormData(f => ({ ...f, unite: String(personnel.id_unite) }));
            }
          } catch (error) {
            console.error('Erreur lors de la récupération de l\'unité de travail:', error);
          }
        }

        // Initialiser l'unité d'origine
        if (personnel.unite_origine && personnel.id_unite_origine) {
          setUniteOrigineInput(personnel.unite_origine);
          setFormData(f => ({ ...f, unite_origine: String(personnel.id_unite_origine) }));
        } else if (personnel.id_unite_origine && !personnel.unite_origine) {
          // Si on a l'ID mais pas le nom, récupérer le nom depuis l'API
          try {
            const res = await apiService.getPersonnelUnites();
            const uniteOrigine = res.data.find(u => u.id === personnel.id_unite_origine);
            if (uniteOrigine) {
              setUniteOrigineInput(uniteOrigine.nom_unite);
              setFormData(f => ({ ...f, unite_origine: String(personnel.id_unite_origine) }));
            }
          } catch (error) {
            console.error('Erreur lors de la récupération de l\'unité d\'origine:', error);
          }
        }
      }
    };

    initializeUniteInputs();
  }, [personnel]);

  // Recherche dynamique d'unités de travail
  useEffect(() => {
    if (uniteInput.length > 0) {
      apiService.searchUnites(uniteInput)
        .then(res => setUniteSuggestions(res.data))
        .catch(() => setUniteSuggestions([]));
    } else {
      setUniteSuggestions([]);
    }
  }, [uniteInput]);

  // Recherche dynamique d'unités d'origine
  useEffect(() => {
    if (uniteOrigineInput.length > 0) {
      apiService.searchUnites(uniteOrigineInput)
        .then(res => setUniteOrigineSuggestions(res.data))
        .catch(() => setUniteOrigineSuggestions([]));
    } else {
      setUniteOrigineSuggestions([]);
    }
  }, [uniteOrigineInput]);

  // Sélection d'une suggestion pour unité de travail
  const handleUniteSelect = (unite) => {
    setFormData({ ...formData, unite: String(unite.id) });
    setUniteInput(unite.nom_unite);
    setShowUniteSuggestions(false);
  };

  // Sélection d'une suggestion pour unité d'origine
  const handleUniteOrigineSelect = (unite) => {
    setFormData({ ...formData, unite_origine: String(unite.id) });
    setUniteOrigineInput(unite.nom_unite);
    setShowUniteOrigineSuggestions(false);
  };

  // Ajout d'une nouvelle unité de travail
  const handleAddUnite = async () => {
    if (!uniteInput.trim()) return;
    try {
      const res = await apiService.createUnite({ nom_unite: uniteInput });
      setFormData({ ...formData, unite: String(res.data.id) });
      setUniteInput(res.data.nom_unite);
      setShowUniteSuggestions(false);
    } catch {
      alert("Erreur lors de l'ajout de l'unité de travail");
    }
  };

  // Ajout d'une nouvelle unité d'origine
  const handleAddUniteOrigine = async () => {
    if (!uniteOrigineInput.trim()) return;
    try {
      const res = await apiService.createUnite({ nom_unite: uniteOrigineInput });
      setFormData({ ...formData, unite_origine: String(res.data.id) });
      setUniteOrigineInput(res.data.nom_unite);
      setShowUniteOrigineSuggestions(false);
    } catch {
      alert("Erreur lors de l'ajout de l'unité d'origine");
    }
  };

  // Fonction de validation simplifiée pour militaires internes
  const validateField = (name, value) => {
    let error = '';
    if (name === 'matricule') {
      if (!value || value.length < 5) error = 'Le matricule doit comporter au moins 5 caractères';
    }
    setFieldErrors(prev => ({ ...prev, [name]: error }));
  };

  // Modifie handleChange pour valider en temps réel
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    validateField(name, value);
  };

  // Plus besoin de filtrer les badges pour militaires internes

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validation simplifiée pour militaires internes
      if (!formData.nom || !formData.prenom || !formData.matricule || !formData.grade || !formData.unite || !formData.unite_origine) {
        setError('Tous les champs sont obligatoires');
        return;
      }

      const gradeId = parseInt(formData.grade);
      const uniteId = parseInt(formData.unite);
      const uniteOrigineId = parseInt(formData.unite_origine);

      if (isNaN(gradeId) || isNaN(uniteId) || isNaN(uniteOrigineId)) {
        setError('Veuillez sélectionner un grade, une unité de travail et une unité d\'origine valides');
        return;
      }

      // Données simplifiées pour militaire interne
      const dataToSend = {
        nom: formData.nom,
        prenom: formData.prenom,
        type: 'militaire_interne',
        matricule: formData.matricule,
        id_grade: gradeId,
        id_unite: uniteId,
        id_unite_origine: uniteOrigineId
      };

      console.log('Données à envoyer:', dataToSend); // Debug

      if (personnel) {
        await apiService.updatePersonnel(personnel.id, dataToSend);
      } else {
        await apiService.createPersonnel(dataToSend);
      }
      onSuccess();
    } catch (err) {
      console.error('Erreur lors de la soumission:', err);
      let errorMessage = 'Erreur lors de la sauvegarde';
      
      if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.response?.status === 400) {
        errorMessage = 'Données invalides. Veuillez vérifier les informations saisies.';
      } else if (err.response?.status === 409) {
        errorMessage = 'Un personnel avec ces informations existe déjà.';
      } else if (err.response?.status === 500) {
        errorMessage = 'Erreur serveur. Veuillez réessayer.';
      } else if (err.message) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const renderMilitaireInterneFields = () => (
    <>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Matricule *
          </label>
          <input
            type="text"
            name="matricule"
            value={formData.matricule}
            onChange={handleChange}
            required
            className={`w-full border ${fieldErrors.matricule ? 'border-red-500' : 'border-gray-300'} rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500`}
            placeholder="Ex: M12345"
          />
          {fieldErrors.matricule && <div className="text-red-500 text-xs mt-1">{fieldErrors.matricule}</div>}
          <div className="text-xs text-gray-500">5 caractères minimum</div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Grade *
          </label>
          <select
            name="grade"
            value={formData.grade}
            onChange={handleChange}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Sélectionner un grade</option>
            {grades.map(grade => (
              <option key={grade.id} value={String(grade.id)}>{grade.nom_grade}</option>
            ))}
          </select>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-1">Unité de travail *</label>
          <input
            type="text"
            name="unite_autocomplete"
            value={uniteInput}
            onChange={e => {
              setUniteInput(e.target.value);
              setFormData({ ...formData, unite: '' });
              setShowUniteSuggestions(true);
            }}
            onFocus={() => setShowUniteSuggestions(true)}
            ref={uniteInputRef}
            autoComplete="off"
            placeholder="Rechercher ou ajouter une unité de travail"
            className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
          {showUniteSuggestions && uniteInput.length > 0 && (
            <div className="absolute z-10 bg-white border border-gray-200 rounded shadow max-h-48 overflow-y-auto w-full mt-1">
              {uniteSuggestions.length > 0 ? uniteSuggestions.map(u => (
                <div
                  key={u.id}
                  className="px-4 py-2 hover:bg-blue-50 cursor-pointer"
                  onClick={() => handleUniteSelect(u)}
                >
                  {u.nom_unite}
                </div>
              )) : (
                <div className="px-4 py-2 text-gray-500">Aucune unité trouvée.<br />
                  <button type="button" className="text-blue-600 hover:underline mt-1" onClick={handleAddUnite}>Ajouter cette unité</button>
                </div>
              )}
            </div>
          )}
        </div>
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-1">Unité d'origine *</label>
          <input
            type="text"
            name="unite_origine_autocomplete"
            value={uniteOrigineInput}
            onChange={e => {
              setUniteOrigineInput(e.target.value);
              setFormData({ ...formData, unite_origine: '' });
              setShowUniteOrigineSuggestions(true);
            }}
            onFocus={() => setShowUniteOrigineSuggestions(true)}
            ref={uniteOrigineInputRef}
            autoComplete="off"
            placeholder="Rechercher ou ajouter une unité d'origine"
            className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
          {showUniteOrigineSuggestions && uniteOrigineInput.length > 0 && (
            <div className="absolute z-10 bg-white border border-gray-200 rounded shadow max-h-48 overflow-y-auto w-full mt-1">
              {uniteOrigineSuggestions.length > 0 ? uniteOrigineSuggestions.map(u => (
                <div
                  key={u.id}
                  className="px-4 py-2 hover:bg-blue-50 cursor-pointer"
                  onClick={() => handleUniteOrigineSelect(u)}
                >
                  {u.nom_unite}
                </div>
              )) : (
                <div className="px-4 py-2 text-gray-500">Aucune unité trouvée.<br />
                  <button type="button" className="text-blue-600 hover:underline mt-1" onClick={handleAddUniteOrigine}>Ajouter cette unité</button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );

  // Plus besoin de renderVisiteurFields pour militaires internes uniquement

  // Désactive le bouton de soumission si une erreur existe
  const hasFieldError = Object.values(fieldErrors).some(Boolean);

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Alert variant="error" dismissible onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* En-tête pour militaire interne */}
      <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">
          {personnel ? 'Modifier Militaire Interne' : 'Nouveau Militaire Interne'}
        </h3>
        <p className="text-sm text-blue-600">Formulaire dédié à la gestion du personnel militaire interne de l'unité.</p>
      </div>

      {/* Informations de base */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Nom *
          </label>
          <input
            type="text"
            name="nom"
            value={formData.nom}
            onChange={handleChange}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Prénom *
          </label>
          <input
            type="text"
            name="prenom"
            value={formData.prenom}
            onChange={handleChange}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Champs militaire interne */}
      {renderMilitaireInterneFields()}

      {/* Boutons */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Annuler
        </button>
        <button
          type="submit"
          disabled={loading || hasFieldError}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Sauvegarde...' : (personnel ? 'Modifier le militaire' : 'Créer le militaire')}
        </button>
      </div>
    </form>
  );
}
