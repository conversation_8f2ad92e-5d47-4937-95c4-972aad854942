import { NavLink } from 'react-router-dom';
import {
  Home,
  Users,
  CreditCard,
  Calendar,
  Shield
} from 'lucide-react';

const menuItems = [
  { path: '/', icon: Home, label: 'Tableau de bord', description: 'Vue générale' },
  { path: '/personnel', icon: Users, label: 'Personnel Interne', description: 'Gestion personnel interne' },
  { path: '/visiteurs', icon: Shield, label: 'Visiteurs', description: 'Gestion visiteurs EM/ZS' },
  { path: '/badges', icon: CreditCard, label: 'Badges', description: 'Gestion des badges' },
  { path: '/passages', icon: Calendar, label: 'Passages', description: 'Journal des passages' },
];

export function Sidebar() {
  return (
    <aside className="w-64 bg-gray-800 text-white hidden md:block">
      <div className="p-6 border-b border-gray-700">
        <div className="flex items-center space-x-3">
          <Shield className="h-8 w-8 text-blue-400" />
          <div>
            <div className="text-xl font-bold">Access Control</div>
            <div className="text-xs text-gray-400">Système de contrôle</div>
          </div>
        </div>
      </div>

      <nav className="p-4">
        <div className="space-y-1">
          {menuItems.map((item) => {
            const Icon = item.icon;
            return (
              <NavLink
                key={item.path}
                to={item.path}
                className={({ isActive }) =>
                  `flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors group ${
                    isActive
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`
                }
              >
                <Icon className="h-5 w-5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium">{item.label}</div>
                  <div className="text-xs text-gray-400 group-hover:text-gray-300">
                    {item.description}
                  </div>
                </div>
              </NavLink>
            );
          })}
        </div>
      </nav>
    </aside>
  );
}