import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import Personnel from './pages/Personnel'
import Visiteurs from './pages/Visiteurs'
import Badges from './pages/Badges'
import Passages from './pages/Passages'

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/personnel" element={<Personnel />} />
          <Route path="/visiteurs" element={<Visiteurs />} />
          <Route path="/badges" element={<Badges />} />
          <Route path="/passages" element={<Passages />} />
        </Routes>
      </Layout>
    </Router>
  )
}

export default App
