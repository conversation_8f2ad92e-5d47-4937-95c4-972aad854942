import React, { useState, useEffect } from 'react';
import { Activity, RefreshCw, Filter, Download, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { apiService, wsService, formatDate, formatTime } from '../services/apiService';
import { Card, CardHeader, CardContent, CardTitle } from '../components/ui/Card';
import { useLocation } from 'react-router-dom';

function Passages() {
  const [passages, setPassages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filter, setFilter] = useState('all');
  const [page, setPage] = useState(1);

  // États pour les filtres temporaires (avant application)
  const [tempFilters, setTempFilters] = useState({
    resultat: [],      // ['autorise', 'refuse', 'erreur']
    type_acces: [],    // ['entree', 'sortie']
  });

  // États pour les filtres appliqués (envoyés au serveur)
  const [appliedFilters, setAppliedFilters] = useState({
    resultat: [],
    type_acces: [],
  });
  const limit = 20;
  const [total, setTotal] = useState(0);
  // Ajout d'un état pour les totaux par type
  const [selected, setSelected] = useState([]);
  const [totalAutorise, setTotalAutorise] = useState(0);
  const [totalRefuse, setTotalRefuse] = useState(0);
  const [totalErreur, setTotalErreur] = useState(0);
  const [totalEntree, setTotalEntree] = useState(0);
  const [totalSortie, setTotalSortie] = useState(0);
  const [dateDebut, setDateDebut] = useState('');
  const [dateFin, setDateFin] = useState('');

  const handleSelect = (id) => {
    setSelected((prev) => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id]);
  };
  const handleSelectAll = () => {
    if (selected.length === filteredPassages.length) {
      setSelected([]);
    } else {
      setSelected(filteredPassages.map(p => p.id));
    }
  };
  const handleDeleteSelected = async () => {
    if (selected.length === 0) return;
    if (!window.confirm('Supprimer les passages sélectionnés ?')) return;
    try {
      await apiService.deletePassages(selected);
      setSelected([]);
      await loadPassages();
    } catch {
      alert('Erreur lors de la suppression');
    }
  };
  const handleDeleteOne = async (id) => {
    if (!window.confirm('Supprimer ce passage ?')) return;
    try {
      await apiService.deletePassage(id);
      setSelected((prev) => prev.filter(x => x !== id));
      await loadPassages();
    } catch {
      alert('Erreur lors de la suppression');
    }
  };

  const location = useLocation();

  // Fonctions pour gérer les filtres temporaires
  const handleFilterChange = (filterType, value) => {
    setTempFilters(prev => {
      const currentValues = prev[filterType];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];

      return {
        ...prev,
        [filterType]: newValues
      };
    });
  };

  const applyFilters = () => {
    setAppliedFilters({ ...tempFilters });
    setPage(1); // Réinitialiser à la première page
  };

  const clearFilters = () => {
    setTempFilters({
      resultat: [],
      type_acces: [],
    });
    setAppliedFilters({
      resultat: [],
      type_acces: [],
    });
    setPage(1);
  };

  const hasUnappliedChanges = () => {
    return JSON.stringify(tempFilters) !== JSON.stringify(appliedFilters);
  };

  // Initialiser les filtres depuis la query string au montage
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const filterParam = params.get('filter');
    if (filterParam && ['autorise', 'refuse', 'erreur'].includes(filterParam)) {
      setTempFilters(prev => ({
        ...prev,
        resultat: [filterParam]
      }));
      setAppliedFilters(prev => ({
        ...prev,
        resultat: [filterParam]
      }));
    } else if (filterParam && ['entree', 'sortie'].includes(filterParam)) {
      setTempFilters(prev => ({
        ...prev,
        type_acces: [filterParam]
      }));
      setAppliedFilters(prev => ({
        ...prev,
        type_acces: [filterParam]
      }));
    }
  }, [location.search]);

  // Charger les passages quand la page change ou les filtres appliqués changent
  useEffect(() => { loadPassages(); }, [page, limit, appliedFilters, dateDebut, dateFin]);
  useEffect(() => {
    const unsubscribe = wsService.subscribe('passage_detected', (event) => {
      // Vérifier si le nouveau passage correspond aux filtres appliqués
      let shouldAddToList = true;

      // Vérifier les filtres de résultat
      if (appliedFilters.resultat.length > 0) {
        shouldAddToList = shouldAddToList && appliedFilters.resultat.includes(event.resultat);
      }

      // Vérifier les filtres de type d'accès
      if (appliedFilters.type_acces.length > 0) {
        shouldAddToList = shouldAddToList && appliedFilters.type_acces.includes(event.type_acces);
      }

      if (shouldAddToList && page === 1) {
        setPassages(prev => {
          // Vérifie si le passage existe déjà (évite les doublons)
          const exists = prev.some(p => p.id === event.id && p.date_acces === event.date_acces);
          if (exists) return prev;
          // Ajoute en haut, limite à 20
          return [event, ...prev].slice(0, limit);
        });
      }

      // Recharger les compteurs globaux depuis le backend (sans filtre pour avoir les vrais totaux)
      apiService.getPassages({ limit: 1, offset: 0 }).then(response => {
        setTotal(response.data.total || 0);
        setTotalAutorise(response.data.totalAutorise || 0);
        setTotalRefuse(response.data.totalRefuse || 0);
        setTotalErreur(response.data.totalErreur || 0);
        setTotalEntree(response.data.totalEntree || 0);
        setTotalSortie(response.data.totalSortie || 0);
      });
    });
    return unsubscribe;
  }, [limit, appliedFilters, page]);

  // Charger les stats globales au montage
  // useEffect(() => {
  //   const fetchStats = async () => {
  //     try {
  //       const res = await apiService.getStats();
  //       // setStats({
  //       //   all: res.data.total_passages || res.data.passages_today || 0,
  //       //   autorise: res.data.total_autorises || res.data.passages_autorises_today || 0,
  //       //   refuse: res.data.total_refuses || res.data.passages_refuses_today || 0,
  //       //   erreur: res.data.total_erreurs || 0
  //       // });
  //     } catch {
  //       // setStats({ all: 0, autorise: 0, refuse: 0, erreur: 0 });
  //     }
  //   };
  //   fetchStats();
  // }, []);

  const loadPassages = async () => {
    try {
      setLoading(true);
      const offset = (page - 1) * limit;
      const params = { limit, offset };

      // Filtres de date
      if (dateDebut) params.dateDebut = dateDebut;
      if (dateFin) params.dateFin = dateFin;

      // Filtres appliqués par résultat
      if (appliedFilters.resultat.length > 0) {
        params.resultat = appliedFilters.resultat.join(',');
      }

      // Filtres appliqués par type d'accès
      if (appliedFilters.type_acces.length > 0) {
        params.type_acces = appliedFilters.type_acces.join(',');
      }

      const response = await apiService.getPassages(params);
      setPassages(response.data.passages);
      // On suppose que le backend renverra bientôt le total, sinon à calculer côté backend !
      if (response.data.total !== undefined) {
        setTotal(response.data.total);
      } else {
        // fallback : si pas de total, on désactive le bouton suivant si moins que limit
        setTotal((page - 1) * limit + response.data.passages.length);
      }
      // Stocker les totaux globaux par type
      setTotalAutorise(response.data.totalAutorise || 0);
      setTotalRefuse(response.data.totalRefuse || 0);
      setTotalErreur(response.data.totalErreur || 0);
      setTotalEntree(response.data.totalEntree || 0);
      setTotalSortie(response.data.totalSortie || 0);
      setError(null);
    } catch {
      setError('Erreur lors du chargement des passages');
    } finally {
      setLoading(false);
    }
  };

  // Le filtrage se fait maintenant côté serveur, donc on utilise directement les passages
  const filteredPassages = passages;

  // Calcul dynamique des compteurs pour chaque filtre
  const countAll = total;
  const countAutorise = totalAutorise;
  const countRefuse = totalRefuse;
  const countErreur = totalErreur;

  const getResultatIcon = (resultat) => {
    switch (resultat) {
      case 'autorise': return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'refuse': return <XCircle className="w-5 h-5 text-red-600" />;
      case 'erreur': return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      default: return <Activity className="w-5 h-5 text-gray-600" />;
    }
  };
  const getResultatBadge = (resultat) => {
    switch (resultat) {
      case 'autorise': return 'bg-green-100 text-green-800';
      case 'refuse': return 'bg-red-100 text-red-800';
      case 'erreur': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };
  const getResultatLabel = (resultat) => {
    switch (resultat) {
      case 'autorise': return 'Autorisé';
      case 'refuse': return 'Refusé';
      case 'erreur': return 'Erreur';
      default: return resultat;
    }
  };

  const exportToCSV = () => {
    const headers = ['Date', 'Heure', 'Personnel', 'Matricule', 'Badge EPC', 'Porte', 'Type', 'Résultat'];
    const csvData = filteredPassages.map(passage => [
      new Date(passage.date_acces).toLocaleDateString('fr-FR'),
      formatTime(passage.date_acces),
      passage.personnel ? `${passage.personnel.nom} ${passage.personnel.prenom}` : 'Badge inconnu',
      passage.personnel?.matricule || '-',
      passage.epc_code || '-',
      passage.porte_libelle || 'Entrée Principale',
      passage.type_acces || 'entree',
      getResultatLabel(passage.resultat)
    ]);
    const csvContent = [headers, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `passages_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* En-tête */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-1">Historique des passages</h1>
          <p className="text-gray-500 text-sm">Suivi en temps réel des accès RFID</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={exportToCSV}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors flex items-center shadow"
            disabled={filteredPassages.length === 0}
          >
            <Download className="w-4 h-4 mr-2" />
            Exporter CSV
          </button>
          <button
            onClick={loadPassages}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center shadow"
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Actualiser
          </button>
        </div>
      </div>

      {/* Feedback utilisateur */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded animate-pulse">
          {error}
        </div>
      )}

      {/* Nouveau système de filtres avec cases à cocher */}
      <Card className="shadow-lg">
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Filter className="w-5 h-5 text-gray-400" />
                <h3 className="text-sm font-medium text-gray-700">Filtres</h3>
              </div>
              <div className="flex items-center space-x-2">
                {hasUnappliedChanges() && (
                  <span className="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded">
                    Modifications non appliquées
                  </span>
                )}
                <button
                  onClick={clearFilters}
                  className="text-xs text-gray-500 hover:text-gray-700 underline"
                >
                  Effacer tout
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Filtres par résultat */}
              <div>
                <h4 className="text-xs font-medium text-gray-600 mb-3">Par résultat</h4>
                <div className="space-y-2">
                  {[
                    { value: 'autorise', label: '✅ Autorisés', count: countAutorise, color: 'text-green-700' },
                    { value: 'refuse', label: '⛔ Refusés', count: countRefuse, color: 'text-red-700' },
                    { value: 'erreur', label: '⚠️ Erreurs', count: countErreur, color: 'text-yellow-700' }
                  ].map(({ value, label, count, color }) => (
                    <label key={value} className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-1 rounded">
                      <input
                        type="checkbox"
                        checked={tempFilters.resultat.includes(value)}
                        onChange={() => handleFilterChange('resultat', value)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className={`text-sm ${color}`}>{label} ({count})</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Filtres par type d'accès */}
              <div>
                <h4 className="text-xs font-medium text-gray-600 mb-3">Par type d'accès</h4>
                <div className="space-y-2">
                  {[
                    { value: 'entree', label: '🟢 Entrées', count: totalEntree, color: 'text-green-700' },
                    { value: 'sortie', label: '🔴 Sorties', count: totalSortie, color: 'text-red-700' }
                  ].map(({ value, label, count, color }) => (
                    <label key={value} className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-1 rounded">
                      <input
                        type="checkbox"
                        checked={tempFilters.type_acces.includes(value)}
                        onChange={() => handleFilterChange('type_acces', value)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className={`text-sm ${color}`}>{label} ({count})</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            {/* Boutons d'action */}
            <div className="flex items-center justify-between pt-3 border-t border-gray-200">
              <div className="flex items-center space-x-3">
                <button
                  onClick={applyFilters}
                  disabled={!hasUnappliedChanges()}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    hasUnappliedChanges()
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  Appliquer les filtres
                </button>
                <span className="text-xs text-gray-500">
                  {appliedFilters.resultat.length + appliedFilters.type_acces.length > 0
                    ? `${appliedFilters.resultat.length + appliedFilters.type_acces.length} filtre(s) actif(s)`
                    : 'Aucun filtre actif'
                  }
                </span>
              </div>

              {/* Filtres de date */}
              <div className="flex items-center space-x-2">
                <label className="text-sm text-gray-600">Du</label>
                <input type="date" value={dateDebut} onChange={e => { setDateDebut(e.target.value); setPage(1); }} className="border rounded px-2 py-1 text-sm" />
                <label className="text-sm text-gray-600">au</label>
                <input type="date" value={dateFin} onChange={e => { setDateFin(e.target.value); setPage(1); }} className="border rounded px-2 py-1 text-sm" />
                <button onClick={loadPassages} className="ml-2 px-2 py-1 bg-blue-500 text-white rounded text-sm">Filtrer</button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tableau des passages avec sélection multiple */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>Passages ({total})</CardTitle>
          <p className="text-sm text-gray-500">Mise à jour automatique en temps réel</p>
        </CardHeader>
        <CardContent className="overflow-x-auto">
          <div className="mb-2 flex items-center gap-2">
            <button
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center shadow disabled:opacity-50"
              onClick={handleDeleteSelected}
              disabled={selected.length === 0}
            >
              Supprimer la sélection ({selected.length})
            </button>
          </div>
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                <th className="px-2 py-3">
                  <input type="checkbox" checked={selected.length === filteredPassages.length && filteredPassages.length > 0} onChange={handleSelectAll} />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Heure</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Personnel</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Badge EPC</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Porte</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Résultat</th>
                <th className="px-2 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPassages.map((passage, index) => (
                <tr key={passage.id || index} className="hover:bg-blue-50 transition-colors">
                  <td className="px-2 py-4 text-center">
                    <input type="checkbox" checked={selected.includes(passage.id)} onChange={() => handleSelect(passage.id)} />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{formatDate(passage.date_acces)}</div>
                    <div className="text-sm text-gray-500">{formatTime(passage.date_acces)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">{getResultatIcon(passage.resultat)}</div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{passage.personnel ? `${passage.personnel.nom} ${passage.personnel.prenom}` : (passage.nom || passage.prenom ? `${passage.nom || ''} ${passage.prenom || ''}`.trim() : 'Badge inconnu')}</div>
                        <div className="text-sm text-gray-500">{passage.personnel?.matricule || passage.matricule || '-'}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm font-mono text-gray-900">{passage.epc_code || '-'}</div></td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{passage.porte_libelle || 'Entrée Principale'}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${
                      passage.type_acces === 'sortie'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {passage.type_acces === 'sortie' ? '🔴 Sortie' : '🟢 Entrée'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap"><span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getResultatBadge(passage.resultat)}`}>{getResultatLabel(passage.resultat)}</span></td>
                  <td className="px-2 py-4 text-center">
                    <button onClick={() => handleDeleteOne(passage.id)} className="text-red-600 hover:text-red-900 font-medium text-xs">Supprimer</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {filteredPassages.length === 0 && (
            <div className="text-center py-12">
              <Activity className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">{filter === 'all' ? 'Aucun passage' : `Aucun passage ${getResultatLabel(filter).toLowerCase()}`}</h3>
              <p className="mt-1 text-sm text-gray-500">{filter === 'all' ? 'Les passages apparaîtront ici automatiquement lors des scans RFID' : 'Changez le filtre pour voir d\'autres types de passages'}</p>
            </div>
          )}
          {/* Pagination */}
          <div className="flex justify-between items-center mt-4">
            <button
              className="px-4 py-2 bg-gray-200 rounded disabled:opacity-50"
              onClick={() => setPage(page - 1)}
              disabled={page === 1 || loading}
            >
              Précédent
            </button>
            <span className="text-sm text-gray-700">
              Page {page} {total ? `/ ${Math.ceil(total / limit)}` : ''}
            </span>
            <button
              className="px-4 py-2 bg-gray-200 rounded disabled:opacity-50"
              onClick={() => setPage(page + 1)}
              disabled={loading || (passages.length < limit)}
            >
              Suivant
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="shadow-lg">
          <CardContent className="flex items-center">
            <div className="p-3 rounded-full bg-green-100"><CheckCircle className="w-6 h-6 text-green-600" /></div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Accès autorisés</p>
              <p className="text-2xl font-bold text-gray-900">{passages.filter(p => p.resultat === 'autorise').length}</p>
            </div>
          </CardContent>
        </Card>
        <Card className="shadow-lg">
          <CardContent className="flex items-center">
            <div className="p-3 rounded-full bg-red-100"><XCircle className="w-6 h-6 text-red-600" /></div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Accès refusés</p>
              <p className="text-2xl font-bold text-gray-900">{passages.filter(p => p.resultat === 'refuse').length}</p>
            </div>
          </CardContent>
        </Card>
        <Card className="shadow-lg">
          <CardContent className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100"><Activity className="w-6 h-6 text-blue-600" /></div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total passages</p>
              <p className="text-2xl font-bold text-gray-900">{passages.length}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default Passages;
