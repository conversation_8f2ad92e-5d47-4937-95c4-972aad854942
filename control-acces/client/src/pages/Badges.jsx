import React, { useState, useEffect, useRef } from 'react';
import { CreditCard, UserCheck, Search, Plus, Trash2, PenTool, QrCode, Users, Shield } from 'lucide-react';
import QRCode from 'react-qr-code';
import { apiService, wsService } from '../services/apiService';
import { Card, CardHeader, CardContent, CardTitle } from '../components/ui/Card';
import { Modal } from '../components/ui/Modal';
import { BadgeStatusTag } from '../components/BadgeStatusTag';

function Badges() {
  // États principaux
  const [activeTab, setActiveTab] = useState('permanents'); // 'permanents' ou 'multitaches'
  const [badgesPermanents, setBadgesPermanents] = useState([]);
  const [badgesMultitaches, setBadgesMultitaches] = useState([]);
  const [typesBadges, setTypesBadges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Plus besoin d'états pour badges permanents (création automatique)

  // États pour badges multitâches
  const [showFormMultitache, setShowFormMultitache] = useState(false);
  const [formDataMultitache, setFormDataMultitache] = useState({
    type_badge_id: '',
    numero_visuel: '',
    description: ''
  });

  // États pour l'autocomplete du type de badge
  const [typeBadgeInput, setTypeBadgeInput] = useState('');
  const [typeBadgeSuggestions, setTypeBadgeSuggestions] = useState([]);
  const [showTypeBadgeSuggestions, setShowTypeBadgeSuggestions] = useState(false);
  const typeBadgeInputRef = useRef();

  // États communs
  const [formLoading, setFormLoading] = useState(false);
  const [terminalIp, setTerminalIp] = useState(localStorage.getItem("terminalIp") || "");
  const [showQr, setShowQr] = useState(false);
  const [qrValue, setQrValue] = useState('');
  const qrRef = useRef();

  useEffect(() => {
    localStorage.setItem("terminalIp", terminalIp);
  }, [terminalIp]);

  // Charger les données initiales
  useEffect(() => {
    loadAllData();
  }, []);

  // Plus besoin de charger le personnel (badges permanents automatiques)

  // WebSocket pour les mises à jour en temps réel
  useEffect(() => {
    const unsubscribe = wsService.subscribe('badge_attributed', (data) => {
      setSuccess(`Badge ${data.badge.epc_code} attribué à ${data.personnel.nom} ${data.personnel.prenom}`);
      setTimeout(() => setSuccess(null), 5000);
      loadAllData();
    });
    return unsubscribe;
  }, []);

  // Fermer les suggestions en cliquant en dehors
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (typeBadgeInputRef.current && !typeBadgeInputRef.current.contains(event.target)) {
        // Vérifier si on clique sur une suggestion
        const suggestionContainer = event.target.closest('.suggestions-container');
        if (!suggestionContainer) {
          console.log('Clic en dehors, fermeture des suggestions');
          setShowTypeBadgeSuggestions(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Charger toutes les données
  const loadAllData = async () => {
    try {
      setLoading(true);

      // Charger les badges
      const badgesRes = await apiService.getBadges();
      const badges = badgesRes.data.badges || [];

      // Séparer badges permanents et multitâches
      const permanents = badges.filter(badge => badge.permanent === true);
      const multitaches = badges.filter(badge => badge.permanent === false);

      console.log('Badges permanents:', permanents);
      console.log('Badges multitâches:', multitaches);
      if (multitaches.length > 0) {
        console.log('Premier badge multitâche:', multitaches[0]);
        console.log('Numéro visuel du premier badge:', multitaches[0].numero_visuel);
        console.log('Date création du premier badge:', multitaches[0].created_at);
        console.log('Type badge du premier badge:', multitaches[0].type_badge);
      }

      setBadgesPermanents(permanents);
      setBadgesMultitaches(multitaches);

      // Charger les types de badges (avec gestion d'erreur)
      try {
        console.log('Chargement des types de badges...');
        const typesRes = await apiService.getTypesBadges();
        console.log('Réponse complète API types:', typesRes);

        let types = [];
        if (typesRes && typesRes.data && typesRes.data.data) {
          types = typesRes.data.data; // Extraire le tableau depuis data.data
        } else if (typesRes && typesRes.data && Array.isArray(typesRes.data)) {
          types = typesRes.data; // Fallback si la structure est différente
        }

        console.log('Types extraits:', types, 'IsArray:', Array.isArray(types));

        if (Array.isArray(types) && types.length > 0) {
          setTypesBadges(types);
          console.log('Types de badges définis:', types);
        } else {
          console.log('Aucun type trouvé, utilisation des types par défaut');
          const typesParDefaut = [
            { id: 1, nom_type_badge: 'Badge Permanent', description: 'Badge personnel permanent', couleur: '#10B981' },
            { id: 2, nom_type_badge: 'Badge Multitâche', description: 'Badge temporaire réutilisable', couleur: '#3B82F6' },
            { id: 3, nom_type_badge: 'Badge Visiteur', description: 'Badge pour visiteurs externes', couleur: '#F59E0B' }
          ];
          setTypesBadges(typesParDefaut);
        }
      } catch (typesErr) {
        console.error('Erreur lors du chargement des types de badges:', typesErr);
        // Utiliser des types par défaut si l'API échoue
        const typesParDefaut = [
          { id: 1, nom_type_badge: 'Badge Permanent', description: 'Badge personnel permanent', couleur: '#10B981' },
          { id: 2, nom_type_badge: 'Badge Multitâche', description: 'Badge temporaire réutilisable', couleur: '#3B82F6' },
          { id: 3, nom_type_badge: 'Badge Visiteur', description: 'Badge pour visiteurs externes', couleur: '#F59E0B' }
        ];
        setTypesBadges(typesParDefaut);
        console.log('Types par défaut définis après erreur');
      }

      setError(null);
    } catch (err) {
      console.error('Erreur lors du chargement des données:', err);
      setError('Erreur lors du chargement des badges');
    } finally {
      setLoading(false);
    }
  };

  // Fonctions pour les actions sur les badges
  const handleShowQr = (epcCode) => {
    setQrValue(epcCode);
    setShowQr(true);
  };

  const handleWriteEpc = async (badgeId) => {
    if (!terminalIp) {
      setError('Veuillez configurer l\'IP du terminal C72');
      return;
    }

    try {
      await apiService.writeBadgeEpc(badgeId, terminalIp);
      setSuccess('Commande d\'écriture envoyée au terminal');
    } catch (err) {
      setError('Erreur lors de l\'écriture sur le badge');
    }
  };

  const handleDownloadQr = () => {
    if (qrRef.current) {
      const canvas = qrRef.current.querySelector('canvas');
      if (canvas) {
        const link = document.createElement('a');
        link.download = `badge-qr-${qrValue}.png`;
        link.href = canvas.toDataURL();
        link.click();
      }
    }
  };

  // Gestion des badges multitâches
  const handleSubmitMultitache = async (e) => {
    e.preventDefault();
    setFormLoading(true);
    setError(null);

    try {
      // Validation : il faut un type de badge
      if (!formDataMultitache.type_badge_id && !typeBadgeInput.trim()) {
        setError('Veuillez sélectionner ou créer un type de badge');
        return;
      }

      if (!formDataMultitache.numero_visuel) {
        setError('Le numéro visuel est obligatoire');
        return;
      }

      // Si pas de type_badge_id, créer le type d'abord
      let typeBadgeId = formDataMultitache.type_badge_id;
      if (!typeBadgeId && typeBadgeInput.trim()) {
        try {
          console.log('Création du type lors de la soumission:', typeBadgeInput.trim());
          const typeRes = await apiService.createTypeBadge({ nom_type_badge: typeBadgeInput.trim() });
          console.log('Réponse création type lors soumission:', typeRes);

          if (typeRes.data && typeRes.data.data) {
            typeBadgeId = typeRes.data.data.id;

            // Ajouter le nouveau type à la liste
            setTypesBadges(prev => [...prev, {
              id: typeRes.data.data.id,
              nom_type_badge: typeRes.data.data.nom_type_badge,
              description: `Type créé automatiquement: ${typeRes.data.data.nom_type_badge}`,
              couleur: '#3B82F6'
            }]);
          } else {
            setError("Réponse invalide lors de la création du type de badge");
            return;
          }
        } catch (error) {
          console.error('Erreur création type lors soumission:', error);
          setError(error.response?.data?.message || "Erreur lors de la création du type de badge");
          return;
        }
      }

      // Générer un token EPC 24 HEX automatiquement
      const token = generateToken24Hex();

      const badgeData = {
        ...formDataMultitache,
        type_badge_id: typeBadgeId,
        epc_code: token,
        permanent: false,
        actif: true
      };

      console.log('Données envoyées au backend:', badgeData);
      console.log('formDataMultitache avant envoi:', formDataMultitache);

      await apiService.createBadgeMultitache(badgeData);
      setFormDataMultitache({ type_badge_id: '', numero_visuel: '', description: '' });
      setTypeBadgeInput('');
      setShowFormMultitache(false);
      await loadAllData();
      setSuccess('Badge multitâche créé avec succès');
    } catch (err) {
      setError(err.response?.data?.message || "Erreur lors de la création du badge multitâche");
    } finally {
      setFormLoading(false);
    }
  };

  // Générer un token EPC 24 caractères hexadécimaux
  const generateToken24Hex = () => {
    const chars = '0123456789ABCDEF';
    let token = '';
    for (let i = 0; i < 24; i++) {
      token += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return token;
  };

  // Recherche dynamique de types de badges
  useEffect(() => {
    console.log('Recherche types badges - Input:', typeBadgeInput, 'Types disponibles:', typesBadges);
    if (typeBadgeInput.length > 0 && Array.isArray(typesBadges)) {
      const suggestions = typesBadges.filter(type =>
        type.nom_type_badge && type.nom_type_badge.toLowerCase().includes(typeBadgeInput.toLowerCase())
      );
      console.log('Suggestions trouvées:', suggestions);
      setTypeBadgeSuggestions(suggestions);
    } else {
      setTypeBadgeSuggestions([]);
    }
  }, [typeBadgeInput, typesBadges]);

  // Sélection d'un type de badge
  const handleTypeBadgeSelect = (type) => {
    console.log('Sélection du type:', type);
    setFormDataMultitache(prev => ({ ...prev, type_badge_id: type.id }));
    setTypeBadgeInput(type.nom_type_badge);
    setShowTypeBadgeSuggestions(false);
    console.log('Type sélectionné - ID:', type.id, 'Nom:', type.nom_type_badge);
  };

  // Ajout d'un nouveau type de badge
  const handleAddTypeBadge = async () => {
    if (!typeBadgeInput.trim()) return;
    try {
      console.log('Création du type de badge:', typeBadgeInput);
      const res = await apiService.createTypeBadge({ nom_type_badge: typeBadgeInput.trim() });
      console.log('Réponse création type:', res);

      if (res.data && res.data.data) {
        const nouveauType = res.data.data;
        setFormDataMultitache({ ...formDataMultitache, type_badge_id: nouveauType.id });
        setTypeBadgeInput(nouveauType.nom_type_badge);
        setShowTypeBadgeSuggestions(false);

        // Ajouter le nouveau type à la liste existante
        setTypesBadges(prev => [...prev, {
          id: nouveauType.id,
          nom_type_badge: nouveauType.nom_type_badge,
          description: `Type créé automatiquement: ${nouveauType.nom_type_badge}`,
          couleur: '#3B82F6'
        }]);

        setSuccess('Type de badge créé avec succès');
      }
    } catch (error) {
      console.error('Erreur lors de la création du type de badge:', error);
      setError(error.response?.data?.message || "Erreur lors de l'ajout du type de badge");
    }
  };

  // Gestion des changements de formulaire multitâche
  const handleChangeMultitache = (e) => {
    const { name, value } = e.target;
    setFormDataMultitache(prev => ({ ...prev, [name]: value }));
  };

  // Plus besoin de génération EPC pour badges permanents

  // Supprimer un badge
  const handleDelete = async (id, type) => {
    const message = type === 'permanent'
      ? 'Voulez-vous vraiment supprimer ce badge permanent ?'
      : 'Voulez-vous vraiment supprimer ce badge multitâche ?';

    if (window.confirm(message)) {
      try {
        await apiService.deleteBadge(id);
        await loadAllData();
        setSuccess('Badge supprimé avec succès');
      } catch (err) {
        setError('Erreur lors de la suppression du badge');
      }
    }
  };

  // Fonctions dupliquées supprimées

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Debug: vérifier l'état de typesBadges
  console.log('État typesBadges dans le rendu:', typesBadges, 'Type:', typeof typesBadges, 'IsArray:', Array.isArray(typesBadges));

  // Protection contre les erreurs de rendu
  if (!Array.isArray(typesBadges)) {
    console.error('typesBadges n\'est pas un tableau:', typesBadges);
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-red-600">Erreur de chargement des types de badges</div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* En-tête */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-1">Gestion Globale des Badges</h1>
          <p className="text-gray-500 text-sm">Visualiser les badges permanents (créés automatiquement) et gérer les badges multitâches</p>
        </div>
        <button
          onClick={() => setShowFormMultitache(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center shadow"
        >
          <Shield className="w-4 h-4 mr-2" />
          Nouveau Badge Multitâche
        </button>
      </div>

      {/* Feedback utilisateur */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded animate-pulse">
          {error}
        </div>
      )}
      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded animate-fade-in">
          {success}
        </div>
      )}

      {/* Onglets de navigation */}
      <div className="flex space-x-2 mb-6">
        <button
          className={`px-6 py-3 rounded-lg font-medium transition-colors flex items-center ${
            activeTab === 'permanents'
              ? 'bg-green-600 text-white shadow-lg'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
          onClick={() => setActiveTab('permanents')}
        >
          <Users className="w-4 h-4 mr-2" />
          Badges Permanents ({badgesPermanents.length})
        </button>
        <button
          className={`px-6 py-3 rounded-lg font-medium transition-colors flex items-center ${
            activeTab === 'multitaches'
              ? 'bg-blue-600 text-white shadow-lg'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
          onClick={() => setActiveTab('multitaches')}
        >
          <Shield className="w-4 h-4 mr-2" />
          Badges Multitâches ({badgesMultitaches.length})
        </button>
      </div>

      {/* Champ IP terminal */}
      <div className="mb-4 flex items-center gap-2">
        <label htmlFor="terminal-ip" className="font-semibold">IP du terminal C72 :</label>
        <input
          id="terminal-ip"
          type="text"
          value={terminalIp}
          onChange={e => setTerminalIp(e.target.value)}
          placeholder="ex: ***********"
          className="border rounded px-2 py-1 w-40"
        />
      </div>

      {/* Badge permanent créé automatiquement lors de la création du personnel interne */}

      {/* Formulaire modal Badge Multitâche */}
      <Modal isOpen={showFormMultitache} onClose={() => setShowFormMultitache(false)} title="Créer un Badge Multitâche" size="md">
        <form onSubmit={handleSubmitMultitache} className="space-y-6">
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">Nouveau Badge Multitâche</h3>
            <p className="text-sm text-blue-600">Badge réutilisable avec token EPC généré automatiquement.</p>
          </div>

          {/* Type de badge */}
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-1">Type de badge *</label>
            <input
              type="text"
              name="type_badge_autocomplete"
              value={typeBadgeInput}
              onChange={e => {
                const value = e.target.value;
                console.log('Input changé:', value);
                setTypeBadgeInput(value);
                setFormDataMultitache({ ...formDataMultitache, type_badge_id: '' });
                setShowTypeBadgeSuggestions(true);
              }}
              onFocus={() => {
                console.log('Focus sur input, types disponibles:', typesBadges);
                setShowTypeBadgeSuggestions(true);
                // Si le champ est vide, afficher tous les types
                if (!typeBadgeInput) {
                  setTypeBadgeSuggestions(typesBadges || []);
                }
              }}
              ref={typeBadgeInputRef}
              autoComplete="off"
              placeholder="Rechercher ou créer un type de badge"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
            {showTypeBadgeSuggestions && (
              <div className="suggestions-container absolute z-10 bg-white border border-gray-200 rounded shadow max-h-48 overflow-y-auto w-full mt-1">
                {/* Afficher les suggestions ou tous les types si pas de recherche */}
                {(typeBadgeInput.length > 0 ? typeBadgeSuggestions : typesBadges || []).length > 0 ?
                  (typeBadgeInput.length > 0 ? typeBadgeSuggestions : typesBadges || []).map(type => (
                    <div
                      key={type.id}
                      className="px-4 py-2 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Clic sur type:', type);
                        handleTypeBadgeSelect(type);
                      }}
                      onMouseDown={(e) => {
                        e.preventDefault(); // Empêcher la perte de focus
                      }}
                    >
                      <div className="font-medium text-gray-900">{type.nom_type_badge}</div>
                      {type.description && (
                        <div className="text-xs text-gray-500">{type.description}</div>
                      )}
                    </div>
                  )) : (
                    <div className="px-4 py-2 text-gray-500">
                      {typeBadgeInput.length > 0 ? 'Aucun type trouvé.' : 'Aucun type disponible.'}<br />
                      {typeBadgeInput.length > 0 && (
                        <button type="button" className="text-blue-600 hover:underline mt-1" onClick={handleAddTypeBadge}>
                          Créer ce type
                        </button>
                      )}
                    </div>
                  )
                }
              </div>
            )}
          </div>

          {/* Numéro visuel */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Numéro visuel *</label>
            <input
              type="text"
              name="numero_visuel"
              value={formDataMultitache.numero_visuel}
              onChange={handleChangeMultitache}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Ex: MT001, VIP-01, MAINT-05..."
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              name="description"
              value={formDataMultitache.description}
              onChange={handleChangeMultitache}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows="3"
              placeholder="Description du badge (optionnel)..."
            />
          </div>

          {/* Boutons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={() => setShowFormMultitache(false)}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors duration-200"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={formLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
            >
              {formLoading ? 'Création...' : 'Créer Badge'}
            </button>
          </div>
        </form>
      </Modal>

      {/* Contenu des onglets */}
      {activeTab === 'permanents' && (
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="w-5 h-5 mr-2 text-green-600" />
              Badges Permanents ({badgesPermanents.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">EPC</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Personnel</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unité</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {badgesPermanents.map((badge) => (
                  <tr key={badge.id} className="hover:bg-green-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap font-mono text-green-700">{badge.epc_code}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <BadgeStatusTag status={badge.actif ? 'active' : 'inactive'} />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {badge.personnel ? (
                        <div>
                          <div className="font-medium text-gray-900">{badge.personnel.nom} {badge.personnel.prenom}</div>
                          <div className="text-xs text-gray-500">Matricule: {badge.personnel.matricule}</div>
                        </div>
                      ) : (
                        <span className="text-gray-400 italic">Non attribué</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {badge.personnel?.grade || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {badge.personnel?.unite || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <button onClick={() => handleShowQr(badge.epc_code)} title="Afficher QR Code" className="text-blue-600 hover:text-blue-900 mr-2 p-1 rounded hover:bg-blue-50 transition-colors">
                        <QrCode className="w-5 h-5" />
                      </button>
                      <button onClick={() => handleWriteEpc(badge.id)} title="Écrire sur badge" className="text-green-600 hover:text-green-900 mr-2 p-1 rounded hover:bg-green-50 transition-colors">
                        <PenTool className="w-5 h-5" />
                      </button>
                      <button onClick={() => handleDelete(badge.id, 'permanent')} title="Supprimer" className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50 transition-colors">
                        <Trash2 className="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {badgesPermanents.length === 0 && (
              <div className="text-center py-12">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun badge permanent</h3>
                <p className="mt-1 text-sm text-gray-500">Les badges permanents sont créés automatiquement lors de l'ajout du personnel interne</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {activeTab === 'multitaches' && (
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="w-5 h-5 mr-2 text-blue-600" />
              Badges Multitâches ({badgesMultitaches.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Token EPC</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Numéro</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Créé le</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {badgesMultitaches.map((badge) => (
                  <tr key={badge.id} className="hover:bg-blue-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap font-mono text-blue-700 text-xs">{badge.epc_code}</td>
                    <td className="px-6 py-4 whitespace-nowrap font-semibold text-gray-900">
                      {badge.numero_visuel || 'Non défini'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {badge.type_badge?.nom_type_badge || 'Type non défini'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <BadgeStatusTag status={badge.actif ? 'active' : 'inactive'} />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs truncate">
                      {badge.description || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {badge.created_at ? new Date(badge.created_at).toLocaleDateString('fr-FR') : 'Non défini'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <button onClick={() => handleShowQr(badge.epc_code)} title="Afficher QR Code" className="text-blue-600 hover:text-blue-900 mr-2 p-1 rounded hover:bg-blue-50 transition-colors">
                        <QrCode className="w-5 h-5" />
                      </button>
                      <button onClick={() => handleWriteEpc(badge.id)} title="Écrire sur badge" className="text-green-600 hover:text-green-900 mr-2 p-1 rounded hover:bg-green-50 transition-colors">
                        <PenTool className="w-5 h-5" />
                      </button>
                      <button onClick={() => handleDelete(badge.id, 'multitache')} title="Supprimer" className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50 transition-colors">
                        <Trash2 className="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {badgesMultitaches.length === 0 && (
              <div className="text-center py-12">
                <Shield className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun badge multitâche</h3>
                <p className="mt-1 text-sm text-gray-500">Créez des badges multitâches réutilisables</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Modal QR Code */}
      <Modal isOpen={showQr} onClose={() => setShowQr(false)} title="QR Code du badge" size="sm">
        <div className="flex flex-col items-center justify-center p-4" ref={qrRef}>
          <QRCode value={qrValue || ''} size={180} />
        </div>
        <div className="flex justify-center mt-4">
          <button onClick={handleDownloadQr} className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">Exporter en PNG</button>
        </div>
      </Modal>
    </div>
  );
}

export default Badges;
