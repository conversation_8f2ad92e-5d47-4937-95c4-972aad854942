import React, { useState, useEffect } from 'react';
import { UserPlus, Users, Search, Eye, Edit, Trash2 } from 'lucide-react';
import { apiService, wsService, formatDate } from '../services/apiService';
import { Card, CardHeader, CardContent, CardTitle } from '../components/ui/Card';
import { Modal } from '../components/ui/Modal';
import { AddPersonnelForm } from '../components/AddPersonnelForm';
import { PersonnelForm } from '../components/PersonnelForm';

// Plus besoin de getTypeLabel car on affiche que des militaires internes

function CartePersonnel({ person }) {
  return (
    <div className="flex flex-col items-center p-6 bg-white rounded-lg shadow-md w-80 mx-auto">
      <div className="mb-4">
        {person.photo ? (
          <img src={person.photo.startsWith('http') ? person.photo : `${window.location.origin}${person.photo}`} alt="avatar" className="h-24 w-24 rounded-full object-cover border-4 border-blue-200" />
        ) : (
          <div className="h-24 w-24 rounded-full bg-blue-100 flex items-center justify-center">
            <Users className="h-12 w-12 text-blue-600" />
          </div>
        )}
      </div>
      <div className="text-xl font-bold text-gray-900 mb-1">{person.nom} {person.prenom}</div>
      <div className="text-sm text-gray-600 mb-2">{person.grade || '-'} | {person.unite || '-'}</div>
      <div className="text-sm text-gray-500 mb-2">Matricule : {person.matricule || '-'}</div>
      <div className="text-sm text-gray-500 mb-2">Type : Militaire Interne</div>
      {person.unite_origine && (
        <div className="text-sm text-gray-500 mb-2">Unité d'origine : {person.unite_origine}</div>
      )}
      {/* <div className="text-sm text-gray-500 mb-2">CIN : {person.cin || '-'}</div> */}
      {/* <div className="text-sm text-gray-500 mb-2">Société : {person.societe || '-'}</div> */}
    </div>
  );
}

function Personnel() {
  const [personnel, setPersonnel] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('view'); // 'view' ou 'edit'
  const [selectedPerson, setSelectedPerson] = useState(null);
  // const [formData, setFormData] = useState({
  //   nom: '', prenom: '', type: 'militaire_interne', matricule: '', cin: '', societe: ''
  // }); // supprimé car plus utilisé

  useEffect(() => { loadPersonnel(); }, []);
  useEffect(() => {
    const unsubscribe = wsService.subscribe('personnel_created', (data) => {
      setPersonnel(prev => [data, ...prev]);
      setSuccess(`Personnel ${data.nom} ${data.prenom} créé avec succès`);
      setTimeout(() => setSuccess(null), 5000);
    });
    return unsubscribe;
  }, []);

  const loadPersonnel = async () => {
    try {
      setLoading(true);
      const response = await apiService.getPersonnel();
      setPersonnel(response.data.personnel);
      setError(null);
    } catch {
      setError('Erreur lors du chargement du personnel');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Voulez-vous vraiment supprimer ce personnel ?')) {
      try {
        await apiService.deletePersonnel(id);
        await loadPersonnel();
      } catch {
        alert('Erreur lors de la suppression');
      }
    }
  };

  const filteredPersonnel = personnel.filter(p =>
    p.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    p.prenom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (p.matricule && p.matricule.toLowerCase().includes(searchTerm.toLowerCase())) ||
    p.cin.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Plus besoin de getTypeBadge car on affiche que des militaires internes

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* En-tête */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-1">Gestion du Personnel Militaire Interne</h1>
          <p className="text-gray-500 text-sm">Créer, rechercher et gérer le personnel militaire interne de l'unité</p>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center shadow"
        >
          <UserPlus className="w-4 h-4 mr-2" />
          Nouveau Militaire
        </button>
      </div>

      {/* Feedback utilisateur */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded animate-pulse">
          {error}
        </div>
      )}
      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded animate-fade-in">
          {success}
        </div>
      )}

      {/* Formulaire modal */}
      <Modal isOpen={showForm} onClose={() => setShowForm(false)} title="Nouveau militaire interne" size="lg">
        <AddPersonnelForm
          onSuccess={async () => {
            setShowForm(false);
            await loadPersonnel();
          }}
        />
      </Modal>

      {/* Recherche */}
      <Card className="shadow-lg">
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Rechercher par nom, prénom ou matricule..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </CardContent>
      </Card>

      {/* Liste du personnel */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>Personnel Militaire Interne ({filteredPersonnel.length})</CardTitle>
        </CardHeader>
        <CardContent className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Personnel</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Matricule</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unité de travail</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unité d'origine</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Créé le</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPersonnel.map((person) => (
                <tr key={person.id} className="hover:bg-blue-50 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        {person.photo ? (
                          <img src={person.photo.startsWith('http') ? person.photo : `${window.location.origin}${person.photo}`} alt="avatar" className="h-10 w-10 rounded-full object-cover border" />
                        ) : (
                          <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <Users className="h-5 w-5 text-blue-600" />
                          </div>
                        )}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{person.nom} {person.prenom}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{person.grade || '-'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{person.matricule || '-'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{person.unite || '-'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{person.unite_origine || '-'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {person.created_at ? formatDate(new Date(person.created_at)) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm flex gap-2">
                    <button title="Afficher" className="text-blue-600 hover:text-blue-900" onClick={() => { setSelectedPerson(person); setModalMode('view'); setModalOpen(true); }}><Eye className="w-5 h-5" /></button>
                    <button title="Modifier" className="text-yellow-600 hover:text-yellow-900" onClick={() => { setSelectedPerson(person); setModalMode('edit'); setModalOpen(true); }}><Edit className="w-5 h-5" /></button>
                    <button onClick={() => handleDelete(person.id)} title="Supprimer" className="text-red-600 hover:text-red-900"><Trash2 className="w-5 h-5" /></button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {filteredPersonnel.length === 0 && (
            <div className="text-center py-12">
              <Users className="mx-auto h-12 w-12 text-blue-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun personnel</h3>
              <p className="mt-1 text-sm text-gray-500">{searchTerm ? 'Aucun résultat pour cette recherche' : 'Commencez par créer un nouveau personnel'}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modale détails/modification */}
      <Modal isOpen={modalOpen} onClose={() => setModalOpen(false)} title={modalMode === 'edit' ? 'Modifier le militaire' : 'Détails du militaire'} size="md">
        {modalMode === 'edit' && selectedPerson ? (
          <PersonnelForm personnel={selectedPerson} onSuccess={async () => { setModalOpen(false); await loadPersonnel(); }} onCancel={() => setModalOpen(false)} />
        ) : selectedPerson ? (
          <CartePersonnel person={selectedPerson} />
        ) : null}
      </Modal>
    </div>
  );
}

export default Personnel;
