import React, { useState, useEffect } from 'react';
import { Users, CreditCard, Activity, CheckCircle, XCircle, Clock, UserPlus, UserCheck, Calendar } from 'lucide-react';
import { apiService, wsService, formatTime, getStatusIcon } from '../services/apiService';
import { Card, CardHeader, CardContent, CardTitle } from '../components/ui/Card';
import { useToast } from '../components/ui/Toast';
import { useNavigate } from 'react-router-dom';

function Dashboard() {
  const [stats, setStats] = useState({
    passages_today: 0,
    passages_autorises_today: 0,
    passages_refuses_today: 0,
    badges_actifs: 0,
    attributions_actives: 0,
    total_personnel: 0
  });
  const [recentPassages, setRecentPassages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const toast = useToast();
  const navigate = useNavigate();

  useEffect(() => { loadDashboardData(); }, []);

  useEffect(() => {
    const unsubscribePassage = wsService.subscribe('passage_detected', (event) => {
      setRecentPassages(prev => {
        const exists = prev.some(p => p.id === event.id && p.date_acces === event.date_acces);
        if (exists) return prev;
        return [event, ...prev.slice(0, 9)];
      });
      apiService.getStats().then(res => setStats(res.data));
      // Notification détaillée
      let type = 'info';
      let message = '';
      let grade = event.personnel?.grade ? event.personnel.grade : '';
      let nom = event.personnel?.nom ? event.personnel.nom : '';
      let prenom = event.personnel?.prenom ? event.personnel.prenom : '';
      let unite = event.personnel?.unite ? event.personnel.unite : '';
      let heure = event.date_acces ? formatTime(event.date_acces) : '';

      // Déterminer l'icône et le texte selon le type d'accès
      const typeIcon = event.type_acces === 'sortie' ? '🔴' : '🟢';
      const typeText = event.type_acces === 'sortie' ? 'Sortie' : 'Entrée';
      const typeColor = event.type_acces === 'sortie' ? 'text-red-700' : 'text-green-700';

      if (event.resultat === 'autorise') {
        type = 'success';
        message = <><span className="font-semibold">{typeIcon} <span className={typeColor}>{typeText}</span> autorisée à <span className="font-bold text-green-700">{grade ? grade + ' ' : ''}{nom} {prenom}</span> <span className="text-gray-700">({unite})</span> à <span className="text-blue-700">{heure}</span></span></>;
      } else if (event.resultat === 'refuse') {
        type = 'error';
        message = <><span className="font-semibold">⛔ <span className={typeColor}>{typeText}</span> refusée</span>{' '}
          {event.raison && <span className="text-red-700 font-bold">Motif : {event.raison}</span>}{' '}
          <span className="text-gray-700">à</span> <span className="text-blue-700">{heure}</span></>;
      } else if (event.resultat === 'erreur') {
        type = 'warning';
        message = <><span className="font-semibold">⚠️ Erreur badge {typeIcon} <span className={typeColor}>{typeText}</span> <span className="font-bold text-yellow-700">{grade ? grade + ' ' : ''}{nom} {prenom}</span> <span className="text-gray-700">({unite})</span> à <span className="text-blue-700">{heure}</span></span></>;
      } else {
        message = <><span className="font-semibold">🔔 {typeIcon} <span className={typeColor}>{typeText}</span> détectée <span className="font-bold">{grade ? grade + ' ' : ''}{nom} {prenom}</span> <span className="text-gray-700">({unite})</span> à <span className="text-blue-700">{heure}</span></span></>;
      }
      toast[type](message, { duration: 4000 });
    });
    const unsubscribePersonnel = wsService.subscribe('personnel_created', () => {
      setStats(prev => ({ ...prev, total_personnel: prev.total_personnel + 1 }));
    });
    const unsubscribeBadge = wsService.subscribe('badge_attributed', () => {
      setStats(prev => ({ ...prev, attributions_actives: prev.attributions_actives + 1 }));
    });
    return () => {
      unsubscribePassage();
      unsubscribePersonnel();
      unsubscribeBadge();
    };
  }, [toast]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [statsResponse, passagesResponse] = await Promise.all([
        apiService.getStats(),
        apiService.getPassages()
      ]);
      setStats(statsResponse.data);
      setRecentPassages(passagesResponse.data.passages.slice(0, 10));
      setError(null);
    } catch {
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    { title: "Passages aujourd'hui", value: stats.passages_today, icon: <Calendar className="w-6 h-6 text-blue-500" />, color: 'from-blue-100 to-blue-50' },
    { title: 'Accès autorisés', value: stats.passages_autorises_today, icon: <CheckCircle className="w-6 h-6 text-green-500" />, color: 'from-green-100 to-green-50' },
    { title: 'Accès refusés', value: stats.passages_refuses_today, icon: <XCircle className="w-6 h-6 text-red-500" />, color: 'from-red-100 to-red-50' },
    { title: 'Badges actifs', value: stats.badges_actifs, icon: <CreditCard className="w-6 h-6 text-blue-600" />, color: 'from-blue-200 to-blue-50' },
    { title: 'Attributions actives', value: stats.attributions_actives, icon: <UserCheck className="w-6 h-6 text-yellow-500" />, color: 'from-yellow-100 to-yellow-50' },
    { title: 'Total personnel', value: stats.total_personnel, icon: <Users className="w-6 h-6 text-gray-600" />, color: 'from-gray-100 to-gray-50' },
  ];

  const quickActions = [
    { label: 'Nouveau Personnel', icon: <UserPlus className="w-5 h-5" />, href: '/personnel', color: 'bg-blue-600 hover:bg-blue-700' },
    { label: 'Attribuer Badge', icon: <UserCheck className="w-5 h-5" />, href: '/badges', color: 'bg-green-600 hover:bg-green-700' },
    { label: 'Journal Passages', icon: <Activity className="w-5 h-5" />, href: '/passages', color: 'bg-purple-600 hover:bg-purple-700' },
  ];

  // PassageCard : affiche comme la notification, fallback si pas de personnel
  const PassageCard = ({ passage }) => {
    let grade = passage.personnel?.grade || passage.grade || '';
    let nom = passage.personnel?.nom || passage.nom || '';
    let prenom = passage.personnel?.prenom || passage.prenom || '';
    let unite = passage.personnel?.unite || passage.unite || '';
    let heure = passage.date_acces ? formatTime(passage.date_acces) : '';
    let raison = passage.raison || passage.motif_refus;
    let resultat = passage.resultat;
    let contenu;
    if (resultat === 'autorise') {
      const typeIcon = passage.type_acces === 'sortie' ? '🔴' : '🟢';
      const typeText = passage.type_acces === 'sortie' ? 'Sortie' : 'Entrée';
      const typeColor = passage.type_acces === 'sortie' ? 'text-red-700' : 'text-green-700';

      contenu = (
        <span className="font-semibold">
          {typeIcon} <span className={typeColor}>{typeText}</span> autorisée à <span className="font-bold text-green-700">{grade ? grade + ' ' : ''}{nom} {prenom}</span> <span className="text-gray-700">({unite})</span> à <span className="text-blue-700">{heure}</span>
        </span>
      );
    } else if (resultat === 'refuse') {
      const typeIcon = passage.type_acces === 'sortie' ? '🔴' : '🟢';
      const typeText = passage.type_acces === 'sortie' ? 'Sortie' : 'Entrée';
      const typeColor = passage.type_acces === 'sortie' ? 'text-red-700' : 'text-green-700';

      contenu = (
        <span className="font-semibold">
          ⛔ <span className={typeColor}>{typeText}</span> refusée{' '}
          {raison && (
            <span className="text-red-700 font-bold">
              Motif : {raison}
              {/* N'ajoute l'EPC que s'il n'est pas déjà dans le motif */}
              {(!nom && passage.epc_code && !raison.includes(passage.epc_code)) && <> (EPC={passage.epc_code})</>}
            </span>
          )}{' '}
          <span className="text-gray-700">à</span> <span className="text-blue-700">{heure}</span>
        </span>
      );
    } else if (resultat === 'erreur') {
      const typeIcon = passage.type_acces === 'sortie' ? '🔴' : '🟢';
      const typeText = passage.type_acces === 'sortie' ? 'Sortie' : 'Entrée';
      const typeColor = passage.type_acces === 'sortie' ? 'text-red-700' : 'text-green-700';

      contenu = (
        <span className="font-semibold">
          ⚠️ Erreur badge {typeIcon} <span className={typeColor}>{typeText}</span> <span className="font-bold text-yellow-700">{grade ? grade + ' ' : ''}{nom} {prenom}</span> <span className="text-gray-700">({unite})</span> à <span className="text-blue-700">{heure}</span>
        </span>
      );
    } else {
      const typeIcon = passage.type_acces === 'sortie' ? '🔴' : '🟢';
      const typeText = passage.type_acces === 'sortie' ? 'Sortie' : 'Entrée';
      const typeColor = passage.type_acces === 'sortie' ? 'text-red-700' : 'text-green-700';

      contenu = (
        <span className="font-semibold">
          🔔 {typeIcon} <span className={typeColor}>{typeText}</span> détectée <span className="font-bold">{grade ? grade + ' ' : ''}{nom} {prenom}</span> <span className="text-gray-700">({unite})</span> à <span className="text-blue-700">{heure}</span>
        </span>
      );
    }
    return (
      <div className={`flex items-center p-4 border-l-4 shadow-sm transition bg-white hover:bg-blue-50/60 rounded-lg mb-2 ${
        resultat === 'autorise' ? 'border-green-500' :
        resultat === 'refuse' ? 'border-red-500' :
        resultat === 'erreur' ? 'border-yellow-500' :
        'border-blue-400'
      }`}>
        <span className="text-xl mr-3">{getStatusIcon(resultat)}</span>
        <div className="flex-1 text-base">{contenu}</div>
        <div className="text-xs text-gray-400 min-w-[90px] text-right">{passage.porte_libelle || 'Entrée Principale'}</div>
      </div>
    );
  };

  // Ajout d'une fonction de navigation selon la carte
  const handleStatCardClick = (title) => {
    if (title === "Passages aujourd'hui") {
      navigate('/passages?filter=all&today=1');
    } else if (title === 'Accès autorisés') {
      navigate('/passages?filter=autorise');
    } else if (title === 'Accès refusés') {
      navigate('/passages?filter=refuse');
    } else if (title === 'Badges actifs' || title === 'Attributions actives') {
      navigate('/badges');
    } else if (title === 'Total personnel') {
      navigate('/personnel');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* En-tête */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-1">Tableau de bord</h1>
          <p className="text-gray-500 text-sm">Vue d'ensemble en temps réel du contrôle d'accès</p>
        </div>
        <button
          onClick={loadDashboardData}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 flex items-center shadow"
          disabled={loading}
        >
          <Clock className="w-4 h-4 mr-2" />
          Actualiser
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Quick actions */}
      <div className="flex flex-wrap gap-4">
        {quickActions.map((action) => (
          <a
            key={action.label}
            href={action.href}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-white font-medium shadow transition-colors ${action.color}`}
          >
            {action.icon}
            <span>{action.label}</span>
          </a>
        ))}
      </div>

      {/* Cartes de statistiques */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((card) => (
          <div key={card.title} className="cursor-pointer" onClick={() => handleStatCardClick(card.title)}>
            <Card className={`bg-gradient-to-br ${card.color} shadow-lg hover:scale-[1.03] transition-transform`}>
              <CardHeader className="flex items-center space-x-4 border-none bg-transparent">
                <div className="p-3 rounded-full bg-white shadow">{card.icon}</div>
                <CardTitle>{card.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900">{card.value}</div>
              </CardContent>
            </Card>
          </div>
        ))}
      </div>

      {/* Passages récents */}
      <div className="w-full">
        <Card className="shadow-lg">
          <CardHeader className="flex flex-row items-center gap-3 border-b pb-2 mb-4">
            <Activity className="w-7 h-7 text-blue-600" />
            <CardTitle className="text-2xl">Passages récents</CardTitle>
            <span className="ml-auto text-sm text-gray-500">Mise à jour automatique en temps réel</span>
          </CardHeader>
          <CardContent>
            {recentPassages.length > 0 ? (
              <div className="space-y-3 max-h-96 overflow-y-auto pr-2">
                {recentPassages.map((passage, idx) => (
                  <PassageCard
                    key={passage.id ? `${passage.id}-${passage.date_acces}` : idx}
                    passage={passage}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Activity className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Aucun passage récent</p>
                <p className="text-sm">Les nouveaux passages apparaîtront ici automatiquement</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default Dashboard;
