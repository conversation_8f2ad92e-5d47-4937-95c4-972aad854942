import { useState, useEffect } from 'react';
import { UserPlus, Search, Eye, Edit, Trash2, Clock, CheckCircle } from 'lucide-react';
import { apiService } from '../services/apiService';
import { Card, CardHeader, CardTitle, CardContent } from '../components/ui/Card';
import { Modal } from '../components/ui/Modal';
import { Alert } from '../components/ui/Alert';
import { AddVisiteurForm } from '../components/AddVisiteurForm';
import { VisiteurForm } from '../components/VisiteurForm';

const formatDate = (date) => {
  return new Intl.DateTimeFormat('fr-FR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

function Visiteurs() {
  const [visiteurs, setVisiteurs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('view');
  const [selectedVisiteur, setSelectedVisiteur] = useState(null);

  // Charger les visiteurs
  const loadVisiteurs = async () => {
    try {
      setLoading(true);
      const response = await apiService.getVisiteurs();
      console.log('Response visiteurs:', response);
      const visiteursList = response.data?.data || response.data || [];
      console.log('Visiteurs list:', visiteursList);
      setVisiteurs(Array.isArray(visiteursList) ? visiteursList : []);
    } catch (err) {
      console.error('Erreur lors du chargement des visiteurs:', err);
      setError('Erreur lors du chargement des visiteurs');
      setVisiteurs([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadVisiteurs();
  }, []);

  // Filtrer les visiteurs
  const filteredVisiteurs = Array.isArray(visiteurs) ? visiteurs.filter(visiteur => {
    const searchLower = searchTerm.toLowerCase();
    return (
      visiteur.nom?.toLowerCase().includes(searchLower) ||
      visiteur.prenom?.toLowerCase().includes(searchLower) ||
      visiteur.cin?.toLowerCase().includes(searchLower) ||
      visiteur.matricule?.toLowerCase().includes(searchLower) ||
      visiteur.societe?.toLowerCase().includes(searchLower)
    );
  }) : [];

  // Actions
  const handleView = (visiteur) => {
    setSelectedVisiteur(visiteur);
    setModalMode('view');
    setModalOpen(true);
  };

  const handleEdit = (visiteur) => {
    setSelectedVisiteur(visiteur);
    setModalMode('edit');
    setModalOpen(true);
  };

  const handleDelete = async (visiteur) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer ${visiteur.nom} ${visiteur.prenom} ?`)) {
      try {
        await apiService.deletePersonnel(visiteur.id);
        await loadVisiteurs();
      } catch (err) {
        console.error('Erreur lors de la suppression:', err);
        setError('Erreur lors de la suppression du visiteur');
      }
    }
  };

  const handleTerminerVisite = async (visiteur) => {
    if (window.confirm(`Terminer la visite de ${visiteur.nom} ${visiteur.prenom} ?`)) {
      try {
        await apiService.terminerVisite(visiteur.id);
        await loadVisiteurs();
      } catch (err) {
        console.error('Erreur lors de la fin de visite:', err);
        setError('Erreur lors de la fin de visite');
      }
    }
  };

  const getStatutBadge = (visiteur) => {
    if (visiteur.attribution_active) {
      return (
        <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
          <CheckCircle className="w-3 h-3 mr-1" />
          En visite
        </span>
      );
    }
    return (
      <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
        <Clock className="w-3 h-3 mr-1" />
        Visite terminée
      </span>
    );
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'militaire_externe': return 'Militaire Externe';
      case 'civil_externe': return 'Civil Externe';
      default: return type;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-600">Chargement des visiteurs...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="error" dismissible onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* En-tête */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-1">Gestion des Visiteurs EM/ZS</h1>
          <p className="text-gray-500 text-sm">Créer, rechercher et gérer les visiteurs externes (militaires et civils)</p>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center shadow"
        >
          <UserPlus className="w-4 h-4 mr-2" />
          Nouveau Visiteur
        </button>
      </div>

      {/* Barre de recherche */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Rechercher par nom, prénom, CIN, matricule ou société..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Tableau des visiteurs */}
      <Card>
        <CardHeader>
          <CardTitle>Visiteurs ({filteredVisiteurs.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visiteur</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CIN/Matricule</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Société/Unité</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Destination</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entrée</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredVisiteurs.map((visiteur) => (
                  <tr key={visiteur.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          {visiteur.photo ? (
                            <img className="h-10 w-10 rounded-full object-cover" src={visiteur.photo} alt="" />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-700">
                                {visiteur.nom?.charAt(0)}{visiteur.prenom?.charAt(0)}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{visiteur.nom} {visiteur.prenom}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{getTypeLabel(visiteur.type)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {visiteur.type === 'militaire_externe' ? visiteur.matricule : visiteur.cin}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {visiteur.type === 'militaire_externe' ? visiteur.unite_origine : visiteur.societe}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{visiteur.destination || '-'}</td>
                    <td className="px-6 py-4 whitespace-nowrap">{getStatutBadge(visiteur)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {visiteur.horaire_entree ? formatDate(new Date(visiteur.horaire_entree)) : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button onClick={() => handleView(visiteur)} className="text-blue-600 hover:text-blue-900">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button onClick={() => handleEdit(visiteur)} className="text-green-600 hover:text-green-900">
                        <Edit className="w-4 h-4" />
                      </button>
                      {visiteur.attribution_active && (
                        <button onClick={() => handleTerminerVisite(visiteur)} className="text-orange-600 hover:text-orange-900" title="Terminer la visite">
                          <Clock className="w-4 h-4" />
                        </button>
                      )}
                      <button onClick={() => handleDelete(visiteur)} className="text-red-600 hover:text-red-900">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {filteredVisiteurs.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                Aucun visiteur trouvé
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Formulaire modal d'ajout */}
      <Modal isOpen={showForm} onClose={() => setShowForm(false)} title="Nouveau visiteur" size="lg">
        <AddVisiteurForm
          onSuccess={async () => {
            setShowForm(false);
            await loadVisiteurs();
          }}
        />
      </Modal>

      {/* Modale détails/modification */}
      <Modal isOpen={modalOpen} onClose={() => setModalOpen(false)} title={modalMode === 'edit' ? 'Modifier le visiteur' : 'Détails du visiteur'} size="md">
        {modalMode === 'edit' && selectedVisiteur ? (
          <VisiteurForm visiteur={selectedVisiteur} onSuccess={async () => { setModalOpen(false); await loadVisiteurs(); }} onCancel={() => setModalOpen(false)} />
        ) : selectedVisiteur ? (
          <div>Détails du visiteur (à implémenter)</div>
        ) : null}
      </Modal>
    </div>
  );
}

export default Visiteurs;
