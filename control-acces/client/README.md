# Frontend – Application Web (React)

Ce dossier contient le code source du frontend de l’application de contrôle d’accès. Il s’agit d’une application web développée avec React, Vite et TailwindCSS.

## Fonctionnalités principales
- Tableau de bord des accès
- Gestion des badges, du personnel, des passages
- Visualisation des alertes
- Interface moderne et responsive

## Technologies utilisées
- **React** 19+
- **Vite** (build ultra-rapide)
- **TailwindCSS** (design moderne)
- **React Router** (navigation)
- **Axios** (requêtes API)
- **Recharts** (graphiques)

## Installation
```bash
cd control-acces/client
npm install
```

## Lancement en développement
```bash
npm run dev
```
L’application sera accessible sur [http://localhost:5173](http://localhost:5173)

## Build pour la production
```bash
npm run build
```

## Linting
```bash
npm run lint
```

## Structure des dossiers principaux
- `src/components/` : Composants réutilisables (tableaux, formulaires, cartes, etc.)
- `src/pages/` : Pages principales (Dashboard, Badges, Passages…)
- `src/services/` : Appels API (axios)
- `src/assets/` : Images et ressources statiques
- `src/contexts/` : Contextes React (état global)
- `src/hooks/` : Hooks personnalisés

## Configuration
- Les appels API pointent par défaut sur le backend Express (http://localhost:3011/api)
- Adapter l’URL si besoin dans `src/services/apiService.js`

## Auteur
- [Ton Nom ou Équipe] 

## Écriture RFID à distance (fonctionnalité avancée)

- Un champ "IP du terminal C72" est disponible en haut de la page "Badges".
- Saisissez l'adresse IP du terminal Android une seule fois : elle est mémorisée automatiquement (localStorage).
- Le bouton "Écrire sur badge" utilise cette IP pour envoyer la commande d'écriture RFID à distance, sans vous la redemander à chaque fois.
- Le message de retour (succès ou erreur) s'affiche directement dans l'interface sous forme de bannière colorée.
- Un bip sonore est émis sur le terminal Android en cas d'écriture réussie.

**Pré-requis** :
- Le terminal Android doit être sur le même réseau que le backend.
- L'application Android doit être lancée et le serveur HTTP démarré. 