# Backend – API Node.js/Express

Ce dossier contient le code source du backend de l’application de contrôle d’accès. Il s’agit d’une API REST développée avec Node.js, Express et PostgreSQL.

## Fonctionnalités principales
- Gestion des utilisateurs, badges, passages, alertes
- Authentification JWT
- WebSocket pour notifications temps réel
- Upload de photos (Multer)
- Sécurité (Helmet, CORS, Rate Limiting)

## Technologies utilisées
- **Node.js** 18+
- **Express**
- **Sequelize** (ORM PostgreSQL)
- **PostgreSQL**
- **WebSocket (ws)**
- **JWT** (authentification)
- **Multer** (upload fichiers)

## Installation
```bash
cd control-acces/server
npm install
```

## Lancement en développement
```bash
npm start
```
L’API sera accessible sur [http://localhost:3011/api](http://localhost:3011/api)

## WebSocket
- Port par défaut : `3012`
- Utilisé pour notifier le frontend en temps réel (nouveaux passages, alertes…)

## Structure des dossiers principaux
- `controllers/` : Logique métier (badges, passages, personnel, alertes…)
- `models/` : Modèles Sequelize (tables SQL)
- `routes/` : Définition des routes API
- `uploads/` : Stockage des fichiers uploadés (photos)
- `utils/` : Fonctions utilitaires (CORS, upload, broadcast…)
- `config/` : Configuration base de données

## Configuration
- Variables d’environnement à placer dans un fichier `.env` (exemple) :
  ```env
  DB_HOST=localhost
  DB_PORT=5432
  DB_NAME=controle_acces
  DB_USER=postgres
  DB_PASS=motdepasse
  PORT=3011
  WS_PORT=3012
  JWT_SECRET=un_secret
  ```
- Adapter la configuration dans `config/database.js` si besoin.



## Auteur
- [Ton Nom ou Équipe] 

## Écriture RFID à distance (fonctionnalité avancée)

- Le backend expose une route POST `/api/badges/:id/write`.
- Cette route reçoit l'ID du badge et l'IP du terminal Android (C72) depuis le frontend.
- Le backend récupère l'EPC du badge dans la base de données, puis envoie une requête HTTP POST à `http://<ip_terminal>:8080/write-epc` avec `{ "epc": "..." }`.
- Le terminal Android tente d'écrire l'EPC sur le badge présenté et retourne un message de succès ou d'erreur.
- Le backend relaie ce message au frontend, qui l'affiche à l'utilisateur.

**Pré-requis** :
- Le terminal Android doit être sur le même réseau local que le backend.
- L'application Android doit être lancée et le serveur HTTP démarré.

**Flux complet** :
1. L'utilisateur saisit l'IP du terminal dans le frontend (une seule fois).
2. Il clique sur "Écrire sur badge" pour un badge donné.
3. Le frontend appelle `/api/badges/:id/write` avec l'IP du terminal.
4. Le backend contacte le terminal Android pour écrire l'EPC.
5. Le résultat (succès/erreur) est affiché dans l'interface web. 