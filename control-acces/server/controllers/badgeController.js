const { Badge, AttributionBadge, Personnel, TypeBadge, Grade, Unite } = require('../models');
const { Op } = require('sequelize');
const axios = require('axios');

exports.attribuerBadge = async (req, res) => {
  try {
    const { personnel_id, epc_code, objet_visite } = req.body;
    if (!personnel_id || !epc_code) {
      return res.status(400).json({ error: 'ID personnel et code EPC requis' });
    }
    const personnel = await Personnel.findByPk(personnel_id);
    if (!personnel) {
      return res.status(404).json({ error: 'Personnel non trouvé' });
    }
    // Empêcher plusieurs badges actifs pour un personnel interne
    if (personnel.id_type_personnel === 1) {
      const { AttributionBadge } = require('../models');
      const activeAttribution = await AttributionBadge.findOne({
        where: { id_personnel: personnel_id, statut: 'actif' }
      });
      if (activeAttribution) {
        return res.status(400).json({ error: 'Ce personnel interne possède déjà un badge actif.' });
      }
    }
    let badge = await Badge.findOne({ where: { epc_code } });
    if (!badge) {
      badge = await Badge.create({
        epc_code,
        id_type_badge: personnel.id_type_personnel,
        permanent: personnel.id_type_personnel === 1,
        actif: true,
        description: `Badge pour ${personnel.nom} ${personnel.prenom}`
      });
    }
    const attribution = await AttributionBadge.create({
      id_personnel: personnel_id,
      id_badge: badge.id,
      statut: 'actif',
      objet_visite: objet_visite || null
    });
    res.json({
      message: 'Badge attribué avec succès',
      attribution: {
        id: attribution.id,
        personnel: { nom: personnel.nom, prenom: personnel.prenom },
        badge: { epc_code },
        date_attribution: attribution.date_attribution
      }
    });
  } catch (error) {
    res.status(500).json({ error: "Erreur lors de l'attribution du badge" });
  }
};

exports.listerBadges = async (req, res) => {
  try {
    // On récupère tous les badges avec leur attribution active (si existante) et le personnel associé
    const badges = await Badge.findAll({
      attributes: ['id', 'epc_code', 'numero_visuel', 'id_type_badge', 'permanent', 'actif', 'description', 'created_at', 'updated_at'],
      include: [
        {
          model: TypeBadge,
          as: 'type_badge',
          attributes: ['nom_type_badge', 'description', 'couleur']
        },
        {
          model: AttributionBadge,
          as: 'attributions',
          where: { statut: 'actif' },
          required: false,
          include: [
            {
              model: Personnel,
              as: 'personnel',
              include: [
                {
                  model: Grade,
                  as: 'grade',
                  attributes: ['nom_grade']
                },
                {
                  model: Unite,
                  as: 'unite',
                  attributes: ['nom_unite']
                }
              ]
            }
          ]
        }
      ]
    });
    // Formatage pour le front
    const result = badges.map(badge => {
      const attribution = badge.attributions && badge.attributions[0];
      console.log('Badge brut de la DB:', {
        id: badge.id,
        numero_visuel: badge.numero_visuel,
        created_at: badge.created_at,
        createdAt: badge.createdAt,
        permanent: badge.permanent
      });

      // Log spécial pour les badges multitâches
      if (!badge.permanent) {
        console.log(`Badge multitâche ${badge.id} - createdAt:`, badge.createdAt, 'Type:', typeof badge.createdAt);
        console.log(`Badge multitâche ${badge.id} - dataValues:`, badge.dataValues);
        console.log(`Badge multitâche ${badge.id} - get('created_at'):`, badge.get('created_at'));
      }
      return {
        id: badge.id,
        epc_code: badge.epc_code,
        numero_visuel: badge.numero_visuel,
        actif: badge.actif,
        description: badge.description,
        type: badge.id_type_badge,
        permanent: badge.permanent,
        created_at: badge.get('created_at') || badge.createdAt,
        type_badge: badge.type_badge ? {
          nom_type_badge: badge.type_badge.nom_type_badge,
          description: badge.type_badge.description,
          couleur: badge.type_badge.couleur
        } : null,
        personnel: attribution && attribution.personnel ? {
          id: attribution.personnel.id,
          nom: attribution.personnel.nom,
          prenom: attribution.personnel.prenom,
          matricule: attribution.personnel.matricule,
          type: attribution.personnel.id_type_personnel,
          grade: attribution.personnel.grade ? attribution.personnel.grade.nom_grade : null,
          unite: attribution.personnel.unite ? attribution.personnel.unite.nom_unite : null
        } : null
      };
    });
    res.json({ badges: result });
  } catch (error) {
    res.status(500).json({ error: "Erreur lors de la récupération des badges" });
  }
};

exports.supprimerBadge = async (req, res) => {
  try {
    const { id } = req.params;
    const { Badge, AttributionBadge, Passage } = require('../models');
    // Supprimer d'abord les passages liés
    await Passage.destroy({ where: { id_badge: id } });
    // Supprimer les attributions liées
    await AttributionBadge.destroy({ where: { id_badge: id } });
    // Puis supprimer le badge
    const deleted = await Badge.destroy({ where: { id } });
    if (deleted) {
      res.json({ message: 'Badge supprimé avec succès' });
    } else {
      res.status(404).json({ error: 'Badge non trouvé' });
    }
  } catch (error) {
    res.status(500).json({ error: 'Erreur lors de la suppression du badge' });
  }
};

exports.writeEpcToTerminal = async (req, res) => {
  try {
    const { id } = req.params;
    const { ip } = req.body;
    if (!ip) {
      return res.status(400).json({ success: false, message: "IP du terminal requise" });
    }
    const badge = await Badge.findByPk(id);
    if (!badge) {
      return res.status(404).json({ success: false, message: "Badge non trouvé" });
    }
    const epc = badge.epc_code;
    try {
      const response = await axios.post(`http://${ip}:8080/write-epc`, { epc }, { timeout: 5000 });
      return res.json({ success: response.data.success, message: response.data.message });
    } catch (err) {
      let msg = err.response?.data?.message || err.message || 'Erreur lors de la requête vers le terminal';
      return res.status(500).json({ success: false, message: msg });
    }
  } catch (error) {
    res.status(500).json({ success: false, message: "Erreur serveur: " + error.message });
  }
};

// Récupérer les badges disponibles selon le type de visiteur
exports.getBadgesDisponibles = async (req, res) => {
  try {
    const { type } = req.query;

    // Déterminer le type de badge selon le type de visiteur
    let typeBadgeFilter = {};
    if (type === 'militaire_externe') {
      typeBadgeFilter = { nom_type_badge: { [Op.like]: '%militaire%' } };
    } else if (type === 'civil_externe') {
      typeBadgeFilter = { nom_type_badge: { [Op.like]: '%civil%' } };
    }

    const badges = await Badge.findAll({
      where: {
        actif: true,
        permanent: false
      },
      include: [
        {
          model: TypeBadge,
          as: 'type_badge',
          where: typeBadgeFilter,
          attributes: ['nom_type_badge', 'couleur']
        },
        {
          model: AttributionBadge,
          as: 'attributions',
          where: { statut: 'actif' },
          required: false
        }
      ]
    });

    // Filtrer les badges non attribués
    const badgesDisponibles = badges.filter(badge =>
      !badge.attributions || badge.attributions.length === 0
    );

    const badgesFormatted = badgesDisponibles.map(badge => ({
      id: badge.id,
      numero_visuel: badge.numero_visuel,
      epc_code: badge.epc_code,
      type_badge: badge.type_badge.nom_type_badge,
      couleur: badge.type_badge.couleur
    }));

    res.json({
      success: true,
      data: badgesFormatted
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des badges disponibles:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des badges disponibles'
    });
  }
};

// Récupérer les types de badges
exports.getTypesBadges = async (req, res) => {
  try {
    // Vérifier si le modèle TypeBadge existe
    if (!TypeBadge) {
      // Retourner des types par défaut si le modèle n'existe pas
      const typesParDefaut = [
        { id: 1, nom_type_badge: 'Badge Permanent', description: 'Badge personnel permanent', couleur: '#10B981' },
        { id: 2, nom_type_badge: 'Badge Multitâche', description: 'Badge temporaire réutilisable', couleur: '#3B82F6' },
        { id: 3, nom_type_badge: 'Badge Visiteur', description: 'Badge pour visiteurs externes', couleur: '#F59E0B' }
      ];

      return res.json({
        success: true,
        data: typesParDefaut
      });
    }

    const types = await TypeBadge.findAll({
      attributes: ['id', 'nom_type_badge', 'description', 'couleur'],
      order: [['nom_type_badge', 'ASC']]
    });

    res.json({
      success: true,
      data: types
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des types de badges:', error);

    // En cas d'erreur, retourner des types par défaut
    const typesParDefaut = [
      { id: 1, nom_type_badge: 'Badge Permanent', description: 'Badge personnel permanent', couleur: '#10B981' },
      { id: 2, nom_type_badge: 'Badge Multitâche', description: 'Badge temporaire réutilisable', couleur: '#3B82F6' },
      { id: 3, nom_type_badge: 'Badge Visiteur', description: 'Badge pour visiteurs externes', couleur: '#F59E0B' }
    ];

    res.json({
      success: true,
      data: typesParDefaut
    });
  }
};

// Créer un type de badge
exports.createTypeBadge = async (req, res) => {
  try {
    console.log('Création type de badge - Body reçu:', req.body);
    const { nom_type_badge } = req.body;

    if (!nom_type_badge || !nom_type_badge.trim()) {
      console.log('Nom du type manquant');
      return res.status(400).json({
        success: false,
        message: 'Le nom du type de badge est obligatoire'
      });
    }

    const nomType = nom_type_badge.trim();
    console.log('Création du type:', nomType);

    // Vérifier si le type existe déjà
    const typeExistant = await TypeBadge.findOne({
      where: { nom_type_badge: nomType }
    });

    if (typeExistant) {
      console.log('Type déjà existant:', typeExistant.id);
      return res.json({
        success: true,
        message: 'Type de badge déjà existant',
        data: {
          id: typeExistant.id,
          nom_type_badge: typeExistant.nom_type_badge
        }
      });
    }

    // Créer le nouveau type
    const nouveauType = await TypeBadge.create({
      nom_type_badge: nomType,
      description: `Type créé automatiquement: ${nomType}`,
      couleur: '#3B82F6' // Bleu par défaut
    });

    console.log('Nouveau type créé:', nouveauType.id);

    res.status(201).json({
      success: true,
      message: 'Type de badge créé avec succès',
      data: {
        id: nouveauType.id,
        nom_type_badge: nouveauType.nom_type_badge,
        description: nouveauType.description,
        couleur: nouveauType.couleur
      }
    });
  } catch (error) {
    console.error('Erreur lors de la création du type de badge:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création du type de badge',
      error: error.message
    });
  }
};

// Créer un badge permanent
exports.createBadgePermanent = async (req, res) => {
  try {
    const { personnel_id, epc_code } = req.body;

    if (!personnel_id || !epc_code) {
      return res.status(400).json({
        success: false,
        message: 'Personnel et code EPC sont obligatoires'
      });
    }

    // Vérifier que le personnel existe et est militaire interne
    const personnel = await Personnel.findByPk(personnel_id);
    if (!personnel || personnel.id_type_personnel !== 1) {
      return res.status(400).json({
        success: false,
        message: 'Personnel non trouvé ou n\'est pas un militaire interne'
      });
    }

    // Créer le badge permanent
    const badge = await Badge.create({
      epc_code,
      numero_visuel: `PERM-${personnel.matricule}`,
      permanent: true,
      actif: true,
      id_type_badge: 1, // Type badge permanent par défaut
      description: `Badge permanent de ${personnel.nom} ${personnel.prenom}`
    });

    // Créer l'attribution permanente
    await AttributionBadge.create({
      id_personnel: personnel_id,
      id_badge: badge.id,
      statut: 'actif',
      date_attribution: new Date()
    });

    res.status(201).json({
      success: true,
      message: 'Badge permanent créé avec succès',
      data: { id: badge.id }
    });
  } catch (error) {
    console.error('Erreur lors de la création du badge permanent:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création du badge permanent'
    });
  }
};

// Créer un badge multitâche
exports.createBadgeMultitache = async (req, res) => {
  try {
    console.log('Données reçues pour création badge multitâche:', req.body);
    const { type_badge_id, nouveau_type, numero_visuel, description, epc_code } = req.body;
    console.log('Champs extraits:', { type_badge_id, nouveau_type, numero_visuel, description, epc_code });

    if (!numero_visuel || !epc_code) {
      return res.status(400).json({
        success: false,
        message: 'Numéro visuel et code EPC sont obligatoires'
      });
    }

    let typeBadgeId = type_badge_id;

    // Créer un nouveau type de badge si spécifié
    if (nouveau_type && !type_badge_id) {
      const nouveauTypeBadge = await TypeBadge.create({
        nom_type_badge: nouveau_type,
        description: `Type créé automatiquement: ${nouveau_type}`,
        couleur: '#3B82F6' // Bleu par défaut
      });
      typeBadgeId = nouveauTypeBadge.id;
    }

    // Créer le badge multitâche
    const badgeCreateData = {
      epc_code,
      numero_visuel,
      permanent: false,
      actif: true,
      id_type_badge: typeBadgeId || 2, // Type multitâche par défaut
      description: description || `Badge multitâche ${numero_visuel}`
    };

    console.log('Données pour création badge:', badgeCreateData);

    const badge = await Badge.create(badgeCreateData);

    res.status(201).json({
      success: true,
      message: 'Badge multitâche créé avec succès',
      data: { id: badge.id }
    });
  } catch (error) {
    console.error('Erreur lors de la création du badge multitâche:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création du badge multitâche'
    });
  }
};