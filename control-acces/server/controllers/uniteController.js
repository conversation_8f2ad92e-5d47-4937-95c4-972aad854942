const { Unite } = require('../models');
const { Op } = require('sequelize');

exports.listerUnites = async (req, res) => {
  try {
    const { search } = req.query;
    let where = {};
    if (search) {
      where = {
        nom_unite: { [Op.iLike]: `%${search}%` }
      };
    }
    const unites = await Unite.findAll({ where, order: [['nom_unite', 'ASC']] });
    res.json(unites);
  } catch (error) {
    res.status(500).json({ error: 'Erreur lors de la récupération des unités' });
  }
};

exports.creerUnite = async (req, res) => {
  try {
    const { nom_unite, code_unite, description } = req.body;
    if (!nom_unite) {
      return res.status(400).json({ error: 'Le nom de l\'unité est requis' });
    }
    const unite = await Unite.create({ nom_unite, code_unite, description });
    res.status(201).json(unite);
  } catch (error) {
    res.status(500).json({ error: 'Erreur lors de la création de l\'unité' });
  }
}; 