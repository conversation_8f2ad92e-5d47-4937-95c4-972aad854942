const { Personnel, Grade, Unite, Badge, TypeBadge, AttributionBadge, TypePersonnel } = require('../models');
const { PersonnelValidationService } = require('../services/PersonnelValidationService');
const { Op } = require('sequelize');

class VisiteurController {
  // Récupérer tous les visiteurs
  async getVisiteurs(req, res) {
    try {
      const visiteurs = await Personnel.findAll({
        where: {
          id_type_personnel: [2, 3] // 2 = militaire_externe, 3 = civil_externe
        },
        attributes: [
          'id', 'nom', 'prenom', 'matricule', 'cin', 'societe', 'photo',
          'id_type_personnel', 'id_grade', 'id_unite_origine', 'created_at'
        ],
        include: [
          {
            model: Grade,
            as: 'grade',
            attributes: ['nom_grade']
          },
          {
            model: Unite,
            as: 'unite_origine',
            attributes: ['nom_unite']
          },
          {
            model: AttributionBadge,
            as: 'attributions',
            where: { statut: 'actif' },
            required: false,
            include: [
              {
                model: Badge,
                as: 'badge',
                attributes: ['numero_visuel', 'epc_code'],
                include: [
                  {
                    model: TypeBadge,
                    as: 'type_badge',
                    attributes: ['nom_type_badge']
                  }
                ]
              },
              {
                model: Unite,
                as: 'destination_unite',
                attributes: ['nom_unite']
              }
            ]
          }
        ],
        order: [['created_at', 'DESC']]
      });

      // Transformer les données pour le frontend
      const visiteursFormatted = visiteurs.map(visiteur => {
        const obj = visiteur.toJSON();
        const attribution = obj.attributions && obj.attributions[0];
        
        return {
          ...obj,
          type: obj.id_type_personnel === 2 ? 'militaire_externe' : 'civil_externe',
          grade: obj.grade ? obj.grade.nom_grade : null,
          unite_origine: obj.unite_origine ? obj.unite_origine.nom_unite : null,
          attribution_active: !!attribution,
          badge_numero: attribution?.badge?.numero_visuel || null,
          badge_id: attribution?.id_badge || null,
          destination: attribution?.destination_unite?.nom_unite || null,
          destination_id: attribution?.destination || null,
          objet_visite: attribution?.objet_visite || null,
          horaire_entree: attribution?.date_attribution || null
        };
      });

      res.json({
        success: true,
        data: visiteursFormatted
      });
    } catch (error) {
      console.error('Erreur lors de la récupération des visiteurs:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des visiteurs'
      });
    }
  }

  // Créer un nouveau visiteur
  async createVisiteur(req, res) {
    try {
      const { type } = req.body;
      
      // Validation selon le type
      let validationResult;
      if (type === 'militaire_externe') {
        validationResult = PersonnelValidationService.validateMilitaireExterne(req.body);
      } else if (type === 'civil_externe') {
        validationResult = PersonnelValidationService.validateCivilExterne(req.body);
      } else {
        return res.status(400).json({
          success: false,
          message: 'Type de visiteur invalide'
        });
      }

      if (!validationResult.isValid) {
        return res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: validationResult.errors
        });
      }

      // Créer le personnel
      const personnelData = {
        ...validationResult.sanitizedData,
        id_type_personnel: type === 'militaire_externe' ? 2 : 3,
        photo: req.file ? `/uploads/${req.file.filename}` : null
      };

      const personnel = await Personnel.create(personnelData);

      // Créer l'attribution de badge si spécifiée
      if (req.body.id_badge && req.body.destination) {
        await AttributionBadge.create({
          id_personnel: personnel.id,
          id_badge: parseInt(req.body.id_badge),
          destination: parseInt(req.body.destination),
          objet_visite: req.body.objet_visite || '',
          date_attribution: req.body.horaire_entree || new Date(),
          statut: 'actif'
        });
      }

      res.status(201).json({
        success: true,
        message: 'Visiteur créé avec succès',
        data: { id: personnel.id }
      });
    } catch (error) {
      console.error('Erreur lors de la création du visiteur:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la création du visiteur'
      });
    }
  }

  // Mettre à jour un visiteur
  async updateVisiteur(req, res) {
    try {
      const { id } = req.params;
      const visiteur = await Personnel.findByPk(id);

      if (!visiteur) {
        return res.status(404).json({
          success: false,
          message: 'Visiteur non trouvé'
        });
      }

      // Validation des données
      const validationResult = PersonnelValidationService.validateForUpdate(req.body);
      if (!validationResult.isValid) {
        return res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: validationResult.errors
        });
      }

      // Mise à jour du personnel
      await visiteur.update(validationResult.sanitizedData);

      // Mise à jour du badge si nécessaire
      if (req.body.id_badge) {
        const attribution = await AttributionBadge.findOne({
          where: { id_personnel: id, statut: 'actif' }
        });

        if (attribution && parseInt(req.body.id_badge) !== attribution.id_badge) {
          // Désactiver l'ancienne attribution
          await attribution.update({ statut: 'desactive', date_fin: new Date() });
          
          // Créer une nouvelle attribution
          await AttributionBadge.create({
            id_personnel: id,
            id_badge: parseInt(req.body.id_badge),
            destination: req.body.destination || attribution.destination,
            objet_visite: req.body.objet_visite || attribution.objet_visite,
            statut: 'actif'
          });
        }
      }

      res.json({
        success: true,
        message: 'Visiteur mis à jour avec succès'
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour du visiteur:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du visiteur'
      });
    }
  }

  // Terminer une visite
  async terminerVisite(req, res) {
    try {
      const { id } = req.params;

      const attribution = await AttributionBadge.findOne({
        where: { id_personnel: id, statut: 'actif' }
      });

      if (!attribution) {
        return res.status(404).json({
          success: false,
          message: 'Aucune visite active trouvée pour ce visiteur'
        });
      }

      await attribution.update({
        statut: 'expire',
        date_fin: new Date()
      });

      res.json({
        success: true,
        message: 'Visite terminée avec succès'
      });
    } catch (error) {
      console.error('Erreur lors de la fin de visite:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la fin de visite'
      });
    }
  }

  // Supprimer un visiteur
  async deleteVisiteur(req, res) {
    try {
      const { id } = req.params;
      const visiteur = await Personnel.findByPk(id);

      if (!visiteur) {
        return res.status(404).json({
          success: false,
          message: 'Visiteur non trouvé'
        });
      }

      // Terminer toutes les attributions actives
      await AttributionBadge.update(
        { statut: 'desactive', date_fin: new Date() },
        { where: { id_personnel: id, statut: 'actif' } }
      );

      // Supprimer le visiteur
      await visiteur.destroy();

      res.json({
        success: true,
        message: 'Visiteur supprimé avec succès'
      });
    } catch (error) {
      console.error('Erreur lors de la suppression du visiteur:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression du visiteur'
      });
    }
  }
}

module.exports = new VisiteurController();
