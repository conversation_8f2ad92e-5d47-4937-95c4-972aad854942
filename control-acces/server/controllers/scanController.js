const scanService = require('../services/scanService');

/**
 * Endpoint de test pour l'application Android
 */
exports.testConnection = (req, res) => {
  res.json({
    message: 'API Maquette Test - Contrôle d\'Accès RFID',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
};

/**
 * Endpoint de test spécifique pour le scan Android
 */
exports.testScan = (req, res) => {
  res.json({ 
    message: 'Connexion Android OK',
    server: 'Maquette Test Backend',
    timestamp: new Date().toISOString()
  });
};

/**
 * Traite un scan de badge RFID depuis l'application Android
 */
exports.processScan = async (req, res) => {
  try {
    const { epc, timestamp, type_acces } = req.body;
    
    const result = await scanService.processScan(epc, timestamp, type_acces);
    res.json(result);
    
  } catch (error) {
    console.error('Erreur lors du traitement du scan:', error);
    res.status(500).json({ 
      error: 'Erreur lors du traitement du scan',
      message: error.message 
    });
  }
};
