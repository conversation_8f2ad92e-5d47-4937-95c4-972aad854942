// ===========================
// IMPORTS ET DÉPENDANCES
// ===========================
const { Personnel, Grade, Unite, AttributionBadge } = require('../models');
const BadgeService = require('../services/BadgeService');
const PersonnelValidationService = require('../services/PersonnelValidationService');

/**
 * ===========================
 * CRÉATION D'UN PERSONNEL MILITAIRE INTERNE - VERSION OPTIMISÉE
 * ===========================
 * Cette fonction gère la création complète d'un militaire interne
 * ARCHITECTURE AMÉLIORÉE : Logique séparée en services spécialisés
 */
exports.creerPersonnel = async (req, res) => {
  try {
    // ===========================
    // SECTION 1 : VALIDATION DES DONNÉES
    // ===========================
    const validation = PersonnelValidationService.validateCreationData(req.body);

    if (!validation.isValid) {
      return res.status(400).json({
        error: 'Données invalides',
        details: validation.errors
      });
    }

    // ===========================
    // SECTION 2 : VALIDATION DE LA PHOTO
    // ===========================
    const photoValidation = PersonnelValidationService.validatePhotoFile(req.file);

    if (!photoValidation.isValid) {
      return res.status(400).json({
        error: 'Fichier photo invalide',
        details: [photoValidation.error]
      });
    }

    // ===========================
    // SECTION 3 : PRÉPARATION DES DONNÉES PERSONNEL
    // ===========================
    const personnelData = {
      ...validation.sanitizedData,
      id_type_personnel: 1, // Force le type militaire interne
      photo: PersonnelValidationService.buildPhotoPath(req.file)
    };

    // ===========================
    // SECTION 4 : CRÉATION DU PERSONNEL
    // ===========================
    const personnel = await Personnel.create(personnelData);
    const personnelFull = await Personnel.findByPk(personnel.id);

    // ===========================
    // SECTION 5 : CRÉATION ET ATTRIBUTION DU BADGE
    // ===========================
    const { badge, attribution } = await BadgeService.createAndAttributeBadge(personnelFull);

    // ===========================
    // SECTION 6 : RÉPONSE STRUCTURÉE
    // ===========================
    res.status(201).json({
      message: 'Militaire interne créé avec succès et badge attribué',
      personnel: {
        id: personnelFull.id,
        nom: personnelFull.nom,
        prenom: personnelFull.prenom,
        matricule: personnelFull.matricule,
        id_grade: personnelFull.id_grade,
        id_unite: personnelFull.id_unite,
        photo: personnelFull.photo,
        created_at: personnelFull.created_at
      },
      badge: {
        id: badge.id,
        epc_code: badge.epc_code,
        id_type_badge: badge.id_type_badge,
        permanent: badge.permanent,
        actif: badge.actif,
        description: badge.description
      },
      attribution: {
        id: attribution.id,
        date_attribution: attribution.date_attribution,
        statut: attribution.statut
      }
    });

  } catch (error) {
    console.error('Erreur lors de la création du personnel:', error);
    res.status(500).json({
      error: 'Erreur lors de la création du militaire interne',
      message: error.message
    });
  }
};

/**
 * ===========================
 * LISTING DU PERSONNEL AVEC JOINTURES
 * ===========================
 * Récupère la liste complète du personnel avec les informations de grade et d'unité
 * Fonction bien structurée avec une seule responsabilité
 */
exports.listerPersonnel = async (req, res) => {
  try {
    // ===========================
    // SECTION 1 : REQUÊTE AVEC JOINTURES OPTIMISÉES
    // ===========================
    const personnel = await Personnel.findAll({
      // Sélection explicite des champs pour optimiser les performances
      attributes: [
        'id', 'nom', 'prenom', 'matricule', 'photo',
        'id_type_personnel', 'id_grade', 'id_unite', 'id_unite_origine',
        'societe', 'created_at'
      ],

      // Jointures avec les tables de référence
      include: [
        {
          model: Grade,
          as: 'grade',
          attributes: ['nom_grade'] // Seulement le nom du grade pour optimiser
        },
        {
          model: Unite,
          as: 'unite',
          attributes: ['nom_unite'] // Unité de travail
        },
        {
          model: Unite,
          as: 'unite_origine',
          attributes: ['nom_unite'] // Unité d'origine
        }
      ],

      // Tri par date de création décroissante (plus récents en premier)
      order: [['created_at', 'DESC']],

      // Limitation à 50 résultats pour éviter la surcharge
      limit: 50
    });

    // ===========================
    // SECTION 2 : TRANSFORMATION DES DONNÉES POUR LE FRONTEND
    // ===========================
    res.json({
      personnel: personnel.map(p => {
        const obj = p.toJSON(); // Conversion en objet JavaScript simple

        return {
          ...obj,
          // Extraction des noms depuis les objets de jointure
          grade: p.grade ? p.grade.nom_grade : null,
          unite: p.unite ? p.unite.nom_unite : null,
          unite_origine: p.unite_origine ? p.unite_origine.nom_unite : null,

          // Conversion des IDs de type en libellés lisibles
          type: p.id_type_personnel === 1 ? 'militaire_interne' :
                p.id_type_personnel === 2 ? 'militaire_externe' :
                'civil_externe'
        };
      })
    });
  } catch (error) {
    console.error('Erreur lors du listing du personnel:', error);
    res.status(500).json({ error: 'Erreur lors de la récupération du personnel' });
  }
};

/**
 * ===========================
 * SUPPRESSION D'UN PERSONNEL - VERSION OPTIMISÉE
 * ===========================
 * Supprime un personnel avec gestion des badges associés
 */
exports.supprimerPersonnel = async (req, res) => {
  try {
    // ===========================
    // SECTION 1 : VALIDATION DE L'ID
    // ===========================
    const { id } = req.params;

    if (!PersonnelValidationService.isValidId(id)) {
      return res.status(400).json({ error: 'ID de personnel invalide' });
    }

    // ===========================
    // SECTION 2 : VÉRIFICATION DE L'EXISTENCE
    // ===========================
    const existingPersonnel = await Personnel.findByPk(id);

    if (!existingPersonnel) {
      return res.status(404).json({ error: 'Personnel non trouvé' });
    }

    // ===========================
    // SECTION 3 : GESTION DES BADGES ASSOCIÉS
    // ===========================
    // Récupération des badges actifs du personnel
    const activeBadges = await BadgeService.getBadgesForPersonnel(id, 'actif');

    // Désactivation des badges au lieu de suppression pour conserver l'historique
    for (const badgeInfo of activeBadges) {
      await BadgeService.deactivateBadge(badgeInfo.badge.id);
    }

    // ===========================
    // SECTION 4 : SUPPRESSION EN CASCADE MANUELLE
    // ===========================
    // Suppression des attributions (historique conservé via désactivation des badges)
    await AttributionBadge.destroy({ where: { id_personnel: id } });

    // Suppression du personnel
    const deleted = await Personnel.destroy({ where: { id: parseInt(id) } });

    // ===========================
    // SECTION 5 : RÉPONSE AVEC DÉTAILS
    // ===========================
    res.json({
      message: 'Personnel supprimé avec succès',
      details: {
        personnelId: parseInt(id),
        badgesDesactives: activeBadges.length,
        nom: existingPersonnel.nom,
        prenom: existingPersonnel.prenom
      }
    });

  } catch (error) {
    console.error('Erreur lors de la suppression du personnel:', error);
    res.status(500).json({
      error: 'Erreur lors de la suppression du personnel',
      message: error.message
    });
  }
};

/**
 * ===========================
 * MODIFICATION D'UN PERSONNEL - VERSION OPTIMISÉE
 * ===========================
 * Met à jour les informations d'un personnel existant avec validation
 */
exports.modifierPersonnel = async (req, res) => {
  try {
    // ===========================
    // SECTION 1 : VALIDATION DE L'ID
    // ===========================
    const { id } = req.params;

    if (!PersonnelValidationService.isValidId(id)) {
      return res.status(400).json({ error: 'ID de personnel invalide' });
    }

    // ===========================
    // SECTION 2 : VALIDATION DES DONNÉES
    // ===========================
    const validation = PersonnelValidationService.validateUpdateData(req.body);

    if (!validation.isValid) {
      return res.status(400).json({
        error: 'Données invalides',
        details: validation.errors
      });
    }

    // ===========================
    // SECTION 3 : VALIDATION DE LA PHOTO (SI FOURNIE)
    // ===========================
    if (req.file) {
      const photoValidation = PersonnelValidationService.validatePhotoFile(req.file);

      if (!photoValidation.isValid) {
        return res.status(400).json({
          error: 'Fichier photo invalide',
          details: [photoValidation.error]
        });
      }

      // Ajout du chemin de la photo aux données validées
      validation.sanitizedData.photo = PersonnelValidationService.buildPhotoPath(req.file);
    }

    // ===========================
    // SECTION 4 : VÉRIFICATION DE L'EXISTENCE
    // ===========================
    const existingPersonnel = await Personnel.findByPk(id);

    if (!existingPersonnel) {
      return res.status(404).json({ error: 'Personnel non trouvé' });
    }

    // ===========================
    // SECTION 5 : MISE À JOUR EN BASE
    // ===========================
    const [updated] = await Personnel.update(validation.sanitizedData, { where: { id } });

    if (updated) {
      // Récupération des données mises à jour
      const updatedPersonnel = await Personnel.findByPk(id);

      res.json({
        message: 'Personnel modifié avec succès',
        personnel: {
          id: updatedPersonnel.id,
          nom: updatedPersonnel.nom,
          prenom: updatedPersonnel.prenom,
          matricule: updatedPersonnel.matricule,
          id_grade: updatedPersonnel.id_grade,
          id_unite: updatedPersonnel.id_unite,
          id_unite_origine: updatedPersonnel.id_unite_origine,
          photo: updatedPersonnel.photo,
          updated_at: updatedPersonnel.updated_at
        }
      });
    } else {
      res.status(500).json({ error: 'Aucune modification effectuée' });
    }

  } catch (error) {
    console.error('Erreur lors de la modification du personnel:', error);
    res.status(500).json({
      error: 'Erreur lors de la modification du personnel',
      message: error.message
    });
  }
};