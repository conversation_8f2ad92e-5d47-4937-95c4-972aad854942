// ===========================
// IMPORTS ET DÉPENDANCES
// ===========================
const PassageFilterService = require('../services/PassageFilterService');
const PassageStatsService = require('../services/PassageStatsService');
const PassageQueryService = require('../services/PassageQueryService');

/**
 * ===========================
 * LISTING DES PASSAGES AVEC FILTRAGE AVANCÉ - VERSION OPTIMISÉE
 * ===========================
 * Cette fonction gère l'affichage paginé des passages avec de multiples filtres
 * ARCHITECTURE AMÉLIORÉE : Logique séparée en services spécialisés
 */
exports.listerPassages = async (req, res) => {
  try {
    // ===========================
    // SECTION 1 : VALIDATION ET EXTRACTION DES PARAMÈTRES
    // ===========================
    const { limit, offset } = PassageFilterService.validatePagination(req.query);

    const filters = {
      dateDebut: req.query.dateDebut,
      dateFin: req.query.dateFin,
      resultat: req.query.resultat,
      type_acces: req.query.type_acces
    };

    // ===========================
    // SECTION 2 : CONSTRUCTION DES FILTRES
    // ===========================
    const { whereClause, whereReplacements } = PassageFilterService.buildFilters(filters);

    // ===========================
    // SECTION 3 : CALCUL DES STATISTIQUES
    // ===========================
    const stats = await PassageStatsService.calculateAllStats(whereClause, whereReplacements);

    // ===========================
    // SECTION 4 : RÉCUPÉRATION DES DONNÉES PRINCIPALES
    // ===========================
    const passages = await PassageQueryService.getPassagesWithDetails(
      whereClause,
      whereReplacements,
      limit,
      offset
    );

    // ===========================
    // SECTION 5 : RÉPONSE STRUCTURÉE
    // ===========================
    res.json({
      passages,
      total: stats.total,
      totalAutorise: stats.totalAutorise,
      totalRefuse: stats.totalRefuse,
      totalErreur: stats.totalErreur,
      totalEntree: stats.totalEntree,
      totalSortie: stats.totalSortie
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des passages:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des passages',
      message: error.message
    });
  }
};

/**
 * ===========================
 * SUPPRESSION D'UN PASSAGE UNIQUE - VERSION OPTIMISÉE
 * ===========================
 * Fonction simple et bien structurée avec gestion d'erreur améliorée
 */
exports.supprimerPassage = async (req, res) => {
  try {
    const { id } = req.params;

    // Validation de l'ID
    if (!id || isNaN(parseInt(id))) {
      return res.status(400).json({ error: 'ID de passage invalide' });
    }

    const { Passage } = require('../models');
    const deleted = await Passage.destroy({ where: { id: parseInt(id) } });

    if (deleted) {
      res.json({
        message: 'Passage supprimé avec succès',
        deletedId: parseInt(id)
      });
    } else {
      res.status(404).json({ error: 'Passage non trouvé' });
    }
  } catch (error) {
    console.error('Erreur lors de la suppression du passage:', error);
    res.status(500).json({
      error: 'Erreur lors de la suppression du passage',
      message: error.message
    });
  }
};

/**
 * ===========================
 * SUPPRESSION MULTIPLE DE PASSAGES - VERSION OPTIMISÉE
 * ===========================
 * Permet de supprimer plusieurs passages avec validation renforcée
 */
exports.supprimerPassages = async (req, res) => {
  try {
    const { ids } = req.body;

    // Validation des données d'entrée
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ error: 'Aucun ID fourni ou format invalide' });
    }

    // Validation que tous les IDs sont des nombres valides
    const validIds = ids.filter(id => !isNaN(parseInt(id))).map(id => parseInt(id));

    if (validIds.length === 0) {
      return res.status(400).json({ error: 'Aucun ID valide fourni' });
    }

    if (validIds.length !== ids.length) {
      console.warn(`IDs invalides ignorés: ${ids.filter(id => isNaN(parseInt(id)))}`);
    }

    const { Passage } = require('../models');
    const deleted = await Passage.destroy({ where: { id: validIds } });

    res.json({
      message: `${deleted} passage(s) supprimé(s) avec succès`,
      deletedCount: deleted,
      requestedCount: validIds.length
    });
  } catch (error) {
    console.error('Erreur lors de la suppression des passages:', error);
    res.status(500).json({
      error: 'Erreur lors de la suppression des passages',
      message: error.message
    });
  }
};