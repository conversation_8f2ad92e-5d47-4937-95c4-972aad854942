const { sequelize } = require('../models');

/**
 * Récupère les statistiques pour le dashboard
 */
exports.getStats = async (req, res) => {
  try {
    const stats = await sequelize.query(`
      SELECT
        (SELECT COUNT(*) FROM passage WHERE DATE(date_acces) = CURRENT_DATE) as passages_today,
        (SELECT COUNT(*) FROM passage WHERE DATE(date_acces) = CURRENT_DATE AND resultat = 'autorise') as passages_autorises_today,
        (SELECT COUNT(*) FROM passage WHERE DATE(date_acces) = CURRENT_DATE AND resultat = 'refuse') as passages_refuses_today,
        (SELECT COUNT(*) FROM badge WHERE actif = true) as badges_actifs,
        (SELECT COUNT(*) FROM attribution_badge WHERE statut = 'actif') as attributions_actives,
        (SELECT COUNT(*) FROM personnel) as total_personnel
    `, { type: sequelize.QueryTypes.SELECT });
    
    res.json(stats[0]);
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({ error: 'Erreur lors de la récupération des statistiques' });
  }
};
