{"name": "controle-acces-server", "version": "1.0.0", "description": "Backend API pour le système de contrôle d'accès militaire", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "pg": "^8.11.3", "sequelize": "^6.35.2", "winston": "^3.17.0", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["express", "postgresql", "rfid", "controle-acces"], "author": "Système de Contrôle d'Accès", "license": "MIT"}