const { sequelize } = require('../models');

/**
 * ===========================
 * SERVICE DE CALCUL DES STATISTIQUES POUR LES PASSAGES
 * ===========================
 * Ce service gère le calcul des statistiques et comptages
 * pour les passages avec filtres appliqués
 */
class PassageStatsService {
  /**
   * ===========================
   * MÉTHODE PRINCIPALE - CALCUL DE TOUTES LES STATISTIQUES
   * ===========================
   * Calcule toutes les statistiques nécessaires en une seule fois
   * 
   * @param {string} whereClause - Clause WHERE SQL
   * @param {Object} whereReplacements - Paramètres de remplacement
   * @returns {Object} Objet contenant toutes les statistiques
   */
  async calculateAllStats(whereClause, whereReplacements) {
    try {
      // Exécution parallèle de toutes les requêtes de statistiques
      const [
        totalResult,
        totalByTypeResult,
        totalByAccessTypeResult
      ] = await Promise.all([
        this._getTotalCount(whereClause, whereReplacements),
        this._getCountByResultat(whereClause, whereReplacements),
        this._getCountByTypeAcces(whereClause, whereReplacements)
      ]);

      // Traitement des résultats
      const total = totalResult[0]?.total || 0;
      const totalByType = this._processTotalByType(totalByTypeResult);
      const totalByAccessType = this._processTotalByAccessType(totalByAccessTypeResult);

      return {
        total,
        totalAutorise: totalByType.autorise,
        totalRefuse: totalByType.refuse,
        totalErreur: totalByType.erreur,
        totalEntree: totalByAccessType.entree,
        totalSortie: totalByAccessType.sortie
      };
    } catch (error) {
      console.error('Erreur lors du calcul des statistiques:', error);
      throw new Error('Erreur lors du calcul des statistiques');
    }
  }

  /**
   * ===========================
   * COMPTAGE TOTAL POUR LA PAGINATION
   * ===========================
   * Compte le nombre total de passages correspondant aux filtres
   */
  async _getTotalCount(whereClause, whereReplacements) {
    const query = `SELECT COUNT(*) as total FROM passage pa ${whereClause}`;
    
    return await sequelize.query(query, {
      type: sequelize.QueryTypes.SELECT,
      replacements: whereReplacements
    });
  }

  /**
   * ===========================
   * COMPTAGE PAR RÉSULTAT (autorisé/refusé/erreur)
   * ===========================
   * Compte les passages par type de résultat
   */
  async _getCountByResultat(whereClause, whereReplacements) {
    const query = `
      SELECT resultat, COUNT(*) as count
      FROM passage pa
      ${whereClause}
      GROUP BY resultat
    `;

    return await sequelize.query(query, {
      type: sequelize.QueryTypes.SELECT,
      replacements: whereReplacements
    });
  }

  /**
   * ===========================
   * COMPTAGE PAR TYPE D'ACCÈS (entrée/sortie)
   * ===========================
   * Compte les passages par type d'accès
   */
  async _getCountByTypeAcces(whereClause, whereReplacements) {
    const query = `
      SELECT type_acces, COUNT(*) as count
      FROM passage pa
      ${whereClause}
      GROUP BY type_acces
    `;

    return await sequelize.query(query, {
      type: sequelize.QueryTypes.SELECT,
      replacements: whereReplacements
    });
  }

  /**
   * ===========================
   * TRAITEMENT DES RÉSULTATS PAR TYPE
   * ===========================
   * Transforme les résultats de comptage par résultat en objet structuré
   */
  _processTotalByType(totalByTypeResult) {
    const totalByType = { autorise: 0, refuse: 0, erreur: 0 };
    
    totalByTypeResult.forEach(row => {
      if (row.resultat && totalByType.hasOwnProperty(row.resultat)) {
        totalByType[row.resultat] = parseInt(row.count, 10);
      }
    });

    return totalByType;
  }

  /**
   * ===========================
   * TRAITEMENT DES RÉSULTATS PAR TYPE D'ACCÈS
   * ===========================
   * Transforme les résultats de comptage par type d'accès en objet structuré
   */
  _processTotalByAccessType(totalByAccessTypeResult) {
    const totalByAccessType = { entree: 0, sortie: 0 };
    
    totalByAccessTypeResult.forEach(row => {
      if (row.type_acces && totalByAccessType.hasOwnProperty(row.type_acces)) {
        totalByAccessType[row.type_acces] = parseInt(row.count, 10);
      }
    });

    return totalByAccessType;
  }

  /**
   * ===========================
   * STATISTIQUES RAPIDES POUR LE DASHBOARD
   * ===========================
   * Calcule des statistiques simplifiées pour l'affichage rapide
   */
  async getQuickStats() {
    try {
      const query = `
        SELECT
          COUNT(*) as total_passages,
          COUNT(CASE WHEN resultat = 'autorise' THEN 1 END) as total_autorise,
          COUNT(CASE WHEN resultat = 'refuse' THEN 1 END) as total_refuse,
          COUNT(CASE WHEN resultat = 'erreur' THEN 1 END) as total_erreur,
          COUNT(CASE WHEN type_acces = 'entree' THEN 1 END) as total_entree,
          COUNT(CASE WHEN type_acces = 'sortie' THEN 1 END) as total_sortie
        FROM passage
        WHERE DATE(date_acces) = CURRENT_DATE
      `;

      const result = await sequelize.query(query, {
        type: sequelize.QueryTypes.SELECT
      });

      return result[0] || {
        total_passages: 0,
        total_autorise: 0,
        total_refuse: 0,
        total_erreur: 0,
        total_entree: 0,
        total_sortie: 0
      };
    } catch (error) {
      console.error('Erreur lors du calcul des statistiques rapides:', error);
      throw new Error('Erreur lors du calcul des statistiques rapides');
    }
  }
}

module.exports = new PassageStatsService();
