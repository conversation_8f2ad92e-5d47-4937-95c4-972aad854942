/**
 * ===========================
 * SERVICE DE VALIDATION POUR LE PERSONNEL
 * ===========================
 * Ce service gère toute la logique de validation des données
 * pour les opérations sur le personnel
 */
class PersonnelValidationService {
  /**
   * ===========================
   * VALIDATION DES DONNÉES DE CRÉATION
   * ===========================
   * Valide les données requises pour créer un personnel
   * 
   * @param {Object} data - Données à valider
   * @returns {Object} { isValid, errors, sanitizedData }
   */
  validateCreationData(data) {
    const errors = [];
    const sanitizedData = {};

    // Validation des champs obligatoires
    const requiredFields = ['nom', 'prenom', 'matricule', 'id_grade', 'id_unite', 'id_unite_origine'];
    
    for (const field of requiredFields) {
      if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
        errors.push(`Le champ '${field}' est requis`);
      } else {
        // Nettoyage des chaînes de caractères
        if (typeof data[field] === 'string') {
          sanitizedData[field] = data[field].trim();
        } else {
          sanitizedData[field] = data[field];
        }
      }
    }

    // Validation spécifique du nom et prénom
    if (data.nom && !this.isValidName(data.nom)) {
      errors.push('Le nom ne doit contenir que des lettres, espaces et tirets');
    }

    if (data.prenom && !this.isValidName(data.prenom)) {
      errors.push('Le prénom ne doit contenir que des lettres, espaces et tirets');
    }

    // Validation du matricule
    if (data.matricule && !this.isValidMatricule(data.matricule)) {
      errors.push('Le matricule doit contenir entre 3 et 50 caractères alphanumériques');
    }

    // Validation des IDs numériques
    if (data.id_grade && !this.isValidId(data.id_grade)) {
      errors.push('L\'ID du grade doit être un nombre entier positif');
    }

    if (data.id_unite && !this.isValidId(data.id_unite)) {
      errors.push('L\'ID de l\'unité doit être un nombre entier positif');
    }

    // Validation du CIN si fourni
    if (data.cin && !this.isValidCIN(data.cin)) {
      errors.push('Le CIN doit contenir entre 8 et 20 caractères alphanumériques');
    }

    // Validation de la société si fournie
    if (data.societe && data.societe.length > 100) {
      errors.push('Le nom de la société ne peut pas dépasser 100 caractères');
    }

    // Ajout des champs optionnels nettoyés
    if (data.cin) sanitizedData.cin = data.cin.trim();
    if (data.societe) sanitizedData.societe = data.societe.trim();

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData
    };
  }

  /**
   * ===========================
   * VALIDATION DES DONNÉES DE MODIFICATION
   * ===========================
   * Valide les données pour modifier un personnel existant
   * 
   * @param {Object} data - Données à valider
   * @returns {Object} { isValid, errors, sanitizedData }
   */
  validateUpdateData(data) {
    const errors = [];
    const sanitizedData = {};

    // Pour la modification, aucun champ n'est obligatoire
    // mais ceux fournis doivent être valides

    if (data.nom !== undefined) {
      if (!data.nom || data.nom.trim() === '') {
        errors.push('Le nom ne peut pas être vide');
      } else if (!this.isValidName(data.nom)) {
        errors.push('Le nom ne doit contenir que des lettres, espaces et tirets');
      } else {
        sanitizedData.nom = data.nom.trim();
      }
    }

    if (data.prenom !== undefined) {
      if (!data.prenom || data.prenom.trim() === '') {
        errors.push('Le prénom ne peut pas être vide');
      } else if (!this.isValidName(data.prenom)) {
        errors.push('Le prénom ne doit contenir que des lettres, espaces et tirets');
      } else {
        sanitizedData.prenom = data.prenom.trim();
      }
    }

    if (data.matricule !== undefined) {
      if (!data.matricule || data.matricule.trim() === '') {
        errors.push('Le matricule ne peut pas être vide');
      } else if (!this.isValidMatricule(data.matricule)) {
        errors.push('Le matricule doit contenir entre 3 et 50 caractères alphanumériques');
      } else {
        sanitizedData.matricule = data.matricule.trim();
      }
    }

    if (data.id_grade !== undefined) {
      if (!this.isValidId(data.id_grade)) {
        errors.push('L\'ID du grade doit être un nombre entier positif');
      } else {
        sanitizedData.id_grade = parseInt(data.id_grade);
      }
    }

    if (data.id_unite !== undefined) {
      if (!this.isValidId(data.id_unite)) {
        errors.push('L\'ID de l\'unité doit être un nombre entier positif');
      } else {
        sanitizedData.id_unite = parseInt(data.id_unite);
      }
    }

    if (data.id_unite_origine !== undefined) {
      if (!this.isValidId(data.id_unite_origine)) {
        errors.push('L\'ID de l\'unité d\'origine doit être un nombre entier positif');
      } else {
        sanitizedData.id_unite_origine = parseInt(data.id_unite_origine);
      }
    }

    if (data.cin !== undefined) {
      if (data.cin && !this.isValidCIN(data.cin)) {
        errors.push('Le CIN doit contenir entre 8 et 20 caractères alphanumériques');
      } else {
        sanitizedData.cin = data.cin ? data.cin.trim() : null;
      }
    }

    if (data.societe !== undefined) {
      if (data.societe && data.societe.length > 100) {
        errors.push('Le nom de la société ne peut pas dépasser 100 caractères');
      } else {
        sanitizedData.societe = data.societe ? data.societe.trim() : null;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData
    };
  }

  /**
   * ===========================
   * VALIDATION D'UN NOM/PRÉNOM
   * ===========================
   * Vérifie qu'un nom ne contient que des caractères autorisés
   * 
   * @param {string} name - Nom à valider
   * @returns {boolean} True si valide
   */
  isValidName(name) {
    if (!name || typeof name !== 'string') return false;
    
    // Autorise lettres (avec accents), espaces, tirets et apostrophes
    const nameRegex = /^[a-zA-ZÀ-ÿ\s\-']{2,50}$/;
    return nameRegex.test(name.trim());
  }

  /**
   * ===========================
   * VALIDATION D'UN MATRICULE
   * ===========================
   * Vérifie qu'un matricule respecte le format attendu
   * 
   * @param {string} matricule - Matricule à valider
   * @returns {boolean} True si valide
   */
  isValidMatricule(matricule) {
    if (!matricule || typeof matricule !== 'string') return false;
    
    // Autorise lettres, chiffres et quelques caractères spéciaux
    const matriculeRegex = /^[a-zA-Z0-9\-_]{3,50}$/;
    return matriculeRegex.test(matricule.trim());
  }

  /**
   * ===========================
   * VALIDATION D'UN CIN
   * ===========================
   * Vérifie qu'un CIN respecte le format attendu
   * 
   * @param {string} cin - CIN à valider
   * @returns {boolean} True si valide
   */
  isValidCIN(cin) {
    if (!cin || typeof cin !== 'string') return false;
    
    // Autorise lettres et chiffres, entre 8 et 20 caractères
    const cinRegex = /^[a-zA-Z0-9]{8,20}$/;
    return cinRegex.test(cin.trim());
  }

  /**
   * ===========================
   * VALIDATION D'UN ID NUMÉRIQUE
   * ===========================
   * Vérifie qu'un ID est un nombre entier positif
   * 
   * @param {any} id - ID à valider
   * @returns {boolean} True si valide
   */
  isValidId(id) {
    const numId = parseInt(id);
    return !isNaN(numId) && numId > 0 && numId === parseFloat(id);
  }

  /**
   * ===========================
   * VALIDATION D'UN FICHIER PHOTO
   * ===========================
   * Vérifie qu'un fichier uploadé est une image valide
   * 
   * @param {Object} file - Fichier Multer
   * @returns {Object} { isValid, error }
   */
  validatePhotoFile(file) {
    if (!file) {
      return { isValid: true, error: null }; // Photo optionnelle
    }

    // Vérification du type MIME
    const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedMimeTypes.includes(file.mimetype)) {
      return { 
        isValid: false, 
        error: 'Format de fichier non autorisé. Utilisez JPG, PNG ou GIF.' 
      };
    }

    // Vérification de la taille (5MB max)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return { 
        isValid: false, 
        error: 'La taille du fichier ne doit pas dépasser 5MB.' 
      };
    }

    return { isValid: true, error: null };
  }

  /**
   * ===========================
   * CONSTRUCTION DU CHEMIN DE PHOTO
   * ===========================
   * Construit le chemin relatif pour l'accès à la photo
   * 
   * @param {Object} file - Fichier Multer
   * @returns {string|null} Chemin de la photo ou null
   */
  buildPhotoPath(file) {
    if (!file || !file.filename) {
      return null;
    }

    return `/uploads/photos/${file.filename}`;
  }
}

module.exports = new PersonnelValidationService();
