const { Passage } = require('../models');

/**
 * ===========================
 * SERVICE DE CRÉATION DES PASSAGES
 * ===========================
 * Ce service gère la création des enregistrements de passage
 * avec validation et normalisation des données
 */
class PassageCreationService {
  /**
   * ===========================
   * CRÉATION D'UN PASSAGE STANDARD
   * ===========================
   * Crée un enregistrement de passage avec les données fournies
   * 
   * @param {Object} passageData - Données du passage
   * @returns {Object} Passage créé
   */
  async createPassage(passageData) {
    try {
      // Validation des données obligatoires
      this._validatePassageData(passageData);

      // Normalisation des données
      const normalizedData = this._normalizePassageData(passageData);

      // Création du passage
      const passage = await Passage.create(normalizedData);

      return passage;
    } catch (error) {
      console.error('Erreur lors de la création du passage:', error);
      throw new Error(`Erreur lors de la création du passage: ${error.message}`);
    }
  }

  /**
   * ===========================
   * CRÉATION D'UN PASSAGE REFUSÉ
   * ===========================
   * Crée un passage avec statut refusé et motif
   * 
   * @param {Object} params - Paramètres du passage refusé
   * @returns {Object} Passage créé
   */
  async createRefusedPassage(params) {
    const {
      epc_code,
      id_badge = null,
      type_acces,
      motif_refus,
      timestamp,
      id_porte = 1
    } = params;

    const passageData = {
      id_badge,
      epc_code,
      id_porte,
      type_acces,
      resultat: 'refuse',
      date_acces: timestamp ? new Date(timestamp) : new Date(),
      motif_refus
    };

    return await this.createPassage(passageData);
  }

  /**
   * ===========================
   * CRÉATION D'UN PASSAGE AUTORISÉ
   * ===========================
   * Crée un passage avec statut autorisé
   * 
   * @param {Object} params - Paramètres du passage autorisé
   * @returns {Object} Passage créé
   */
  async createAuthorizedPassage(params) {
    const {
      epc_code,
      id_badge,
      type_acces,
      timestamp,
      id_porte = 1
    } = params;

    const passageData = {
      id_badge,
      epc_code,
      id_porte,
      type_acces,
      resultat: 'autorise',
      date_acces: timestamp ? new Date(timestamp) : new Date()
      // Pas de motif_refus pour un passage autorisé
    };

    return await this.createPassage(passageData);
  }

  /**
   * ===========================
   * CRÉATION D'UN PASSAGE D'ERREUR
   * ===========================
   * Crée un passage avec statut erreur (pour les cas exceptionnels)
   * 
   * @param {Object} params - Paramètres du passage d'erreur
   * @returns {Object} Passage créé
   */
  async createErrorPassage(params) {
    const {
      epc_code,
      id_badge = null,
      type_acces,
      motif_refus,
      timestamp,
      id_porte = 1
    } = params;

    const passageData = {
      id_badge,
      epc_code,
      id_porte,
      type_acces,
      resultat: 'erreur',
      date_acces: timestamp ? new Date(timestamp) : new Date(),
      motif_refus
    };

    return await this.createPassage(passageData);
  }

  /**
   * ===========================
   * VALIDATION DES DONNÉES DE PASSAGE
   * ===========================
   * Valide les données obligatoires pour créer un passage
   * 
   * @param {Object} passageData - Données à valider
   */
  _validatePassageData(passageData) {
    const requiredFields = ['epc_code', 'type_acces', 'resultat'];
    
    for (const field of requiredFields) {
      if (!passageData[field]) {
        throw new Error(`Le champ '${field}' est requis pour créer un passage`);
      }
    }

    // Validation du type d'accès
    const validTypeAcces = ['entree', 'sortie'];
    if (!validTypeAcces.includes(passageData.type_acces)) {
      throw new Error(`Type d'accès invalide. Valeurs autorisées: ${validTypeAcces.join(', ')}`);
    }

    // Validation du résultat
    const validResultats = ['autorise', 'refuse', 'erreur'];
    if (!validResultats.includes(passageData.resultat)) {
      throw new Error(`Résultat invalide. Valeurs autorisées: ${validResultats.join(', ')}`);
    }

    // Validation de l'ID de porte
    if (passageData.id_porte && (!Number.isInteger(passageData.id_porte) || passageData.id_porte <= 0)) {
      throw new Error('L\'ID de porte doit être un nombre entier positif');
    }
  }

  /**
   * ===========================
   * NORMALISATION DES DONNÉES
   * ===========================
   * Normalise et nettoie les données avant insertion
   * 
   * @param {Object} passageData - Données à normaliser
   * @returns {Object} Données normalisées
   */
  _normalizePassageData(passageData) {
    const normalized = { ...passageData };

    // Nettoyage du code EPC
    if (normalized.epc_code) {
      normalized.epc_code = normalized.epc_code.toString().trim().toUpperCase();
    }

    // Normalisation du type d'accès
    if (normalized.type_acces) {
      normalized.type_acces = normalized.type_acces.toString().toLowerCase().trim();
    }

    // Normalisation du résultat
    if (normalized.resultat) {
      normalized.resultat = normalized.resultat.toString().toLowerCase().trim();
    }

    // Validation et normalisation de la date
    if (normalized.date_acces) {
      if (!(normalized.date_acces instanceof Date)) {
        normalized.date_acces = new Date(normalized.date_acces);
      }
      
      // Vérification que la date est valide
      if (isNaN(normalized.date_acces.getTime())) {
        normalized.date_acces = new Date(); // Fallback sur la date actuelle
      }
    } else {
      normalized.date_acces = new Date();
    }

    // Nettoyage du motif de refus
    if (normalized.motif_refus) {
      normalized.motif_refus = normalized.motif_refus.toString().trim();
      
      // Limitation de la longueur
      if (normalized.motif_refus.length > 500) {
        normalized.motif_refus = normalized.motif_refus.substring(0, 500);
      }
    }

    // Valeur par défaut pour l'ID de porte
    if (!normalized.id_porte) {
      normalized.id_porte = 1;
    }

    return normalized;
  }

  /**
   * ===========================
   * RÉCUPÉRATION DES STATISTIQUES DE PASSAGE
   * ===========================
   * Récupère des statistiques rapides sur les passages
   * 
   * @param {Object} filters - Filtres optionnels
   * @returns {Object} Statistiques
   */
  async getPassageStats(filters = {}) {
    try {
      const whereClause = this._buildStatsWhereClause(filters);
      
      const stats = await Passage.findAll({
        attributes: [
          [Passage.sequelize.fn('COUNT', '*'), 'total'],
          [Passage.sequelize.fn('COUNT', Passage.sequelize.literal("CASE WHEN resultat = 'autorise' THEN 1 END")), 'autorises'],
          [Passage.sequelize.fn('COUNT', Passage.sequelize.literal("CASE WHEN resultat = 'refuse' THEN 1 END")), 'refuses'],
          [Passage.sequelize.fn('COUNT', Passage.sequelize.literal("CASE WHEN resultat = 'erreur' THEN 1 END")), 'erreurs']
        ],
        where: whereClause,
        raw: true
      });

      return stats[0] || { total: 0, autorises: 0, refuses: 0, erreurs: 0 };
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw new Error(`Erreur lors de la récupération des statistiques: ${error.message}`);
    }
  }

  /**
   * ===========================
   * CONSTRUCTION DE LA CLAUSE WHERE POUR STATISTIQUES
   * ===========================
   * Construit la clause WHERE pour les requêtes de statistiques
   * 
   * @param {Object} filters - Filtres
   * @returns {Object} Clause WHERE
   */
  _buildStatsWhereClause(filters) {
    const where = {};

    if (filters.dateDebut && filters.dateFin) {
      where.date_acces = {
        [Passage.sequelize.Op.between]: [new Date(filters.dateDebut), new Date(filters.dateFin)]
      };
    } else if (filters.dateDebut) {
      where.date_acces = {
        [Passage.sequelize.Op.gte]: new Date(filters.dateDebut)
      };
    } else if (filters.dateFin) {
      where.date_acces = {
        [Passage.sequelize.Op.lte]: new Date(filters.dateFin)
      };
    }

    if (filters.type_acces) {
      where.type_acces = filters.type_acces;
    }

    if (filters.resultat) {
      where.resultat = filters.resultat;
    }

    return where;
  }
}

module.exports = new PassageCreationService();
