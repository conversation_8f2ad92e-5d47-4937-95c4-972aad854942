const { sequelize } = require('../models');

/**
 * ===========================
 * SERVICE DE REQUÊTES POUR LES PASSAGES
 * ===========================
 * Ce service gère la construction et l'exécution des requêtes
 * complexes pour récupérer les données de passages
 */
class PassageQueryService {
  /**
   * ===========================
   * REQUÊTE PRINCIPALE - RÉCUPÉRATION DES PASSAGES
   * ===========================
   * Exécute la requête principale avec jointures pour récupérer
   * les passages avec toutes les informations associées
   * 
   * @param {string} whereClause - Clause WHERE SQL
   * @param {Object} whereReplacements - Paramètres de remplacement
   * @param {number} limit - Limite de résultats
   * @param {number} offset - Décalage pour la pagination
   * @returns {Array} Liste des passages avec informations complètes
   */
  async getPassagesWithDetails(whereClause, whereReplacements, limit, offset) {
    try {
      const query = this._buildMainQuery(whereClause);
      
      const passages = await sequelize.query(query, {
        type: sequelize.QueryTypes.SELECT,
        replacements: { ...whereReplacements, limit, offset }
      });

      return passages;
    } catch (error) {
      console.error('Erreur lors de la récupération des passages:', error);
      throw new Error('Erreur lors de la récupération des passages');
    }
  }

  /**
   * ===========================
   * CONSTRUCTION DE LA REQUÊTE PRINCIPALE
   * ===========================
   * Construit la requête SQL complexe avec toutes les jointures nécessaires
   */
  _buildMainQuery(whereClause) {
    return `
      SELECT
        -- Données du passage
        pa.id,
        pa.date_acces,
        pa.type_acces,
        pa.resultat,
        pa.motif_refus,
        pa.epc_code,

        -- Données de la porte
        po.libelle as porte_libelle,

        -- Données du badge
        b.epc_code as badge_epc_code,

        -- Données du personnel (peuvent être NULL si badge non attribué)
        p.nom,
        p.prenom,
        p.matricule,

        -- Données de référence (grade et unité)
        g.nom_grade as grade,
        u.nom_unite as unite

      FROM passage pa

      -- JOINTURE OBLIGATOIRE : Chaque passage a une porte
      JOIN porte po ON pa.id_porte = po.id

      -- JOINTURE OPTIONNELLE : Le passage peut ne pas avoir de badge (badge inconnu)
      LEFT JOIN badge b ON pa.id_badge = b.id

      -- JOINTURE COMPLEXE : Attribution active au moment du passage
      LEFT JOIN attribution_badge ab ON b.id = ab.id_badge
        AND ab.statut = 'actif'
        AND ab.date_attribution = (
          SELECT MAX(date_attribution)
          FROM attribution_badge
          WHERE id_badge = b.id
            AND statut = 'actif'
            AND date_attribution <= pa.date_acces
        )

      -- JOINTURES OPTIONNELLES : Personnel et ses références
      LEFT JOIN personnel p ON ab.id_personnel = p.id
      LEFT JOIN grade g ON p.id_grade = g.id
      LEFT JOIN unite u ON p.id_unite = u.id

      -- Application des filtres construits dynamiquement
      ${whereClause}

      -- Tri par date décroissante (plus récents en premier)
      ORDER BY pa.date_acces DESC

      -- Pagination
      LIMIT :limit OFFSET :offset
    `;
  }

  /**
   * ===========================
   * REQUÊTE OPTIMISÉE ALTERNATIVE
   * ===========================
   * Version optimisée de la requête principale qui évite la sous-requête corrélée
   * en utilisant une CTE (Common Table Expression)
   */
  async getPassagesWithDetailsOptimized(whereClause, whereReplacements, limit, offset) {
    try {
      const query = this._buildOptimizedQuery(whereClause);
      
      const passages = await sequelize.query(query, {
        type: sequelize.QueryTypes.SELECT,
        replacements: { ...whereReplacements, limit, offset }
      });

      return passages;
    } catch (error) {
      console.error('Erreur lors de la récupération optimisée des passages:', error);
      // Fallback vers la requête standard
      return await this.getPassagesWithDetails(whereClause, whereReplacements, limit, offset);
    }
  }

  /**
   * ===========================
   * CONSTRUCTION DE LA REQUÊTE OPTIMISÉE
   * ===========================
   * Version optimisée utilisant une CTE pour éviter la sous-requête corrélée
   */
  _buildOptimizedQuery(whereClause) {
    return `
      WITH latest_attributions AS (
        SELECT DISTINCT ON (id_badge) 
          id_badge,
          id_personnel,
          date_attribution
        FROM attribution_badge
        WHERE statut = 'actif'
        ORDER BY id_badge, date_attribution DESC
      )
      SELECT
        -- Données du passage
        pa.id,
        pa.date_acces,
        pa.type_acces,
        pa.resultat,
        pa.motif_refus,
        pa.epc_code,

        -- Données de la porte
        po.libelle as porte_libelle,

        -- Données du badge
        b.epc_code as badge_epc_code,

        -- Données du personnel
        p.nom,
        p.prenom,
        p.matricule,

        -- Données de référence
        g.nom_grade as grade,
        u.nom_unite as unite

      FROM passage pa
      JOIN porte po ON pa.id_porte = po.id
      LEFT JOIN badge b ON pa.id_badge = b.id
      LEFT JOIN latest_attributions la ON b.id = la.id_badge 
        AND la.date_attribution <= pa.date_acces
      LEFT JOIN personnel p ON la.id_personnel = p.id
      LEFT JOIN grade g ON p.id_grade = g.id
      LEFT JOIN unite u ON p.id_unite = u.id

      ${whereClause}

      ORDER BY pa.date_acces DESC
      LIMIT :limit OFFSET :offset
    `;
  }

  /**
   * ===========================
   * REQUÊTE SIMPLE POUR COMPTAGE
   * ===========================
   * Requête simplifiée pour les opérations de comptage uniquement
   */
  async getPassagesForCount(whereClause, whereReplacements) {
    try {
      const query = `
        SELECT 
          pa.id,
          pa.resultat,
          pa.type_acces,
          pa.date_acces
        FROM passage pa
        ${whereClause}
        ORDER BY pa.date_acces DESC
      `;

      return await sequelize.query(query, {
        type: sequelize.QueryTypes.SELECT,
        replacements: whereReplacements
      });
    } catch (error) {
      console.error('Erreur lors de la récupération pour comptage:', error);
      throw new Error('Erreur lors de la récupération pour comptage');
    }
  }

  /**
   * ===========================
   * REQUÊTE POUR UN PASSAGE UNIQUE
   * ===========================
   * Récupère un passage unique avec tous ses détails
   */
  async getPassageById(id) {
    try {
      const query = this._buildMainQuery('WHERE pa.id = :id');
      
      const passages = await sequelize.query(query, {
        type: sequelize.QueryTypes.SELECT,
        replacements: { id, limit: 1, offset: 0 }
      });

      return passages[0] || null;
    } catch (error) {
      console.error('Erreur lors de la récupération du passage:', error);
      throw new Error('Erreur lors de la récupération du passage');
    }
  }
}

module.exports = new PassageQueryService();
