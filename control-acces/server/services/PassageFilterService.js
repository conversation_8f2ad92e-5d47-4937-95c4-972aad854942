/**
 * ===========================
 * SERVICE DE GESTION DES FILTRES POUR LES PASSAGES
 * ===========================
 * Ce service gère la construction dynamique des clauses WHERE
 * et la validation des filtres pour les requêtes de passages
 */
class PassageFilterService {
  /**
   * ===========================
   * MÉTHODE PRINCIPALE - CONSTRUCTION DES FILTRES
   * ===========================
   * Construit la clause WHERE et les paramètres de remplacement
   * à partir des filtres fournis
   * 
   * @param {Object} filters - Objet contenant les filtres
   * @param {string} filters.dateDebut - Date de début (YYYY-MM-DD)
   * @param {string} filters.dateFin - Date de fin (YYYY-MM-DD)
   * @param {string} filters.resultat - Résultats séparés par virgules
   * @param {string} filters.type_acces - Types d'accès séparés par virgules
   * @returns {Object} { whereClause, whereReplacements }
   */
  buildFilters(filters) {
    const whereConditions = [];
    const whereReplacements = {};

    // Filtrage par dates
    this._addDateFilters(filters, whereConditions, whereReplacements);
    
    // Filtrage par résultat
    this._addResultatFilters(filters, whereConditions, whereReplacements);
    
    // Filtrage par type d'accès
    this._addTypeAccesFilters(filters, whereConditions, whereReplacements);

    // Construction de la clause WHERE finale
    const whereClause = whereConditions.length > 0 
      ? 'WHERE ' + whereConditions.join(' AND ') 
      : '';

    return { whereClause, whereReplacements };
  }

  /**
   * ===========================
   * FILTRAGE PAR PLAGE DE DATES
   * ===========================
   * Ajoute les conditions de filtrage par date
   */
  _addDateFilters(filters, whereConditions, whereReplacements) {
    const { dateDebut, dateFin } = filters;

    if (dateDebut && dateFin) {
      // Plage de dates complète
      whereConditions.push('pa.date_acces BETWEEN :dateDebut AND :dateFin');
      whereReplacements.dateDebut = dateDebut;
      whereReplacements.dateFin = dateFin;
    } else if (dateDebut) {
      // Seulement date de début (à partir de...)
      whereConditions.push('pa.date_acces >= :dateDebut');
      whereReplacements.dateDebut = dateDebut;
    } else if (dateFin) {
      // Seulement date de fin (jusqu'à...)
      whereConditions.push('pa.date_acces <= :dateFin');
      whereReplacements.dateFin = dateFin;
    }
  }

  /**
   * ===========================
   * FILTRAGE PAR RÉSULTAT (MULTIPLE)
   * ===========================
   * Ajoute les conditions de filtrage par résultat d'accès
   */
  _addResultatFilters(filters, whereConditions, whereReplacements) {
    const { resultat: filterResultat } = filters;

    if (!filterResultat) return;

    // Parsing et validation des résultats multiples
    const resultats = this._parseAndValidateValues(
      filterResultat, 
      ['autorise', 'refuse', 'erreur']
    );

    if (resultats.length > 0) {
      this._addInClause(
        'pa.resultat', 
        resultats, 
        'resultat', 
        whereConditions, 
        whereReplacements
      );
    }
  }

  /**
   * ===========================
   * FILTRAGE PAR TYPE D'ACCÈS (MULTIPLE)
   * ===========================
   * Ajoute les conditions de filtrage par type d'accès
   */
  _addTypeAccesFilters(filters, whereConditions, whereReplacements) {
    const { type_acces: filterTypeAcces } = filters;

    if (!filterTypeAcces) return;

    // Parsing et validation des types d'accès multiples
    const typesAcces = this._parseAndValidateValues(
      filterTypeAcces, 
      ['entree', 'sortie']
    );

    if (typesAcces.length > 0) {
      this._addInClause(
        'pa.type_acces', 
        typesAcces, 
        'type_acces', 
        whereConditions, 
        whereReplacements
      );
    }
  }

  /**
   * ===========================
   * UTILITAIRE - PARSING ET VALIDATION
   * ===========================
   * Parse une chaîne séparée par virgules et valide les valeurs
   */
  _parseAndValidateValues(filterString, validValues) {
    return filterString
      .split(',')
      .filter(value => validValues.includes(value.trim()));
  }

  /**
   * ===========================
   * UTILITAIRE - CONSTRUCTION CLAUSE IN
   * ===========================
   * Construit une clause IN pour les filtres multiples
   */
  _addInClause(column, values, paramPrefix, whereConditions, whereReplacements) {
    if (values.length === 1) {
      // Cas simple : une seule valeur
      whereConditions.push(`${column} = :${paramPrefix}`);
      whereReplacements[paramPrefix] = values[0];
    } else {
      // Cas complexe : plusieurs valeurs (clause IN)
      const placeholders = values.map((_, i) => `:${paramPrefix}${i}`).join(', ');
      whereConditions.push(`${column} IN (${placeholders})`);
      
      values.forEach((value, i) => {
        whereReplacements[`${paramPrefix}${i}`] = value;
      });
    }
  }

  /**
   * ===========================
   * VALIDATION DES PARAMÈTRES DE PAGINATION
   * ===========================
   * Valide et normalise les paramètres de pagination
   */
  validatePagination(query) {
    const limit = Math.max(1, Math.min(100, parseInt(query.limit, 10) || 20));
    const offset = Math.max(0, parseInt(query.offset, 10) || 0);
    
    return { limit, offset };
  }
}

module.exports = new PassageFilterService();
