const { Alerte } = require('../models');

/**
 * ===========================
 * SERVICE DE GESTION DES ALERTES
 * ===========================
 * Ce service gère la création et la gestion des alertes de sécurité
 * pour le système de contrôle d'accès
 */
class AlerteService {
  /**
   * ===========================
   * CRÉATION D'UNE ALERTE STANDARD
   * ===========================
   * Crée une alerte avec validation des données
   * 
   * @param {Object} alerteData - Données de l'alerte
   * @returns {Object} Alerte créée
   */
  async createAlerte(alerteData) {
    try {
      // Validation des données
      this._validateAlerteData(alerteData);

      // Normalisation des données
      const normalizedData = this._normalizeAlerteData(alerteData);

      // Création de l'alerte
      const alerte = await Alerte.create(normalizedData);

      return alerte;
    } catch (error) {
      console.error('Erreur lors de la création de l\'alerte:', error);
      throw new Error(`Erreur lors de la création de l'alerte: ${error.message}`);
    }
  }

  /**
   * ===========================
   * CRÉATION D'UNE ALERTE BADGE INCONNU
   * ===========================
   * Crée une alerte spécifique pour un badge inconnu
   * 
   * @param {Object} params - Paramètres de l'alerte
   * @returns {Object} Alerte créée
   */
  async createBadgeInconnuAlerte(params) {
    const { id_passage, epc_code, message } = params;

    const alerteData = {
      id_passage,
      id_badge: null, // Badge inconnu
      type_alerte: 'badge_inconnu',
      niveau: 'rouge', // Niveau de sécurité élevé
      message: message || `Scan d'un badge inconnu EPC=${epc_code}`,
      statut: 'ouvert'
    };

    return await this.createAlerte(alerteData);
  }

  /**
   * ===========================
   * CRÉATION D'UNE ALERTE ACCÈS REFUSÉ
   * ===========================
   * Crée une alerte pour un accès refusé
   * 
   * @param {Object} params - Paramètres de l'alerte
   * @returns {Object} Alerte créée
   */
  async createAccesRefuseAlerte(params) {
    const { id_passage, id_badge, id_personnel, motif, niveau = 'orange' } = params;

    const alerteData = {
      id_passage,
      id_badge,
      id_personnel,
      type_alerte: 'acces_refuse',
      niveau,
      message: `Accès refusé: ${motif}`,
      statut: 'ouvert'
    };

    return await this.createAlerte(alerteData);
  }

  /**
   * ===========================
   * CRÉATION D'UNE ALERTE BADGE EXPIRÉ
   * ===========================
   * Crée une alerte pour un badge expiré
   * 
   * @param {Object} params - Paramètres de l'alerte
   * @returns {Object} Alerte créée
   */
  async createBadgeExpireAlerte(params) {
    const { id_passage, id_badge, id_personnel, epc_code } = params;

    const alerteData = {
      id_passage,
      id_badge,
      id_personnel,
      type_alerte: 'badge_expire',
      niveau: 'orange',
      message: `Badge expiré détecté: ${epc_code}`,
      statut: 'ouvert'
    };

    return await this.createAlerte(alerteData);
  }

  /**
   * ===========================
   * CRÉATION D'UNE ALERTE TENTATIVE INTRUSION
   * ===========================
   * Crée une alerte pour une tentative d'intrusion
   * 
   * @param {Object} params - Paramètres de l'alerte
   * @returns {Object} Alerte créée
   */
  async createTentativeIntrusionAlerte(params) {
    const { id_passage, epc_code, details } = params;

    const alerteData = {
      id_passage,
      id_badge: null,
      type_alerte: 'tentative_intrusion',
      niveau: 'rouge',
      message: `Tentative d'intrusion détectée: ${details || epc_code}`,
      statut: 'ouvert'
    };

    return await this.createAlerte(alerteData);
  }

  /**
   * ===========================
   * CRÉATION D'UNE ALERTE SYSTÈME
   * ===========================
   * Crée une alerte système (erreur technique, etc.)
   * 
   * @param {Object} params - Paramètres de l'alerte
   * @returns {Object} Alerte créée
   */
  async createSystemeAlerte(params) {
    const { message, niveau = 'jaune', details } = params;

    const alerteData = {
      type_alerte: 'systeme',
      niveau,
      message: `Alerte système: ${message}`,
      statut: 'ouvert',
      commentaire: details
    };

    return await this.createAlerte(alerteData);
  }

  /**
   * ===========================
   * FERMETURE D'UNE ALERTE
   * ===========================
   * Marque une alerte comme fermée avec commentaire
   * 
   * @param {number} alerteId - ID de l'alerte
   * @param {string} commentaire - Commentaire de fermeture
   * @returns {boolean} Succès de l'opération
   */
  async fermerAlerte(alerteId, commentaire = null) {
    try {
      const [updated] = await Alerte.update(
        { 
          statut: 'ferme',
          commentaire: commentaire || 'Alerte fermée automatiquement'
        },
        { where: { id: alerteId } }
      );

      return updated > 0;
    } catch (error) {
      console.error('Erreur lors de la fermeture de l\'alerte:', error);
      throw new Error(`Erreur lors de la fermeture de l'alerte: ${error.message}`);
    }
  }

  /**
   * ===========================
   * RÉCUPÉRATION DES ALERTES ACTIVES
   * ===========================
   * Récupère les alertes ouvertes avec filtres optionnels
   * 
   * @param {Object} filters - Filtres optionnels
   * @returns {Array} Liste des alertes
   */
  async getAlertesActives(filters = {}) {
    try {
      const where = { statut: 'ouvert' };

      if (filters.niveau) {
        where.niveau = filters.niveau;
      }

      if (filters.type_alerte) {
        where.type_alerte = filters.type_alerte;
      }

      if (filters.dateDebut) {
        where.created_at = {
          [Alerte.sequelize.Op.gte]: new Date(filters.dateDebut)
        };
      }

      const alertes = await Alerte.findAll({
        where,
        order: [['created_at', 'DESC']],
        limit: filters.limit || 50
      });

      return alertes;
    } catch (error) {
      console.error('Erreur lors de la récupération des alertes:', error);
      throw new Error(`Erreur lors de la récupération des alertes: ${error.message}`);
    }
  }

  /**
   * ===========================
   * VALIDATION DES DONNÉES D'ALERTE
   * ===========================
   * Valide les données obligatoires pour créer une alerte
   * 
   * @param {Object} alerteData - Données à valider
   */
  _validateAlerteData(alerteData) {
    const requiredFields = ['type_alerte', 'message'];
    
    for (const field of requiredFields) {
      if (!alerteData[field]) {
        throw new Error(`Le champ '${field}' est requis pour créer une alerte`);
      }
    }

    // Validation du type d'alerte
    const validTypes = [
      'badge_inconnu', 
      'acces_refuse', 
      'badge_expire', 
      'tentative_intrusion', 
      'systeme'
    ];
    
    if (!validTypes.includes(alerteData.type_alerte)) {
      throw new Error(`Type d'alerte invalide. Valeurs autorisées: ${validTypes.join(', ')}`);
    }

    // Validation du niveau
    const validNiveaux = ['vert', 'jaune', 'orange', 'rouge'];
    if (alerteData.niveau && !validNiveaux.includes(alerteData.niveau)) {
      throw new Error(`Niveau d'alerte invalide. Valeurs autorisées: ${validNiveaux.join(', ')}`);
    }

    // Validation du statut
    const validStatuts = ['ouvert', 'ferme', 'en_cours'];
    if (alerteData.statut && !validStatuts.includes(alerteData.statut)) {
      throw new Error(`Statut d'alerte invalide. Valeurs autorisées: ${validStatuts.join(', ')}`);
    }
  }

  /**
   * ===========================
   * NORMALISATION DES DONNÉES
   * ===========================
   * Normalise et nettoie les données avant insertion
   * 
   * @param {Object} alerteData - Données à normaliser
   * @returns {Object} Données normalisées
   */
  _normalizeAlerteData(alerteData) {
    const normalized = { ...alerteData };

    // Nettoyage du message
    if (normalized.message) {
      normalized.message = normalized.message.toString().trim();
      
      // Limitation de la longueur
      if (normalized.message.length > 1000) {
        normalized.message = normalized.message.substring(0, 1000);
      }
    }

    // Normalisation du type d'alerte
    if (normalized.type_alerte) {
      normalized.type_alerte = normalized.type_alerte.toString().toLowerCase().trim();
    }

    // Normalisation du niveau
    if (normalized.niveau) {
      normalized.niveau = normalized.niveau.toString().toLowerCase().trim();
    } else {
      normalized.niveau = 'orange'; // Valeur par défaut
    }

    // Normalisation du statut
    if (normalized.statut) {
      normalized.statut = normalized.statut.toString().toLowerCase().trim();
    } else {
      normalized.statut = 'ouvert'; // Valeur par défaut
    }

    // Nettoyage du commentaire
    if (normalized.commentaire) {
      normalized.commentaire = normalized.commentaire.toString().trim();
      
      // Limitation de la longueur
      if (normalized.commentaire.length > 2000) {
        normalized.commentaire = normalized.commentaire.substring(0, 2000);
      }
    }

    return normalized;
  }
}

module.exports = new AlerteService();
