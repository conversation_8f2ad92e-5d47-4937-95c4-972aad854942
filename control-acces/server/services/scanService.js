// ===========================
// IMPORTS ET DÉPENDANCES
// ===========================
const { Badge, AttributionBadge, Personnel } = require('../models');
const PassageCreationService = require('./PassageCreationService');
const AlerteService = require('./AlerteService');
const EventBroadcastService = require('./EventBroadcastService');
const PersonnelEnrichmentService = require('./PersonnelEnrichmentService');

/**
 * ===========================
 * SERVICE DE TRAITEMENT DES SCANS RFID - VERSION OPTIMISÉE
 * ===========================
 * Ce service gère le traitement complet des scans de badges RFID
 * ARCHITECTURE AMÉLIORÉE : Logique séparée en services spécialisés
 */
class ScanService {
  /**
   * ===========================
   * MÉTHODE PRINCIPALE - ORCHESTRATEUR OPTIMISÉ
   * ===========================
   * Point d'entrée principal qui délègue le traitement selon le type de badge
   * 
   * @param {string} epc - Code EPC du badge scanné
   * @param {string} timestamp - Timestamp du scan (optionnel)
   * @param {string} type_acces - Type d'accès ('entree' ou 'sortie')
   * @returns {Object} Résultat du scan avec statut et informations
   */
  async processScan(epc, timestamp, type_acces = 'entree') {
    // Validation des paramètres d'entrée
    if (!epc) {
      throw new Error('Code EPC requis');
    }

    // Validation et normalisation du type d'accès
    const typeAcces = this._validateTypeAcces(type_acces);

    try {
      // Recherche du badge
      const badge = await this._findBadge(epc);

      // Délégation selon le cas (Pattern Strategy optimisé)
      if (!badge) {
        return await this._handleUnknownBadge(epc, typeAcces, timestamp);
      }

      if (!badge.attributions || badge.attributions.length === 0) {
        return await this._handleUnassignedBadge(badge, typeAcces, timestamp);
      }

      return await this._handleAuthorizedBadge(badge, typeAcces, timestamp);

    } catch (error) {
      console.error('Erreur lors du traitement du scan:', error);
      
      // Création d'un passage d'erreur
      await this._handleScanError(epc, typeAcces, timestamp, error);
      
      throw error;
    }
  }

  /**
   * ===========================
   * RECHERCHE D'UN BADGE PAR CODE EPC - OPTIMISÉE
   * ===========================
   * Recherche un badge actif avec ses attributions et personnel associé
   */
  async _findBadge(epc) {
    return await Badge.findOne({
      where: {
        epc_code: epc.toString().trim().toUpperCase(),
        actif: true
      },
      include: [{
        model: AttributionBadge,
        as: 'attributions',
        where: { statut: 'actif' },
        include: [{
          model: Personnel,
          as: 'personnel'
        }],
        required: false
      }]
    });
  }

  /**
   * ===========================
   * HANDLER : BADGE INCONNU - VERSION OPTIMISÉE
   * ===========================
   * Traite le cas d'un badge non référencé dans le système
   */
  async _handleUnknownBadge(epc, typeAcces, timestamp) {
    const raison = `Badge inconnu (EPC=${epc})`;

    try {
      // Création du passage refusé
      const passage = await PassageCreationService.createRefusedPassage({
        epc_code: epc,
        type_acces: typeAcces,
        motif_refus: raison,
        timestamp
      });

      // Création de l'alerte de sécurité
      const alerte = await AlerteService.createBadgeInconnuAlerte({
        id_passage: passage.id,
        epc_code: epc
      });

      // Diffusion de l'événement en temps réel
      await EventBroadcastService.broadcastPassageEvent(passage, {
        personnel: null,
        alerte: alerte,
        raison
      });

      // Réponse standardisée
      return this._buildScanResponse({
        epc_code: epc,
        resultat: 'refuse',
        personnel: null,
        raison
      });

    } catch (error) {
      console.error('Erreur dans handleUnknownBadge:', error);
      throw new Error(`Erreur lors du traitement du badge inconnu: ${error.message}`);
    }
  }

  /**
   * ===========================
   * HANDLER : BADGE NON ATTRIBUÉ - VERSION OPTIMISÉE
   * ===========================
   * Traite le cas d'un badge connu mais sans attribution active
   */
  async _handleUnassignedBadge(badge, typeAcces, timestamp) {
    const raison = 'Badge non attribué ou attribution inactive';

    try {
      // Création du passage refusé
      const passage = await PassageCreationService.createRefusedPassage({
        epc_code: badge.epc_code,
        id_badge: badge.id,
        type_acces: typeAcces,
        motif_refus: raison,
        timestamp
      });

      // Diffusion de l'événement en temps réel
      await EventBroadcastService.broadcastPassageEvent(passage, {
        personnel: null,
        raison
      });

      // Réponse standardisée
      return this._buildScanResponse({
        epc_code: badge.epc_code,
        resultat: 'refuse',
        personnel: null,
        raison
      });

    } catch (error) {
      console.error('Erreur dans handleUnassignedBadge:', error);
      throw new Error(`Erreur lors du traitement du badge non attribué: ${error.message}`);
    }
  }

  /**
   * ===========================
   * HANDLER : BADGE AUTORISÉ - VERSION OPTIMISÉE
   * ===========================
   * Traite le cas d'un badge avec attribution active (accès autorisé)
   */
  async _handleAuthorizedBadge(badge, typeAcces, timestamp) {
    try {
      // Enrichissement des données du personnel
      const personnel = await PersonnelEnrichmentService.enrichForScan(
        badge.attributions[0].personnel
      );

      // Création du passage autorisé
      const passage = await PassageCreationService.createAuthorizedPassage({
        epc_code: badge.epc_code,
        id_badge: badge.id,
        type_acces: typeAcces,
        timestamp
      });

      // Diffusion de l'événement en temps réel
      await EventBroadcastService.broadcastPassageEvent(passage, {
        personnel
      });

      // Réponse avec données personnel
      return this._buildScanResponse({
        epc_code: badge.epc_code,
        resultat: 'autorise',
        personnel
      });

    } catch (error) {
      console.error('Erreur dans handleAuthorizedBadge:', error);
      throw new Error(`Erreur lors du traitement du badge autorisé: ${error.message}`);
    }
  }

  /**
   * ===========================
   * HANDLER : ERREUR DE SCAN
   * ===========================
   * Traite les erreurs lors du scan
   */
  async _handleScanError(epc, typeAcces, timestamp, error) {
    try {
      // Création d'un passage d'erreur
      await PassageCreationService.createErrorPassage({
        epc_code: epc,
        type_acces: typeAcces,
        motif_refus: `Erreur système: ${error.message}`,
        timestamp
      });

      // Création d'une alerte système
      await AlerteService.createSystemeAlerte({
        message: `Erreur lors du scan EPC=${epc}`,
        details: error.message,
        niveau: 'rouge'
      });

    } catch (alertError) {
      console.error('Erreur lors de la création de l\'alerte d\'erreur:', alertError);
    }
  }

  /**
   * ===========================
   * VALIDATION DU TYPE D'ACCÈS
   * ===========================
   * Valide et normalise le type d'accès
   */
  _validateTypeAcces(type_acces) {
    const validTypes = ['entree', 'sortie'];
    const normalized = type_acces ? type_acces.toString().toLowerCase().trim() : 'entree';
    
    return validTypes.includes(normalized) ? normalized : 'entree';
  }

  /**
   * ===========================
   * CONSTRUCTION DE LA RÉPONSE STANDARDISÉE
   * ===========================
   * Construit une réponse standardisée pour tous les cas
   */
  _buildScanResponse({ epc_code, resultat, personnel = null, raison = null }) {
    const response = {
      success: true,
      epc_code,
      resultat,
      timestamp: new Date().toISOString()
    };

    if (personnel) {
      response.personnel = {
        nom: personnel.nom,
        prenom: personnel.prenom,
        matricule: personnel.matricule,
        grade: personnel.grade,
        unite: personnel.unite
      };
    } else {
      response.personnel = null;
    }

    if (raison) {
      response.raison = raison;
    }

    return response;
  }

  /**
   * ===========================
   * MÉTHODE DE COMPATIBILITÉ
   * ===========================
   * Maintient la compatibilité avec l'ancienne interface
   */
  async findBadge(epc) {
    return await this._findBadge(epc);
  }

  async handleUnknownBadge(epc, typeAcces, timestamp) {
    return await this._handleUnknownBadge(epc, typeAcces, timestamp);
  }

  async handleUnassignedBadge(badge, typeAcces, timestamp) {
    return await this._handleUnassignedBadge(badge, typeAcces, timestamp);
  }

  async handleAuthorizedBadge(badge, typeAcces, timestamp) {
    return await this._handleAuthorizedBadge(badge, typeAcces, timestamp);
  }
}

module.exports = new ScanService();
