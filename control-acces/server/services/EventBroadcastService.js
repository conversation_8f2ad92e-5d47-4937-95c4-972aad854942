const { broadcastEvent } = require('../utils/broadcast');

/**
 * ===========================
 * SERVICE DE DIFFUSION D'ÉVÉNEMENTS
 * ===========================
 * Ce service gère la préparation et la diffusion des événements
 * en temps réel via WebSocket pour le système de contrôle d'accès
 */
class EventBroadcastService {
  /**
   * ===========================
   * DIFFUSION D'UN ÉVÉNEMENT DE PASSAGE
   * ===========================
   * Prépare et diffuse un événement de passage détecté
   * 
   * @param {Object} passageData - Données du passage
   * @param {Object} options - Options supplémentaires
   */
  async broadcastPassageEvent(passageData, options = {}) {
    try {
      const eventData = await this._preparePassageEventData(passageData, options);
      broadcastEvent('passage_detected', eventData);
    } catch (error) {
      console.error('Erreur lors de la diffusion de l\'événement de passage:', error);
      // Ne pas faire échouer le processus principal si la diffusion échoue
    }
  }

  /**
   * ===========================
   * DIFFUSION D'UN ÉVÉNEMENT D'ALERTE
   * ===========================
   * Prépare et diffuse un événement d'alerte
   * 
   * @param {Object} alerteData - Données de l'alerte
   * @param {Object} options - Options supplémentaires
   */
  async broadcastAlerteEvent(alerteData, options = {}) {
    try {
      const eventData = this._prepareAlerteEventData(alerteData, options);
      broadcastEvent('alerte_created', eventData);
    } catch (error) {
      console.error('Erreur lors de la diffusion de l\'événement d\'alerte:', error);
    }
  }

  /**
   * ===========================
   * DIFFUSION D'UN ÉVÉNEMENT SYSTÈME
   * ===========================
   * Diffuse un événement système (connexion, déconnexion, etc.)
   * 
   * @param {string} type - Type d'événement système
   * @param {Object} data - Données de l'événement
   */
  async broadcastSystemEvent(type, data = {}) {
    try {
      const eventData = {
        ...data,
        timestamp: new Date().toISOString(),
        source: 'system'
      };
      
      broadcastEvent(type, eventData);
    } catch (error) {
      console.error('Erreur lors de la diffusion de l\'événement système:', error);
    }
  }

  /**
   * ===========================
   * DIFFUSION D'ÉVÉNEMENT DE BADGE
   * ===========================
   * Diffuse un événement lié aux badges (création, attribution, etc.)
   * 
   * @param {string} action - Action effectuée ('created', 'attributed', 'deactivated')
   * @param {Object} badgeData - Données du badge
   * @param {Object} options - Options supplémentaires
   */
  async broadcastBadgeEvent(action, badgeData, options = {}) {
    try {
      const eventData = {
        action,
        badge: {
          id: badgeData.id,
          epc_code: badgeData.epc_code,
          actif: badgeData.actif,
          permanent: badgeData.permanent
        },
        timestamp: new Date().toISOString(),
        ...options
      };

      broadcastEvent('badge_event', eventData);
    } catch (error) {
      console.error('Erreur lors de la diffusion de l\'événement de badge:', error);
    }
  }

  /**
   * ===========================
   * DIFFUSION D'ÉVÉNEMENT DE PERSONNEL
   * ===========================
   * Diffuse un événement lié au personnel (création, modification, etc.)
   * 
   * @param {string} action - Action effectuée
   * @param {Object} personnelData - Données du personnel
   * @param {Object} options - Options supplémentaires
   */
  async broadcastPersonnelEvent(action, personnelData, options = {}) {
    try {
      const eventData = {
        action,
        personnel: {
          id: personnelData.id,
          nom: personnelData.nom,
          prenom: personnelData.prenom,
          matricule: personnelData.matricule
        },
        timestamp: new Date().toISOString(),
        ...options
      };

      broadcastEvent('personnel_event', eventData);
    } catch (error) {
      console.error('Erreur lors de la diffusion de l\'événement de personnel:', error);
    }
  }

  /**
   * ===========================
   * PRÉPARATION DES DONNÉES D'ÉVÉNEMENT DE PASSAGE
   * ===========================
   * Prépare les données structurées pour un événement de passage
   * 
   * @param {Object} passageData - Données du passage
   * @param {Object} options - Options supplémentaires
   * @returns {Object} Données d'événement formatées
   */
  async _preparePassageEventData(passageData, options = {}) {
    const {
      personnel = null,
      alerte = null,
      porte = null,
      raison = null
    } = options;

    // Données de base du passage
    const eventData = {
      id: passageData.id,
      epc_code: passageData.epc_code,
      type_acces: passageData.type_acces,
      resultat: passageData.resultat,
      date_acces: passageData.date_acces,
      timestamp: new Date().toISOString()
    };

    // Ajout des informations de personnel si disponibles
    if (personnel) {
      eventData.personnel = {
        nom: personnel.nom,
        prenom: personnel.prenom,
        matricule: personnel.matricule,
        grade: personnel.grade || null,
        unite: personnel.unite || null
      };
    } else {
      eventData.personnel = null;
    }

    // Ajout des informations de porte
    eventData.porte = porte || { libelle: 'Entrée Principale' };

    // Ajout du motif de refus si applicable
    if (passageData.motif_refus || raison) {
      eventData.raison = passageData.motif_refus || raison;
    }

    // Ajout des informations d'alerte si applicable
    if (alerte) {
      eventData.alerte = {
        type_alerte: alerte.type_alerte,
        niveau: alerte.niveau,
        message: alerte.message
      };
    } else {
      eventData.alerte = null;
    }

    return eventData;
  }

  /**
   * ===========================
   * PRÉPARATION DES DONNÉES D'ÉVÉNEMENT D'ALERTE
   * ===========================
   * Prépare les données structurées pour un événement d'alerte
   * 
   * @param {Object} alerteData - Données de l'alerte
   * @param {Object} options - Options supplémentaires
   * @returns {Object} Données d'événement formatées
   */
  _prepareAlerteEventData(alerteData, options = {}) {
    return {
      id: alerteData.id,
      type_alerte: alerteData.type_alerte,
      niveau: alerteData.niveau,
      message: alerteData.message,
      statut: alerteData.statut,
      id_passage: alerteData.id_passage || null,
      id_badge: alerteData.id_badge || null,
      id_personnel: alerteData.id_personnel || null,
      created_at: alerteData.created_at,
      timestamp: new Date().toISOString(),
      ...options
    };
  }

  /**
   * ===========================
   * DIFFUSION DE STATISTIQUES EN TEMPS RÉEL
   * ===========================
   * Diffuse les statistiques mises à jour
   * 
   * @param {Object} stats - Statistiques à diffuser
   */
  async broadcastStatsUpdate(stats) {
    try {
      const eventData = {
        ...stats,
        timestamp: new Date().toISOString(),
        type: 'stats_update'
      };

      broadcastEvent('stats_update', eventData);
    } catch (error) {
      console.error('Erreur lors de la diffusion des statistiques:', error);
    }
  }

  /**
   * ===========================
   * DIFFUSION D'ÉVÉNEMENT DE CONNEXION ANDROID
   * ===========================
   * Diffuse un événement de connexion/déconnexion de l'app Android
   * 
   * @param {string} action - 'connected' ou 'disconnected'
   * @param {Object} deviceInfo - Informations sur l'appareil
   */
  async broadcastAndroidConnectionEvent(action, deviceInfo = {}) {
    try {
      const eventData = {
        action,
        device: {
          ip: deviceInfo.ip || 'unknown',
          timestamp: new Date().toISOString(),
          ...deviceInfo
        },
        timestamp: new Date().toISOString()
      };

      broadcastEvent('android_connection', eventData);
    } catch (error) {
      console.error('Erreur lors de la diffusion de l\'événement de connexion Android:', error);
    }
  }

  /**
   * ===========================
   * DIFFUSION D'ÉVÉNEMENT DE MAINTENANCE
   * ===========================
   * Diffuse un événement de maintenance système
   * 
   * @param {string} type - Type de maintenance
   * @param {string} message - Message de maintenance
   * @param {Object} details - Détails supplémentaires
   */
  async broadcastMaintenanceEvent(type, message, details = {}) {
    try {
      const eventData = {
        type,
        message,
        details,
        timestamp: new Date().toISOString(),
        source: 'maintenance'
      };

      broadcastEvent('maintenance', eventData);
    } catch (error) {
      console.error('Erreur lors de la diffusion de l\'événement de maintenance:', error);
    }
  }
}

module.exports = new EventBroadcastService();
