const { Personnel, Grade, Unite } = require('../models');

/**
 * ===========================
 * SERVICE D'ENRICHISSEMENT DES DONNÉES PERSONNEL
 * ===========================
 * Ce service gère l'enrichissement des données personnel
 * avec les informations de grade et d'unité
 */
class PersonnelEnrichmentService {
  /**
   * ===========================
   * ENRICHISSEMENT D'UN PERSONNEL UNIQUE
   * ===========================
   * Enrichit les données d'un personnel avec grade et unité
   * 
   * @param {Object} personnel - Objet personnel à enrichir
   * @returns {Object} Personnel enrichi
   */
  async enrichPersonnelData(personnel) {
    if (!personnel) {
      return null;
    }

    try {
      // Récupération parallèle du grade et de l'unité
      const [grade, unite] = await Promise.all([
        personnel.id_grade ? Grade.findByPk(personnel.id_grade) : null,
        personnel.id_unite ? Unite.findByPk(personnel.id_unite) : null
      ]);

      return {
        id: personnel.id,
        nom: personnel.nom,
        prenom: personnel.prenom,
        matricule: personnel.matricule,
        cin: personnel.cin || null,
        id_type_personnel: personnel.id_type_personnel,
        societe: personnel.societe || null,
        photo: personnel.photo || null,
        grade: grade ? grade.nom_grade : null,
        unite: unite ? unite.nom_unite : null,
        created_at: personnel.created_at,
        updated_at: personnel.updated_at
      };
    } catch (error) {
      console.error('Erreur lors de l\'enrichissement du personnel:', error);
      
      // Retour des données de base en cas d'erreur
      return {
        id: personnel.id,
        nom: personnel.nom,
        prenom: personnel.prenom,
        matricule: personnel.matricule,
        grade: null,
        unite: null
      };
    }
  }

  /**
   * ===========================
   * ENRICHISSEMENT MULTIPLE
   * ===========================
   * Enrichit une liste de personnel avec optimisation des requêtes
   * 
   * @param {Array} personnelList - Liste du personnel à enrichir
   * @returns {Array} Liste du personnel enrichi
   */
  async enrichMultiplePersonnelData(personnelList) {
    if (!personnelList || personnelList.length === 0) {
      return [];
    }

    try {
      // Extraction des IDs uniques de grades et d'unités
      const gradeIds = [...new Set(
        personnelList
          .map(p => p.id_grade)
          .filter(id => id !== null && id !== undefined)
      )];

      const uniteIds = [...new Set(
        personnelList
          .map(p => p.id_unite)
          .filter(id => id !== null && id !== undefined)
      )];

      // Récupération en lot des grades et unités
      const [grades, unites] = await Promise.all([
        gradeIds.length > 0 ? Grade.findAll({ where: { id: gradeIds } }) : [],
        uniteIds.length > 0 ? Unite.findAll({ where: { id: uniteIds } }) : []
      ]);

      // Création des maps pour accès rapide
      const gradeMap = new Map(grades.map(g => [g.id, g.nom_grade]));
      const uniteMap = new Map(unites.map(u => [u.id, u.nom_unite]));

      // Enrichissement de chaque personnel
      return personnelList.map(personnel => ({
        id: personnel.id,
        nom: personnel.nom,
        prenom: personnel.prenom,
        matricule: personnel.matricule,
        cin: personnel.cin || null,
        id_type_personnel: personnel.id_type_personnel,
        societe: personnel.societe || null,
        photo: personnel.photo || null,
        grade: gradeMap.get(personnel.id_grade) || null,
        unite: uniteMap.get(personnel.id_unite) || null,
        type: this._getTypePersonnelLabel(personnel.id_type_personnel),
        created_at: personnel.created_at,
        updated_at: personnel.updated_at
      }));
    } catch (error) {
      console.error('Erreur lors de l\'enrichissement multiple:', error);
      
      // Retour des données de base en cas d'erreur
      return personnelList.map(personnel => ({
        id: personnel.id,
        nom: personnel.nom,
        prenom: personnel.prenom,
        matricule: personnel.matricule,
        grade: null,
        unite: null,
        type: this._getTypePersonnelLabel(personnel.id_type_personnel)
      }));
    }
  }

  /**
   * ===========================
   * ENRICHISSEMENT POUR SCAN
   * ===========================
   * Enrichissement optimisé pour les scans (données minimales)
   * 
   * @param {Object} personnel - Personnel à enrichir
   * @returns {Object} Personnel enrichi pour scan
   */
  async enrichForScan(personnel) {
    if (!personnel) {
      return null;
    }

    try {
      // Récupération parallèle optimisée
      const [grade, unite] = await Promise.all([
        personnel.id_grade ? 
          Grade.findByPk(personnel.id_grade, { attributes: ['nom_grade'] }) : null,
        personnel.id_unite ? 
          Unite.findByPk(personnel.id_unite, { attributes: ['nom_unite'] }) : null
      ]);

      return {
        nom: personnel.nom,
        prenom: personnel.prenom,
        matricule: personnel.matricule,
        grade: grade ? grade.nom_grade : null,
        unite: unite ? unite.nom_unite : null
      };
    } catch (error) {
      console.error('Erreur lors de l\'enrichissement pour scan:', error);
      
      // Retour des données de base
      return {
        nom: personnel.nom,
        prenom: personnel.prenom,
        matricule: personnel.matricule,
        grade: null,
        unite: null
      };
    }
  }

  /**
   * ===========================
   * RÉCUPÉRATION AVEC ENRICHISSEMENT
   * ===========================
   * Récupère un personnel par ID avec enrichissement automatique
   * 
   * @param {number} personnelId - ID du personnel
   * @returns {Object|null} Personnel enrichi ou null
   */
  async getEnrichedPersonnelById(personnelId) {
    try {
      const personnel = await Personnel.findByPk(personnelId, {
        include: [
          {
            model: Grade,
            as: 'grade',
            attributes: ['nom_grade']
          },
          {
            model: Unite,
            as: 'unite',
            attributes: ['nom_unite']
          }
        ]
      });

      if (!personnel) {
        return null;
      }

      return {
        id: personnel.id,
        nom: personnel.nom,
        prenom: personnel.prenom,
        matricule: personnel.matricule,
        cin: personnel.cin,
        id_type_personnel: personnel.id_type_personnel,
        societe: personnel.societe,
        photo: personnel.photo,
        grade: personnel.grade ? personnel.grade.nom_grade : null,
        unite: personnel.unite ? personnel.unite.nom_unite : null,
        type: this._getTypePersonnelLabel(personnel.id_type_personnel),
        created_at: personnel.created_at,
        updated_at: personnel.updated_at
      };
    } catch (error) {
      console.error('Erreur lors de la récupération enrichie:', error);
      return null;
    }
  }

  /**
   * ===========================
   * RECHERCHE AVEC ENRICHISSEMENT
   * ===========================
   * Recherche du personnel avec enrichissement et filtres
   * 
   * @param {Object} filters - Filtres de recherche
   * @param {Object} options - Options de pagination
   * @returns {Object} Résultats enrichis avec pagination
   */
  async searchEnrichedPersonnel(filters = {}, options = {}) {
    try {
      const { limit = 50, offset = 0 } = options;
      const where = this._buildSearchWhereClause(filters);

      const { count, rows } = await Personnel.findAndCountAll({
        where,
        include: [
          {
            model: Grade,
            as: 'grade',
            attributes: ['nom_grade']
          },
          {
            model: Unite,
            as: 'unite',
            attributes: ['nom_unite']
          }
        ],
        limit,
        offset,
        order: [['created_at', 'DESC']]
      });

      const enrichedPersonnel = rows.map(personnel => ({
        id: personnel.id,
        nom: personnel.nom,
        prenom: personnel.prenom,
        matricule: personnel.matricule,
        grade: personnel.grade ? personnel.grade.nom_grade : null,
        unite: personnel.unite ? personnel.unite.nom_unite : null,
        type: this._getTypePersonnelLabel(personnel.id_type_personnel),
        photo: personnel.photo,
        created_at: personnel.created_at
      }));

      return {
        personnel: enrichedPersonnel,
        total: count,
        limit,
        offset
      };
    } catch (error) {
      console.error('Erreur lors de la recherche enrichie:', error);
      return {
        personnel: [],
        total: 0,
        limit,
        offset
      };
    }
  }

  /**
   * ===========================
   * CONSTRUCTION DE LA CLAUSE WHERE POUR RECHERCHE
   * ===========================
   * Construit la clause WHERE pour les recherches
   * 
   * @param {Object} filters - Filtres
   * @returns {Object} Clause WHERE
   */
  _buildSearchWhereClause(filters) {
    const where = {};

    if (filters.nom) {
      where.nom = { [Personnel.sequelize.Op.iLike]: `%${filters.nom}%` };
    }

    if (filters.prenom) {
      where.prenom = { [Personnel.sequelize.Op.iLike]: `%${filters.prenom}%` };
    }

    if (filters.matricule) {
      where.matricule = { [Personnel.sequelize.Op.iLike]: `%${filters.matricule}%` };
    }

    if (filters.id_type_personnel) {
      where.id_type_personnel = filters.id_type_personnel;
    }

    if (filters.id_grade) {
      where.id_grade = filters.id_grade;
    }

    if (filters.id_unite) {
      where.id_unite = filters.id_unite;
    }

    return where;
  }

  /**
   * ===========================
   * CONVERSION DU TYPE DE PERSONNEL EN LIBELLÉ
   * ===========================
   * Convertit l'ID du type de personnel en libellé lisible
   * 
   * @param {number} idTypePersonnel - ID du type
   * @returns {string} Libellé du type
   */
  _getTypePersonnelLabel(idTypePersonnel) {
    const typeMapping = {
      1: 'militaire_interne',
      2: 'militaire_externe',
      3: 'civil_externe'
    };

    return typeMapping[idTypePersonnel] || 'inconnu';
  }
}

module.exports = new PersonnelEnrichmentService();
