const { Badge, AttributionBadge, TypeBadge, Personnel } = require('../models');
const crypto = require('crypto');

/**
 * ===========================
 * SERVICE DE GESTION DES BADGES
 * ===========================
 * Ce service gère toute la logique liée aux badges RFID :
 * génération, création, attribution et gestion des types
 */
class BadgeService {
  /**
   * ===========================
   * GÉNÉRATION D'UN CODE EPC UNIQUE
   * ===========================
   * Génère un code EPC unique de 24 caractères hexadécimaux
   * et vérifie qu'il n'existe pas déjà en base
   * 
   * @returns {string} Code EPC unique
   */
  async generateUniqueEPC() {
    let epc_code;
    let attempts = 0;
    const maxAttempts = 10;

    do {
      // Génération d'un code EPC de 24 caractères hexadécimaux
      epc_code = crypto.randomBytes(12).toString('hex').toUpperCase();
      
      // Vérification de l'unicité
      const existingBadge = await Badge.findOne({ where: { epc_code } });
      
      if (!existingBadge) {
        return epc_code;
      }
      
      attempts++;
    } while (attempts < maxAttempts);

    throw new Error('Impossible de générer un code EPC unique après plusieurs tentatives');
  }

  /**
   * ===========================
   * RÉCUPÉRATION DU TYPE DE BADGE APPROPRIÉ
   * ===========================
   * Récupère le type de badge selon le type de personnel
   * 
   * @param {number} typePersonnel - Type de personnel (1=militaire_interne, 2=militaire_externe, 3=civil_externe)
   * @returns {Object} Type de badge trouvé
   */
  async getBadgeTypeForPersonnel(typePersonnel) {
    const typeMapping = {
      1: 'militaire_interne',
      2: 'militaire_externe', 
      3: 'civil_externe'
    };

    const nomTypeBadge = typeMapping[typePersonnel];
    
    if (nomTypeBadge) {
      const typeBadge = await TypeBadge.findOne({
        where: { nom_type_badge: nomTypeBadge }
      });
      
      if (typeBadge) {
        return typeBadge;
      }
    }

    // Fallback : utilise le premier type de badge disponible
    const fallbackType = await TypeBadge.findOne();
    
    if (!fallbackType) {
      throw new Error('Aucun type de badge disponible dans le système');
    }

    return fallbackType;
  }

  /**
   * ===========================
   * CRÉATION D'UN BADGE POUR UN PERSONNEL
   * ===========================
   * Crée un badge avec les paramètres appropriés selon le type de personnel
   * 
   * @param {Object} personnel - Objet personnel
   * @param {string} epc_code - Code EPC (optionnel, sera généré si non fourni)
   * @returns {Object} Badge créé
   */
  async createBadgeForPersonnel(personnel, epc_code = null) {
    try {
      // Génération du code EPC si non fourni
      if (!epc_code) {
        epc_code = await this.generateUniqueEPC();
      }

      // Récupération du type de badge approprié
      const typeBadge = await this.getBadgeTypeForPersonnel(personnel.id_type_personnel);

      // Détermination des propriétés du badge selon le type de personnel
      const isPermanent = personnel.id_type_personnel === 1; // Permanent pour militaires internes
      const description = `Badge pour ${personnel.nom} ${personnel.prenom}`;

      // Création du badge
      const badge = await Badge.create({
        epc_code,
        id_type_badge: typeBadge.id,
        permanent: isPermanent,
        actif: true,
        description
      });

      return badge;
    } catch (error) {
      console.error('Erreur lors de la création du badge:', error);
      throw new Error(`Erreur lors de la création du badge: ${error.message}`);
    }
  }

  /**
   * ===========================
   * ATTRIBUTION D'UN BADGE À UN PERSONNEL
   * ===========================
   * Crée une attribution active entre un badge et un personnel
   * 
   * @param {number} personnelId - ID du personnel
   * @param {number} badgeId - ID du badge
   * @param {string} objetVisite - Objet de la visite (optionnel)
   * @returns {Object} Attribution créée
   */
  async attributeBadgeToPersonnel(personnelId, badgeId, objetVisite = null) {
    try {
      // Vérification que le personnel n'a pas déjà un badge actif (pour militaires internes)
      const personnel = await Personnel.findByPk(personnelId);
      
      if (!personnel) {
        throw new Error('Personnel non trouvé');
      }

      if (personnel.id_type_personnel === 1) {
        const existingAttribution = await AttributionBadge.findOne({
          where: { 
            id_personnel: personnelId, 
            statut: 'actif' 
          }
        });

        if (existingAttribution) {
          throw new Error('Ce personnel interne possède déjà un badge actif');
        }
      }

      // Création de l'attribution
      const attribution = await AttributionBadge.create({
        id_personnel: personnelId,
        id_badge: badgeId,
        statut: 'actif',
        objet_visite: objetVisite
      });

      return attribution;
    } catch (error) {
      console.error('Erreur lors de l\'attribution du badge:', error);
      throw new Error(`Erreur lors de l'attribution du badge: ${error.message}`);
    }
  }

  /**
   * ===========================
   * CRÉATION COMPLÈTE : BADGE + ATTRIBUTION
   * ===========================
   * Méthode principale qui crée un badge et l'attribue à un personnel
   * 
   * @param {Object} personnel - Objet personnel
   * @param {string} epc_code - Code EPC (optionnel)
   * @param {string} objetVisite - Objet de la visite (optionnel)
   * @returns {Object} { badge, attribution }
   */
  async createAndAttributeBadge(personnel, epc_code = null, objetVisite = null) {
    try {
      // Création du badge
      const badge = await this.createBadgeForPersonnel(personnel, epc_code);

      // Attribution du badge
      const attribution = await this.attributeBadgeToPersonnel(
        personnel.id, 
        badge.id, 
        objetVisite
      );

      return { badge, attribution };
    } catch (error) {
      console.error('Erreur lors de la création et attribution du badge:', error);
      throw new Error(`Erreur lors de la création et attribution du badge: ${error.message}`);
    }
  }

  /**
   * ===========================
   * DÉSACTIVATION D'UN BADGE
   * ===========================
   * Désactive un badge et ses attributions
   * 
   * @param {number} badgeId - ID du badge à désactiver
   * @returns {boolean} Succès de l'opération
   */
  async deactivateBadge(badgeId) {
    try {
      // Désactivation du badge
      await Badge.update(
        { actif: false }, 
        { where: { id: badgeId } }
      );

      // Désactivation des attributions actives
      await AttributionBadge.update(
        { statut: 'desactive' },
        { where: { id_badge: badgeId, statut: 'actif' } }
      );

      return true;
    } catch (error) {
      console.error('Erreur lors de la désactivation du badge:', error);
      throw new Error(`Erreur lors de la désactivation du badge: ${error.message}`);
    }
  }

  /**
   * ===========================
   * VÉRIFICATION D'EXISTENCE D'UN CODE EPC
   * ===========================
   * Vérifie si un code EPC existe déjà
   * 
   * @param {string} epc_code - Code EPC à vérifier
   * @returns {boolean} True si le code existe
   */
  async epcExists(epc_code) {
    const badge = await Badge.findOne({ where: { epc_code } });
    return !!badge;
  }

  /**
   * ===========================
   * RÉCUPÉRATION DES BADGES D'UN PERSONNEL
   * ===========================
   * Récupère tous les badges attribués à un personnel
   * 
   * @param {number} personnelId - ID du personnel
   * @param {string} statut - Statut des attributions ('actif', 'desactive', 'expire')
   * @returns {Array} Liste des badges avec attributions
   */
  async getBadgesForPersonnel(personnelId, statut = 'actif') {
    try {
      const attributions = await AttributionBadge.findAll({
        where: { 
          id_personnel: personnelId,
          statut: statut
        },
        include: [{
          model: Badge,
          as: 'badge'
        }]
      });

      return attributions.map(attr => ({
        attribution: attr,
        badge: attr.badge
      }));
    } catch (error) {
      console.error('Erreur lors de la récupération des badges:', error);
      throw new Error(`Erreur lors de la récupération des badges: ${error.message}`);
    }
  }
}

module.exports = new BadgeService();
