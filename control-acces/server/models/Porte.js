module.exports = (sequelize, DataTypes) => {
  return sequelize.define('Porte', {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    libelle: { type: DataTypes.STRING(100), allowNull: false },
    description: { type: DataTypes.TEXT },
    localisation: { type: DataTypes.STRING(200) },
    type_acces: { type: DataTypes.STRING(20), defaultValue: 'entree' },
    actif: { type: DataTypes.BOOLEAN, defaultValue: true }
  }, { tableName: 'porte', timestamps: true, underscored: true });
}; 