const { Sequelize, DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Personnel = require('./Personnel')(sequelize, DataTypes);
const Badge = require('./Badge')(sequelize, DataTypes);
const AttributionBadge = require('./AttributionBadge')(sequelize, DataTypes);
const Passage = require('./Passage')(sequelize, DataTypes);
const Porte = require('./Porte')(sequelize, DataTypes);
const TypeBadge = require('./TypeBadge')(sequelize, DataTypes);
const Alerte = require('./Alerte')(sequelize, DataTypes);
const Grade = require('./Grade')(sequelize, DataTypes);
const Unite = require('./Unite')(sequelize, DataTypes);

// Associations
Personnel.hasMany(AttributionBadge, { foreignKey: 'id_personnel', as: 'attributions' });
AttributionBadge.belongsTo(Personnel, { foreignKey: 'id_personnel', as: 'personnel' });
Badge.hasMany(AttributionBadge, { foreignKey: 'id_badge', as: 'attributions' });
AttributionBadge.belongsTo(Badge, { foreignKey: 'id_badge', as: 'badge' });
AttributionBadge.belongsTo(Unite, { foreignKey: 'destination', as: 'destination_unite' });
Badge.hasMany(Passage, { foreignKey: 'id_badge', as: 'passages' });
Passage.belongsTo(Badge, { foreignKey: 'id_badge', as: 'badge' });
Passage.belongsTo(Porte, { foreignKey: 'id_porte', as: 'porte' });
Personnel.belongsTo(Grade, { foreignKey: 'id_grade', as: 'grade' });
Personnel.belongsTo(Unite, { foreignKey: 'id_unite', as: 'unite' });
Personnel.belongsTo(Unite, { foreignKey: 'id_unite_origine', as: 'unite_origine' });
Badge.belongsTo(TypeBadge, { foreignKey: 'id_type_badge', as: 'type_badge' });
TypeBadge.hasMany(Badge, { foreignKey: 'id_type_badge', as: 'badges' });

module.exports = {
  sequelize,
  Sequelize,
  Personnel,
  Badge,
  AttributionBadge,
  Passage,
  Porte,
  TypeBadge,
  Alerte,
  Grade,
  Unite
};
