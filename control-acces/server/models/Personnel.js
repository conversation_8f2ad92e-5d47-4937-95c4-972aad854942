module.exports = (sequelize, DataTypes) => {
  return sequelize.define('Personnel', {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    nom: { type: DataTypes.STRING(100), allowNull: false },
    prenom: { type: DataTypes.STRING(100), allowNull: false },
    matricule: { type: DataTypes.STRING(50), unique: true },
    cin: { type: DataTypes.STRING(20), unique: true },
    id_type_personnel: { type: DataTypes.INTEGER, allowNull: false },
    id_grade: { type: DataTypes.INTEGER, allowNull: true },
    id_unite: { type: DataTypes.INTEGER, allowNull: true },
    id_unite_origine: { type: DataTypes.INTEGER, allowNull: true },
    societe: { type: DataTypes.STRING(100) },
    photo: { type: DataTypes.TEXT, allowNull: true }
  }, { tableName: 'personnel', timestamps: true, underscored: true });
};