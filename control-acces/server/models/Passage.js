module.exports = (sequelize, DataTypes) => {
  return sequelize.define('Passage', {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    id_badge: { type: DataTypes.INTEGER, allowNull: true },
    epc_code: { type: DataTypes.STRING(100), allowNull: true },
    id_porte: { type: DataTypes.INTEGER, allowNull: false },
    date_acces: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
    type_acces: { type: DataTypes.STRING(10), allowNull: false },
    resultat: { type: DataTypes.STRING(20), defaultValue: 'autorise' },
    motif_refus: { type: DataTypes.TEXT, allowNull: true }
  }, { tableName: 'passage', timestamps: true, underscored: true });
}; 