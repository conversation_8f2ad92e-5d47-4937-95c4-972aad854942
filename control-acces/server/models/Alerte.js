module.exports = (sequelize, DataTypes) => {
  return sequelize.define('Alerte', {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    id_passage: { type: DataTypes.INTEGER },
    id_personnel: { type: DataTypes.INTEGER },
    id_badge: { type: DataTypes.INTEGER },
    type_alerte: { type: DataTypes.STRING(50), allowNull: false },
    niveau: { type: DataTypes.STRING(10), defaultValue: 'orange' },
    message: { type: DataTypes.TEXT, allowNull: false },
    statut: { type: DataTypes.STRING(20), defaultValue: 'ouvert' },
    commentaire: { type: DataTypes.TEXT }
  }, { tableName: 'alerte', timestamps: true, underscored: true });
}; 