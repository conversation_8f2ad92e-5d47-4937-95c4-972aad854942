module.exports = (sequelize, DataTypes) => {
  return sequelize.define('Badge', {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    epc_code: { type: DataTypes.STRING(100), allowNull: false, unique: true },
    numero_visuel: { type: DataTypes.STRING(50), allowNull: true },
    id_type_badge: { type: DataTypes.INTEGER, allowNull: false },
    permanent: { type: DataTypes.BOOLEAN, defaultValue: false },
    actif: { type: DataTypes.BOOLEAN, defaultValue: true },
    description: { type: DataTypes.TEXT }
  }, {
    tableName: 'badge',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });
};