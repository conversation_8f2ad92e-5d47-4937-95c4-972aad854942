module.exports = (sequelize, DataTypes) => {
  return sequelize.define('AttributionBadge', {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    id_personnel: { type: DataTypes.INTEGER, allowNull: false },
    id_badge: { type: DataTypes.INTEGER, allowNull: false },
    date_attribution: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
    date_fin: { type: DataTypes.DATE },
    statut: { type: DataTypes.STRING(20), defaultValue: 'actif' },
    destination: { type: DataTypes.INTEGER },
    objet_visite: { type: DataTypes.TEXT }
  }, { tableName: 'attribution_badge', timestamps: true, underscored: true });
};