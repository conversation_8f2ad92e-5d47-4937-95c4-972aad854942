module.exports = (sequelize, DataTypes) => {
  return sequelize.define('TypeBadge', {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    nom_type_badge: { type: DataTypes.STRING(50), allowNull: false },
    description: { type: DataTypes.TEXT },
    couleur: { type: DataTypes.STRING(7), defaultValue: '#3B82F6' },
    duree_validite: { type: DataTypes.INTEGER }
  }, { tableName: 'type_badge', timestamps: true, underscored: true });
}; 