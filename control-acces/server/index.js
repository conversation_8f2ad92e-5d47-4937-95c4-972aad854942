const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const path = require('path');
require('dotenv').config();

const { sequelize } = require('./models');
const { initWebSocketServer } = require('./utils/broadcast');
const { detectWifiIP, displayAndroidConnectionInfo } = require('./utils/networkUtils');
const corsOptions = require('./utils/corsOptions');

const app = express();
const PORT = process.env.PORT || 3011;
const WS_PORT = process.env.WS_PORT || 3012;

// Initialisation WebSocket
initWebSocketServer(WS_PORT);

// ===========================
// MIDDLEWARE
// ===========================
app.use(helmet());
app.use(compression());
app.use(morgan('combined'));
app.use(cors(corsOptions));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// ===========================
// ROUTES API
// ===========================
app.use('/api/personnel', require('./routes/personnel'));
app.use('/api/visiteurs', require('./routes/visiteurs'));
app.use('/api/badges', require('./routes/badges'));
app.use('/api/passages', require('./routes/passages'));
app.use('/api/scan', require('./routes/scan'));
app.use('/api/stats', require('./routes/stats'));

// Routes de debug/test
app.use('/api', require('./routes/debug'));

// ===========================
// DÉMARRAGE DU SERVEUR
// ===========================
const startServer = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ Connexion à PostgreSQL établie avec succès');
    await sequelize.sync();
    
    // Détection de l'IP WiFi pour l'application Android
    const wifiIp = detectWifiIP();
    displayAndroidConnectionInfo(wifiIp, PORT);
    
    app.listen(PORT, () => {
      console.log(`🚀 Serveur API démarré sur le port ${PORT}`);
      console.log(`📡 WebSocket démarré sur le port ${WS_PORT}`);
      console.log(`🌐 URL API: http://localhost:${PORT}`);
      console.log(`⚙️ Environnement: ${process.env.NODE_ENV || 'development'}`);
    });
  } catch (error) {
    console.error('❌ Erreur lors du démarrage:', error.message);
    process.exit(1);
  }
};

startServer();
