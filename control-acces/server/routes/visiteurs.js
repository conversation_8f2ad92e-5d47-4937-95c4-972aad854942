const express = require('express');
const multer = require('multer');
const path = require('path');
const visiteurController = require('../controllers/visiteurController');

const router = express.Router();

// Configuration multer pour l'upload de photos
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'visiteur-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB max
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Seules les images sont autorisées'), false);
    }
  }
});

// Routes
router.get('/', visiteurController.getVisiteurs);
router.post('/', upload.single('photo'), visiteurController.createVisiteur);
router.put('/:id', visiteurController.updateVisiteur);
router.post('/:id/terminer', visiteurController.terminerVisite);
router.delete('/:id', visiteurController.deleteVisiteur);

module.exports = router;
