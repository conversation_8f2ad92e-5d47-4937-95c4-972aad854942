const express = require('express');
const router = express.Router();
const personnelController = require('../controllers/personnelController');
const upload = require('../utils/upload');
const gradeController = require('../controllers/gradeController');
const uniteController = require('../controllers/uniteController');

router.post('/', upload.single('photo'), personnelController.creerPersonnel);
router.get('/', personnelController.listerPersonnel);
router.delete('/:id', personnelController.supprimerPersonnel);
router.put('/:id', personnelController.modifierPersonnel);

// Routes pour listes grades et unités
router.get('/grades', gradeController.listerGrades);
router.get('/unites', uniteController.listerUnites);
router.post('/unites', uniteController.creerUnite);

module.exports = router; 