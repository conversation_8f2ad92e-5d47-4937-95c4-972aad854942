const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const scanController = require('../controllers/scanController');

// Route de test général de l'API
router.get('/test', scanController.testConnection);

// Route pour vérifier le chemin absolu du dossier uploads
router.get('/uploads-path', (req, res) => {
  const uploadsPath = path.join(__dirname, '..', 'uploads');
  const photosPath = path.join(uploadsPath, 'photos');
  
  try {
    res.json({
      uploadsPath,
      exists: fs.existsSync(uploadsPath),
      photosExists: fs.existsSync(photosPath),
      files: fs.existsSync(photosPath) ? fs.readdirSync(photosPath) : []
    });
  } catch (error) {
    res.status(500).json({
      error: 'Erreur lors de la vérification du dossier uploads',
      message: error.message
    });
  }
});

module.exports = router;
