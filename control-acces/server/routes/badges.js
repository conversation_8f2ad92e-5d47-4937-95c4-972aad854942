const express = require('express');
const router = express.Router();
const badgeController = require('../controllers/badgeController');

router.post('/attribuer', badgeController.attribuerBadge);
router.get('/', badgeController.listerBadges);
router.get('/disponibles', badgeController.getBadgesDisponibles);
router.get('/types', badgeController.getTypesBadges);
router.post('/types', badgeController.createTypeBadge);
router.post('/permanent', badgeController.createBadgePermanent);
router.post('/multitache', badgeController.createBadgeMultitache);
router.delete('/:id', badgeController.supprimerBadge);
router.post('/:id/write', badgeController.writeEpcToTerminal);

module.exports = router; 