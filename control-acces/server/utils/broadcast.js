const WebSocket = require('ws');

let wss = null;
const clients = new Set();

function initWebSocketServer(port) {
  wss = new WebSocket.Server({ port });
  wss.on('connection', (ws) => {
    clients.add(ws);
    console.log(`📡 Client WebSocket connecté. Total: ${clients.size}`);
    ws.on('close', () => {
      clients.delete(ws);
      console.log(`📡 Client WebSocket déconnecté. Total: ${clients.size}`);
    });
  });
  return wss;
}

function broadcastEvent(type, data) {
  const message = JSON.stringify({ type, data, timestamp: new Date().toISOString() });
  clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(message);
    }
  });
}

module.exports = {
  initWebSocketServer,
  broadcastEvent
}; 