const os = require('os');

/**
 * Détecte l'adresse IP WiFi de la machine
 * @returns {string|null} Adresse IP WiFi ou null si non trouvée
 */
function detectWifiIP() {
  const interfaces = os.networkInterfaces();
  let wifiIp = null;

  // Recherche prioritaire sur les interfaces WiFi
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && 
          !iface.internal && 
          (name.toLowerCase().includes('wi-fi') || 
           name.toLowerCase().includes('wifi') || 
           name.toLowerCase().includes('wlan'))) {
        wifiIp = iface.address;
        break;
      }
    }
    if (wifiIp) break;
  }

  // Fallback sur toute interface réseau non-interne
  if (!wifiIp) {
    for (const name of Object.keys(interfaces)) {
      for (const iface of interfaces[name]) {
        if (iface.family === 'IPv4' && !iface.internal) {
          wifiIp = iface.address;
          break;
        }
      }
      if (wifiIp) break;
    }
  }

  return wifiIp;
}

/**
 * Affiche les informations de connexion pour l'application Android
 * @param {string} ip - Adresse IP détectée
 * @param {number} port - Port du serveur API
 */
function displayAndroidConnectionInfo(ip, port) {
  if (ip) {
    console.log('📱 Adresse à saisir dans l\'app Android (C72) :', ip);
    console.log(`➡️ Exemple à saisir : ${ip}`);
    console.log(`➡️ Exemple URL test : http://${ip}:${port}/api/scan/test`);
    console.log(`➡️ Exemple URL scan : http://${ip}:${port}/api/scan`);
  } else {
    console.log('⚠️ Impossible de détecter automatiquement l\'adresse IP WiFi. Vérifiez votre connexion réseau.');
  }
}

module.exports = {
  detectWifiIP,
  displayAndroidConnectionInfo
};
